<?php

namespace V4;

class LearnerController extends Controller
{
    /** @var LearnerModel */
    public $learnerModel;

    /** @var Response */
    public $response;

    /** @var LearnerFilter */
    public $learnerFilter;

    /** @var UserModel */
    public $userModel;

    /** @var Common */
    public $common;

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadLibary('common');
        $this->loadModel('learner');
        $this->loadModel('user');
        $this->loadFilter('learner');
    }

    /**
     * Private function to handle authentication and authorization check
     * 
     * @param \WP_REST_Request $request The request object
     * @return array|null Returns error response array if authentication fails, null if successful
     */
    private function authenticateAndAuthorize($request)
    {
        $userId = 0;
        $authToken = $request->get_header('authorization');

        // Check if authorization token is present
        if (empty($authToken)) {
            return $this->response->error('TOKEN_FAIL', ['message' => 'Authorization token not found.']);
        }

        // Check token format and extract token
        if (stripos($authToken, 'bearer ') !== 0) {
            return $this->response->error('TOKEN_FAIL', ['message' => 'Invalid token format. Missing Bearer prefix.']);
        }

        list(, $cleanToken) = explode(" ", $authToken, 2);
        if (empty($cleanToken)) {
            return $this->response->error('TOKEN_FAIL', ['message' => 'Token is empty after Bearer.']);
        }

        // Try jwt_token_validation_check function first
        if (function_exists('jwt_token_validation_check')) {
            $validationResult = jwt_token_validation_check($cleanToken);
            
            if (is_numeric($validationResult) && $validationResult > 0) {
                $userId = (int) $validationResult;
            } elseif ($validationResult === true && function_exists('token_validation_check')) {
                // Fallback to token_validation_check
                $userIdFromTvCheck = token_validation_check($cleanToken);
                if (is_numeric($userIdFromTvCheck) && $userIdFromTvCheck > 0) {
                    $userId = (int) $userIdFromTvCheck;
                }
            }
        }
        
        // Manual JWT token decoding as last resort
        if (!($userId > 0)) {
            try {
                $tokenParts = explode('.', $cleanToken);
                if (count($tokenParts) === 3) {
                    // Decode the payload (second part)
                    $payloadEncoded = $tokenParts[1];
                    
                    // Add padding if necessary for base64 decoding
                    $paddingLength = 4 - (strlen($payloadEncoded) % 4);
                    if ($paddingLength !== 4) {
                        $payloadEncoded .= str_repeat('=', $paddingLength);
                    }
                    
                    $payloadJson = base64_decode(strtr($payloadEncoded, '-_', '+/'));
                    $payload = json_decode($payloadJson, true);
                    
                    if ($payload && is_array($payload)) {
                        // Try different possible user ID fields
                        $possibleUserIdFields = ['userId', 'user_id', 'sub', 'id', 'uid'];
                        
                        foreach ($possibleUserIdFields as $field) {
                            if (isset($payload[$field]) && is_numeric($payload[$field]) && $payload[$field] > 0) {
                                $userId = (int) $payload[$field];
                                break;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Token decoding failed, $userId remains 0
            }
        }

        if (!($userId > 0)) {
            return $this->response->error('USER_ID_FAIL', ['message' => 'Could not determine User ID from token.']);
        }

        // Get user role
        $userRole = $this->userModel->getUserRole($userId);
        if (!$userRole || $userRole === 'unknown' || $userRole === false) {
            return $this->response->error('ROLE_FAIL', ['message' => 'Could not determine user role.']);
        }

        // Check if user has authorized role (instructor, yuno-admin, org-admin, or admin)
        $allowedRoles = ['instructor', 'yuno-admin', 'org-admin', 'admin'];
        if (!in_array($userRole, $allowedRoles)) {
            return $this->response->error('ACCESS_DENIED', [
                'message' => 'You are not authorized to access this resource. Required roles: ' . implode(', ', $allowedRoles) . '. Your role: ' . $userRole
            ]);
        }
        // Authentication and authorization successful
        return null;
    }

    private function parseFilterParam($param, bool $asInt = true)
    {
        if (empty($param)) {
            return [];
        }

        $ids = is_string($param) ? explode(',', trim($param, '[] ')) : (array) $param;
        $ids = array_map('trim', $ids);
        $ids = array_filter($ids);
        
        if ($asInt) {
            $ids = array_map('intval', $ids);
        }

        return array_values($ids);
    }

    public function getLearners($request)
    {
        try {
            // Authentication and Authorization Check
            $authResult = $this->authenticateAndAuthorize($request);
            if ($authResult !== null) {
                return $authResult; // Return the error response
            }

            $rawFilters = $request->get_params();
            
            $searchFilters = [
                'academies'   => $this->parseFilterParam($rawFilters['academies'] ?? null),
                'courses'     => $this->parseFilterParam($rawFilters['courses'] ?? null),
                'batches'     => $this->parseFilterParam($rawFilters['batches'] ?? null),
                'classes'     => $this->parseFilterParam($rawFilters['classes'] ?? null),
                'org'         => $this->parseFilterParam($rawFilters['org'] ?? null, false),
                'instructors' => $this->parseFilterParam($rawFilters['instructors'] ?? null, false),
            ];

            $finalFilters = array_filter($searchFilters);
            if (isset($rawFilters['limit']) && isset($rawFilters['offset'])) {
                $finalFilters['limit'] = (int) $rawFilters['limit'];
                $finalFilters['offset'] = (int) $rawFilters['offset'];
            } else {
                $finalFilters['limit'] = YN_DEFAULT_LIMIT;
                $finalFilters['offset'] = YN_DEFAULT_OFFSET;
            }

            $data = $this->learnerModel->getLearners($finalFilters);

            if (empty($data['data'])) {
                return $this->response->error('GET_FAIL', ['replace' => 'Learners']);
            }

            return $this->response->success('GET_SUCCESS', $data, ['message' => 'Learners list fetched.']);

        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }

    public function getLearnerFilters($request)
    {
        try {
            // Authentication and Authorization Check
            $authResult = $this->authenticateAndAuthorize($request);
            if ($authResult !== null) {
                return $authResult; // Return the error response
            }

            if (function_exists('ynLog')) {
                ynLog("getLearnerFilters - Request received: " . json_encode($request->get_params()), 'getLearnerFilters');
            }

            $getVars = $request->get_query_params();
            if (isset($getVars['limit']) && isset($getVars['offset'])) {
                $limit = (int)$getVars['limit'];
                $offset = (int)$getVars['offset'];
            } else {
                $limit = YN_DEFAULT_LIMIT;
                $offset = YN_DEFAULT_OFFSET;
            }

            $filters = [];

            $filters[] = $this->learnerFilter->generateAcademyFilterData($request, $limit, $offset);
            $filters[] = $this->learnerFilter->generateCourseFilterData($request, $limit, $offset);
            $filters[] = $this->learnerFilter->generateBatchFilterData($request, $limit, $offset);
            $filters[] = $this->learnerFilter->generateClassFilterData($request, $limit, $offset);
            $filters[] = $this->learnerFilter->generateOrganizationFilterData($request, $limit, $offset);
            $filters[] = $this->learnerFilter->generateInstructorFilterData($request, $limit, $offset);

            $filters = array_filter($filters);
            
            if (empty($filters)) {
                 return $this->response->error("GET_FAIL", ['message' => "No filters found."]);
            }
            
            return $this->response->success("GET_SUCCESS", $filters, ['message' => "Learner filters fetched successfully."]);

        } catch (\Exception $e) {
            if (function_exists('ynLog')) {
                ynLog("getLearnerFilters - Exception: " . $e->getMessage(), 'getLearnerFilters_Error');
            }
            return $this->response->error('GET_FAIL', [
                'message' => isset($this->common) ? $this->common->globalExceptionMessage($e) : $e->getMessage()
            ]);
        }
    }
} 