<?php


namespace V4;

/**
 * Class AcademyModel
 * Handles Academy-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class AcademyModel extends Model
{
    /** @var object $es */ // ElasticSearch library instance
    public $es;

    /** @var object $common */ // Common library instance
    public $common;

    /** @var object $locale */ // Locale library instance
    public $locale;

    /** @var object $schema */ // Schema library instance
    public $schema;

    /** @var \V4\UserModel $userModel */
    public $userModel;

    /**
     * Constructor for AcademyModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('schema');
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('common');
        $this->loadLibary('locale');
        $this->loadLibary('dateTime', 'dt');
    }


    /**
     * Retrieves a list of academies based on organization ID.
     *
     * This function queries Elasticsearch to fetch academies linked to a specific organization ID, 
     * along with the associated instructors, courses, and other academy details.
     *
     * @since 1.0.0
     * @access public
     * @param array $query The query parameters containing the organization ID.
     * @param array $filter (optional) Additional filters for the response.
     * @return mixed An associative array of academies, or false if no academies are found or the query fails.
     * @throws Exception If an error occurs during the Elasticsearch query or response handling.
     * <AUTHOR>
     */
    public function getAcademies($query, $filter = [])
    {
        if (isset($query)) {

            if (isset($query['orgId'])) {
                $query['custom'] = [
                    "query" => [
                        "match" => [
                            "data.details.org_id" => $query['orgId']
                        ]
                    ]
                ];

                if (isset($query['_source']) && is_array($query['_source']) && !empty($query['_source'])) {
                    $query['custom']['_source'] = $query['_source'];
                }
            }

            if (isset($query['custom'])) {
                if (ynSchemaType($filter, 'Academy_Minimal')) {
                    $query['custom']['_source'] = [
                        'data.details.id',
                        'data.details.academy_name'
                    ];
                } else {
                    $query['custom']['_source'] = [
                        'data.details.id',
                        'data.details.academy_name',
                        'data.details.logo',
                        'data.details.fav_icon_url',
                        'data.details.banner_image',
                        'data.details.excerpt',
                        'data.details.description',
                        'data.details.org_id',
                        'data.details.category',
                        'data.details.published_at'
                    ];
                }

                if (isset($query['custom']['_source']) && is_array($query['custom']['_source']) && !empty($query['custom']['_source'])) {
                    $query['custom']['_source'] = $query['custom']['_source'];
                }
            }

            $academyCntResponse = $this->es->count('academies', $query['custom']);

            if ($academyCntResponse['status_code'] == 200) {
                $academyDataResponse = $this->es->customQuery($query['custom'], 'academies', $query['qryStr'] ?? null);
            } else {
                return false;
            }
        } else {
            return false;
        }

        if ($academyDataResponse['status_code'] == 200) {
            $responseCount = $academyCntResponse['body']['count'];
            $academies = $academyDataResponse['body']['hits']['hits'];

            if ($responseCount > 0 && is_countable($academies) && count($academies) > 0) {
                $responseData = [];

                foreach ($academies as $academyData) {
                    $academy = $academyData['_source']['data']['details'];

                    $responseData[] = [
                        'id' => $academy['id'],
                        'name' => $academy['academy_name'],
                        'logo_url' => [
                            'url' => $academy['logo'] ?? '',
                            'alt_text' => !empty($academy['logo'])
                                ? $this->common->imgAltTextFromUrl($academy['logo']) : ""
                        ],
                        'fav_icon_url' => [
                            'url' => $academy['fav_icon_url'] ?? '',
                            'alt_text' => !empty($academy['fav_icon_url'])
                                ? $this->common->imgAltTextFromUrl($academy['fav_icon_url']) : ""
                        ],
                        'banner_image_url' => [
                            'url' => $academy['banner_image'] ?? '',
                            'alt_text' => !empty($academy['banner_image'])
                                ? $this->common->imgAltTextFromUrl($academy['banner_image']) : ""
                        ],
                        'short_description' => $academy['excerpt'] ?? '',
                        'long_description' => $academy['description'] ?? '',
                        'active_learners' => $this->load->subData('academy', 'getAcademyActiveLearnersCount', $academy['id']),
                        'past_learners' => $this->load->subData('academy', 'getAcademyInactiveLearnersCount', $academy['id']),
                        'org' => $this->load->subData('org', 'getOrganization', $academy['org_id'] ?? 0, ['schema' => 'Organization_Minimal', 'noResponse' =>'Organization_Minimal']),
                        'courses' => $this->load->subData('academy', 'getAcademyCourseCount', $academy['id']),
                        'mapped_instructors' => $this->load->subData('academy', 'getAcademyMappedInstructorsCount', $academy['id']),
                        'category' => !empty($academy['category']) && is_array($academy['category']) ? array_map(function ($category) {
                            if (is_string($category)) {
                                return [
                                    'id' => 0,
                                    'name' => $category,
                                    'slug' => sanitize_title($category)
                                ];
                            }
                            return [
                                'id' => $category['id'] ?? 0,
                                'name' => $category['name'] ?? '',
                                'slug' => $category['slug'] ?? sanitize_title($category['name'] ?? '')
                            ];
                        }, $academy['category']) : false,
                        'created_time' => [
                            'time' => isset($academy['published_at'])
                                ? $this->dt->convertToActiveDT($academy['published_at'])
                                : '0000-00-00T00:00:00Z',
                            'timezone' => $this->locale->activeTimezone()
                        ]
                    ];
                }
                if (isset($filter['schema'])) {
                    $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
                }
                
                // Validate the response against the Academy schema
                return $this->schema->validate(['count' => $responseCount, 'data' => $responseData], ['count' => 'integer', 'data' => ['Refer#Academy']], $filter);
            }
        }

        return false;
    }

    /**
     * Retrieves detailed information about a specific academy.
     *
     * This function queries Elasticsearch to fetch data for a specific academy, including instructors, courses, 
     * learners, and other related details.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query Academy ID or an associative array containing the academy ID.
     * @param array $filter (optional) Additional filters for the response.
     * @return mixed An associative array containing academy details or false if no academy is found.
     * @throws Exception If an error occurs during the Elasticsearch query or response handling.
     * <AUTHOR>
     */
    public function getAcademy($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            if(ynSchemaType($filter, 'Academy_Minimal')){
                $qryStr['_source'] = implode(',', [
                    'data.details.id',
                    'data.details.academy_name'
                ]);
            }else{
                $qryStr['_source'] = implode(',', [
                    'data.details.id',
                    'data.details.academy_name',
                    'data.details.logo',
                    'data.details.fav_icon_url',
                    'data.details.banner_image',
                    'data.details.excerpt',
                    'data.details.description',
                    'data.details.org_id',
                    'data.details.category',
                    'data.details.published_at'
                ]);
            }

            if(isset($query['_source']) && is_array($query['_source']) && !empty($query['_source'])){
                $qryStr['_source'] = implode(',', $query['_source']);
            }

            $academyDataResponse = $this->es->read('academies', 'academies-' . $query['id'], $qryStr);
        } else {
            return false;
        }

        if ($academyDataResponse['status_code'] == 200) {

            $academy = $academyDataResponse['body']['_source']['data']['details'];

            $academyResponse = [
                'id' => $academy['id'],
                'name' => $academy['academy_name'] ?? '',
                'logo_url' => [
                    'url' => $academy['logo'] ?? '',
                    'alt_text' => !empty($academy['logo'])
                        ? $this->common->imgAltTextFromUrl($academy['logo']) : ""
                ],
                'fav_icon_url' => [
                    'url' => $academy['fav_icon_url'] ?? '',
                    'alt_text' => !empty($academy['fav_icon_url'])
                        ? $this->common->imgAltTextFromUrl($academy['fav_icon_url']) : ""
                ],
                'banner_image_url' => [
                    'url' => $academy['banner_image'] ?? '',
                    'alt_text' => !empty($academy['banner_image'])
                        ? $this->common->imgAltTextFromUrl($academy['banner_image']) : ""
                ],
                'short_description' => $academy['excerpt'] ?? '',
                'long_description' => $academy['description'] ?? '',
                'active_learners' => $this->load->subData('academy', 'getAcademyActiveLearnersCount', $academy['id']),
                'past_learners' => $this->load->subData('academy', 'getAcademyInactiveLearnersCount', $academy['id']),
                'org' => $this->load->subData('org', 'getOrganization', $academy['org_id'] ?? 0, ['schema' => 'Organization_Minimal']),
                'courses' => $this->load->subData('academy', 'getAcademyCourseCount', $academy['id']),
                'mapped_instructors' => $this->load->subData('academy', 'getAcademyMappedInstructorsCount', $academy['id']),
                'category' => !empty($academy['category']) && is_array($academy['category']) ? array_map(function ($category) {
                    if (is_string($category)) {
                        return [
                            'id' => 0,
                            'name' => $category,
                            'slug' => sanitize_title($category)
                        ];
                    }
                    return [
                        'id' => $category['id'] ?? 0,
                        'name' => $category['name'] ?? '',
                        'slug' => $category['slug'] ?? sanitize_title($category['name'] ?? '')
                    ];
                }, $academy['category']) : false,
                'created_time' => [
                    'time' => isset($academy['published_at'])
                        ? $this->dt->convertToActiveDT($academy['published_at'])
                        : '0000-00-00T00:00:00Z',
                    'timezone' => $this->locale->activeTimezone()
                ]
            ];

            return $this->schema->validate($academyResponse, 'Academy', $filter);
        }

        return false;
    }

    /**
     * Retrieves the count of active learners for the given courses.
     *
     * This function queries Elasticsearch to count users who are enrolled in specific courses
     * and have an enrollment status of 'ACTIVE'.
     *
     * @since 1.0.0
     * @access public
     * @param array $courseIds An array of course IDs.
     * @return int The count of active learners.
     * <AUTHOR>
     */
    public function getAcademyActiveLearnersCount($academyId)
    {
        if (!is_numeric($academyId) || $academyId <= 0) {
            return 0;
        }

        $query = [
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['term' => ['data.details.academies' => (int) $academyId]],
                                ['term' => ['data.details.is_enable' => true]]
                            ]
                        ]
                    ]
                ]
            ],
            'size' => 0,
            '_source' => false,
            'aggs' => [
                'record_ids' => [
                    'nested' => ['path' => 'data.details'],
                    'aggs' => [
                        'record_id_list' => [
                            'terms' => [
                                'field' => 'data.details.record_id',
                                'size' => 1000
                            ]
                        ]
                    ]
                ]
            ]
        ];
        
        $results = $this->es->customQuery($query, 'course', ['size' => 0]);
       
        $courseIds = [];

        if ($results['status_code'] === 200) {
            $courseResponse = $results['body']['aggregations']['record_ids']['record_id_list']['buckets'] ?? [];

            foreach ($courseResponse as $key => $course) {
                $courseIds[] = $course['key'];
                unset($results['body']['aggregations']['record_ids']['record_id_list']['buckets'][$key]);
            }
        }

        unset($results);
        // Validate and format courses array

        if (!is_array($courseIds) && !empty($courseIds)) {
            return 0;
        }

        // Build the query dynamically
        $query = [
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'terms' => [
                                'data.details.course_id' => $courseIds
                            ]
                        ],
                        [
                            'term' => [
                                'data.details.enrollment_status.keyword' => 'ACTIVE'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Send the query to Elasticsearch
        $response = $this->es->count('batchenrollmentevent', $query);

        if ($response['status_code'] == 200) {
            return $response['body']['count'];
        }
        return 0;
    }

    /**
     * Retrieves the count of inactive learners for the given courses.
     *
     * This function queries Elasticsearch to count users who are enrolled in specific courses
     * and have an enrollment status of 'INACTIVE'.
     *
     * @since 1.0.0     
     * @access public
     * @param array $courseIds An array of course IDs.
     * @return int The count of inactive learners.
     * <AUTHOR>
     */
    public function getAcademyInactiveLearnersCount($academyId)
    {
        if (!is_numeric($academyId) || $academyId <= 0) {
            return 0;
        }

        $query = [
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['term' => ['data.details.academies' => (int)$academyId]],
                                ['term' => ['data.details.is_enable' => true]]
                            ]
                        ]
                    ]
                ]
            ],
            'size' => 0,
            '_source' => false,
            'aggs' => [
                'record_ids' => [
                    'nested' => ['path' => 'data.details'],
                    'aggs' => [
                        'record_id_list' => [
                            'terms' => [
                                'field' => 'data.details.record_id',
                                'size' => 1000
                            ]
                        ]
                    ]
                ]
            ]
        ];
        
        $results = $this->es->customQuery($query, 'course', ['size' => 0]);
       
        $courseIds = [];

        if ($results['status_code'] === 200) {
            $courseResponse = $results['body']['aggregations']['record_ids']['record_id_list']['buckets'] ?? [];

            foreach ($courseResponse as $key => $course) {
                $courseIds[] = $course['key'];
                unset($results['body']['aggregations']['record_ids']['record_id_list']['buckets'][$key]);
            }
        }

        unset($results);
        // Validate and format courses array

        if (!is_array($courseIds) && !empty($courseIds)) {
            return 0;
        }

        // Build the query dynamically
        $query = [
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'terms' => [
                                'data.details.course_id' => $courseIds
                            ]
                        ],
                        [
                            'term' => [
                                'data.details.enrollment_status.keyword' => 'INACTIVE'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Send the query to Elasticsearch
        $response = $this->es->count('batchenrollmentevent', $query);

        if ($response['status_code'] == 200) {
            return $response['body']['count'];
        }
        return 0;
    }

    /**
     * Retrieves the count of active courses for a specific academy.
     *
     * @since 1.0.0
     * @access public
     * @param int $academyId The ID of the academy to get course count for
     * @return int The number of active courses for the academy, 0 if none found
     * <AUTHOR>
     */
    public function getAcademyCourseCount($academyId) 
    {
        if (!is_numeric($academyId) || $academyId <= 0) {
            return 0;
        }

        $query = [
            "query" => [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                ["term" => ["data.details.academies" => (int)$academyId]],
                                ["term" => ["data.details.is_enable" => true]]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $response = $this->es->count('course', $query);

        if ($response['status_code'] === 200) {
            return $response['body']['count'];
        }

        return 0;
    }

    
    /**
     * Retrieves the count of mapped instructors for a specific academy.
     *
     * @since 1.0.0
     * @access public
     * @param int $academyId The ID of the academy to get instructor count for
     * @return int The number of mapped instructors for the academy, 0 if none found
     * <AUTHOR>
     */
    public function getAcademyMappedInstructorsCount($academyId)
    {
        if (!is_numeric($academyId) || $academyId <= 0) {
            return 0;
        }

        $query = [
            "size" => 0,
            "query" => [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                ["term" => ["data.details.academies" => (int)$academyId]],
                                ["term" => ["data.details.is_enable" => true]]
                            ]
                        ]
                    ]
                ]
            ],
            "aggs" => [
                "mapped_instructors_count" => [
                    "nested" => [
                        "path" => "data.details"
                    ],
                    "aggs" => [
                        "distinct_mapped_instructors" => [
                            "terms" => [
                                "field" => "data.details.mapped_instructors.keyword",
                                "size" => 1000
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $response = $this->es->customQuery($query, 'course', ['size' => 0]);

        if ($response['status_code'] === 200) {
            // Get the doc_count from the mapped_instructors_count aggregation
            $docCount = $response['body']['aggregations']['mapped_instructors_count']['doc_count'] ?? 0;
            return $docCount;
        }

        return 0;
    }

    /**
     * Generates academy filter for a dropdown.
     *
     * Fetches details for a selected academy (if provided) and retrieves academy items from ES
     * based on predefined categories. Returns an array to populate the academy filter UI.
     *
     * @since 1.0.0
     * @access public
     * @param int $academyId The academy identifier.
     * @return array The filter array for academies.
     * <AUTHOR>
     */
    public function generateAcademyFilters($academyId)
    {
        $row1 = [
            'filter' => 'academy',
            'title' => 'academy',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Academy',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($academyId)) {
            $academy_data = $this->getAcademy($academyId, ['schema' => 'Academy_Minimal']);

            $academy_name = $academy_data['name'] ?? '';
            $sel_academy_id = $academy_data['id'] ?? 0;

            $sel_items = [
                'id' => $sel_academy_id,
                'label' => $academy_name,
                'filter' => 'academy',
            ];
            $row1['selected'] = $sel_items['id'];
        }

        $categories = ["all", "ielts", "pte", "english-speaking", "toefl", "duolingo", "french", "data-science-and-analytics"];

        $shouldQueries = [];
        foreach ($categories as $category) {
            $shouldQueries[] = [
                'match' => [
                    'data.details.category.name' => $category
                ]
            ];
        }

        $curlPost = [
            'query' => [
                'bool' => [
                    'should' => $shouldQueries,
                    'minimum_should_match' => 1
                ]
            ]
        ];

        $response = $this->es->customQuery($curlPost, 'academies');

        if (isset($response['body']['hits']['hits']) && !empty($response['body']['hits']['hits'])) {
            foreach ($response['body']['hits']['hits'] as $academy) {
                $academyDetails = $academy['_source']['data']['details'];
                $academy_name = $academyDetails['academy_name'] ?? '';
                $acad_id = $academyDetails['id'] ?? 0;

                $exists = array_filter($row1['items'], function ($item) use ($acad_id) {
                    return $item['id'] === $acad_id;
                });

                if (!$exists) {
                    $sel_items = [
                        'id' => $acad_id,
                        'label' => $academy_name,
                        'filter' => 'academy',
                    ];
                    $row1['items'][] = $sel_items;
                }
            }
        }

        return $row1;
    }

    /**
     * Retrieves a list of academies with enriched metadata such as course count, instructor count,
     * and learner enrollment stats. Supports filtering, pagination, sorting, and category filtering.
     *
     * Accepts query params such as org ID, category IDs, course/enrollment filters, and pagination values.
     * Also handles advanced filtering like has_active_enrollments, has_past_enrollments, and has_courses.
     *
     * @since 1.0.0
     * @access public
     * @param array $query {
     *     Query parameters to filter and paginate the results.
     *
     *     @type int    $org_id                 Organization ID to filter academies.
     *     @type array  $categories             List of category IDs to filter.
     *     @type string $sort_by                Sort field (e.g., 'name', 'recently_signed_up').
     *     @type string $sort_order             Sort direction ('asc' or 'desc').
     *     @type int    $limit                  Pagination limit.
     *     @type int    $offset                 Pagination offset.
     *     @type string $has_active_enrollments 'true' to include only with active learners, 'false' to exclude them.
     *     @type string $has_past_enrollments   'true' to include only with past learners, 'false' to exclude them.
     *     @type string $has_courses            'true' to include only academies with courses, 'false' to exclude them.
     * }
     * @return array|false {
     *     Array with total count and formatted academy data, or false on failure.
     *
     *     @type int   $count Total number of matching academies.
     *     @type array $data  List of academy objects.
     * }
     * <AUTHOR>
     */
    /*public function getAcademyList($esQuery, $filterParams = [], $qryStr = [])
    {
        try {
            if (isset($esQuery['custom'])) {
                $esQuery = $esQuery['custom'];
            }

            $limit = isset($qryStr['size']) ? (int) $qryStr['size'] : 20;
            $offset = isset($qryStr['from']) ? (int) $qryStr['from'] : 0;
            $overfetchLimit = $limit * 3;

            $overfetchQryStr = [
                'from' => 0, 
                'size' => $overfetchLimit
            ];

            $response = $this->es->customQuery($esQuery, 'academies', $overfetchQryStr);

            if ($response['status_code'] !== 200) {
                return false;
            }

            $hits = $response['body']['hits']['hits'];
            $schemaClasses = ['Refer#Academy'];
            $academies = [];

            foreach ($hits as $hit) {
                $academy = $hit['_source']['data']['details'];
                $academy['org_id'] = (int) $academy['org_id'];
                $academy['id'] = (int) $academy['id'];

                $orgDetails = [];
                if (!empty($academy['org_id'])) {
                    $orgDetails = $this->load->subData("org", "getOrganization", $academy['org_id'], ['schema' => 'Organization_Minimal', 'noResponse' => ['id' => 0, 'name' => '']]);
                }

                $courseQuery = [
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["term" => ["data.details.org_id" => $academy['org_id']]],
                                        ["term" => ["data.details.academies" => $academy['id']]],
                                        ["term" => ["data.details.is_enable" => true]]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "size" => 0
                ];
                $courseResponse = $this->es->customQuery($courseQuery, 'course');
                $academy['courses'] = $courseResponse['body']['hits']['total']['value'] ?? 0;

                $instructorQuery = [
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["term" => ["data.details.org_id" => $academy['org_id']]],
                                        ["term" => ["data.details.academies" => $academy['id']]],
                                        ["term" => ["data.details.is_enable" => true]]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "size" => 100,
                    "_source" => ["data.details.mapped_instructors"]
                ];
                $instructorResponse = $this->es->customQuery($instructorQuery, 'course');
                $instructorIds = [];

                if (!empty($instructorResponse['body']['hits']['hits'])) {
                    foreach ($instructorResponse['body']['hits']['hits'] as $course) {
                        $instructors = $course['_source']['data']['details']['mapped_instructors'] ?? [];
                        foreach ($instructors as $inst) {
                            if (!empty($inst['id'])) {
                                $instructorIds[$inst['id']] = true;
                            }
                        }
                    }
                }

                $academy['instructors'] = count($instructorIds);

                $enrollmentQuery = [
                    "query" => [
                        "bool" => [
                            "must" => [
                                ["exists" => ["field" => "data.details.course_name"]],
                                ["term" => ["data.details.org_admin.id" => $academy['org_id']]]
                            ]
                        ]
                    ],
                    "size" => 10000
                ];
                $enrollmentResponse = $this->es->customQuery($enrollmentQuery, 'batchenrollmentevent');
                $activeEnrollments = 0;
                $pastEnrollments = 0;

                if ($enrollmentResponse['status_code'] === 200) {
                    foreach ($enrollmentResponse['body']['hits']['hits'] as $enrollment) {
                        $status = strtoupper($enrollment['_source']['data']['details']['enrollment_status'] ?? '');
                        if ($status === 'ACTIVE') {
                            $activeEnrollments++;
                        } elseif ($status === 'INACTIVE') {
                            $pastEnrollments++;
                        }
                    }
                }

                $academy['active_learners'] = $activeEnrollments;
                $academy['past_learners'] = $pastEnrollments;

                if (isset($filterParams['has_active_enrollments'])) {
                    if ($filterParams['has_active_enrollments'] === 'true' && $academy['active_learners'] === 0) {
                        continue;
                    }
                    if ($filterParams['has_active_enrollments'] === 'false' && $academy['active_learners'] > 0) {
                        continue;
                    }
                }

                if (isset($filterParams['has_past_enrollments'])) {
                    if ($filterParams['has_past_enrollments'] === 'true' && $academy['past_learners'] === 0) {
                        continue;
                    }
                    if ($filterParams['has_past_enrollments'] === 'false' && $academy['past_learners'] > 0) {
                        continue;
                    }
                }

                if (isset($filterParams['has_courses'])) {
                    if ($filterParams['has_courses'] === 'true' && $academy['courses'] === 0) {
                        continue;
                    }
                    if ($filterParams['has_courses'] === 'false' && $academy['courses'] > 0) {
                        continue;
                    }
                }

                $academies[] = [
                    'id' => $academy['id'],
                    'name' => $academy['academy_name'],
                    'logo_url' => [
                        'url' => $academy['logo'] ?? '',
                        'alt_text' => $this->common->imgAltTextFromUrl($academy['logo']) ?: 'Academy logo'
                    ],
                    'banner_image_url' => [
                        'url' => $academy['banner_image'] ?? '',
                        'alt_text' => $this->common->imgAltTextFromUrl($academy['banner_image']) ?: 'Banner Image'
                    ],
                    "fav_icon_url" => ['url' => '', 'alt_text' => ''],
                    'short_description' => $academy['excerpt'] ?? '',
                    'long_description' => $academy['description'] ?? '',
                    'category' => !empty($academy['category']) && is_array($academy['category'])
                        ? array_map(function($category) {
                            if (is_string($category)) {
                                return [
                                    'id' => 0,
                                    'name' => $category,
                                    'slug' => sanitize_title($category)
                                ];
                            }

                            return [
                                'id' => $category['id'] ?? 0,
                                'name' => $category['name'] ?? '',
                                'slug' => $category['slug'] ?? sanitize_title($category['name'] ?? '')
                            ];
                        }, $academy['category'])
                        : [[
                            'id' => 0,
                            'name' => '',
                            'slug' => ''
                        ]],
                    'org' => $orgDetails,
                    'active_learners' => $academy['active_learners'],
                    'past_learners' => $academy['past_learners'],
                    'courses' => $academy['courses'],
                    'mapped_instructors' => $academy['instructors'],
                    'created_time' => [
                        'time' => isset($academy['published_at']) 
                            ? $this->dt->convertToSystemDT($academy['published_at'], "Y-m-d\TH:i:s\Z") 
                            : '0000-00-00T00:00:00Z',
                        'timezone' => $this->locale->activeTimezone()
                    ]

                ];
            }

            $paginatedAcademies = array_slice($academies, $offset, $limit);

            if (empty($paginatedAcademies)) {
                return false;
            }

            $schema = [
                'count' => 'integer',
                'data' => $schemaClasses
            ];

            return $this->schema->validate(
                [
                    'count' => count($academies), 
                    'data' => $paginatedAcademies  
                ],
                $schema,
                $filterParams
            );

        } catch (\Exception $e) {
            error_log("Error in AcademyModel::getAcademyList: " . $e->getMessage());
            return false;
        }
    }*/

    /**
     * Prepares filter data for organizations and categories.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The user ID for role-based filtering
     * @param int $orgId The organization ID for role-based filtering
     * @return array Array containing organization and category data
     * <AUTHOR>
     */
    
    public function prepareAcademiesFilterData($userId, $request = [])
     {
         try {
             $orgId = $request['orgId'] ?? 0;
             $this->loadModel('user');
             $role = $this->userModel->getUserRole($userId);
             $queryConditions = [];
     
             if ($role === 'org-admin' && $orgId > 0) {
                 $queryConditions[] = ['term' => ['data.details.org_id' => $orgId]];
             }
     
             $orgQuery = [
                 "query" => [
                     "bool" => [
                         "must" => $queryConditions
                     ]
                 ],
                 "size" => 1000,
                 "_source" => ["data.details.record_id", "data.details.organisation_name"]
             ];
             $orgResponse = $this->es->customQuery($orgQuery, 'org');
     
             $orgs = [];
             if ($orgResponse['status_code'] === 200 && !empty($orgResponse['body']['hits']['hits'])) {
                 foreach ($orgResponse['body']['hits']['hits'] as $org) {
                     $orgData = $org['_source']['data']['details'];
                     if (!empty($orgData['record_id']) && !empty($orgData['organisation_name'])) {
                         $orgs[] = [
                             'id' => $orgData['record_id'],
                             'label' => $orgData['organisation_name'],
                             'filter' => 'organization'
                         ];
                     }
                 }
             }
     
             $allowedCategories = [
                 'IELTS',
                 'English Speaking',
                 'TOEFL',
                 'Duolingo',
                 'French',
                 'Data Science & Analytics'
             ];
     
             $categories = [];
             $terms = get_terms([
                 'taxonomy' => 'course_category',
                 'hide_empty' => false,
                 'parent' => 0
             ]);
     
             if (!is_wp_error($terms) && is_array($terms)) {
                 foreach ($terms as $term) {
                     if (in_array($term->name, $allowedCategories)) {
                         $categories[] = [
                             'id' => (int)$term->term_id,
                             'label' => $term->name,
                             'slug' => $term->slug,
                             'filter' => 'category'
                         ];
                     }
                 }
             } else {
                 error_log("Warning: get_terms() returned unexpected result: " . print_r($terms, true));
             }
     
             $categoryRaw = $request['category'] ?? [];
     
             if (is_string($categoryRaw)) {
                 $cleaned = preg_replace('/\s+/', '', $categoryRaw); 
                 $decoded = json_decode($cleaned, true);
                 $requestedCategoryIds = is_array($decoded) ? $decoded : [];
             } elseif (is_array($categoryRaw)) {
                 $requestedCategoryIds = $categoryRaw;
             } else {
                 $requestedCategoryIds = [];
             }
     
             $cleanCategoryIds = array_filter(
                 array_map('intval', $requestedCategoryIds),
                 fn($id) => $id > 0
             );
     
             $filteredCategories = $categories;
     
             // Other filters
             $hasActiveEnrollments = $request['has_active_enrollments'] ?? null;
             $hasPastEnrollments = $request['has_past_enrollments'] ?? null;
             $hasCourses = $request['has_courses'] ?? null;
     
             // Filter orgs
             if (!empty($orgId) && is_numeric($orgId)) {
                 $orgs = array_filter($orgs, function ($org) use ($orgId) {
                     return (int)$org['id'] === (int)$orgId;
                 });
                 $orgs = array_values($orgs);
             }

             $selectedOrgId = ($orgId > 0 && !empty($orgs)) ? (int)$orgs[0]['id'] : 'all';
     
             // Build filters
             $filters = [];
     
             $filters[] = [
                 'filter' => 'organization',
                 'title' => 'Organization',
                 'is_active' => true,
                 'multiple' => false,
                 'placeholder' => 'Select Organization',
                 'ui_control_type' => 'dropdown',
                 'selected' =>  $selectedOrgId,
                 'items' => $orgs
             ];
     
             $filters[] = [
                 'filter' => 'category',
                 'title' => 'Category',
                 'is_active' => true,
                 'multiple' => true,
                 'placeholder' => 'Select Category',
                 'ui_control_type' => 'dropdown',
                 'selected' => $cleanCategoryIds,
                 'items' => $filteredCategories
             ];
     
            //  $filters[] = [
            //     'filter' => 'has_active_enrollments',
            //     'title' => 'Has Active Enrollments',
            //     'is_active' => true,
            //     'multiple' => false,
            //     'placeholder' => null,
            //     'ui_control_type' => 'checkbox',
            //     'selected' => filter_var($hasActiveEnrollments, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false,
            //     'items' => []
            // ];
            
            // $filters[] = [
            //     'filter' => 'has_past_enrollments',
            //     'title' => 'Has Past Enrollments',
            //     'is_active' => true,
            //     'multiple' => false,
            //     'placeholder' => null,
            //     'ui_control_type' => 'checkbox',
            //     'selected' => filter_var($hasPastEnrollments, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false,
            //     'items' => []
            // ];
            
            // $filters[] = [
            //     'filter' => 'has_course',
            //     'title' => 'Has a Course',
            //     'is_active' => true,
            //     'multiple' => false,
            //     'placeholder' => null,
            //     'ui_control_type' => 'checkbox',
            //     'selected' => filter_var($hasCourses, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false,
            //     'items' => []
            // ];            
     
             return $filters;
     
         } catch (Exception $e) {
             error_log("prepareAcademiesFilterData error: " . $e->getMessage());
             return [];
         }
     }
    
    
    /**
     * Retrieves demo instructors for a list of academy IDs.
     *
     * @since 1.0.0
     * @access public
     * @param array $academyIds An array of academy IDs.
     * @return array|false An array containing demo instructors for each academy, or false on failure.
     * <AUTHOR> Name
     */
    public function getDemoInstructorsForAcademies(array $academyIds, string $numericCategoryFilterValue = 'all', string $type = 'all')
    {
        if (empty($academyIds)) {
            return [];
        }

        error_log("getDemoInstructorsForAcademies called with academyIds: " . json_encode($academyIds) . ", categoryFilter: " . $numericCategoryFilterValue . ", type: " . $type); // DEBUG

        $esDocumentIds = array_map(function ($id) {
            return 'academies-' . $id;
        }, $academyIds);

        $query = [
            'query' => [
                'terms' => [
                    '_id' => $esDocumentIds,
                ],
            ],
            '_source' => ['data.details.id', 'data.details.demo_instructors', 'data.details.category'],
            'size' => count($academyIds), 
        ];

        try {
            $response = $this->es->customQuery($query, 'academies');
            error_log("ES response for academies: " . json_encode($response)); // DEBUG

            if ($response['status_code'] !== 200 || !isset($response['body']['hits']['hits'])) {
                error_log('Elasticsearch query failed or returned unexpected structure: ' . json_encode($response));
                return false;
            }

            $results = [];
            foreach ($response['body']['hits']['hits'] as $hit) {
                error_log("Processing hit: " . ($hit['_id'] ?? 'unknown_id')); // DEBUG
                if (!isset($hit['_source']['data']['details'])) {
                    error_log("Skipping hit due to missing _source.data.details: " . ($hit['_id'] ?? 'unknown_id')); // DEBUG
                    continue;
                }
                
                $details = $hit['_source']['data']['details'];
                $academyId = $details['id'] ?? null;
                $academyCategories = $details['category'] ?? []; // Academy's own category associations
                $rawDemoInstructors = $details['demo_instructors'] ?? [];

                if (!$academyId) {
                    error_log("Skipping hit due to missing academyId."); // DEBUG
                    continue; 
                }

                error_log("Academy ID: " . $academyId . ", Raw Categories from ES: " . json_encode($academyCategories) . ", Raw Demo Instructors: " . json_encode($rawDemoInstructors)); // DEBUG

                $finalInstructorsForThisAcademy = [];
                $shouldIncludeThisAcademy = false;

                if ($numericCategoryFilterValue === 'all') {
                    $shouldIncludeThisAcademy = true;
                    $finalInstructorsForThisAcademy = $rawDemoInstructors; // Take all instructors as-is
                    error_log("Academy " . $academyId . ": numericCategoryFilterValue is 'all'. Including with all raw demo instructors."); // DEBUG
                } else {
                    // A specific numeric category filter is applied.
                    $targetNumericCatIds = array_map('intval', explode(',', $numericCategoryFilterValue));
                    error_log("Academy " . $academyId . ": Target Numeric Category IDs: " . json_encode($targetNumericCatIds)); // DEBUG

                    $academyIsAssociatedWithTargetCatID = false;
                    $slugsFromAcademyToFilterBy = []; 

                    if (is_array($academyCategories)) {
                        foreach ($academyCategories as $catAssoc) {
                            if (isset($catAssoc['id']) && in_array((int)$catAssoc['id'], $targetNumericCatIds, true)) {
                                $academyIsAssociatedWithTargetCatID = true;
                                if (isset($catAssoc['slug']) && !empty($catAssoc['slug'])) {
                                    $slugsFromAcademyToFilterBy[] = $catAssoc['slug'];
                                }
                            }
                        }
                    }
                    $slugsFromAcademyToFilterBy = array_unique($slugsFromAcademyToFilterBy);

                    error_log("Academy " . $academyId . ": Association with target CatIDs: " . ($academyIsAssociatedWithTargetCatID ? 'Yes' : 'No') . ". Slugs from academy to filter by: " . json_encode($slugsFromAcademyToFilterBy)); // DEBUG

                    if ($academyIsAssociatedWithTargetCatID) {
                        if (!empty($slugsFromAcademyToFilterBy)) {
                            $instructorsMatchingSlugs = [];
                            if (!empty($rawDemoInstructors) && is_array($rawDemoInstructors)) {
                                foreach ($rawDemoInstructors as $categoryEntry) {
                                    if (is_array($categoryEntry) && count($categoryEntry) === 1) {
                                        $slugKeyInDemoInstructors = key($categoryEntry);
                                        if (in_array($slugKeyInDemoInstructors, $slugsFromAcademyToFilterBy, true)) {
                                            // Ensure the instructor list for this slug is not empty
                                            $instructorListForSlug = reset($categoryEntry); // Get the value (instructor array)
                                            if (!empty($instructorListForSlug)) {
                                                $instructorsMatchingSlugs[] = $categoryEntry;
                                            }
                                        }
                                    }
                                }
                            }
                            
                            if (!empty($instructorsMatchingSlugs)) {
                                $finalInstructorsForThisAcademy = $instructorsMatchingSlugs;
                                $shouldIncludeThisAcademy = true;
                                error_log("Academy " . $academyId . ": Associated, slugs found, and non-empty matching instructors found. Including."); // DEBUG
                            } else {
                                error_log("Academy " . $academyId . ": Associated, slugs found, but matching instructors list is empty or no match. Skipping."); // DEBUG
                            }
                        } else {
                            error_log("Academy " . $academyId . ": Associated with target CatID, but NO slugs derived from its own categories. Skipping."); // DEBUG
                        }
                    } else {
                        error_log("Academy " . $academyId . ": NOT associated with target CatIDs. Skipping."); // DEBUG
                    }
                }

                if ($shouldIncludeThisAcademy) {
                    $results[] = [
                        'academy_id' => (int)$academyId,
                        'instructors' => $finalInstructorsForThisAcademy, 
                    ];
                    error_log("Academy " . $academyId . ": Added to results. Instructors: " . json_encode($finalInstructorsForThisAcademy)); // DEBUG
                } else {
                     error_log("Academy " . $academyId . ": NOT added to results due to filtering conditions."); // DEBUG
                }
            }
            error_log("Final results from getDemoInstructorsForAcademies: " . json_encode($results)); // DEBUG
            return $results;
        } catch (\Exception $e) {
            error_log("Error in AcademyModel::getDemoInstructorsForAcademies: " . $e->getMessage());
            return false;
        }
    }
}
