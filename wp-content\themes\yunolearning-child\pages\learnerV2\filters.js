Vue.component("yuno-filter-tab", {
  template: `
    <section class="filterWrapper">
      	<template v-if="filters.loading">
			<div class="filterDropdown">
				<div class="skeletonFilter" v-for="i in 3" :key="i">
					<b-skeleton height="43px" width="150px"></b-skeleton>
				</div>
			</div>
      	</template>
      	<template v-else-if="filters.success && filters.error === null">
			<div class="filterHeader" v-if="showAllFilters && isResponsive">
				<h3 class="largerTitle">Filters</h3>
				<a class="filterTrigger" @click="toggleFilters">
					<span class="material-icons">close</span>
				</a>
			</div>
			<div class="filter">
				<div class="filterDropdown">
					<template v-for="(tab, index) in filters.data">
						<div 
							v-if = "tab.ui_control_type === 'dropdown'"
							:key="index"
							class="yunoDropdown" 
							:class="{ 'hidden': isResponsive && !showAllFilters && index !== 0 }"
						>
							<b-dropdown 
								v-model="tab.selected"
								aria-role="list"
								:mobile-modal="false"
							>
								<template #trigger>
									<div class="labelWrapper">
										<span class="placeHolder onSurfaceVariant caption1 noBold">{{ tab.title }}</span>
										<span class="selectedItem onSurface subtitle2 noBold">{{ showSelected(tab.selected, tab) }}</span>
										<a 
											href="#" 
											class="clearFilter" 
											v-if="manageClearFilterCTA(tab)"
											@click="clearFilter($event, tab)"
										>
											<span class="material-icons">cancel</span>
										</a>
										<span class="material-icons-outlined iconWrapper onSurfaceVariant">arrow_drop_down</span>
									</div>
								</template>
								<template>
									<b-dropdown-item 
										v-for="(option, i) in tab.items" 
										:key="i"
										:value="option.slug" 
										aria-role="listitem"
										@click="onChange(option.id, tab , index)"
									>
										{{ option.label }}
									</b-dropdown-item>
								</template>
							</b-dropdown>
						</div>
					</template>
				</div>
				<a 
					class="filterTrigger" 
					v-if="isResponsive && !showAllFilters" 
					@click="toggleFilters"
				>
					<span class="material-icons">filter_list</span>
				</a>
			</div>
      	</template>
    </section>
  `,
  data() {
    return {
      showAllFilters: false,
      isResponsive: false,
    };
  },

  computed: {
    ...Vuex.mapState(["filterResult", "filters"]),

    activeTab() {
      return this.filterResult.tabs.activeTab;
    },
  },
  mounted() {
    this.updateResponsiveState();
    window.addEventListener("resize", this.updateResponsiveState);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateResponsiveState);
  },
  methods: {
    toggleFilters() {
      this.showAllFilters = !this.showAllFilters;
	  this.$emit("toggleFilters", this.showAllFilters);
    },
    manageClearFilterCTA(tab) {
      return tab.selected !== null && tab.selected !== 0;
    },
    clearFilter(e, tab) {
      e.preventDefault();
      tab.selected = 0;
      this.updatePayload(tab.filter, 0);
      this.$emit(
        "filterChange",
        this.filterResult.tabs.items[this.activeTab].slug
      );
    },
    showSelected(selected, tab) {
      if (!selected || selected === 0) {
        return tab.placeholder;
      } else {
        return tab.items.find((item) => item.id === selected).label;
      }
    },
    onChange(selectedId, tab, index) {
      tab.selected = selectedId;
      this.updatePayload(tab.filter, selectedId);
      this.$emit(
        "filterChange",
        this.filterResult.tabs.items[this.activeTab].slug
      );
	  this.showAllFilters = false;
	  this.$emit("toggleFilters", false);
    },
    updatePayload(filterType, value) {
      this.filterResult.payload[filterType] = value;
    },
    updateResponsiveState() {
      this.isResponsive = window.innerWidth <= 768;
    },
  },
});
