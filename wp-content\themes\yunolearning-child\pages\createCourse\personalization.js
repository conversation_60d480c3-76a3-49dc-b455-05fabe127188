Vue.component('personalization', {
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    template: `
        <div class="yunoFormWrapper">
            <validation-observer 
                tag="div" 
                class="observer row"
                ref="personalizationObserver" 
                v-slot="{ handleSubmit, invalid }"
            >
                <form @submit.prevent="handleSubmit(initForm)" class="col-12 col-md-6">
                    <h2 class="subtitle1 onSurfaceVariant m-bottom-small-times-1">Personalization</h2>
                    <p class="caption1 onSurfaceVariant noBold m-bottom-large-times-1">How do you offer classes of this course while teaching online?</p>
                    
                    <b-field 
                        v-for="(field, i) in fields"
                        :key="i"
                        class="hasInput"
                    >
                        <!-- Checkbox Field -->
                        <validation-provider 
                            tag="div" 
                            class="fieldWrapper" 
                            :rules="{ required: true }" 
                            v-slot="{ errors, classes }"
                        >
                            <b-checkbox 
                                v-model="filterResult.payload.personalization"
                                :native-value="field.value"
                                :class="classes"
                                @input="onInput($event, field)"
                            >
                                <p class="subtitle2 onSurfaceVariant">{{ field.label }}</p>
                                <small class="caption1 onSurfaceVariant italic noBold">{{ field.helper }}</small>
                            </b-checkbox>
                            <p class="error">{{errors[0]}}</p>
                        </validation-provider>
                        <validation-provider 
                            class="inputField"
                            tag="div"
                            v-if="field.value === '1-Many' || field.value === '1-1'"
                            :rules="{ required: !isDisabled(field.value), numeric: true }" 
                            v-slot="{ errors, classes }"
                        >
                            <b-field>
                                <b-input 
                                    :disabled="isDisabled(field.value)"
                                    :placeholder="field.pricePlaceholder"
                                    v-model="filterResult.payload[field.value === '1-Many' ? 'group_price' : 'one_to_one_price']"
                                    :class="classes"
                                    @input="onPriceInput($event, field)"
                                    type="number"
                                    min="0"
                                ></b-input>
                                <p class="error">{{errors[0]}}</p>
                            </b-field>
                        </validation-provider>
                    </b-field>
                </form>
            </validation-observer>
        </div>
    `,
    data() {
        return {
            personalization: [],
            isFormValid: false,
            fields: [
                {
                    name: "personalization",
                    label: "In a group setting",
                    helper: "Classes will be taught in a group setting, consisting of multiple learners.",
                    value: "1-Many",
                    showPrice: false,
                    isSelected: false,
                    price: "",
                    pricePlaceholder: "Enter price for group"
                },
                {
                    name: "personalization",
                    label: "1-to-1",
                    helper: "Classes will be taught in a 1-to-1 setting, consisting of only one learner.",
                    value: "1-1",
                    showPrice: false,
                    isSelected: false,
                    price: "",
                    pricePlaceholder: "Enter price for 1-to-1"
                }
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'filterResult',
        ]),
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        isDisabled(value) {
            return !this.filterResult.payload.personalization.includes(value);
        },
        onPriceInput(e, field) {
            this.$nextTick(() => {
                this.$refs.personalizationObserver.validate().then(valid => {
                    this.$props.data.isCompleted = valid;
                });
            });
        },
        onInput(e, field) {
            const priceFieldMap = {
                '1-Many': 'group_price',
                '1-1': 'one_to_one_price'
            };

            // If the field is being unchecked, clear its price
            if (!e.includes(field.value)) {
                this.$refs.personalizationObserver.reset();
                this.filterResult.payload[priceFieldMap[field.value]] = "";
            }

            //  Validate form state
            this.$nextTick(() => {
                this.$refs.personalizationObserver.validate().then(valid => {
                    this.$props.data.isCompleted = valid;
                });
            });
        },
        initForm() {
            
        }
    }
});