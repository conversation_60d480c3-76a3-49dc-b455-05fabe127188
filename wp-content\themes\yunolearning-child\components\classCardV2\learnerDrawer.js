Vue.component("yuno-learner-drawer", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
  	<div class="wrapper">
		<div class="cardContentWrapper">
			<div class="videoWrapper" v-if="isPast && data?.recording?.url != ''">
				<template v-if="videoList.loading">
					<b-skeleton height="300px"></b-skeleton>
				</template>
				<template v-if="videoList.success && videoList.error === null && videoEmbed != ''">
					<div class="videoLPPlayer" v-html="videoEmbed"></div>
				</template>
			</div>
			<div class="classStatus" v-if="!isPast && data.temporal_status != 'upcoming'">
				<span class="dot"></span>
				<span>{{ data.temporal_status }}</span>
			</div>
			<div>
				<span :class="['classType mr-2', 'onSurfaceVariant', data.type == 'WEBINAR' ? 'lightPeach' : 'bgLightBlue']">
					{{ data.type }}
				</span>
				<span 
					class="classType bgLightGreen" 
					v-if="isPast"
				>
					{{ data?.recording?.url ? 'RECORDING AVAILABLE' : 'RECORDING NOT AVAILABLE' }}
				</span>
			</div>
			<span class="classTitle headline5">{{ data.class_title.title }}</span>
			<div class="d-flex align-items-center">
				<span class="onSurfaceVariant subtitle2 noBold"> {{ formattedDateTime }} (EST)</span>
				<span class="separator">|</span>
				<span v-if="hasDuration" class="onSurfaceVariant subtitle2 noBold">{{ totalDuration }} Minutes</span>
			</div>
			<div class="classDetails">
				<span class="onSurfaceVariant subtitle2">BATCH: {{ data.batch.title }}</span>
				<span class="separator">|</span>
				<span class="onSurfaceVariant subtitle2">ID: {{ data.batch.id }}</span> 
				<span v-if="isPast" class="separator">|</span>
				<div class="d-flex" v-if="isPast && data?.recording?.url != ''">
					<div class="recordingDuration">
						<div class="playIcon">
							<span class="material-icons">play_arrow</span>
						</div>
					<span>{{ data?.recording?.duration }} minutes</span>
					</div>
				</div>
			</div>
			<div class="learnerAttendence" v-if="isPast">
				<template v-if="data?.attendance.status">
					<div class="d-flex">
						<span class="onSurfaceVariant pr-1">
							Attendance: {{ data?.attendance.duration }} minutes
						</span>
						<span class="onSurfaceVariant">
						({{ (data?.attendance?.duration_percentage || 0) }}%)
						</span>
					</div>
					<b-progress
						:type="attendanceClass" 
						:value="data?.attendance?.duration_percentage || 0"
						style="flex-grow: 1;"
					>
					</b-progress>
				</template>
				<template v-else>
					<span class="classType bgLightPink">ABSENT</span>
				</template>
			</div>
			<div class="userProfile" >
				<span>
					<img :src="data.instructor.user.image_url ">
				</span>
				<div class="userDescription">
					<span class="onSurfaceVariant subtitle3">{{ data.instructor.user.role[0] }}</span>
					<span class="onSurface subtitle2 underline">{{ data.instructor.user.full_name }}</span>
				</div>
			</div>       
		</div>
		<div class="buttonWrapper" v-if="data?.temporal_status == 'upcoming'">
			<div class="cta">
				<b-button 
					tag="a"
					class="button yunoPrimaryCTA"
					:disabled="!data.virtual_classroom.meeting_url || data.virtual_classroom.meeting_url.trim() === ''"
					:href="data.virtual_classroom.meeting_url"
				>
					Launch Class
				</b-button>
			</div>
		</div>
		<div class="courseDetails d-flex flex-column">
			<span class="onSurfaceVariant overline">COURSE</span>
			<span class="subtitle1 onSurface">{{ data.course.title }}</span>
			<div>
				<span v-if="academy?.logo_url?.url != ''" class="academyLogo">
					<img :src="academy.logo_url.url" alt="Academy Icon" />
				</span>
				<span v-if="academy?.name" class="onSurface">{{ formattedAcademyName }}</span>
			</div>
		</div>
		<template v-if="false">
			<div class="pt-4">
				<b-skeleton height="14px" width="26%" type="is-rounded"></b-skeleton>
				<div class="pt-4" v-for="index in 3" :key="index">
					<div class="classReview d-flex pt-2">
						<span>
							<b-skeleton circle  height="32px" width="32px" type="is-rounded"></b-skeleton>
						</span>
						<div class="learnerReviewWrapper w-100">
							<b-skeleton height="14px" width="26%" type="is-rounded"></b-skeleton>
							<b-skeleton height="40px" width="70%" type="is-rounded"></b-skeleton>
						</div>
					</div>
				</div>
			</div>
		</template>
		<template v-if="false">
			<div class="pt-4" v-if="drawer?.data?.attendance_of_each?.length > 0">
				<span class="onSurface subtitle1 ">Class Reviews</span>
				<div class="classReview d-flex pt-2" v-for="(review, index) in drawer?.data?.attendance_of_each" :key="index">
  					<template v-if="review?.learner?.full_name !== '' ">
						<span>
							<img v-if="review?.learner?.image_url" :src="review?.learner?.image_url">
							<span v-else class="material-icons profileIcon">account_circle</span>
						</span>
						<div class="learnerReviewWrapper">
							<span class="onSurface subtitle2">{{ review?.learner?.full_name }}</span>
							<span class="onSurfaceVariant overline">May 24, 2024</span>
							<div>
								<span class="material-icons starIcon">star</span>
								<span class="material-icons starIcon">star</span>
								<span class="material-icons starIcon">star</span>
								<span class="material-icons starIcon">star</span>
								<span class="material-icons starIcon">star</span>
							</div>
							<div class="onSurfaceVariant subtitle2 noBold">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</div>
						</div>
					</template>
				</div>
			</div>
		</template>
	</div>
  `,
  data() {
    return {};
  },
  computed: {
    ...Vuex.mapState(["userInfo", "userRole", "drawer", "videoList"]),

    videoEmbed() {
      return this.videoList.data.videoEmbed;
    },

    academy() {
      return this.data.academy;
    },

    attendanceClass() {
      const p = this.data?.attendance?.duration_percentage || 0;
      if (p <= 30) return "is-red";
      if (p <= 50) return "is-orange";
      if (p <= 70) return "is-yellow";
      if (p <= 80) return "is-lightGreen";
      if (p <= 90) return "is-blue";
      return "is-green";
    },

    isPast() {
      return this.data.temporal_status === "past";
    },

    formattedDateTime() {
      const time =
        this.data.temporal_status === "past"
          ? this.data?.actual?.start?.time
          : this.data?.scheduled?.start?.time;

      if (!time || time.trim() === "") {
        return "Schedule pending";
      }

      const dateTime = new Date(time);

      // Check if the date is valid
      if (isNaN(dateTime.getTime())) {
        return "Invalid date format";
      }

      try {
        const date = dateTime.toLocaleDateString("en-US", {
          day: "2-digit",
          month: "long",
          year: "numeric",
        });
        const timeStr = dateTime.toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        });
        return `${date} - ${timeStr}`;
      } catch (error) {
        console.error("Error formatting date:", error);
        return "Error formatting date";
      }
    },
    totalDuration() {
      if (this.data.temporal_status !== "past") {
        return this.data.scheduled.duration;
      } else {
        return this.data.actual.duration;
      }
    },
    hasDuration() {
      if (this.data.temporal_status !== "past") {
        return this.data?.scheduled?.duration > 0;
      } else {
        return this.data?.actual?.duration > 0;
      }
    },
    formattedAcademyName() {
      // Check if academy name exists and is not empty
      if (!this.data?.academy?.name) {
        return "";
      }
      return this.data.academy.name
        .split("-") // Convert into array by splitting at '-'
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
        .join(" "); // Join back as a string with spaces
    },
  },
  methods: {},
});
