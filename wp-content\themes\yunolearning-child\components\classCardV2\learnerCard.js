Vue.component("yuno-learner-card", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
        <div class="cardContent">
            <div class="cardContentWrapper">
                <div class="classStatus" v-if="!isPast && data.temporal_status != 'upcoming'">
                    <span class="dot"></span>
                    <span>{{ data.temporal_status }}</span>
                </div>
                <span class="onSurfaceVariant caption1">{{ data.course.title }}</span>
                <div class="d-flex flex-wrap">
                    <span :class="['classType overline mr-2', 'onSurfaceVariant', data.type == 'WEBINAR' ? 'lightPeach' : 'bgLightBlue']">
                        {{ data.type }}
                    </span>
                    <span 
                        class="classType overline onSurfaceVariant bgLightGreen" 
                        v-if="isPast && data?.recording?.url == ''"
                    >
                        RECORDING NOT AVAILABLE
                    </span>
                    <span 
                        v-if="isPast && data?.recording?.url != ''" 
                        class="classType overline onSurfaceVariant bgLightGreen"
                    >
                        RECORDING AVAILABLE
                    </span>
                </div>
				<div class="d-flex flex-column">
					<span class="classTitle subtitle1 onSurface">{{ data.class_title.title }}</span>
					<div class="classDetails" :class="{'addMargin': isPast}">
                    	<span class="onSurfaceVariant">{{ formattedSchedule(data) }}</span>
						<span class="separator">|</span>
                    	<span class="onSurfaceVariant">BATCH: {{ data.batch.title }}</span>
						<span class="separator">|</span>
                    	<span class="onSurfaceVariant">ID: {{ data.batch.id }}</span>
						<span v-if="isPast" class="separator">|</span>
						<div class="d-flex">
							<div class="recordingDuration" v-if="isPast && data?.recording?.url !== ''">
								<div class="playIcon">
									<span class="material-icons">play_arrow</span>
								</div>
								<span>{{ data?.recording?.duration }} minutes</span>
							</div>
						</div>
					</div>
					<div class="learnerAttendence" v-if="isPast">
						<template v-if="data?.attendance.status">
							<div class="d-flex">
								<span class="onSurfaceVariant pr-1">
									Attendance: {{ data?.attendance.duration }} minutes
								</span>
								<span class="onSurfaceVariant">
									({{ (data?.attendance?.duration_percentage || 0) }}%)
								</span>
							</div>
							<b-progress
								:type="attendanceClass"
								:value="data?.attendance?.duration_percentage || 0"
								style="flex-grow: 1;"
							>
							</b-progress>
						</template>
						<template v-else>
							<span class="classType overline bgLightPink">ABSENT</span>
						</template>
					</div>
                </div>
                <!-- Instructor profile section -->
                <div v-if="instructor?.user?.image_url != ''" class="userProfile">
                    <span>
                        <img :src="instructor.user.image_url" alt="instructor-profile" />
                    </span>
                    <div class="userDescription">
                        <span class="onSurfaceVariant caption1 noBold">
                            {{ instructor.user.role[0] }}
                        </span>
                        <span class="onSurface subtitle2 underline">
                            {{ instructor.user.full_name }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="buttonWrapper">
                <div class="academyLabel">
  					<span class="academyLogo" v-if="academy?.logo_url.url !== ''">
  						<img :src="academy.logo_url.url" alt="academy.logo_url.alt_text" />
					</span>
                    <span v-if="academy?.name" class="favIcon onSurfaceVariant caption1">
                        {{ formattedAcademyName(academy.name) }}
                    </span>
                </div>
                <div class="cta" v-if="isLive">
                    <b-button
                        tag="a"
                        class="yunoPrimaryCTA noBold"
                        :disabled="!data.virtual_classroom.meeting_url || data.virtual_classroom.meeting_url.trim() === ''"
                        :href="data.virtual_classroom.meeting_url"
                    >
                        Launch Class
                    </b-button>
                </div>
                <div class="cta" v-if="false">
                    <b-button
                        tag="a"
                        class="secondaryCTA"
                        @click.stop="openScheduleModal(data)"
                    >
                        Schedule Class
                    </b-button>
                </div>
            </div>
        </div>
    `,
  computed: {
    ...Vuex.mapState(["userInfo", "userRole", "drawer"]),
    isLive() {
      return (
        this.data.temporal_status === "live" ||
        this.data.temporal_status === "upcoming"
      );
    },
    isPast() {
      return this.data.temporal_status === "past";
    },
    academy() {
      return this.data.academy;
    },
    attendanceClass() {
      const p = this.data?.attendance?.duration_percentage || 0;
      if (p <= 30) return "is-red";
      if (p <= 50) return "is-orange";
      if (p <= 70) return "is-yellow";
      if (p <= 80) return "is-lightGreen";
      if (p <= 90) return "is-blue";
      return "is-green";
    },
    instructor() {
      return this.data.instructor;
    },
  },
  data() {
    return {};
  },
  methods: {
    openDrawer(data) {
      this.open = true;
      this.drawerData = data;
    },
    formattedAcademyName(academy) {
      return academy
        .split("-") // Convert into array by splitting at '-'
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
        .join(" "); // Join back as a string with spaces
    },
    formattedSchedule(data) {
      const isPast = data?.temporal_status === "past";
      const timeData = isPast ? data?.actual : data?.scheduled;

      const startTime = timeData?.start?.time;
      const endTime = timeData?.end?.time;

      if (!startTime || !endTime) return;

      return this.formatDuration(startTime, endTime);
    },

    formatDuration(start, end) {
      const startTime = new Date(start);
      const endTime = new Date(end);
      return `${startTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })} - ${endTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    },
  },
});
