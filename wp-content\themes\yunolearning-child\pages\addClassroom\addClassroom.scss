@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

@font-face {
	font-family: 'Material Icons Outlined';
	font-style: normal;
	font-weight: 400;
	src: url('../../../dist/fonts/material-Icons.woff2?6qrc5l') format('woff2');
}

@font-face {
	font-family: 'Material Icons';
	font-style: normal;
	font-weight: 400;
	src: url('../../../dist/fonts/material-Icons-filled.woff2?8qrc5l') format('woff2');
}

@font-face {
	font-family: 'FontAwesome';
	font-style: normal;
	font-weight: 400;
	src: url('../../../dist/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2');
}

%font-color-dark {
	color: $onSurface;
}

%font-color-dark-variant {
	color: $onSurfaceVariant;
}

%font-color-dark-variant-2 {
	@include setFontColor($primaryCopyColor, 0.38);
}

%placegolder {
	color: #201a1947;
}

%headline5 {
	@include setFont($headline5, 28px, 500, 0);
}

%body1 {
	@include setFont($body1, 28px, 500, 0);
}

%smallCaption {
	@include setFont($caption1, 16px, 400, 0);
}

#app {
	@extend %font-color-dark;

	.mainHeader {
		margin-bottom: $gapLarge;

		h1 {
			@extend %headline5;
			margin: 14px 0 0 20px;

			@media (min-width: 768px) {
				margin: 0;
			}
		}
	}

	.yunoFormWrapper {
		padding-bottom: $gapLargest;

		.ctaWrapper {
			display: flex;
			gap: $gap15;
			margin-top: $gap15;

			.button {
				@extend %body1;
				width: 100%;
			}
		}
	}

	.field {
		.label {
			@extend %body1;
			@extend %font-color-dark;
		}

		.control {
			input[type="text"] {
				border-color: #e6e6e6;
				height: 40px;
			}

			textarea {
				border-color: #e6e6e6;
			}

			.select {
				width: 100%;

				select {
					border-color: #e6e6e6;
					width: 100%;
				}
			}

			&.colorpicker {
				.button {
					height: 40px;
					border-radius: 4px;
				}
			}
		}

		&.uploadField {
			.field {
				&.has-addons {
					display: block;
				}
			}

			.helper {
				@extend %smallCaption;
				@extend %font-color-dark-variant;
			}

			.upload {
				margin-top: $gap15;

				.file-cta {
					background-color: $primary;
					color: white;

					.material-icons-outlined {
						font-size: 18px;
						margin-right: $gapSmaller;
					}
				}
			}
		}

		.dropdown {
			width: 100%;

			.dropdown-trigger {
				width: 100%;
			}

			.button {
				width: 100%;
				justify-content: flex-start;
				border-color: #e6e6e6;
				height: 40px;

				>span {
					display: flex;
					width: 100%;
					align-items: center;
					justify-content: space-between;
				}

				.selected {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					span {
						margin-right: $gapSmaller;

						&::after {
							content: ",";
						}

						&:last-child {
							&::after {
								content: "";
							}
						}
					}
				}
			}

			.dropdown-menu {
				width: 100%;

				.dropdown-item {
					&:hover {
						text-decoration: none;
						background-color: $hover;
					}

					&.is-active {
						background-color: $active2;
					}
				}
			}

			.placeholder {
				color: #8080808c;
			}

			&.invalid {
				.button {
					border-color: red;
				}
			}
		}
	}

	.categoryTaxonomyWrapper {
		padding-top: $gapLargest;
	}

	.categoryTaxonomy {
		background: $whiteBG;
		border: 1px solid $grey;
		border-top: 0;

		&:first-child {
			border-top: 1px solid $grey;
		}

		.collapse-trigger {
			display: block;
			padding: 0;

			.b-radio {
				margin: 0;
			}
		}

		.collapseHeader {
			position: relative;

			&.menuDown {
				box-shadow: rgba(0, 0, 0, 0.1) 0 4px 6px;
			}

			.b-radio {
				display: flex;
				width: 100%;
				padding: $gap15 $gap15;

				+.error {
					display: none;
					margin: 0;
					padding: 0 0 $gap15 $gapLargest + $gap15;
				}

				&.invalid {
					+.error {
						display: block;
					}
				}
			}

			.fa {
				position: absolute;
				right: $gap15;
				top: calc(50% - 8px);
			}
		}

		.collapse-content {
			display: block;
			padding: $gap15 $gapLargest;
		}

		.collapse {
			margin-bottom: $gap15;

			.collapse-trigger {
				position: relative;
				padding-left: 18px;

				.fa {
					position: absolute;
					left: 0;
					top: 3px;
				}
			}

			.collapse-content {
				padding-bottom: 0;
				position: relative;

				&:before {
					content: "";
					width: 1px;
					height: 100%;
					background-color: $grey;
					position: absolute;
					left: 4px;
					top: 0;
				}
			}

			.sub2Content {
				.field {
					display: flex;
					align-items: center;
					position: relative;

					&:before {
						content: "";
						width: 100%;
						height: 1px;
						background-color: $grey;
						position: absolute;
						left: -25px;
						top: 10px;
						z-index: 1;
					}

					&:after {
						content: "";
						background: $whiteBG;
						width: 100%;
						height: 100%;
						position: absolute;
						left: 0;
						top: 0;
						z-index: 2;
					}

					.b-checkbox {
						position: relative;
						z-index: 3;
					}
				}
			}

			.trigger {
				.field {
					display: flex;
					align-items: center;
				}

				.b-checkbox {
					margin: 0;
				}
			}
		}
	}
}

.add-classroom {
	padding: 1rem 0;
	background-color: #fff;

	.goBack {
		display: flex;
		align-items: center;
		gap: 15px;
		cursor: pointer;
	}

	.mainHeader {
		margin-bottom: 1.5rem;

		h1 {
			font-size: 1.5rem;
			color: #333;
			font-weight: 500;
			margin: 0;
		}
	}

	.classroomContainer {
		display: flex;
		flex-direction: column;
		width: 100%;

		@media (min-width: 768px) {
			width: 55%;
		}

		&.details {
			flex-direction: row;
			width: 100%;
			gap: 124px;

			@media (max-width: 768px) {
				flex-direction: column;
			}

			.classroomDetailForm {
				width: 100%;
			}

			.mapWrapper {
				margin-top: 65px;
			}
		}
	}

	.searchLocation {
		margin-bottom: 1rem;

		h2 {
			font-size: 0.875rem;
			color: #333;
			margin-bottom: 0.5rem;
			font-weight: normal;
		}

		.searchContainer {
			display: flex;
			gap: 10px;

			.field {
				width: 100%;
				margin-bottom: 0px !important;
			}

			@media (max-width: 576px) {
				flex-direction: column;
			}

			.main_text {
				font-size: 16px;
				line-height: 1.7rem;
				font-weight: 500;
				color: $onSurfaceVariant;
			}

			.secondary_text {
				font-size: 14px;
				line-height: 1.2rem;
				color: $onSurfaceVariant;
			}

			.cta {
				.button.yunoPrimaryCTA {
					padding: 9px 20px !important;
				}
			}
		}
	}

	.open-hours-table {
		border-collapse: collapse;
		margin-top: 20px;
		border: 1px solid #e6e6e6;

		th,
		td {
			text-align: start;
		}

		th {
			padding: 10px 23px 0 14px;
		}

		td {
			padding: 1rem 23px 5px 14px;
		}

		.timepicker {
			input {
				width: 110px !important;
				padding-left: 5px !important;
			}
		}
	}

	.mapWrapper {
		border-radius: 4px;
		height: 350px;
		width: 100%;
		margin-top: 5px;
	}

	// Loading spinner styles
	.infiniteSpinner {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 200px;
		margin: 0;

		img {
			opacity: 0.7;
		}
	}

	.classDetailForm {
		position: relative;
		padding: 20px;
		margin: 20px 0;
		border: 1px solid #e6e6e6;
		border-radius: 4px;

		.deleteClass {
			position: absolute;
			top: 10px;
			right: 10px;
			cursor: pointer;

			.material-icons {
				font-size: 24px;
			}
		}
	}

	.addClass {
		text-decoration: underline;
		cursor: pointer;
	}

	.yunoFormWrapper {
		.grouped {
			display: flex;
			gap: 15px;
			margin: 20px 0;
		}

		.hasFlex {
			display: flex;
			margin: 10px 0;
			gap: 15px;

			.field {
				width: 100%;
			}
		}

		.fullWidth {
			width: 100%;
		}

		.hasFlexColumn {
			display: flex;
			gap: 15px;
			margin: 10px 0;
			flex-direction: column;
		}

		// Add new styles for facilities checkboxes
		.groupCheckbox {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 2px;
			margin: 10px 0;

			@media (max-width: 768px) {
				grid-template-columns: repeat(2, 1fr);
			}

			@media (max-width: 480px) {
				grid-template-columns: 1fr;
			}

			.b-checkbox {
				.control-label {
					font-size: 14px;
					color: $onSurface;
				}
			}
		}

		.faciltiesCheckbox {
			margin: 10px 0;

			.addMargin {
				margin: 5px 0px 5px 32px;
			}
		}
	}
}

// Additional responsive adjustments
@media (max-width: 768px) {
	.add-classroom {
		.mainHeader {
			padding: 0 1rem;
		}

		.searchSection {
			padding: 0 1rem;
		}

		.map-container {
			margin: 1rem;
		}
	}
}

.open-hours {
	&-wrapper {
		margin-bottom: 20px;
	}

	&-container {
		border-radius: 4px;
	}

	&-table {
		width: 100%;
		border-collapse: separate;
		border-spacing: 0 10px;

		th {
			text-align: left;
			padding: 10px 15px;
			font-weight: normal;
			color: #000;
		}

		td {
			padding: 5px 15px;
			vertical-align: middle;
		}

		.day-cell {
			color: #000;
		}

		.time-picker {
			width: 100%;
			max-width: 150px;

			input {
				border: 1px solid #e0e0e0;
				border-radius: 4px;
				padding: 8px 12px;
				width: 100%;
			}
		}

		.add-button {
			cursor: pointer;
		}
	}
}

@media (max-width: 768px) {
	.open-hours {
		&-container {
			padding: 10px;
		}

		&-table {

			td,
			th {
				padding: 5px;
			}

			.time-picker {
				max-width: none;
			}
		}
	}
}