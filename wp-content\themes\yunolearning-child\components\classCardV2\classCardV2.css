#app .cardSection .hasNoClass {
  border: 1px solid #E6E6E6;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 15px 20px;
  cursor: pointer;
}
#app .cardSection .hasNoClass:hover {
  border-color: #A81E22;
}
#app .cardSection .favIcon img {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}
#app .cardSection .cardContent {
  border: 1px solid #E6E6E6;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 15px 20px;
  cursor: pointer;
}
#app .cardSection .cardContent:hover {
  border-color: #A81E22;
}
#app .cardSection .cardContent .cardContentWrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
#app .cardSection .cardContent .cardContentWrapper .actionList.mobile {
  display: none;
  position: absolute;
  right: 0;
}
@media (max-width: 767px) {
  #app .cardSection .cardContent .cardContentWrapper .actionList.mobile {
    display: block;
  }
}
#app .cardSection .cardContent .cardContentWrapper .classStatus {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 10px;
  font-weight: 500;
  line-height: 14px;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}
#app .cardSection .cardContent .cardContentWrapper .classStatus .dot {
  border-radius: 50%;
  height: 10px;
  width: 10px;
  background: #ff0000;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence {
  display: flex;
  align-items: center;
  gap: 7px;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.25px;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress-value {
  color: rgba(0, 0, 0, 0.7);
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress {
  border: 1px solid #fff;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-orange::-webkit-progress-value {
  background: #fc9927;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-red::-webkit-progress-value {
  background: #ca0813;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is_blue::-webkit-progress-value {
  background: #0000ff;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-yellow::-webkit-progress-value {
  background: #f0c042;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-green::-webkit-progress-value {
  background: #008000;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-lightGreen::-webkit-progress-value {
  background: #669d4f;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-darkGreen::-webkit-progress-value {
  background: #356b21;
}
#app .cardSection .cardContent .cardContentWrapper .learnerAttendence .bgLightPink {
  background: #FFDAD7;
  color: #ef4444;
}
#app .cardSection .cardContent .cardContentWrapper .classType {
  text-align: center;
  padding: 2px 6px;
  border-radius: 4px;
}
#app .cardSection .cardContent .cardContentWrapper .classType.bgLightBlue {
  background: #cce5ff;
}
#app .cardSection .cardContent .cardContentWrapper .classType.lightPeach {
  background-color: #ffe5cc;
}
#app .cardSection .cardContent .cardContentWrapper .classType.bgLightGreen {
  background: #d4edda;
  color: #534342;
}
#app .cardSection .cardContent .cardContentWrapper .classTitle {
  letter-spacing: 0.15px;
  margin-bottom: 5px;
  color: #201A19;
}
#app .cardSection .cardContent .cardContentWrapper .underline {
  text-decoration-line: underline;
  text-decoration-style: solid;
}
#app .cardSection .cardContent .cardContentWrapper .classDetails {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
}
#app .cardSection .cardContent .cardContentWrapper .classDetails.addMargin {
  margin-bottom: 5px;
}
#app .cardSection .cardContent .cardContentWrapper .classDetails span {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.25px;
}
#app .cardSection .cardContent .cardContentWrapper .classDetails .recordingDuration {
  display: flex;
  align-items: center;
  gap: 4px;
}
#app .cardSection .cardContent .cardContentWrapper .classDetails .recordingDuration .playIcon {
  border: 2px solid #5f6368;
  border-radius: 50%;
  color: #5f6368;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
#app .cardSection .cardContent .cardContentWrapper .userProfile {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: center;
}
#app .cardSection .cardContent .cardContentWrapper .userProfile span img {
  border-radius: 50%;
  width: 34px;
  height: 35px;
}
#app .cardSection .cardContent .cardContentWrapper .userProfile .material-icons {
  color: #E6E6E6;
  font-size: 36px;
}
#app .cardSection .cardContent .cardContentWrapper .userProfile .userDescription {
  display: flex;
  flex-direction: column;
  gap: 1px;
}
#app .cardSection .cardContent .cardContentWrapper .progress-wrapper .progress {
  width: 137px;
  height: 7px;
}
#app .cardSection .cardContent .cardContentWrapper .learnerList {
  display: flex;
}
#app .cardSection .cardContent .cardContentWrapper .learnerList li {
  margin-left: -12px;
  z-index: 7;
}
#app .cardSection .cardContent .cardContentWrapper .learnerList li .material-icons {
  background-color: white;
  color: #e6e6e6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 24px;
  font-size: 27px;
}
#app .cardSection .cardContent .cardContentWrapper .learnerList li img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
#app .cardSection .cardContent .cardContentWrapper .progressWrapper {
  display: flex;
  align-items: center;
  gap: 6px;
}
@media (max-width: 767px) {
  #app .cardSection .cardContent .cardContentWrapper .progressWrapper {
    width: 100%;
  }
  #app .cardSection .cardContent .cardContentWrapper .progressWrapper .progress {
    width: 100%;
  }
}
#app .cardSection .cardContent .buttonWrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  padding-top: 10px;
  width: 100%;
}
@media (min-width: 767px) {
  #app .cardSection .cardContent .buttonWrapper {
    width: 150px;
  }
}
#app .cardSection .cardContent .buttonWrapper .hasFlex {
  display: flex;
  justify-content: start;
}
@media (min-width: 767px) {
  #app .cardSection .cardContent .buttonWrapper .hasFlex {
    justify-content: end;
  }
}
#app .cardSection .cardContent .buttonWrapper .noBold {
  font-weight: 400;
}
#app .cardSection .cardContent .buttonWrapper .cta a {
  width: 100%;
}
#app .cardSection .cardContent .buttonWrapper .cta a.secondaryCTA {
  padding: 10px 20px;
  border-radius: 4px;
  border: 1px solid #d0c4c2;
  background-color: #fff;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #201a19;
}
#app .cardSection .cardContent .buttonWrapper .cta a.secondaryCTA:hover {
  text-decoration: none;
  border-color: #a81e22;
}
#app .cardSection .cardContent .buttonWrapper .academyLabel {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 5px;
}
@media (min-width: 767px) {
  #app .cardSection .cardContent .buttonWrapper .academyLabel {
    justify-content: center;
    gap: 10px;
  }
}
#app .cardSection .cardContent .buttonWrapper .academyLabel .academyLogo {
  width: 24px;
  height: 24px;
}
#app .cardSection .cardContent .buttonWrapper .academyLabel .academyLogo img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
@media (max-width: 767px) {
  #app .cardSection .cardContent .buttonWrapper .actionList.desktop {
    display: none;
  }
}
#app .cardSection .cardContent .buttonWrapper .actionList .dropdown-menu {
  min-width: 100px;
  padding: 0;
}
#app .cardSection .cardContent .buttonWrapper .actionList .dropdown-menu a {
  color: #201A19;
}
#app .cardSection .cardContent .starIcon {
  color: #f9b600;
  font-size: 16px;
}
#app .cardSection .scheduleModal .yunoModal .modal-content {
  height: 490px !important;
  border: 1px solid #E6E6E6 !important;
}
#app .cardSection .scheduleModal .yunoModal .modal-background {
  background-color: transparent !important;
}
#app .cardSection .scheduleModal .yunoModal .modalTitle {
  padding: 40px 20px 0 20px;
}
#app .cardSection .scheduleModal .yunoModal .modalBody {
  display: flex;
  flex-grow: 1;
}
@media (max-width: 768px) {
  #app .cardSection .scheduleModal .yunoModal .modalBody {
    flex-direction: column;
    gap: 20px;
  }
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper {
  width: 70%;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo {
  display: flex;
  flex-direction: column;
  gap: 6px;
  border-bottom: 1px solid #E6E6E6;
  width: 300px;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .flexDiv {
  display: flex;
  align-items: center;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList {
  display: flex;
  align-items: center;
  padding-left: 10px;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList li {
  margin-left: -12px;
  z-index: 7;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList li .material-icons {
  background-color: white;
  color: #e6e6e6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 24px;
  font-size: 27px;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList li img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerEnrolled {
  display: flex;
  align-items: baseline;
  gap: 6px;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .courseInfo {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .courseInfo .academyFavIcon img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .addLearners {
  width: 30%;
}
#app .cardSection .scheduleModal .yunoModal .modalBody .addLearners .totalLearners {
  border-bottom: 2px solid #E6E6E6;
}
#app .cardSection .scheduleModal .yunoModal .modalFooter {
  padding-bottom: 20px;
}
#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper {
  gap: 10px;
}
#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper .primaryCTA {
  padding: 7px 10px;
  border-radius: 4px;
  border: 1px solid #a81e22;
  background-color: #a81e22;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}
#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper .primaryCTA:hover {
  text-decoration: none;
  background-color: #410004;
}
#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper .secondaryCTA {
  padding: 7px 10px;
  border-radius: 4px;
  border: 1px solid #d0c4c2;
  background-color: #fff;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  min-width: 80px !important;
  color: #201a19;
}
#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper .secondaryCTA:hover {
  text-decoration: none !important;
  border-color: #a81e22;
}
#app .cardSection .yunoSnackbar .snackbar {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 15px;
  background: white;
  width: 400px;
  overflow-y: auto;
}
#app .cardSection .yunoSnackbar .vue-star-rating[data-v-fde73a0c] {
  justify-content: center !important;
}
#app .cardSection .yunoSnackbar .yunoInput .textarea:not([rows]) {
  max-height: 6em !important;
  min-height: 4em !important;
}
#app .yunoSnackbar .snackbar .closeSnackbar .material-icons-outlined {
  font-size: 18px;
}
#app .yunoSnackbar .titleLarge {
  font-size: 24px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 5px;
}
#app .yunoSnackbar .titleSmall {
  font-size: 16px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 5px;
}
#app .yunoSnackbar .subtitleSmall {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 15px;
}
#app .yunoSnackbar .noticeSmall {
  margin-bottom: 10px;
}
#app .yunoSnackbar .noticeTitle {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1.5px;
}
#app .yunoSnackbar .mappedInstructor {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 15px;
}
#app .yunoSnackbar .mappedInstructor .imgWrapper {
  flex: 0 0 50px;
  margin-right: 10px;
}
#app .yunoSnackbar .mappedInstructor .imgWrapper img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 0;
  background-color: #FFF;
}
#app .yunoSnackbar .mappedInstructor figcaption {
  flex: 0 0 calc(100% - 60px);
}
#app .yunoSnackbar .mappedInstructor .insName {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 5px;
}
#app .yunoSnackbar .mappedInstructor .studentCount {
  flex: 0 0 100%;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}
#app .yunoSnackbar .mappedInstructor.gapBtm15 {
  margin-bottom: 15px;
}
#app .yunoSnackbar .ctaWrapper {
  margin-top: 15px;
  display: flex;
}
#app .yunoSnackbar .ctaWrapper .button {
  margin-right: 10px;
}
#app .yunoSnackbar .formWrapper {
  width: 100%;
  position: relative;
  background: #fafafa;
  padding: 10px;
}
#app .yunoSnackbar .formWrapper .innerWrapper {
  background-color: #FFF;
  padding: 10px;
  margin-bottom: 15px;
}
#app .yunoSnackbar .formWrapper .innerWrapper .groupElement {
  margin: 0;
}
#app .yunoSnackbar .formWrapper .vue-star-rating {
  padding-left: 15px;
}
#app .yunoSnackbar .formWrapper .field.noGap {
  margin: 0;
}
#app .yunoSnackbar .formWrapper .alert {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
#app .yunoSnackbar .formWrapper .alert .material-icons-outlined {
  font-size: 40px;
  margin-bottom: 10px;
}
#app .yunoSnackbar .formWrapper .ctaWrapper {
  justify-content: right;
}
#app .yunoSnackbar .formWrapper .ctaWrapper .button {
  margin-right: 0;
}
#app .yunoSnackbar .formWrapper .ctaWrapper.loading {
  position: absolute;
  right: 5px;
  bottom: 54px;
}
#app .yunoSnackbar .formWrapper .ctaWrapper.loading .button {
  border: 0;
}
#app .yunoSnackbar .formWrapper .checkList .fieldLabel {
  font-size: 14px;
  margin: 0 0 10px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 600;
}
#app .yunoSnackbar .formWrapper .checkList .checkboxList {
  display: flex;
  flex-wrap: wrap;
}
#app .yunoSnackbar .formWrapper .checkList .checkboxList .field {
  margin-right: 10px;
}
#app .yunoSnackbar .formWrapper .checkList .checkboxList .b-checkbox {
  border-radius: 20px;
  padding: 5px 10px;
  height: auto;
  font-size: 12px;
}
#app .yunoSnackbar .formWrapper.noBG {
  background: none;
  padding: 0;
}
#app .yunoSnackbar .formWrapper.gapTop15 {
  padding-top: 15px;
}
#app .yunoSnackbar .starLabel {
  display: flex;
  margin-left: 12px;
  margin-top: 8px;
}
#app .yunoSnackbar .starLabel li {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  flex: 0 0 67px;
  text-align: center;
}
#app .yunoSnackbar .starLabel li.active {
  visibility: visible;
}
#app .yunoSnackbar .starLabel li.notActive {
  visibility: hidden;
}
#app .yunoSnackbar .checkboxList .field {
  display: inline-block !important;
  margin-bottom: 2px !important;
  margin-right: 6px !important;
}

.b-sidebar.extraWide .sidebar-content {
  width: 550px !important;
}

.b-sidebar.card-slidebar .sidebar-content {
  width: 450px;
}
.b-sidebar.card-slidebar .sidebar-content.is-light {
  background-color: white !important;
}
.b-sidebar.card-slidebar .academyLogo img {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}
.b-sidebar.card-slidebar .guestLink.collapse:not(.show) {
  display: block !important;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}
.b-sidebar.card-slidebar .guestLink .card-header {
  box-shadow: none;
  padding: 0;
  border: none;
  height: 40px;
}
.b-sidebar.card-slidebar .guestLink .card-header .card-header-title,
.b-sidebar.card-slidebar .guestLink .card-header .card-header-icon {
  padding: 0.35rem 1rem;
}
.b-sidebar.card-slidebar .guestLink .card-header .card-header-title a:hover,
.b-sidebar.card-slidebar .guestLink .card-header .card-header-icon a:hover {
  text-decoration: none;
}
.b-sidebar.card-slidebar .guestLink .card-header .card-header-title {
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 18px !important;
  letter-spacing: 0.1px !important;
  padding: 8px;
}
.b-sidebar.card-slidebar .content .urlWrapper {
  display: flex;
  align-items: center;
  border-radius: 4px;
  padding: 8px;
  position: relative;
  border: 1px solid #E6E6E6;
  height: 50px;
}
.b-sidebar.card-slidebar .content .urlWrapper .url {
  padding-right: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.b-sidebar.card-slidebar .content .urlWrapper .copyGuestUrl {
  position: absolute;
  right: 8px;
  background: transparent;
  color: #a81e22;
  border: none;
  font-weight: 500;
  text-decoration: underline;
}
.b-sidebar.card-slidebar .cardContent {
  padding-top: 70px;
}
.b-sidebar.card-slidebar .cardContent .headline5 {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}
.b-sidebar.card-slidebar .cardContent .subtitle1 {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}
.b-sidebar.card-slidebar .cardContent .subtitle1.noBold {
  font-weight: 400;
}
.b-sidebar.card-slidebar .cardContent .underline {
  text-decoration-line: underline;
  text-decoration-style: solid;
}
.b-sidebar.card-slidebar .cardContent .subtitle3 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}
.b-sidebar.card-slidebar .cardContent .subtitle2 {
  font-size: 14px;
  line-height: 18px;
  font-weight: 500;
  margin-bottom: 0;
}
.b-sidebar.card-slidebar .cardContent .subtitle2.noBold {
  font-weight: 400;
}
.b-sidebar.card-slidebar .cardContent .subtitle3 {
  font-size: 16px;
  line-height: 18px;
  font-weight: 500;
  margin-bottom: 0;
}
.b-sidebar.card-slidebar .cardContent .subtitle3.noBold {
  font-weight: 400;
}
.b-sidebar.card-slidebar .cardContent .overline {
  font-size: 12px;
  line-height: 14px;
  font-weight: 500;
  margin-bottom: 0;
}
.b-sidebar.card-slidebar .cardContent .wrapper {
  padding: 25px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .videoWrapper {
  padding-bottom: 15px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .videoWrapper .videoLPPlayer {
  height: 300px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .videoWrapper .videoLPPlayer iframe {
  width: 100%;
  height: 100%;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .videoWrapper.loading {
  padding-bottom: 0;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classStatus {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 10px;
  line-height: 14px;
  font-weight: 500;
  text-align: left;
  color: #ff0000;
  text-transform: uppercase;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classStatus .dot {
  border: 1px solid;
  border-radius: 50%;
  height: 10px;
  width: 10px;
  background: #ff0000;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .progress-wrapper .progress {
  width: 137px;
  height: 7px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence {
  display: flex;
  align-items: center;
  gap: 7px;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.25px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress-value {
  color: rgba(0, 0, 0, 0.7);
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress {
  width: 137px;
  height: 7px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-orange::-webkit-progress-value {
  background: #fc9927;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-red::-webkit-progress-value {
  background: #ca0813;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is_blue::-webkit-progress-value {
  background: #0000ff;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-yellow::-webkit-progress-value {
  background: #f0c042;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-green::-webkit-progress-value {
  background: #008000;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-lightGreen::-webkit-progress-value {
  background: #669d4f;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .progress-wrapper .progress.is-darkGreen::-webkit-progress-value {
  background: #356b21;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence .bgLightPink {
  background: #FFDAD7;
  color: #ef4444;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classType {
  font-size: 10px;
  font-weight: 500;
  line-height: 14px;
  letter-spacing: 1.5px;
  text-align: center;
  padding: 4px 6px;
  border-radius: 4px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classType.bgLightGreen {
  background: #d4edda;
  color: #534342;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classType.lightPeach {
  background: #ffe5cc;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classType.bgLightPink {
  background: #FFDAD7;
  color: #ef4444;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classType.bgLightBlue {
  background: #cce5ff;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
  color: #201A19;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classDetails {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classDetails span {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.25px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classDetails .recordingDuration {
  display: flex;
  align-items: center;
  gap: 4px;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .classDetails .recordingDuration .playIcon {
  border: 2px solid #5f6368;
  border-radius: 50%;
  color: #5f6368;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.b-sidebar.card-slidebar .cardContent .cardContentWrapper .closeSidebar {
  margin-left: -8px;
  cursor: pointer;
}
.b-sidebar.card-slidebar .cardContent .button.yunoPrimaryCTA {
  padding: 10px 20px;
  border-radius: 4px;
  border: 1px solid #a81e22;
  background-color: #A81E22;
  line-height: normal;
  font-size: 16px;
  font-weight: 400;
  color: #FFF;
}
.b-sidebar.card-slidebar .cardContent .button.yunoPrimaryCTA:hover {
  text-decoration: none;
  background-color: #410004;
}
.b-sidebar.card-slidebar .cardContent .buttonWrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  padding-top: 20px;
  width: 150px;
}
.b-sidebar.card-slidebar .cardContent .buttonWrapper .cta a {
  width: 100%;
}
.b-sidebar.card-slidebar .cardContent .buttonWrapper .cta a.secondaryCTA {
  padding: 10px 20px;
  border-radius: 4px;
  border: 1px solid #d0c4c2;
  background-color: #fff;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #201a19;
}
.b-sidebar.card-slidebar .cardContent .buttonWrapper .cta a.secondaryCTA:hover {
  text-decoration: none !important;
  border-color: #a81e22;
}
.b-sidebar.card-slidebar .cardContent .courseDetails {
  border-bottom: 1px solid #E6E6E6;
  gap: 8px;
  padding: 25px 0;
}
.b-sidebar.card-slidebar .cardContent .courseDetails .hasFlex {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}
.b-sidebar.card-slidebar .cardContent .courseDetails .academyLogo {
  width: 24px;
  height: 24px;
}
.b-sidebar.card-slidebar .cardContent .courseDetails .academyLogo img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.b-sidebar.card-slidebar .cardContent .classReview {
  gap: 10px;
}
.b-sidebar.card-slidebar .cardContent .classReview img {
  border-radius: 50%;
  width: 70px;
}
.b-sidebar.card-slidebar .cardContent .classReview .material-icons.profileIcon {
  background-color: white;
  color: #e6e6e6;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  width: 18px;
  height: 24px;
  font-size: 34px;
}
.b-sidebar.card-slidebar .cardContent .classReview .learnerReviewWrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.b-sidebar.card-slidebar .cardContent .userProfile {
  display: flex;
  gap: 10px;
  align-items: center;
}
.b-sidebar.card-slidebar .cardContent .userProfile img {
  width: 40px;
  border-radius: 50%;
}
.b-sidebar.card-slidebar .cardContent .userProfile img.userImage {
  width: 82px !important;
}
.b-sidebar.card-slidebar .cardContent .userProfile .userDescription {
  display: flex;
  flex-direction: column;
  gap: 3px;
}
.b-sidebar.card-slidebar .cardContent .learnerList {
  display: flex;
  align-items: center;
}
.b-sidebar.card-slidebar .cardContent .learnerList li {
  margin-left: -12px;
  z-index: 7;
}
.b-sidebar.card-slidebar .cardContent .learnerList li .material-icons {
  background-color: white;
  color: #e6e6e6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 24px;
  font-size: 27px;
}
.b-sidebar.card-slidebar .cardContent .learnerList li img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
.b-sidebar.card-slidebar .cardContent .starIcon {
  color: #f9b600;
  font-size: 16px;
}
.b-sidebar.card-slidebar .cardContent .instructorLearners ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.b-sidebar.card-slidebar .cardContent .instructorLearners ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  border: 1px solid #E6E6E6;
  padding: 10px 10px;
}
.b-sidebar.card-slidebar .cardContent .instructorLearners ul li .learnerProfile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.b-sidebar.card-slidebar .cardContent .instructorLearners ul li .learnerProfile img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
.b-sidebar.card-slidebar .cardContent .instructorLearners ul li .learnerAttendance {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.b-sidebar.card-slidebar .cardContent .instructorLearners ul li .learnerAttendance .progress {
  width: 100px;
  height: 7px;
}
.b-sidebar.card-slidebar .cardContent .instructorLearners ul li .learnerRating {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}
.b-sidebar.card-slidebar .separator {
  margin: 0 4px;
  color: #b0b0b0;
}
@media (max-width: 767px) {
  .b-sidebar.card-slidebar .separator {
    display: none;
  }
}

.yunoSnackbar .classCheckbox {
  display: inline-block;
}
.yunoSnackbar .classCheckbox .button {
  padding: 0 8px !important;
  border-radius: 20px !important;
  font-size: 11px !important;
}

.separator {
  margin: 0 4px;
  color: #b0b0b0;
  display: none;
}
@media (min-width: 767px) {
  .separator {
    display: block;
  }
}

@media (max-width: 767px) {
  #app .cardSection .cardContent {
    flex-direction: column;
  }
  #app .cardSection .cardContent .cardContentWrapper .learnerAttendence {
    flex-direction: column;
    align-items: baseline;
  }
  #app .cardSection .cardContent .cardContentWrapper .classDetails {
    flex-direction: column;
    gap: 5px;
    align-items: baseline;
  }
  #app .cardSection .cardContent .buttonWrapper .cta a {
    width: 100%;
  }
  #app .cardSection .cardContent .buttonWrapper .cta a.secondaryCTA {
    padding: 10px 20px;
    border-radius: 4px;
    border: 1px solid #d0c4c2;
    background-color: #fff;
    line-height: normal;
    font-size: 16px;
    font-weight: 500;
    color: #201a19;
  }
  #app .cardSection .cardContent .buttonWrapper .cta a.secondaryCTA:hover {
    text-decoration: none;
    border-color: #a81e22;
  }
  .b-sidebar.card-slidebar .sidebar-content {
    width: 100%;
  }
  .b-sidebar.card-slidebar .cardContent .cardContentWrapper .learnerAttendence {
    flex-direction: column;
    align-items: baseline;
  }
  .b-sidebar.card-slidebar .cardContent .buttonWrapper a {
    width: 100%;
  }
}/*# sourceMappingURL=classCardV2.css.map */