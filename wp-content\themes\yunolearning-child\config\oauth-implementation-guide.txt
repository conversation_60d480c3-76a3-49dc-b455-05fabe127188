OAuth API Endpoint Implementation Guide
==========================================

OVERVIEW
--------
This guide outlines the complete implementation process for converting the WordPress 
template-based OAuth authentication (/auth page) to a proper V4 REST API endpoint 
(/wp-json/yuno/v4/auth/callback).

COMPLETED CHANGES
-----------------

1. API Endpoint Implementation ✅
   - File: wp-content/themes/yunolearning-child/inc/mvc/controllers/OauthController.php
   - Method: handleAuthCallbackAPI($request) - NEW method added
   - Purpose: Handles OAuth callbacks via REST API instead of template pages
   - Status: COMPLETED

2. Route Configuration ✅
   - File: wp-content/themes/yunolearning-child/inc/mvc/routes/OauthCallbackAPI.php
   - Endpoint: /wp-json/yuno/v4/auth/callback
   - Method: GET (required by OAuth providers)
   - Auth: Disabled (this IS the authentication endpoint)
   - Status: COMPLETED

3. Header.php Updates (COMMENTED) ✅
   - File: wp-content/themes/yunolearning-child/header.php
   - Changes:
     * NEW OAuth detection logic (lines ~61-81) - COMMENTED
     * NEW Cognito login URL (lines ~150-155) - COMMENTED
   - Status: READY TO UNCOMMENT

4. Model Updates (COMMENTED) ✅
   - File: wp-content/themes/yunolearning-child/inc/mvc/models/OauthModel.php
   - Changes: API endpoint redirect URI - COMMENTED
   - Status: READY TO UNCOMMENT

IMPLEMENTATION STEPS
--------------------

Phase 1: Configuration Updates
------------------------------

Step 1: Update wp-config.php
   Comment out old values (keep for rollback):
   // define('YUNO_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/auth/');
   // define('AWS_COGNITO_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/auth/');
   // define('AWS_COGNITO_OAUTH_APP_VIRTUAL_CLASSROOM_REDIRECT_URL', 'https://local.yunolearning.com/switch-virtual-account');

   Add new API endpoint URLs:
   define('YUNO_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/wp-json/yuno/v4/auth/callback');
   define('AWS_COGNITO_OAUTH_APP_REDIRECT_URL', 'https://local.yunolearning.com/wp-json/yuno/v4/auth/callback');
   define('AWS_COGNITO_OAUTH_APP_VIRTUAL_CLASSROOM_REDIRECT_URL', 'https://local.yunolearning.com/wp-json/yuno/v4/auth/callback');

Step 2: Update OAuth Provider Settings

   Google Cloud Console:
   1. Go to APIs & Services > Credentials
   2. Select your OAuth 2.0 Client ID
   3. Update "Authorized redirect URIs" to: 
      https://local.yunolearning.com/wp-json/yuno/v4/auth/callback

   AWS Cognito:
   1. Go to Cognito Console > User Pools > Your Pool > App Clients
   2. Edit app client settings
   3. Update "Callback URLs" to: 
      https://local.yunolearning.com/wp-json/yuno/v4/auth/callback

   Apple Developer Console (if using):
   1. Go to Certificates, IDs & Profiles > Services
   2. Select Sign in with Apple service
   3. Update "Return URLs" to: 
      https://local.yunolearning.com/wp-json/yuno/v4/auth/callback

Phase 2: Code Activation
------------------------

Step 3: Uncomment New Code in header.php

   Lines ~61-81 - NEW OAuth Detection Logic:
   Remove the /* and */ around this block:
   
   // FUTURE: OAuth detection via V4 API endpoint instead of template pages
   // This will be handled automatically by the V4 REST API endpoint: /wp-json/yuno/v4/auth/callback
   // No template-based detection needed when using API endpoints
   // The OAuth providers will directly call the API endpoint with the auth code
   
   // Optional: Add fallback detection for development/testing
   if (isset($filteredURI[0]) && $filteredURI[0] == 'auth' && isset($_GET['code'])) {
       // Log that template-based OAuth is being used (should not happen in production)
       error_log("Template-based OAuth detected - should use API endpoint instead", 3, ABSPATH . "error-logs/cognito-custom-info.log");
       
       // Redirect to API endpoint for consistency
       $api_url = home_url('/wp-json/yuno/v4/auth/callback') . '?' . $_SERVER['QUERY_STRING'];
       wp_redirect($api_url);
       exit;
   }

   Lines ~150-155 - NEW Cognito Login URL:
   Remove the /* and */ around this line:
   
   yunoCognitoLoginURL = "<?php echo AWS_COGNITO_DOMAIN . "/oauth2/authorize?response_type=code&client_id=" . AWS_COGNITO_OAUTH_APP_CLIENT_ID . "&redirect_uri=" . urlencode(home_url('/wp-json/yuno/v4/auth/callback')) . "&identity_provider=Google&state="; ?>",

Step 4: Uncomment Model Updates
   File: wp-content/themes/yunolearning-child/inc/mvc/models/OauthModel.php
   Remove the // from this line:
   // 'redirect_uri' => home_url('/wp-json/yuno/v4/auth/callback'),

Phase 3: Testing
----------------

Step 5: Test OAuth Flows

   Test Google OAuth:
   1. Clear browser cache/cookies
   2. Go to login page
   3. Click "Sign in with Google"
   4. Complete OAuth flow
   5. Verify successful login
   6. Check error logs for any issues

   Test Cognito OAuth:
   1. Clear browser cache/cookies
   2. Use Cognito login flow
   3. Verify successful authentication
   4. Check user creation/login process

   Test Virtual Classroom:
   1. Test Google Meet integration
   2. Verify OAuth callback handling
   3. Check virtual classroom setup

Step 6: Monitor and Debug

   Check these logs:
   - /error-logs/cognito-custom-errors.log
   - /error-logs/cognito-custom-info.log
   - WordPress debug logs

   Verify these functions:
   - User creation/login
   - JWT token generation
   - Session management
   - Redirect handling
   - Error handling

TROUBLESHOOTING
---------------

Common Issues:

1. "Invalid redirect URI" error:
   - Verify OAuth provider settings match exactly
   - Check wp-config.php constants are correct
   - Ensure no trailing slashes in URLs

2. "Missing authorization code" error:
   - Check if API endpoint is accessible
   - Verify route registration is working
   - Test endpoint manually: /wp-json/yuno/v4/auth/callback?code=test

3. Authentication fails:
   - Check if old template detection is interfering
   - Verify new code is uncommented properly
   - Monitor error logs for specific issues

ROLLBACK PROCESS
----------------

If issues occur, rollback by:

1. Revert wp-config.php:
   Comment out new values and uncomment old values

2. Re-comment new code in header.php

3. Revert OAuth provider settings

4. Test old authentication flow

BENEFITS
--------

Technical Benefits:
- Proper V4 MVC architecture compliance
- No WordPress template dependency
- Better error handling and logging
- Improved performance (no template processing)
- Cleaner OAuth provider integration

Development Benefits:
- Easier debugging and testing
- Better code organization
- Improved maintainability
- OAuth compliance best practices
- Future-proof architecture

SUCCESS METRICS
---------------

Functional Tests:
- [ ] Google OAuth login works
- [ ] Cognito OAuth login works
- [ ] Apple OAuth login works (if enabled)
- [ ] Virtual classroom setup works
- [ ] User creation/login successful
- [ ] JWT token generation works
- [ ] Session management works
- [ ] Error handling works

Performance Tests:
- [ ] OAuth callback response time < 2s
- [ ] No memory leaks
- [ ] Error logs are clean
- [ ] Database queries optimized

Security Tests:
- [ ] No authentication bypass
- [ ] Proper error handling
- [ ] No sensitive data exposure
- [ ] CSRF protection works

POST-IMPLEMENTATION
-------------------

Cleanup (After Successful Testing):
1. Remove old OAuth detection code from header.php
2. Remove old wp-config.php values
3. Update documentation
4. Update monitoring/alerting
5. Train team on new architecture

Monitoring:
- Set up alerts for OAuth failures
- Monitor error logs regularly
- Track authentication success rates
- Monitor performance metrics

Implementation Date: [To be filled when implemented]
Tested By: [To be filled]
Production Deployment: [To be filled] 