!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.moment=t()}(this,function(){"use strict";var e;function t(){return e.apply(null,arguments)}function n(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function i(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function a(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function r(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;for(var t in e)if(a(e,t))return;return 1}function s(e){return void 0===e}function o(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function l(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function c(e,t){for(var n=[],i=e.length,a=0;a<i;++a)n.push(t(e[a],a));return n}function u(e,t){for(var n in t)a(t,n)&&(e[n]=t[n]);return a(t,"toString")&&(e.toString=t.toString),a(t,"valueOf")&&(e.valueOf=t.valueOf),e}function d(e,t,n,i){return Tt(e,t,n,i,!0).utc()}function h(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function p(e){var t,n,i=e._d&&!isNaN(e._d.getTime());return i&&(t=h(e),n=m.call(t.parsedDateParts,function(e){return null!=e}),i=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n),e._strict)&&(i=i&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e)?i:(e._isValid=i,e._isValid)}function f(e){var t=d(NaN);return null!=e?u(h(t),e):h(t).userInvalidated=!0,t}var m=Array.prototype.some||function(e){for(var t=Object(this),n=t.length>>>0,i=0;i<n;i++)if(i in t&&e.call(this,t[i],i,t))return!0;return!1},g=t.momentProperties=[],v=!1;function y(e,t){var n,i,a,r=g.length;if(s(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),s(t._i)||(e._i=t._i),s(t._f)||(e._f=t._f),s(t._l)||(e._l=t._l),s(t._strict)||(e._strict=t._strict),s(t._tzm)||(e._tzm=t._tzm),s(t._isUTC)||(e._isUTC=t._isUTC),s(t._offset)||(e._offset=t._offset),s(t._pf)||(e._pf=h(t)),s(t._locale)||(e._locale=t._locale),0<r)for(n=0;n<r;n++)s(a=t[i=g[n]])||(e[i]=a);return e}function b(e){y(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===v&&(v=!0,t.updateOffset(this),v=!1)}function w(e){return e instanceof b||null!=e&&null!=e._isAMomentObject}function _(e){!1===t.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function k(e,n){var i=!0;return u(function(){if(null!=t.deprecationHandler&&t.deprecationHandler(null,e),i){for(var r,s,o=[],l=arguments.length,c=0;c<l;c++){if(r="","object"==typeof arguments[c]){for(s in r+="\n["+c+"] ",arguments[0])a(arguments[0],s)&&(r+=s+": "+arguments[0][s]+", ");r=r.slice(0,-2)}else r=arguments[c];o.push(r)}_(e+"\nArguments: "+Array.prototype.slice.call(o).join("")+"\n"+(new Error).stack),i=!1}return n.apply(this,arguments)},n)}var S={};function x(e,n){null!=t.deprecationHandler&&t.deprecationHandler(e,n),S[e]||(_(n),S[e]=!0)}function C(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function T(e,t){var n,r=u({},e);for(n in t)a(t,n)&&(i(e[n])&&i(t[n])?(r[n]={},u(r[n],e[n]),u(r[n],t[n])):null!=t[n]?r[n]=t[n]:delete r[n]);for(n in e)a(e,n)&&!a(t,n)&&i(e[n])&&(r[n]=u({},r[n]));return r}function M(e){null!=e&&this.set(e)}t.suppressDeprecationWarnings=!1,t.deprecationHandler=null;var A=Object.keys||function(e){var t,n=[];for(t in e)a(e,t)&&n.push(t);return n};function R(e,t,n){var i=""+Math.abs(e);return(0<=e?n?"+":"":"-")+Math.pow(10,Math.max(0,t-i.length)).toString().substr(1)+i}var O=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,L=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,I={},E={};function D(e,t,n,i){var a="string"==typeof i?function(){return this[i]()}:i;e&&(E[e]=a),t&&(E[t[0]]=function(){return R(a.apply(this,arguments),t[1],t[2])}),n&&(E[n]=function(){return this.localeData().ordinal(a.apply(this,arguments),e)})}function Y(e,t){return e.isValid()?(t=N(t,e.localeData()),I[t]=I[t]||function(e){for(var t,n=e.match(O),i=0,a=n.length;i<a;i++)E[n[i]]?n[i]=E[n[i]]:n[i]=(t=n[i]).match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"");return function(t){for(var i="",r=0;r<a;r++)i+=C(n[r])?n[r].call(t,e):n[r];return i}}(t),I[t](e)):e.localeData().invalidDate()}function N(e,t){var n=5;function i(e){return t.longDateFormat(e)||e}for(L.lastIndex=0;0<=n&&L.test(e);)e=e.replace(L,i),L.lastIndex=0,--n;return e}var $={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function P(e){return"string"==typeof e?$[e]||$[e.toLowerCase()]:void 0}function F(e){var t,n,i={};for(n in e)a(e,n)&&(t=P(n))&&(i[t]=e[n]);return i}var j={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1},U=/\d/,W=/\d\d/,B=/\d{3}/,H=/\d{4}/,V=/[+-]?\d{6}/,z=/\d\d?/,G=/\d\d\d\d?/,q=/\d\d\d\d\d\d?/,Z=/\d{1,3}/,Q=/\d{1,4}/,J=/[+-]?\d{1,6}/,X=/\d+/,K=/[+-]?\d+/,ee=/Z|[+-]\d\d:?\d\d/gi,te=/Z|[+-]\d\d(?::?\d\d)?/gi,ne=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,ie=/^[1-9]\d?/,ae=/^([1-9]\d|\d)/;function re(e,t,n){ue[e]=C(t)?t:function(e,i){return e&&n?n:t}}function se(e,t){return a(ue,e)?ue[e](t._strict,t._locale):new RegExp(oe(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,i,a){return t||n||i||a})))}function oe(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function le(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function ce(e){var t=0;return 0!=(e=+e)&&isFinite(e)?le(e):t}var ue={},de={};function he(e,t){var n,i,a=t;for("string"==typeof e&&(e=[e]),o(t)&&(a=function(e,n){n[t]=ce(e)}),i=e.length,n=0;n<i;n++)de[e[n]]=a}function pe(e,t){he(e,function(e,n,i,a){i._w=i._w||{},t(e,i._w,i,a)})}function fe(e){return e%4==0&&e%100!=0||e%400==0}var me=0,ge=1,ve=2,ye=3,be=4,we=5,_e=6,ke=7,Se=8;function xe(e){return fe(e)?366:365}D("Y",0,0,function(){var e=this.year();return e<=9999?R(e,4):"+"+e}),D(0,["YY",2],0,function(){return this.year()%100}),D(0,["YYYY",4],0,"year"),D(0,["YYYYY",5],0,"year"),D(0,["YYYYYY",6,!0],0,"year"),re("Y",K),re("YY",z,W),re("YYYY",Q,H),re("YYYYY",J,V),re("YYYYYY",J,V),he(["YYYYY","YYYYYY"],me),he("YYYY",function(e,n){n[me]=2===e.length?t.parseTwoDigitYear(e):ce(e)}),he("YY",function(e,n){n[me]=t.parseTwoDigitYear(e)}),he("Y",function(e,t){t[me]=parseInt(e,10)}),t.parseTwoDigitYear=function(e){return ce(e)+(68<ce(e)?1900:2e3)};var Ce,Te=Me("FullYear",!0);function Me(e,n){return function(i){return null!=i?(Re(this,e,i),t.updateOffset(this,n),this):Ae(this,e)}}function Ae(e,t){if(!e.isValid())return NaN;var n=e._d,i=e._isUTC;switch(t){case"Milliseconds":return i?n.getUTCMilliseconds():n.getMilliseconds();case"Seconds":return i?n.getUTCSeconds():n.getSeconds();case"Minutes":return i?n.getUTCMinutes():n.getMinutes();case"Hours":return i?n.getUTCHours():n.getHours();case"Date":return i?n.getUTCDate():n.getDate();case"Day":return i?n.getUTCDay():n.getDay();case"Month":return i?n.getUTCMonth():n.getMonth();case"FullYear":return i?n.getUTCFullYear():n.getFullYear();default:return NaN}}function Re(e,t,n){var i,a,r;if(e.isValid()&&!isNaN(n)){switch(i=e._d,a=e._isUTC,t){case"Milliseconds":return a?i.setUTCMilliseconds(n):i.setMilliseconds(n);case"Seconds":return a?i.setUTCSeconds(n):i.setSeconds(n);case"Minutes":return a?i.setUTCMinutes(n):i.setMinutes(n);case"Hours":return a?i.setUTCHours(n):i.setHours(n);case"Date":return a?i.setUTCDate(n):i.setDate(n);case"FullYear":break;default:return}t=n,r=e.month(),e=29!==(e=e.date())||1!==r||fe(t)?e:28,a?i.setUTCFullYear(t,r,e):i.setFullYear(t,r,e)}}function Oe(e,t){var n;return isNaN(e)||isNaN(t)?NaN:(e+=(t-(n=(t%(n=12)+n)%n))/12,1==n?fe(e)?29:28:31-n%7%2)}Ce=Array.prototype.indexOf||function(e){for(var t=0;t<this.length;++t)if(this[t]===e)return t;return-1},D("M",["MM",2],"Mo",function(){return this.month()+1}),D("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),D("MMMM",0,0,function(e){return this.localeData().months(this,e)}),re("M",z,ie),re("MM",z,W),re("MMM",function(e,t){return t.monthsShortRegex(e)}),re("MMMM",function(e,t){return t.monthsRegex(e)}),he(["M","MM"],function(e,t){t[ge]=ce(e)-1}),he(["MMM","MMMM"],function(e,t,n,i){null!=(i=n._locale.monthsParse(e,i,n._strict))?t[ge]=i:h(n).invalidMonth=e});var Le="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Ie="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ee=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,De=ne,Ye=ne;function Ne(e,t){if(e.isValid()){if("string"==typeof t)if(/^\d+$/.test(t))t=ce(t);else if(!o(t=e.localeData().monthsParse(t)))return;var n=(n=e.date())<29?n:Math.min(n,Oe(e.year(),t));e._isUTC?e._d.setUTCMonth(t,n):e._d.setMonth(t,n)}}function $e(e){return null!=e?(Ne(this,e),t.updateOffset(this,!0),this):Ae(this,"Month")}function Pe(){function e(e,t){return t.length-e.length}for(var t,n,i=[],a=[],r=[],s=0;s<12;s++)n=d([2e3,s]),t=oe(this.monthsShort(n,"")),n=oe(this.months(n,"")),i.push(t),a.push(n),r.push(n),r.push(t);i.sort(e),a.sort(e),r.sort(e),this._monthsRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+i.join("|")+")","i")}function Fe(e,t,n,i,a,r,s){var o;return e<100&&0<=e?(o=new Date(e+400,t,n,i,a,r,s),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,i,a,r,s),o}function je(e){var t;return e<100&&0<=e?((t=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,t)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Ue(e,t,n){return(n=7+t-n)-(7+je(e,0,n).getUTCDay()-t)%7-1}function We(e,t,n,i,a){var r;n=(t=1+7*(t-1)+(7+n-i)%7+Ue(e,i,a))<=0?xe(r=e-1)+t:t>xe(e)?(r=e+1,t-xe(e)):(r=e,t);return{year:r,dayOfYear:n}}function Be(e,t,n){var i,a,r=Ue(e.year(),t,n);return(r=Math.floor((e.dayOfYear()-r-1)/7)+1)<1?i=r+He(a=e.year()-1,t,n):r>He(e.year(),t,n)?(i=r-He(e.year(),t,n),a=e.year()+1):(a=e.year(),i=r),{week:i,year:a}}function He(e,t,n){var i=Ue(e,t,n);t=Ue(e+1,t,n);return(xe(e)-i+t)/7}function Ve(e,t){return e.slice(t,7).concat(e.slice(0,t))}D("w",["ww",2],"wo","week"),D("W",["WW",2],"Wo","isoWeek"),re("w",z,ie),re("ww",z,W),re("W",z,ie),re("WW",z,W),pe(["w","ww","W","WW"],function(e,t,n,i){t[i.substr(0,1)]=ce(e)}),D("d",0,"do","day"),D("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),D("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),D("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),D("e",0,0,"weekday"),D("E",0,0,"isoWeekday"),re("d",z),re("e",z),re("E",z),re("dd",function(e,t){return t.weekdaysMinRegex(e)}),re("ddd",function(e,t){return t.weekdaysShortRegex(e)}),re("dddd",function(e,t){return t.weekdaysRegex(e)}),pe(["dd","ddd","dddd"],function(e,t,n,i){null!=(i=n._locale.weekdaysParse(e,i,n._strict))?t.d=i:h(n).invalidWeekday=e}),pe(["d","e","E"],function(e,t,n,i){t[i]=ce(e)});var ze="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Ge="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),qe="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Ze=ne,Qe=ne,Je=ne;function Xe(){function e(e,t){return t.length-e.length}for(var t,n,i,a=[],r=[],s=[],o=[],l=0;l<7;l++)i=d([2e3,1]).day(l),t=oe(this.weekdaysMin(i,"")),n=oe(this.weekdaysShort(i,"")),i=oe(this.weekdays(i,"")),a.push(t),r.push(n),s.push(i),o.push(t),o.push(n),o.push(i);a.sort(e),r.sort(e),s.sort(e),o.sort(e),this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+a.join("|")+")","i")}function Ke(){return this.hours()%12||12}function et(e,t){D(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function tt(e,t){return t._meridiemParse}D("H",["HH",2],0,"hour"),D("h",["hh",2],0,Ke),D("k",["kk",2],0,function(){return this.hours()||24}),D("hmm",0,0,function(){return""+Ke.apply(this)+R(this.minutes(),2)}),D("hmmss",0,0,function(){return""+Ke.apply(this)+R(this.minutes(),2)+R(this.seconds(),2)}),D("Hmm",0,0,function(){return""+this.hours()+R(this.minutes(),2)}),D("Hmmss",0,0,function(){return""+this.hours()+R(this.minutes(),2)+R(this.seconds(),2)}),et("a",!0),et("A",!1),re("a",tt),re("A",tt),re("H",z,ae),re("h",z,ie),re("k",z,ie),re("HH",z,W),re("hh",z,W),re("kk",z,W),re("hmm",G),re("hmmss",q),re("Hmm",G),re("Hmmss",q),he(["H","HH"],ye),he(["k","kk"],function(e,t,n){e=ce(e),t[ye]=24===e?0:e}),he(["a","A"],function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e}),he(["h","hh"],function(e,t,n){t[ye]=ce(e),h(n).bigHour=!0}),he("hmm",function(e,t,n){var i=e.length-2;t[ye]=ce(e.substr(0,i)),t[be]=ce(e.substr(i)),h(n).bigHour=!0}),he("hmmss",function(e,t,n){var i=e.length-4,a=e.length-2;t[ye]=ce(e.substr(0,i)),t[be]=ce(e.substr(i,2)),t[we]=ce(e.substr(a)),h(n).bigHour=!0}),he("Hmm",function(e,t,n){var i=e.length-2;t[ye]=ce(e.substr(0,i)),t[be]=ce(e.substr(i))}),he("Hmmss",function(e,t,n){var i=e.length-4,a=e.length-2;t[ye]=ce(e.substr(0,i)),t[be]=ce(e.substr(i,2)),t[we]=ce(e.substr(a))}),ne=Me("Hours",!0);var nt,it={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Le,monthsShort:Ie,week:{dow:0,doy:6},weekdays:ze,weekdaysMin:qe,weekdaysShort:Ge,meridiemParse:/[ap]\.?m?\.?/i},at={},rt={};function st(e){return e&&e.toLowerCase().replace("_","-")}function ot(e){var t,n;if(void 0===at[e]&&"undefined"!=typeof module&&module&&module.exports&&(n=e)&&n.match("^[^/\\\\]*$"))try{t=nt._abbr,require("./locale/"+e),lt(t)}catch(t){at[e]=null}return at[e]}function lt(e,t){return e&&((t=s(t)?ut(e):ct(e,t))?nt=t:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),nt._abbr}function ct(e,t){if(null===t)return delete at[e],null;var n,i=it;if(t.abbr=e,null!=at[e])x("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),i=at[e]._config;else if(null!=t.parentLocale)if(null!=at[t.parentLocale])i=at[t.parentLocale]._config;else{if(null==(n=ot(t.parentLocale)))return rt[t.parentLocale]||(rt[t.parentLocale]=[]),rt[t.parentLocale].push({name:e,config:t}),null;i=n._config}return at[e]=new M(T(i,t)),rt[e]&&rt[e].forEach(function(e){ct(e.name,e.config)}),lt(e),at[e]}function ut(e){var t;if(!(e=e&&e._locale&&e._locale._abbr?e._locale._abbr:e))return nt;if(!n(e)){if(t=ot(e))return t;e=[e]}return function(e){for(var t,n,i,a,r=0;r<e.length;){for(t=(a=st(e[r]).split("-")).length,n=(n=st(e[r+1]))?n.split("-"):null;0<t;){if(i=ot(a.slice(0,t).join("-")))return i;if(n&&n.length>=t&&function(e,t){for(var n=Math.min(e.length,t.length),i=0;i<n;i+=1)if(e[i]!==t[i])return i;return n}(a,n)>=t-1)break;t--}r++}return nt}(e)}function dt(e){var t=e._a;return t&&-2===h(e).overflow&&(t=t[ge]<0||11<t[ge]?ge:t[ve]<1||t[ve]>Oe(t[me],t[ge])?ve:t[ye]<0||24<t[ye]||24===t[ye]&&(0!==t[be]||0!==t[we]||0!==t[_e])?ye:t[be]<0||59<t[be]?be:t[we]<0||59<t[we]?we:t[_e]<0||999<t[_e]?_e:-1,h(e)._overflowDayOfYear&&(t<me||ve<t)&&(t=ve),h(e)._overflowWeeks&&-1===t&&(t=ke),h(e)._overflowWeekday&&-1===t&&(t=Se),h(e).overflow=t),e}var ht=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ft=/Z|[+-]\d\d(?::?\d\d)?/,mt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],gt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],vt=/^\/?Date\((-?\d+)/i,yt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,bt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function wt(e){var t,n,i,a,r,s,o=e._i,l=ht.exec(o)||pt.exec(o),c=(o=mt.length,gt.length);if(l){for(h(e).iso=!0,t=0,n=o;t<n;t++)if(mt[t][1].exec(l[1])){a=mt[t][0],i=!1!==mt[t][2];break}if(null==a)e._isValid=!1;else{if(l[3]){for(t=0,n=c;t<n;t++)if(gt[t][1].exec(l[3])){r=(l[2]||" ")+gt[t][0];break}if(null==r)return void(e._isValid=!1)}if(i||null==r){if(l[4]){if(!ft.exec(l[4]))return void(e._isValid=!1);s="Z"}e._f=a+(r||"")+(s||""),xt(e)}else e._isValid=!1}}else e._isValid=!1}function _t(e){var t,n,i=yt.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));i?(t=function(e,t,n,i,a,r){return e=[function(e){return(e=parseInt(e,10))<=49?2e3+e:e<=999?1900+e:e}(e),Ie.indexOf(t),parseInt(n,10),parseInt(i,10),parseInt(a,10)],r&&e.push(parseInt(r,10)),e}(i[4],i[3],i[2],i[5],i[6],i[7]),function(e,t,n){if(!e||Ge.indexOf(e)===new Date(t[0],t[1],t[2]).getDay())return 1;h(n).weekdayMismatch=!0,n._isValid=!1}(i[1],t,e)&&(e._a=t,e._tzm=(t=i[8],n=i[9],i=i[10],t?bt[t]:n?0:((t=parseInt(i,10))-(n=t%100))/100*60+n),e._d=je.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),h(e).rfc2822=!0)):e._isValid=!1}function kt(e,t,n){return null!=e?e:null!=t?t:n}function St(e){var n,i,a,r,s,o,l,c,u,d,p,f=[];if(!e._d){for(a=e,r=new Date(t.now()),i=a._useUTC?[r.getUTCFullYear(),r.getUTCMonth(),r.getUTCDate()]:[r.getFullYear(),r.getMonth(),r.getDate()],e._w&&null==e._a[ve]&&null==e._a[ge]&&(null!=(r=(a=e)._w).GG||null!=r.W||null!=r.E?(c=1,u=4,s=kt(r.GG,a._a[me],Be(Mt(),1,4).year),o=kt(r.W,1),((l=kt(r.E,1))<1||7<l)&&(d=!0)):(c=a._locale._week.dow,u=a._locale._week.doy,p=Be(Mt(),c,u),s=kt(r.gg,a._a[me],p.year),o=kt(r.w,p.week),null!=r.d?((l=r.d)<0||6<l)&&(d=!0):null!=r.e?(l=r.e+c,(r.e<0||6<r.e)&&(d=!0)):l=c),o<1||o>He(s,c,u)?h(a)._overflowWeeks=!0:null!=d?h(a)._overflowWeekday=!0:(p=We(s,o,l,c,u),a._a[me]=p.year,a._dayOfYear=p.dayOfYear)),null!=e._dayOfYear&&(r=kt(e._a[me],i[me]),(e._dayOfYear>xe(r)||0===e._dayOfYear)&&(h(e)._overflowDayOfYear=!0),d=je(r,0,e._dayOfYear),e._a[ge]=d.getUTCMonth(),e._a[ve]=d.getUTCDate()),n=0;n<3&&null==e._a[n];++n)e._a[n]=f[n]=i[n];for(;n<7;n++)e._a[n]=f[n]=null==e._a[n]?2===n?1:0:e._a[n];24===e._a[ye]&&0===e._a[be]&&0===e._a[we]&&0===e._a[_e]&&(e._nextDay=!0,e._a[ye]=0),e._d=(e._useUTC?je:Fe).apply(null,f),s=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[ye]=24),e._w&&void 0!==e._w.d&&e._w.d!==s&&(h(e).weekdayMismatch=!0)}}function xt(e){if(e._f===t.ISO_8601)wt(e);else if(e._f===t.RFC_2822)_t(e);else{e._a=[],h(e).empty=!0;for(var n,i,r,s,o,l=""+e._i,c=l.length,u=0,d=N(e._f,e._locale).match(O)||[],p=d.length,f=0;f<p;f++)i=d[f],(n=(l.match(se(i,e))||[])[0])&&(0<(r=l.substr(0,l.indexOf(n))).length&&h(e).unusedInput.push(r),l=l.slice(l.indexOf(n)+n.length),u+=n.length),E[i]?(n?h(e).empty=!1:h(e).unusedTokens.push(i),r=i,o=e,null!=(s=n)&&a(de,r)&&de[r](s,o._a,o,r)):e._strict&&!n&&h(e).unusedTokens.push(i);h(e).charsLeftOver=c-u,0<l.length&&h(e).unusedInput.push(l),e._a[ye]<=12&&!0===h(e).bigHour&&0<e._a[ye]&&(h(e).bigHour=void 0),h(e).parsedDateParts=e._a.slice(0),h(e).meridiem=e._meridiem,e._a[ye]=function(e,t,n){return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((e=e.isPM(n))&&t<12&&(t+=12),t=e||12!==t?t:0):t}(e._locale,e._a[ye],e._meridiem),null!==(c=h(e).era)&&(e._a[me]=e._locale.erasConvertYear(c,e._a[me])),St(e),dt(e)}}function Ct(e){var a,r,d,m=e._i,g=e._f;if(e._locale=e._locale||ut(e._l),null===m||void 0===g&&""===m)return f({nullInput:!0});if("string"==typeof m&&(e._i=m=e._locale.preparse(m)),w(m))return new b(dt(m));if(l(m))e._d=m;else if(n(g)){var v,_,k,S,x,C,T=e,M=!1,A=T._f.length;if(0===A)h(T).invalidFormat=!0,T._d=new Date(NaN);else{for(S=0;S<A;S++)x=0,C=!1,v=y({},T),null!=T._useUTC&&(v._useUTC=T._useUTC),v._f=T._f[S],xt(v),p(v)&&(C=!0),x=(x+=h(v).charsLeftOver)+10*h(v).unusedTokens.length,h(v).score=x,M?x<k&&(k=x,_=v):(null==k||x<k||C)&&(k=x,_=v,C)&&(M=!0);u(T,_||v)}}else g?xt(e):s(g=(m=e)._i)?m._d=new Date(t.now()):l(g)?m._d=new Date(g.valueOf()):"string"==typeof g?(r=m,null!==(a=vt.exec(r._i))?r._d=new Date(+a[1]):(wt(r),!1===r._isValid&&(delete r._isValid,_t(r),!1===r._isValid)&&(delete r._isValid,r._strict?r._isValid=!1:t.createFromInputFallback(r)))):n(g)?(m._a=c(g.slice(0),function(e){return parseInt(e,10)}),St(m)):i(g)?(a=m)._d||(d=void 0===(r=F(a._i)).day?r.date:r.day,a._a=c([r.year,r.month,d,r.hour,r.minute,r.second,r.millisecond],function(e){return e&&parseInt(e,10)}),St(a)):o(g)?m._d=new Date(g):t.createFromInputFallback(m);return p(e)||(e._d=null),e}function Tt(e,t,a,s,o){var l={};return!0!==t&&!1!==t||(s=t,t=void 0),!0!==a&&!1!==a||(s=a,a=void 0),(i(e)&&r(e)||n(e)&&0===e.length)&&(e=void 0),l._isAMomentObject=!0,l._useUTC=l._isUTC=o,l._l=a,l._i=e,l._f=t,l._strict=s,(o=new b(dt(Ct(o=l))))._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function Mt(e,t,n,i){return Tt(e,t,n,i,!1)}function At(e,t){var i,a;if(!(t=1===t.length&&n(t[0])?t[0]:t).length)return Mt();for(i=t[0],a=1;a<t.length;++a)t[a].isValid()&&!t[a][e](i)||(i=t[a]);return i}t.createFromInputFallback=k("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),t.ISO_8601=function(){},t.RFC_2822=function(){},G=k("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=Mt.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:f()}),q=k("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=Mt.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:f()});var Rt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ot(e){var t=(e=F(e)).year||0,n=e.quarter||0,i=e.month||0,r=e.week||e.isoWeek||0,s=e.day||0,o=e.hour||0,l=e.minute||0,c=e.second||0,u=e.millisecond||0;this._isValid=function(e){var t,n,i=!1,r=Rt.length;for(t in e)if(a(e,t)&&(-1===Ce.call(Rt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<r;++n)if(e[Rt[n]]){if(i)return!1;parseFloat(e[Rt[n]])!==ce(e[Rt[n]])&&(i=!0)}return!0}(e),this._milliseconds=+u+1e3*c+6e4*l+1e3*o*60*60,this._days=+s+7*r,this._months=+i+3*n+12*t,this._data={},this._locale=ut(),this._bubble()}function Lt(e){return e instanceof Ot}function It(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Et(e,t){D(e,0,0,function(){var e=this.utcOffset(),n="+";return e<0&&(e=-e,n="-"),n+R(~~(e/60),2)+t+R(~~e%60,2)})}Et("Z",":"),Et("ZZ",""),re("Z",te),re("ZZ",te),he(["Z","ZZ"],function(e,t,n){n._useUTC=!0,n._tzm=Yt(te,e)});var Dt=/([\+\-]|\d\d)/gi;function Yt(e,t){return null===(t=(t||"").match(e))?null:0===(t=60*(e=((t[t.length-1]||[])+"").match(Dt)||["-",0,0])[1]+ce(e[2]))?0:"+"===e[0]?t:-t}function Nt(e,n){var i;return n._isUTC?(n=n.clone(),i=(w(e)||l(e)?e:Mt(e)).valueOf()-n.valueOf(),n._d.setTime(n._d.valueOf()+i),t.updateOffset(n,!1),n):Mt(e).local()}function $t(e){return-Math.round(e._d.getTimezoneOffset())}function Pt(){return!!this.isValid()&&this._isUTC&&0===this._offset}t.updateOffset=function(){};var Ft=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,jt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Ut(e,t){var n,i=e;return Lt(e)?i={ms:e._milliseconds,d:e._days,M:e._months}:o(e)||!isNaN(+e)?(i={},t?i[t]=+e:i.milliseconds=+e):(t=Ft.exec(e))?(n="-"===t[1]?-1:1,i={y:0,d:ce(t[ve])*n,h:ce(t[ye])*n,m:ce(t[be])*n,s:ce(t[we])*n,ms:ce(It(1e3*t[_e]))*n}):(t=jt.exec(e))?(n="-"===t[1]?-1:1,i={y:Wt(t[2],n),M:Wt(t[3],n),w:Wt(t[4],n),d:Wt(t[5],n),h:Wt(t[6],n),m:Wt(t[7],n),s:Wt(t[8],n)}):null==i?i={}:"object"==typeof i&&("from"in i||"to"in i)&&(t=function(e,t){var n;return e.isValid()&&t.isValid()?(t=Nt(t,e),e.isBefore(t)?n=Bt(e,t):((n=Bt(t,e)).milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}(Mt(i.from),Mt(i.to)),(i={}).ms=t.milliseconds,i.M=t.months),n=new Ot(i),Lt(e)&&a(e,"_locale")&&(n._locale=e._locale),Lt(e)&&a(e,"_isValid")&&(n._isValid=e._isValid),n}function Wt(e,t){return e=e&&parseFloat(e.replace(",",".")),(isNaN(e)?0:e)*t}function Bt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function Ht(e,t){return function(n,i){var a;return null===i||isNaN(+i)||(x(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),a=n,n=i,i=a),Vt(this,Ut(n,i),e),this}}function Vt(e,n,i,a){var r=n._milliseconds,s=It(n._days);n=It(n._months);e.isValid()&&(a=null==a||a,n&&Ne(e,Ae(e,"Month")+n*i),s&&Re(e,"Date",Ae(e,"Date")+s*i),r&&e._d.setTime(e._d.valueOf()+r*i),a)&&t.updateOffset(e,s||n)}function zt(e){return"string"==typeof e||e instanceof String}function Gt(e,t){var n,i;return e.date()<t.date()?-Gt(t,e):-((n=12*(t.year()-e.year())+(t.month()-e.month()))+(t-(i=e.clone().add(n,"months"))<0?(t-i)/(i-e.clone().add(n-1,"months")):(t-i)/(e.clone().add(1+n,"months")-i)))||0}function qt(e){return void 0===e?this._locale._abbr:(null!=(e=ut(e))&&(this._locale=e),this)}function Zt(){return this._locale}Ut.fn=Ot.prototype,Ut.invalid=function(){return Ut(NaN)},Le=Ht(1,"add"),ze=Ht(-1,"subtract"),t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]",qe=k("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});var Qt=126227808e5;function Jt(e,t){return(e%t+t)%t}function Xt(e,t,n){return e<100&&0<=e?new Date(e+400,t,n)-Qt:new Date(e,t,n).valueOf()}function Kt(e,t,n){return e<100&&0<=e?Date.UTC(e+400,t,n)-Qt:Date.UTC(e,t,n)}function en(e,t){return t.erasAbbrRegex(e)}function tn(){for(var e,t,n,i=[],a=[],r=[],s=[],o=this.eras(),l=0,c=o.length;l<c;++l)e=oe(o[l].name),t=oe(o[l].abbr),n=oe(o[l].narrow),a.push(e),i.push(t),r.push(n),s.push(e),s.push(t),s.push(n);this._erasRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+a.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+i.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+r.join("|")+")","i")}function nn(e,t){D(0,[e,e.length],0,t)}function an(e,t,n,i,a){var r;return null==e?Be(this,i,a).year:(r=He(e,i,a),function(e,t,n,i,a){return t=je((e=We(e,t,n,i,a)).year,0,e.dayOfYear),this.year(t.getUTCFullYear()),this.month(t.getUTCMonth()),this.date(t.getUTCDate()),this}.call(this,e,t=r<t?r:t,n,i,a))}D("N",0,0,"eraAbbr"),D("NN",0,0,"eraAbbr"),D("NNN",0,0,"eraAbbr"),D("NNNN",0,0,"eraName"),D("NNNNN",0,0,"eraNarrow"),D("y",["y",1],"yo","eraYear"),D("y",["yy",2],0,"eraYear"),D("y",["yyy",3],0,"eraYear"),D("y",["yyyy",4],0,"eraYear"),re("N",en),re("NN",en),re("NNN",en),re("NNNN",function(e,t){return t.erasNameRegex(e)}),re("NNNNN",function(e,t){return t.erasNarrowRegex(e)}),he(["N","NN","NNN","NNNN","NNNNN"],function(e,t,n,i){(i=n._locale.erasParse(e,i,n._strict))?h(n).era=i:h(n).invalidEra=e}),re("y",X),re("yy",X),re("yyy",X),re("yyyy",X),re("yo",function(e,t){return t._eraYearOrdinalRegex||X}),he(["y","yy","yyy","yyyy"],me),he(["yo"],function(e,t,n,i){var a;n._locale._eraYearOrdinalRegex&&(a=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[me]=n._locale.eraYearOrdinalParse(e,a):t[me]=parseInt(e,10)}),D(0,["gg",2],0,function(){return this.weekYear()%100}),D(0,["GG",2],0,function(){return this.isoWeekYear()%100}),nn("gggg","weekYear"),nn("ggggg","weekYear"),nn("GGGG","isoWeekYear"),nn("GGGGG","isoWeekYear"),re("G",K),re("g",K),re("GG",z,W),re("gg",z,W),re("GGGG",Q,H),re("gggg",Q,H),re("GGGGG",J,V),re("ggggg",J,V),pe(["gggg","ggggg","GGGG","GGGGG"],function(e,t,n,i){t[i.substr(0,2)]=ce(e)}),pe(["gg","GG"],function(e,n,i,a){n[a]=t.parseTwoDigitYear(e)}),D("Q",0,"Qo","quarter"),re("Q",U),he("Q",function(e,t){t[ge]=3*(ce(e)-1)}),D("D",["DD",2],"Do","date"),re("D",z,ie),re("DD",z,W),re("Do",function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient}),he(["D","DD"],ve),he("Do",function(e,t){t[ve]=ce(e.match(z)[0])}),Q=Me("Date",!0),D("DDD",["DDDD",3],"DDDo","dayOfYear"),re("DDD",Z),re("DDDD",B),he(["DDD","DDDD"],function(e,t,n){n._dayOfYear=ce(e)}),D("m",["mm",2],0,"minute"),re("m",z,ae),re("mm",z,W),he(["m","mm"],be);var rn;H=Me("Minutes",!1),D("s",["ss",2],0,"second"),re("s",z,ae),re("ss",z,W),he(["s","ss"],we),J=Me("Seconds",!1);for(D("S",0,0,function(){return~~(this.millisecond()/100)}),D(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),D(0,["SSS",3],0,"millisecond"),D(0,["SSSS",4],0,function(){return 10*this.millisecond()}),D(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),D(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),D(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),D(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),D(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),re("S",Z,U),re("SS",Z,W),re("SSS",Z,B),rn="SSSS";rn.length<=9;rn+="S")re(rn,X);function sn(e,t){t[_e]=ce(1e3*("0."+e))}for(rn="S";rn.length<=9;rn+="S")he(rn,sn);function on(e){return e}function ln(e,t,n,i){var a=ut();i=d().set(i,t);return a[n](i,e)}function cn(e,t,n){if(o(e)&&(t=e,e=void 0),e=e||"",null!=t)return ln(e,t,n,"month");for(var i=[],a=0;a<12;a++)i[a]=ln(e,a,n,"month");return i}function un(e,t,n,i){"boolean"==typeof e?o(t)&&(n=t,t=void 0):(t=e,e=!1,o(n=t)&&(n=t,t=void 0)),t=t||"";var a,r=ut(),s=e?r._week.dow:0,l=[];if(null!=n)return ln(t,(n+s)%7,i,"day");for(a=0;a<7;a++)l[a]=ln(t,(a+s)%7,i,"day");return l}V=Me("Milliseconds",!1),D("z",0,0,"zoneAbbr"),D("zz",0,0,"zoneName"),(ie=b.prototype).add=Le,ie.calendar=function(e,s){1===arguments.length&&(arguments[0]?function(e){return w(e)||l(e)||zt(e)||o(e)||function(e){var t=n(e),i=!1;return t&&(i=0===e.filter(function(t){return!o(t)&&zt(e)}).length),t&&i}(e)||function(e){var t,n=i(e)&&!r(e),s=!1,o=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],l=o.length;for(t=0;t<l;t+=1)s=s||a(e,o[t]);return n&&s}(e)||null==e}(arguments[0])?(e=arguments[0],s=void 0):function(e){for(var t=i(e)&&!r(e),n=!1,s=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],o=0;o<s.length;o+=1)n=n||a(e,s[o]);return t&&n}(arguments[0])&&(s=arguments[0],e=void 0):s=e=void 0);var c=Nt(e=e||Mt(),this).startOf("day");c=t.calendarFormat(this,c)||"sameElse",s=s&&(C(s[c])?s[c].call(this,e):s[c]);return this.format(s||this.localeData().calendar(c,this,Mt(e)))},ie.clone=function(){return new b(this)},ie.diff=function(e,t,n){var i,a,r;if(!this.isValid())return NaN;if(!(i=Nt(e,this)).isValid())return NaN;switch(a=6e4*(i.utcOffset()-this.utcOffset()),t=P(t)){case"year":r=Gt(this,i)/12;break;case"month":r=Gt(this,i);break;case"quarter":r=Gt(this,i)/3;break;case"second":r=(this-i)/1e3;break;case"minute":r=(this-i)/6e4;break;case"hour":r=(this-i)/36e5;break;case"day":r=(this-i-a)/864e5;break;case"week":r=(this-i-a)/6048e5;break;default:r=this-i}return n?r:le(r)},ie.endOf=function(e){var n,i;if(void 0!==(e=P(e))&&"millisecond"!==e&&this.isValid()){switch(i=this._isUTC?Kt:Xt,e){case"year":n=i(this.year()+1,0,1)-1;break;case"quarter":n=i(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":n=i(this.year(),this.month()+1,1)-1;break;case"week":n=i(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":n=i(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":n=i(this.year(),this.month(),this.date()+1)-1;break;case"hour":n=this._d.valueOf(),n+=36e5-Jt(n+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":n=this._d.valueOf(),n+=6e4-Jt(n,6e4)-1;break;case"second":n=this._d.valueOf(),n+=1e3-Jt(n,1e3)-1}this._d.setTime(n),t.updateOffset(this,!0)}return this},ie.format=function(e){return e=Y(this,e=e||(this.isUtc()?t.defaultFormatUtc:t.defaultFormat)),this.localeData().postformat(e)},ie.from=function(e,t){return this.isValid()&&(w(e)&&e.isValid()||Mt(e).isValid())?Ut({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},ie.fromNow=function(e){return this.from(Mt(),e)},ie.to=function(e,t){return this.isValid()&&(w(e)&&e.isValid()||Mt(e).isValid())?Ut({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},ie.toNow=function(e){return this.to(Mt(),e)},ie.get=function(e){return C(this[e=P(e)])?this[e]():this},ie.invalidAt=function(){return h(this).overflow},ie.isAfter=function(e,t){return e=w(e)?e:Mt(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=P(t)||"millisecond")?this.valueOf()>e.valueOf():e.valueOf()<this.clone().startOf(t).valueOf())},ie.isBefore=function(e,t){return e=w(e)?e:Mt(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=P(t)||"millisecond")?this.valueOf()<e.valueOf():this.clone().endOf(t).valueOf()<e.valueOf())},ie.isBetween=function(e,t,n,i){return e=w(e)?e:Mt(e),t=w(t)?t:Mt(t),!!(this.isValid()&&e.isValid()&&t.isValid())&&("("===(i=i||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===i[1]?this.isBefore(t,n):!this.isAfter(t,n))},ie.isSame=function(e,t){e=w(e)?e:Mt(e);return!(!this.isValid()||!e.isValid())&&("millisecond"===(t=P(t)||"millisecond")?this.valueOf()===e.valueOf():(e=e.valueOf(),this.clone().startOf(t).valueOf()<=e&&e<=this.clone().endOf(t).valueOf()))},ie.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},ie.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},ie.isValid=function(){return p(this)},ie.lang=qe,ie.locale=qt,ie.localeData=Zt,ie.max=q,ie.min=G,ie.parsingFlags=function(){return u({},h(this))},ie.set=function(e,t){if("object"==typeof e)for(var n=function(e){var t,n=[];for(t in e)a(e,t)&&n.push({unit:t,priority:j[t]});return n.sort(function(e,t){return e.priority-t.priority}),n}(e=F(e)),i=n.length,r=0;r<i;r++)this[n[r].unit](e[n[r].unit]);else if(C(this[e=P(e)]))return this[e](t);return this},ie.startOf=function(e){var n,i;if(void 0!==(e=P(e))&&"millisecond"!==e&&this.isValid()){switch(i=this._isUTC?Kt:Xt,e){case"year":n=i(this.year(),0,1);break;case"quarter":n=i(this.year(),this.month()-this.month()%3,1);break;case"month":n=i(this.year(),this.month(),1);break;case"week":n=i(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":n=i(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":n=i(this.year(),this.month(),this.date());break;case"hour":n=this._d.valueOf(),n-=Jt(n+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":n=this._d.valueOf(),n-=Jt(n,6e4);break;case"second":n=this._d.valueOf(),n-=Jt(n,1e3)}this._d.setTime(n),t.updateOffset(this,!0)}return this},ie.subtract=ze,ie.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},ie.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},ie.toDate=function(){return new Date(this.valueOf())},ie.toISOString=function(e){var t;return this.isValid()?(t=(e=!0!==e)?this.clone().utc():this).year()<0||9999<t.year()?Y(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):C(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",Y(t,"Z")):Y(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ"):null},ie.inspect=function(){var e,t,n;return this.isValid()?(t="moment",e="",this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z"),t="["+t+'("]',n=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(t+n+"-MM-DD[T]HH:mm:ss.SSS"+e+'[")]')):"moment.invalid(/* "+this._i+" */)"},"undefined"!=typeof Symbol&&null!=Symbol.for&&(ie[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),ie.toJSON=function(){return this.isValid()?this.toISOString():null},ie.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},ie.unix=function(){return Math.floor(this.valueOf()/1e3)},ie.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},ie.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},ie.eraName=function(){for(var e,t=this.localeData().eras(),n=0,i=t.length;n<i;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].name;if(t[n].until<=e&&e<=t[n].since)return t[n].name}return""},ie.eraNarrow=function(){for(var e,t=this.localeData().eras(),n=0,i=t.length;n<i;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].narrow;if(t[n].until<=e&&e<=t[n].since)return t[n].narrow}return""},ie.eraAbbr=function(){for(var e,t=this.localeData().eras(),n=0,i=t.length;n<i;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].abbr;if(t[n].until<=e&&e<=t[n].since)return t[n].abbr}return""},ie.eraYear=function(){for(var e,n,i=this.localeData().eras(),a=0,r=i.length;a<r;++a)if(e=i[a].since<=i[a].until?1:-1,n=this.clone().startOf("day").valueOf(),i[a].since<=n&&n<=i[a].until||i[a].until<=n&&n<=i[a].since)return(this.year()-t(i[a].since).year())*e+i[a].offset;return this.year()},ie.year=Te,ie.isLeapYear=function(){return fe(this.year())},ie.weekYear=function(e){return an.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},ie.isoWeekYear=function(e){return an.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},ie.quarter=ie.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},ie.month=$e,ie.daysInMonth=function(){return Oe(this.year(),this.month())},ie.week=ie.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},ie.isoWeek=ie.isoWeeks=function(e){var t=Be(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},ie.weeksInYear=function(){var e=this.localeData()._week;return He(this.year(),e.dow,e.doy)},ie.weeksInWeekYear=function(){var e=this.localeData()._week;return He(this.weekYear(),e.dow,e.doy)},ie.isoWeeksInYear=function(){return He(this.year(),1,4)},ie.isoWeeksInISOWeekYear=function(){return He(this.isoWeekYear(),1,4)},ie.date=Q,ie.day=ie.days=function(e){var t,n,i;return this.isValid()?(t=Ae(this,"Day"),null!=e?(n=e,i=this.localeData(),e="string"!=typeof n?n:isNaN(n)?"number"==typeof(n=i.weekdaysParse(n))?n:null:parseInt(n,10),this.add(e-t,"d")):t):null!=e?this:NaN},ie.weekday=function(e){var t;return this.isValid()?(t=(this.day()+7-this.localeData()._week.dow)%7,null==e?t:this.add(e-t,"d")):null!=e?this:NaN},ie.isoWeekday=function(e){var t,n;return this.isValid()?null!=e?(t=e,n=this.localeData(),n="string"==typeof t?n.weekdaysParse(t)%7||7:isNaN(t)?null:t,this.day(this.day()%7?n:n-7)):this.day()||7:null!=e?this:NaN},ie.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},ie.hour=ie.hours=ne,ie.minute=ie.minutes=H,ie.second=ie.seconds=J,ie.millisecond=ie.milliseconds=V,ie.utcOffset=function(e,n,i){var a,r=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null==e)return this._isUTC?r:$t(this);if("string"==typeof e){if(null===(e=Yt(te,e)))return this}else Math.abs(e)<16&&!i&&(e*=60);return!this._isUTC&&n&&(a=$t(this)),this._offset=e,this._isUTC=!0,null!=a&&this.add(a,"m"),r!==e&&(!n||this._changeInProgress?Vt(this,Ut(e-r,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null)),this},ie.utc=function(e){return this.utcOffset(0,e)},ie.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e)&&this.subtract($t(this),"m"),this},ie.parseZone=function(){var e;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(e=Yt(ee,this._i))?this.utcOffset(e):this.utcOffset(0,!0)),this},ie.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?Mt(e).utcOffset():0,(this.utcOffset()-e)%60==0)},ie.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},ie.isLocal=function(){return!!this.isValid()&&!this._isUTC},ie.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},ie.isUtc=Pt,ie.isUTC=Pt,ie.zoneAbbr=function(){return this._isUTC?"UTC":""},ie.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},ie.dates=k("dates accessor is deprecated. Use date instead.",Q),ie.months=k("months accessor is deprecated. Use month instead",$e),ie.years=k("years accessor is deprecated. Use year instead",Te),ie.zone=k("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(e,t){return null!=e?(this.utcOffset(e="string"!=typeof e?-e:e,t),this):-this.utcOffset()}),ie.isDSTShifted=k("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){var e,t;return s(this._isDSTShifted)&&(y(e={},this),(e=Ct(e))._a?(t=(e._isUTC?d:Mt)(e._a),this._isDSTShifted=this.isValid()&&0<function(e,t){for(var n=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),a=0,r=0;r<n;r++)ce(e[r])!==ce(t[r])&&a++;return a+i}(e._a,t.toArray())):this._isDSTShifted=!1),this._isDSTShifted}),(ae=M.prototype).calendar=function(e,t,n){return C(e=this._calendar[e]||this._calendar.sameElse)?e.call(t,n):e},ae.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(O).map(function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e}).join(""),this._longDateFormat[e])},ae.invalidDate=function(){return this._invalidDate},ae.ordinal=function(e){return this._ordinal.replace("%d",e)},ae.preparse=on,ae.postformat=on,ae.relativeTime=function(e,t,n,i){var a=this._relativeTime[n];return C(a)?a(e,t,n,i):a.replace(/%d/i,e)},ae.pastFuture=function(e,t){return C(e=this._relativeTime[0<e?"future":"past"])?e(t):e.replace(/%s/i,t)},ae.set=function(e){var t,n;for(n in e)a(e,n)&&(C(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},ae.eras=function(e,n){for(var i,a=this._eras||ut("en")._eras,r=0,s=a.length;r<s;++r){if("string"==typeof a[r].since)i=t(a[r].since).startOf("day"),a[r].since=i.valueOf();switch(typeof a[r].until){case"undefined":a[r].until=1/0;break;case"string":i=t(a[r].until).startOf("day").valueOf(),a[r].until=i.valueOf()}}return a},ae.erasParse=function(e,t,n){var i,a,r,s,o,l=this.eras();for(e=e.toUpperCase(),i=0,a=l.length;i<a;++i)if(r=l[i].name.toUpperCase(),s=l[i].abbr.toUpperCase(),o=l[i].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(s===e)return l[i];break;case"NNNN":if(r===e)return l[i];break;case"NNNNN":if(o===e)return l[i]}else if(0<=[r,s,o].indexOf(e))return l[i]},ae.erasConvertYear=function(e,n){var i=e.since<=e.until?1:-1;return void 0===n?t(e.since).year():t(e.since).year()+(n-e.offset)*i},ae.erasAbbrRegex=function(e){return a(this,"_erasAbbrRegex")||tn.call(this),e?this._erasAbbrRegex:this._erasRegex},ae.erasNameRegex=function(e){return a(this,"_erasNameRegex")||tn.call(this),e?this._erasNameRegex:this._erasRegex},ae.erasNarrowRegex=function(e){return a(this,"_erasNarrowRegex")||tn.call(this),e?this._erasNarrowRegex:this._erasRegex},ae.months=function(e,t){return e?(n(this._months)?this._months:this._months[(this._months.isFormat||Ee).test(t)?"format":"standalone"])[e.month()]:n(this._months)?this._months:this._months.standalone},ae.monthsShort=function(e,t){return e?(n(this._monthsShort)?this._monthsShort:this._monthsShort[Ee.test(t)?"format":"standalone"])[e.month()]:n(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},ae.monthsParse=function(e,t,n){var i,a;if(this._monthsParseExact)return function(e,t,n){var i,a,r;e=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],i=0;i<12;++i)r=d([2e3,i]),this._shortMonthsParse[i]=this.monthsShort(r,"").toLocaleLowerCase(),this._longMonthsParse[i]=this.months(r,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(a=Ce.call(this._shortMonthsParse,e))?a:null:-1!==(a=Ce.call(this._longMonthsParse,e))?a:null:"MMM"===t?-1!==(a=Ce.call(this._shortMonthsParse,e))||-1!==(a=Ce.call(this._longMonthsParse,e))?a:null:-1!==(a=Ce.call(this._longMonthsParse,e))||-1!==(a=Ce.call(this._shortMonthsParse,e))?a:null}.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),i=0;i<12;i++){if(a=d([2e3,i]),n&&!this._longMonthsParse[i]&&(this._longMonthsParse[i]=new RegExp("^"+this.months(a,"").replace(".","")+"$","i"),this._shortMonthsParse[i]=new RegExp("^"+this.monthsShort(a,"").replace(".","")+"$","i")),n||this._monthsParse[i]||(a="^"+this.months(a,"")+"|^"+this.monthsShort(a,""),this._monthsParse[i]=new RegExp(a.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[i].test(e))return i;if(n&&"MMM"===t&&this._shortMonthsParse[i].test(e))return i;if(!n&&this._monthsParse[i].test(e))return i}},ae.monthsRegex=function(e){return this._monthsParseExact?(a(this,"_monthsRegex")||Pe.call(this),e?this._monthsStrictRegex:this._monthsRegex):(a(this,"_monthsRegex")||(this._monthsRegex=Ye),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},ae.monthsShortRegex=function(e){return this._monthsParseExact?(a(this,"_monthsRegex")||Pe.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(a(this,"_monthsShortRegex")||(this._monthsShortRegex=De),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},ae.week=function(e){return Be(e,this._week.dow,this._week.doy).week},ae.firstDayOfYear=function(){return this._week.doy},ae.firstDayOfWeek=function(){return this._week.dow},ae.weekdays=function(e,t){return t=n(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"],!0===e?Ve(t,this._week.dow):e?t[e.day()]:t},ae.weekdaysMin=function(e){return!0===e?Ve(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},ae.weekdaysShort=function(e){return!0===e?Ve(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},ae.weekdaysParse=function(e,t,n){var i,a;if(this._weekdaysParseExact)return function(e,t,n){var i,a,r;e=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],i=0;i<7;++i)r=d([2e3,1]).day(i),this._minWeekdaysParse[i]=this.weekdaysMin(r,"").toLocaleLowerCase(),this._shortWeekdaysParse[i]=this.weekdaysShort(r,"").toLocaleLowerCase(),this._weekdaysParse[i]=this.weekdays(r,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(a=Ce.call(this._weekdaysParse,e))?a:null:"ddd"===t?-1!==(a=Ce.call(this._shortWeekdaysParse,e))?a:null:-1!==(a=Ce.call(this._minWeekdaysParse,e))?a:null:"dddd"===t?-1!==(a=Ce.call(this._weekdaysParse,e))||-1!==(a=Ce.call(this._shortWeekdaysParse,e))||-1!==(a=Ce.call(this._minWeekdaysParse,e))?a:null:"ddd"===t?-1!==(a=Ce.call(this._shortWeekdaysParse,e))||-1!==(a=Ce.call(this._weekdaysParse,e))||-1!==(a=Ce.call(this._minWeekdaysParse,e))?a:null:-1!==(a=Ce.call(this._minWeekdaysParse,e))||-1!==(a=Ce.call(this._weekdaysParse,e))||-1!==(a=Ce.call(this._shortWeekdaysParse,e))?a:null}.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),i=0;i<7;i++){if(a=d([2e3,1]).day(i),n&&!this._fullWeekdaysParse[i]&&(this._fullWeekdaysParse[i]=new RegExp("^"+this.weekdays(a,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[i]=new RegExp("^"+this.weekdaysShort(a,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[i]=new RegExp("^"+this.weekdaysMin(a,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[i]||(a="^"+this.weekdays(a,"")+"|^"+this.weekdaysShort(a,"")+"|^"+this.weekdaysMin(a,""),this._weekdaysParse[i]=new RegExp(a.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[i].test(e))return i;if(n&&"ddd"===t&&this._shortWeekdaysParse[i].test(e))return i;if(n&&"dd"===t&&this._minWeekdaysParse[i].test(e))return i;if(!n&&this._weekdaysParse[i].test(e))return i}},ae.weekdaysRegex=function(e){return this._weekdaysParseExact?(a(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(a(this,"_weekdaysRegex")||(this._weekdaysRegex=Ze),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},ae.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(a(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(a(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Qe),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},ae.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(a(this,"_weekdaysRegex")||Xe.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(a(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Je),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},ae.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},ae.meridiem=function(e,t,n){return 11<e?n?"pm":"PM":n?"am":"AM"},lt("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===ce(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),t.lang=k("moment.lang is deprecated. Use moment.locale instead.",lt),t.langData=k("moment.langData is deprecated. Use moment.localeData instead.",ut);var dn=Math.abs;function hn(e,t,n,i){return t=Ut(t,n),e._milliseconds+=i*t._milliseconds,e._days+=i*t._days,e._months+=i*t._months,e._bubble()}function pn(e){return e<0?Math.floor(e):Math.ceil(e)}function fn(e){return 4800*e/146097}function mn(e){return 146097*e/4800}function gn(e){return function(){return this.as(e)}}function vn(e){return function(){return this.isValid()?this._data[e]:NaN}}U=gn("ms"),W=gn("s"),Z=gn("m"),B=gn("h"),Le=gn("d"),q=gn("w"),G=gn("M"),ze=gn("Q"),ne=gn("y"),H=U;J=vn("milliseconds"),V=vn("seconds"),Q=vn("minutes"),Te=vn("hours"),ae=vn("days");var yn=vn("months"),bn=vn("years"),wn=Math.round,_n={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};var kn=Math.abs;function Sn(e){return(0<e)-(e<0)||+e}function xn(){var e,t,n,i,a,r,s,o,l,c,u;return this.isValid()?(e=kn(this._milliseconds)/1e3,t=kn(this._days),n=kn(this._months),(o=this.asSeconds())?(i=le(e/60),a=le(i/60),e%=60,i%=60,r=le(n/12),n%=12,s=e?e.toFixed(3).replace(/\.?0+$/,""):"",l=Sn(this._months)!==Sn(o)?"-":"",c=Sn(this._days)!==Sn(o)?"-":"",u=Sn(this._milliseconds)!==Sn(o)?"-":"",(o<0?"-":"")+"P"+(r?l+r+"Y":"")+(n?l+n+"M":"")+(t?c+t+"D":"")+(a||i||e?"T":"")+(a?u+a+"H":"")+(i?u+i+"M":"")+(e?u+s+"S":"")):"P0D"):this.localeData().invalidDate()}var Cn=Ot.prototype;return Cn.isValid=function(){return this._isValid},Cn.abs=function(){var e=this._data;return this._milliseconds=dn(this._milliseconds),this._days=dn(this._days),this._months=dn(this._months),e.milliseconds=dn(e.milliseconds),e.seconds=dn(e.seconds),e.minutes=dn(e.minutes),e.hours=dn(e.hours),e.months=dn(e.months),e.years=dn(e.years),this},Cn.add=function(e,t){return hn(this,e,t,1)},Cn.subtract=function(e,t){return hn(this,e,t,-1)},Cn.as=function(e){if(!this.isValid())return NaN;var t,n,i=this._milliseconds;if("month"===(e=P(e))||"quarter"===e||"year"===e)switch(t=this._days+i/864e5,n=this._months+fn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(mn(this._months)),e){case"week":return t/7+i/6048e5;case"day":return t+i/864e5;case"hour":return 24*t+i/36e5;case"minute":return 1440*t+i/6e4;case"second":return 86400*t+i/1e3;case"millisecond":return Math.floor(864e5*t)+i;default:throw new Error("Unknown unit "+e)}},Cn.asMilliseconds=U,Cn.asSeconds=W,Cn.asMinutes=Z,Cn.asHours=B,Cn.asDays=Le,Cn.asWeeks=q,Cn.asMonths=G,Cn.asQuarters=ze,Cn.asYears=ne,Cn.valueOf=H,Cn._bubble=function(){var e=this._milliseconds,t=this._days,n=this._months,i=this._data;return 0<=e&&0<=t&&0<=n||e<=0&&t<=0&&n<=0||(e+=864e5*pn(mn(n)+t),n=t=0),i.milliseconds=e%1e3,e=le(e/1e3),i.seconds=e%60,e=le(e/60),i.minutes=e%60,e=le(e/60),i.hours=e%24,t+=le(e/24),n+=e=le(fn(t)),t-=pn(mn(e)),e=le(n/12),n%=12,i.days=t,i.months=n,i.years=e,this},Cn.clone=function(){return Ut(this)},Cn.get=function(e){return e=P(e),this.isValid()?this[e+"s"]():NaN},Cn.milliseconds=J,Cn.seconds=V,Cn.minutes=Q,Cn.hours=Te,Cn.days=ae,Cn.weeks=function(){return le(this.days()/7)},Cn.months=yn,Cn.years=bn,Cn.humanize=function(e,t){var n,i;return this.isValid()?(n=!1,i=_n,"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(n=e),"object"==typeof t&&(i=Object.assign({},_n,t),null!=t.s)&&null==t.ss&&(i.ss=t.s-1),t=function(e,t,n,i){var a=Ut(e).abs(),r=wn(a.as("s")),s=wn(a.as("m")),o=wn(a.as("h")),l=wn(a.as("d")),c=wn(a.as("M")),u=wn(a.as("w"));return a=wn(a.as("y")),r=(r<=n.ss?["s",r]:r<n.s&&["ss",r])||(s<=1?["m"]:s<n.m&&["mm",s])||(o<=1?["h"]:o<n.h&&["hh",o])||(l<=1?["d"]:l<n.d&&["dd",l]),(r=(r=null!=n.w?r||(u<=1?["w"]:u<n.w&&["ww",u]):r)||(c<=1?["M"]:c<n.M&&["MM",c])||(a<=1?["y"]:["yy",a]))[2]=t,r[3]=0<+e,r[4]=i,function(e,t,n,i,a){return a.relativeTime(t||1,!!n,e,i)}.apply(null,r)}(this,!n,i,e=this.localeData()),n&&(t=e.pastFuture(+this,t)),e.postformat(t)):this.localeData().invalidDate()},Cn.toISOString=xn,Cn.toString=xn,Cn.toJSON=xn,Cn.locale=qt,Cn.localeData=Zt,Cn.toIsoString=k("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",xn),Cn.lang=qe,D("X",0,0,"unix"),D("x",0,0,"valueOf"),re("x",K),re("X",/[+-]?\d+(\.\d{1,3})?/),he("X",function(e,t,n){n._d=new Date(1e3*parseFloat(e))}),he("x",function(e,t,n){n._d=new Date(ce(e))}),t.version="2.30.1",e=Mt,t.fn=ie,t.min=function(){return At("isBefore",[].slice.call(arguments,0))},t.max=function(){return At("isAfter",[].slice.call(arguments,0))},t.now=function(){return Date.now?Date.now():+new Date},t.utc=d,t.unix=function(e){return Mt(1e3*e)},t.months=function(e,t){return cn(e,t,"months")},t.isDate=l,t.locale=lt,t.invalid=f,t.duration=Ut,t.isMoment=w,t.weekdays=function(e,t,n){return un(e,t,n,"weekdays")},t.parseZone=function(){return Mt.apply(null,arguments).parseZone()},t.localeData=ut,t.isDuration=Lt,t.monthsShort=function(e,t){return cn(e,t,"monthsShort")},t.weekdaysMin=function(e,t,n){return un(e,t,n,"weekdaysMin")},t.defineLocale=ct,t.updateLocale=function(e,t){var n,i;return null!=t?(i=it,null!=at[e]&&null!=at[e].parentLocale?at[e].set(T(at[e]._config,t)):(t=T(i=null!=(n=ot(e))?n._config:i,t),null==n&&(t.abbr=e),(i=new M(t)).parentLocale=at[e],at[e]=i),lt(e)):null!=at[e]&&(null!=at[e].parentLocale?(at[e]=at[e].parentLocale,e===lt()&&lt(e)):null!=at[e]&&delete at[e]),at[e]},t.locales=function(){return A(at)},t.weekdaysShort=function(e,t,n){return un(e,t,n,"weekdaysShort")},t.normalizeUnits=P,t.relativeTimeRounding=function(e){return void 0===e?wn:"function"==typeof e&&(wn=e,!0)},t.relativeTimeThreshold=function(e,t){return void 0!==_n[e]&&(void 0===t?_n[e]:(_n[e]=t,"s"===e&&(_n.ss=t-1),!0))},t.calendarFormat=function(e,t){return(e=e.diff(t,"days",!0))<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},t.prototype=ie,t.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},t}),function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.VueStarRating=t():e.VueStarRating=t()}("undefined"!=typeof self?self:this,function(){return function(e){var t={};function n(i){if(t[i])return t[i].exports;var a=t[i]={i:i,l:!1,exports:{}};return e[i].call(a.exports,a,a.exports,n),a.l=!0,a.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(i,a,function(t){return e[t]}.bind(null,a));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="fb15")}({"27c2":function(e,t,n){(t=n("4bad")(!1)).push([e.i,".vue-star-rating-star[data-v-fde73a0c]{display:inline-block}.vue-star-rating-pointer[data-v-fde73a0c]{cursor:pointer}.vue-star-rating[data-v-fde73a0c]{display:flex;align-items:center}.vue-star-rating-inline[data-v-fde73a0c]{display:inline-flex}.vue-star-rating-rating-text[data-v-fde73a0c]{margin-left:7px}.vue-star-rating-rtl[data-v-fde73a0c]{direction:rtl}.vue-star-rating-rtl .vue-star-rating-rating-text[data-v-fde73a0c]{margin-right:10px;direction:rtl}.sr-only[data-v-fde73a0c]{position:absolute;left:-10000px;top:auto;width:1px;height:1px;overflow:hidden}",""]),e.exports=t},"2b2b":function(e,t,n){"use strict";var i=n("3c76");n.n(i).a},"3c76":function(e,t,n){var i=n("27c2");"string"==typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals),(0,n("499e").default)("af45d76c",i,!0,{sourceMap:!1,shadowMode:!1})},"499e":function(e,t,n){"use strict";function i(e,t){for(var n=[],i={},a=0;a<t.length;a++){var r=t[a],s=r[0],o={id:e+":"+a,css:r[1],media:r[2],sourceMap:r[3]};i[s]?i[s].parts.push(o):n.push(i[s]={id:s,parts:[o]})}return n}n.r(t),n.d(t,"default",function(){return f});var a="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!a)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var r={},s=a&&(document.head||document.getElementsByTagName("head")[0]),o=null,l=0,c=!1,u=function(){},d=null,h="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function f(e,t,n,a){c=n,d=a||{};var s=i(e,t);return m(s),function(t){for(var n=[],a=0;a<s.length;a++){var o=s[a],l=r[o.id];l.refs--,n.push(l)}for(t?m(s=i(e,t)):s=[],a=0;a<n.length;a++)if(0===(l=n[a]).refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete r[l.id]}}}function m(e){for(var t=0;t<e.length;t++){var n=e[t],i=r[n.id];if(i){i.refs++;for(var a=0;a<i.parts.length;a++)i.parts[a](n.parts[a]);for(;a<n.parts.length;a++)i.parts.push(v(n.parts[a]));i.parts.length>n.parts.length&&(i.parts.length=n.parts.length)}else{var s=[];for(a=0;a<n.parts.length;a++)s.push(v(n.parts[a]));r[n.id]={id:n.id,refs:1,parts:s}}}}function g(){var e=document.createElement("style");return e.setAttribute("nonce",yunoNonce),e.type="text/css",s.appendChild(e),e}function v(e){var t,n,i=document.querySelector("style["+h+'~="'+e.id+'"]');if(i){if(c)return u;i.parentNode.removeChild(i)}if(p){var a=l++;i=o||(o=g()),t=b.bind(null,i,a,!1),n=b.bind(null,i,a,!0)}else i=g(),t=w.bind(null,i),n=function(){i.parentNode.removeChild(i)};return t(e),function(i){if(i){if(i.css===e.css&&i.media===e.media&&i.sourceMap===e.sourceMap)return;t(e=i)}else n()}}var y=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}();function b(e,t,n,i){var a=n?"":i.css;if(e.styleSheet)e.styleSheet.cssText=y(t,a);else{var r=document.createTextNode(a),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(r,s[t]):e.appendChild(r)}}function w(e,t){var n=t.css,i=t.media,a=t.sourceMap;if(i&&e.setAttribute("media",i),d.ssrId&&e.setAttribute(h,t.id),a&&(n+="\n/*# sourceURL="+a.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},"4bad":function(e,t,n){"use strict";function i(e,t){var n=e[1]||"",i=e[3];if(!i)return n;if(t&&"function"==typeof btoa){var a=function(e){var t=btoa(unescape(encodeURIComponent(JSON.stringify(e)))),n="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(t);return"/*# ".concat(n," */")}(i),r=i.sources.map(function(e){return"/*# sourceURL=".concat(i.sourceRoot||"").concat(e," */")});return[n].concat(r).concat([a]).join("\n")}return[n].join("\n")}e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var n=i(t,e);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n}).join("")},t.i=function(e,n,i){"string"==typeof e&&(e=[[null,e,""]]);var a={};if(i)for(var r=0;r<this.length;r++){var s=this[r][0];null!=s&&(a[s]=!0)}for(var o=0;o<e.length;o++){var l=[].concat(e[o]);i&&a[l[0]]||(n&&(l[2]?l[2]="".concat(n," and ").concat(l[2]):l[2]=n),t.push(l))}},t}},"70a0":function(e,t,n){var i=n("812a");"string"==typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals),(0,n("499e").default)("4599b915",i,!0,{sourceMap:!1,shadowMode:!1})},"812a":function(e,t,n){(t=n("4bad")(!1)).push([e.i,".vue-star-rating-star[data-v-ef4bc576]{overflow:visible!important}.vue-star-rating-star-rotate[data-v-ef4bc576]{transition:all .25s}.vue-star-rating-star-rotate[data-v-ef4bc576]:hover{transition:transform .25s;transform:rotate(-15deg) scale(1.3)}",""]),e.exports=t},8875:function(e,t,n){var i,a,r;"undefined"!=typeof self&&self,a=[],i=function(){function e(){var t=Object.getOwnPropertyDescriptor(document,"currentScript");if(!t&&"currentScript"in document&&document.currentScript)return document.currentScript;if(t&&t.get!==e&&document.currentScript)return document.currentScript;try{throw new Error}catch(e){var n,i,a,r=/@([^@]*):(\d+):(\d+)\s*$/gi,s=/.*at [^(]*\((.*):(.+):(.+)\)$/gi.exec(e.stack)||r.exec(e.stack),o=s&&s[1]||!1,l=s&&s[2]||!1,c=document.location.href.replace(document.location.hash,""),u=document.getElementsByTagName("script");o===c&&(n=document.documentElement.outerHTML,i=new RegExp("(?:[^\\n]+?\\n){0,"+(l-2)+"}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*","i"),a=n.replace(i,"$1").trim());for(var d=0;d<u.length;d++){if("interactive"===u[d].readyState)return u[d];if(u[d].src===o)return u[d];if(o===c&&u[d].innerHTML&&u[d].innerHTML.trim()===a)return u[d]}return null}}return e},void 0===(r="function"==typeof i?i.apply(t,a):i)||(e.exports=r)},ab73:function(e,t,n){"use strict";var i=n("70a0");n.n(i).a},d4aa:function(e,t){e.exports=class{constructor(e){this.color=e}parseAlphaColor(){return/^rgba\((\d{1,3}%?\s*,\s*){3}(\d*(?:\.\d+)?)\)$/.test(this.color)?this.parseRgba():/^hsla\(\d+\s*,\s*([\d.]+%\s*,\s*){2}(\d*(?:\.\d+)?)\)$/.test(this.color)?this.parseHsla():/^#([0-9A-Fa-f]{4}|[0-9A-Fa-f]{8})$/.test(this.color)?this.parseAlphaHex():/^transparent$/.test(this.color)?this.parseTransparent():{color:this.color,opacity:"1"}}parseRgba(){return{color:this.color.replace(/,(?!.*,).*(?=\))|a/g,""),opacity:this.color.match(/\.\d+|[01](?=\))/)[0]}}parseHsla(){return{color:this.color.replace(/,(?!.*,).*(?=\))|a/g,""),opacity:this.color.match(/\.\d+|[01](?=\))/)[0]}}parseAlphaHex(){return{color:5===this.color.length?this.color.substring(0,4):this.color.substring(0,7),opacity:5===this.color.length?(parseInt(this.color.substring(4,5)+this.color.substring(4,5),16)/255).toFixed(2):(parseInt(this.color.substring(7,9),16)/255).toFixed(2)}}parseTransparent(){return{color:"#fff",opacity:0}}}},fb15:function(e,t,n){"use strict";if(n.r(t),"undefined"!=typeof window){var i=window.document.currentScript,a=n("8875");i=a(),"currentScript"in document||Object.defineProperty(document,"currentScript",{get:a});var r=i&&i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);r&&(n.p=r[1])}var s=n("d4aa"),o=n.n(s),l={name:"Star",props:{fill:{type:Number,default:0},points:{type:Array,default:()=>[]},size:{type:Number,default:50},starId:{type:Number,required:!0},activeColor:{type:String,required:!0},inactiveColor:{type:String,required:!0},borderColor:{type:String,default:"#000"},activeBorderColor:{type:String,default:"#000"},borderWidth:{type:Number,default:0},roundedCorners:{type:Boolean,default:!1},rtl:{type:Boolean,default:!1},glow:{type:Number,default:0},glowColor:{type:String,default:null,required:!1},animate:{type:Boolean,default:!1}},data:()=>({starPoints:[19.8,2.2,6.6,43.56,39.6,17.16,0,17.16,33,43.56],grad:"",glowId:"",isStarActive:!0}),computed:{starPointsToString(){return this.starPoints.join(",")},gradId(){return"url(#"+this.grad+")"},starSize(){const e=this.roundedCorners&&this.borderWidth<=0?parseInt(this.size)-parseInt(this.border):this.size;return parseInt(e)+parseInt(this.border)},starFill(){return this.rtl?100-this.fill+"%":this.fill+"%"},border(){return this.roundedCorners&&this.borderWidth<=0?6:this.borderWidth},getBorderColor(){return this.roundedCorners&&this.borderWidth<=0?this.fill<=0?this.inactiveColor:this.activeColor:this.fill<=0?this.borderColor:this.activeBorderColor},maxSize(){return this.starPoints.reduce(function(e,t){return Math.max(e,t)})},viewBox(){return"0 0 "+this.maxSize+" "+this.maxSize},shouldAnimate(){return this.animate&&this.isStarActive},strokeLinejoin(){return this.roundedCorners?"round":"miter"}},created(){this.starPoints=this.points.length?this.points:this.starPoints,this.calculatePoints(),this.grad=this.getRandomId(),this.glowId=this.getRandomId()},methods:{mouseMoving(e){"undefined"!==e.touchAction&&this.$emit("star-mouse-move",{event:e,position:this.getPosition(e),id:this.starId})},touchStart(){this.$nextTick(()=>{this.isStarActive=!0})},touchEnd(){this.$nextTick(()=>{this.isStarActive=!1})},getPosition(e){var t=.92*this.size;const n=this.rtl?Math.min(e.offsetX,45):Math.max(e.offsetX,1);var i=Math.round(100/t*n);return Math.min(i,100)},selected(e){this.$emit("star-selected",{id:this.starId,position:this.getPosition(e)})},getRandomId:()=>Math.random().toString(36).substring(7),calculatePoints(){this.starPoints=this.starPoints.map((e,t)=>{const n=t%2==0?1.5*this.border:0;return this.size/this.maxSize*e+n})},getColor:e=>new o.a(e).parseAlphaColor().color,getOpacity:e=>new o.a(e).parseAlphaColor().opacity}},c=l;function u(e,t,n,i,a,r,s,o){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),i&&(c.functional=!0),r&&(c._scopeId="data-v-"+r),s?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),a&&a.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},c._ssrRegister=l):a&&(l=o?function(){a.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:a),l)if(c.functional){c._injectStyles=l;var u=c.render;c.render=function(e,t){return l.call(t),u(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}n("ab73");var d=u(c,function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("svg",{class:["vue-star-rating-star",{"vue-star-rating-star-rotate":e.shouldAnimate}],attrs:{height:e.starSize,width:e.starSize,viewBox:e.viewBox},on:{mousemove:e.mouseMoving,click:e.selected,touchstart:e.touchStart,touchend:e.touchEnd}},[n("linearGradient",{attrs:{id:e.grad,x1:"0",x2:"100%",y1:"0",y2:"0"}},[n("stop",{attrs:{offset:e.starFill,"stop-color":e.rtl?e.getColor(e.inactiveColor):e.getColor(e.activeColor),"stop-opacity":e.rtl?e.getOpacity(e.inactiveColor):e.getOpacity(e.activeColor)}}),n("stop",{attrs:{offset:e.starFill,"stop-color":e.rtl?e.getColor(e.activeColor):e.getColor(e.inactiveColor),"stop-opacity":e.rtl?e.getOpacity(e.activeColor):e.getOpacity(e.inactiveColor)}})],1),n("filter",{attrs:{id:e.glowId,height:"130%",width:"130%",filterUnits:"userSpaceOnUse"}},[n("feGaussianBlur",{attrs:{stdDeviation:e.glow,result:"coloredBlur"}}),n("feMerge",[n("feMergeNode",{attrs:{in:"coloredBlur"}}),n("feMergeNode",{attrs:{in:"SourceGraphic"}})],1)],1),e.glowColor&&e.glow>0?n("polygon",{directives:[{name:"show",rawName:"v-show",value:e.fill>1,expression:"fill > 1"}],attrs:{points:e.starPointsToString,fill:e.gradId,stroke:e.glowColor,filter:"url(#"+e.glowId+")","stroke-width":e.border}}):e._e(),n("polygon",{attrs:{points:e.starPointsToString,fill:e.gradId,stroke:e.getBorderColor,"stroke-width":e.border,"stroke-linejoin":e.strokeLinejoin}}),n("polygon",{attrs:{points:e.starPointsToString,fill:e.gradId}})],1)},[],!1,null,"ef4bc576",null),h={components:{star:d.exports},model:{prop:"rating",event:"rating-selected"},props:{increment:{type:Number,default:1},rating:{type:Number,default:0},roundStartRating:{type:Boolean,default:!0},activeColor:{type:[String,Array],default:"#ffd055"},inactiveColor:{type:String,default:"#d8d8d8"},maxRating:{type:Number,default:5},starPoints:{type:Array,default:()=>[]},starSize:{type:Number,default:50},showRating:{type:Boolean,default:!0},readOnly:{type:Boolean,default:!1},textClass:{type:String,default:""},inline:{type:Boolean,default:!1},borderColor:{type:String,default:"#999"},activeBorderColor:{type:[String,Array],default:null},borderWidth:{type:Number,default:0},roundedCorners:{type:Boolean,default:!1},padding:{type:Number,default:0},rtl:{type:Boolean,default:!1},fixedPoints:{type:Number,default:null},glow:{type:Number,default:0},glowColor:{type:String,default:"#fff"},clearable:{type:Boolean,default:!1},activeOnClick:{type:Boolean,default:!1},animate:{type:Boolean,default:!1}},data:()=>({step:0,fillLevel:[],currentRating:0,selectedRating:0,ratingSelected:!1}),computed:{formattedRating(){return null===this.fixedPoints?this.currentRating:this.currentRating.toFixed(this.fixedPoints)},shouldRound(){return this.ratingSelected||this.roundStartRating},margin(){return this.padding+this.borderWidth},activeColors(){return Array.isArray(this.activeColor)?this.padColors(this.activeColor,this.maxRating,this.activeColor.slice(-1)[0]):new Array(this.maxRating).fill(this.activeColor)},currentActiveColor(){return this.activeOnClick?this.selectedRating>0?this.activeColors[Math.ceil(this.selectedRating)-1]:this.inactiveColor:this.currentRating>0?this.activeColors[Math.ceil(this.currentRating)-1]:this.inactiveColor},activeBorderColors(){if(Array.isArray(this.activeBorderColor))return this.padColors(this.activeBorderColor,this.maxRating,this.activeBorderColor.slice(-1)[0]);let e=this.activeBorderColor?this.activeBorderColor:this.borderColor;return new Array(this.maxRating).fill(e)},currentActiveBorderColor(){return this.activeOnClick?this.selectedRating>0?this.activeBorderColors[Math.ceil(this.selectedRating)-1]:this.borderColor:this.currentRating>0?this.activeBorderColors[Math.ceil(this.currentRating)-1]:this.borderColor}},watch:{rating(e){this.currentRating=e,this.selectedRating=e,this.createStars(this.shouldRound)}},created(){this.step=100*this.increment,this.currentRating=this.rating,this.selectedRating=this.currentRating,this.createStars(this.roundStartRating)},methods:{setRating(e,t){if(!this.readOnly){const n=this.rtl?(100-e.position)/100:e.position/100;this.currentRating=(e.id+n-1).toFixed(2),this.currentRating=this.currentRating>this.maxRating?this.maxRating:this.currentRating,t?(this.createStars(!0,!0),this.clearable&&this.currentRating===this.selectedRating?this.selectedRating=0:this.selectedRating=this.currentRating,this.$emit("rating-selected",this.selectedRating),this.ratingSelected=!0):(this.createStars(!0,!this.activeOnClick),this.$emit("current-rating",this.currentRating))}},resetRating(){this.readOnly||(this.currentRating=this.selectedRating,this.createStars(this.shouldRound))},createStars(e=!0,t=!0){e&&this.round();for(var n=0;n<this.maxRating;n++){let e=0;n<this.currentRating&&(e=this.currentRating-n>1?100:100*(this.currentRating-n)),t&&(this.fillLevel[n]=Math.round(e))}},round(){var e=1/this.increment;this.currentRating=Math.min(this.maxRating,Math.ceil(this.currentRating*e)/e)},padColors:(e,t,n)=>Object.assign(new Array(t).fill(n),e)}},p=h,f=(n("2b2b"),u(p,function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["vue-star-rating",{"vue-star-rating-rtl":e.rtl},{"vue-star-rating-inline":e.inline}]},[n("div",{staticClass:"sr-only"},[e._t("screen-reader",[n("span",[e._v("Rated "+e._s(e.selectedRating)+" stars out of "+e._s(e.maxRating))])],{rating:e.selectedRating,stars:e.maxRating})],2),n("div",{staticClass:"vue-star-rating",on:{mouseleave:e.resetRating}},[e._l(e.maxRating,function(t){return n("span",{key:t,class:[{"vue-star-rating-pointer":!e.readOnly},"vue-star-rating-star"],style:{"margin-right":e.margin+"px"}},[n("star",{attrs:{fill:e.fillLevel[t-1],size:e.starSize,points:e.starPoints,"star-id":t,step:e.step,"active-color":e.currentActiveColor,"inactive-color":e.inactiveColor,"border-color":e.borderColor,"active-border-color":e.currentActiveBorderColor,"border-width":e.borderWidth,"rounded-corners":e.roundedCorners,rtl:e.rtl,glow:e.glow,"glow-color":e.glowColor,animate:e.animate},on:{"star-selected":function(t){return e.setRating(t,!0)},"star-mouse-move":e.setRating}})],1)}),e.showRating?n("span",{class:["vue-star-rating-rating-text",e.textClass]},[e._v(" "+e._s(e.formattedRating))]):e._e()],2)])},[],!1,null,"fde73a0c",null)),m=f.exports;t.default=m}})});var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.arrayIteratorImpl=function(e){var t=0;return function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}},$jscomp.arrayIterator=function(e){return{next:$jscomp.arrayIteratorImpl(e)}},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.ISOLATE_POLYFILLS=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,n){return e==Array.prototype||e==Object.prototype||(e[t]=n.value),e},$jscomp.getGlobal=function(e){e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var t=0;t<e.length;++t){var n=e[t];if(n&&n.Math==Math)return n}throw Error("Cannot find global object")},$jscomp.global=$jscomp.getGlobal(this),$jscomp.IS_SYMBOL_NATIVE="function"==typeof Symbol&&"symbol"==typeof Symbol("x"),$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE,$jscomp.polyfills={},$jscomp.propertyToPolyfillSymbol={},$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(e,t){var n=$jscomp.propertyToPolyfillSymbol[t];return null==n?e[t]:void 0!==(n=e[n])?n:e[t]};$jscomp.polyfill=function(e,t,n,i){t&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(e,t,n,i):$jscomp.polyfillUnisolated(e,t,n,i))},$jscomp.polyfillUnisolated=function(e,t,n,i){for(n=$jscomp.global,e=e.split("."),i=0;i<e.length-1;i++){var a=e[i];if(!(a in n))return;n=n[a]}(t=t(i=n[e=e[e.length-1]]))!=i&&null!=t&&$jscomp.defineProperty(n,e,{configurable:!0,writable:!0,value:t})},$jscomp.polyfillIsolated=function(e,t,n,i){var a=e.split(".");e=1===a.length,i=a[0],i=!e&&i in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var r=0;r<a.length-1;r++){var s=a[r];if(!(s in i))return;i=i[s]}a=a[a.length-1],null!=(t=t(n=$jscomp.IS_SYMBOL_NATIVE&&"es6"===n?i[a]:null))&&(e?$jscomp.defineProperty($jscomp.polyfills,a,{configurable:!0,writable:!0,value:t}):t!==n&&($jscomp.propertyToPolyfillSymbol[a]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(a):$jscomp.POLYFILL_PREFIX+a,a=$jscomp.propertyToPolyfillSymbol[a],$jscomp.defineProperty(i,a,{configurable:!0,writable:!0,value:t})))},$jscomp.initSymbol=function(){},$jscomp.polyfill("Symbol",function(e){if(e)return e;var t=function(e,t){this.$jscomp$symbol$id_=e,$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:t})};t.prototype.toString=function(){return this.$jscomp$symbol$id_};var n=0,i=function(e){if(this instanceof i)throw new TypeError("Symbol is not a constructor");return new t("jscomp_symbol_"+(e||"")+"_"+n++,e)};return i},"es6","es3"),$jscomp.initSymbolIterator=function(){},$jscomp.polyfill("Symbol.iterator",function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var t="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),n=0;n<t.length;n++){var i=$jscomp.global[t[n]];"function"==typeof i&&"function"!=typeof i.prototype[e]&&$jscomp.defineProperty(i.prototype,e,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return e},"es6","es3"),$jscomp.initSymbolAsyncIterator=function(){},$jscomp.iteratorPrototype=function(e){return(e={next:e})[Symbol.iterator]=function(){return this},e},function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t((e=e||self).VeeValidate={},e.Vue)}(this,function(e,t){function n(e,t,n,i){return new(n||(n=Promise))(function(a,r){function s(e){try{l(i.next(e))}catch(e){r(e)}}function o(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?a(e.value):new n(function(t){t(e.value)}).then(s,o)}l((i=i.apply(e,t||[])).next())})}function i(e,t){function n(n){return function(s){return function(n){if(i)throw new TypeError("Generator is already executing.");for(;o;)try{if(i=1,a&&(r=2&n[0]?a.return:n[0]?a.throw||((r=a.return)&&r.call(a),0):a.next)&&!(r=r.call(a,n[1])).done)return r;switch(a=0,r&&(n=[2&n[0],r.value]),n[0]){case 0:case 1:r=n;break;case 4:return o.label++,{value:n[1],done:!1};case 5:o.label++,a=n[1],n=[0];continue;case 7:n=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=0<r.length&&r[r.length-1])||6!==n[0]&&2!==n[0])){o=0;continue}if(3===n[0]&&(!r||n[1]>r[0]&&n[1]<r[3]))o.label=n[1];else if(6===n[0]&&o.label<r[1])o.label=r[1],r=n;else{if(!(r&&o.label<r[2])){r[2]&&o.ops.pop(),o.trys.pop();continue}o.label=r[2],o.ops.push(n)}}n=t.call(e,o)}catch(e){n=[6,e],a=0}finally{i=r=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,s])}}var i,a,r,s,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return s={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s}function a(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;e=Array(e);var i=0;for(t=0;t<n;t++)for(var a=arguments[t],r=0,s=a.length;r<s;r++,i++)e[i]=a[r];return e}function r(e){return null==e}function s(e,t){if(e instanceof RegExp&&t instanceof RegExp)return s(e.source,t.source)&&s(e.flags,t.flags);if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!s(e[n],t[n]))return!1;return!0}return q(e)&&q(t)?Object.keys(e).every(function(n){return s(e[n],t[n])})&&Object.keys(t).every(function(n){return s(e[n],t[n])}):e!=e&&t!=t||e===t}function o(e){return""!==e&&!r(e)}function l(e){return"function"==typeof e}function c(e){return l(e)&&!!e.__locatorRef}function u(e,t){var n=Array.isArray(e)?e:h(e);if(l(n.findIndex))return n.findIndex(t);for(var i=0;i<n.length;i++)if(t(n[i],i))return i;return-1}function d(e,t){return-1!==e.indexOf(t)}function h(e){if(l(Array.from))return Array.from(e);for(var t=[],n=e.length,i=0;i<n;i++)t.push(e[i]);return t}function p(e){return l(Object.values)?Object.values(e):Object.keys(e).map(function(t){return e[t]})}function f(e,t){return Object.keys(t).forEach(function(n){q(t[n])?(e[n]||(e[n]={}),f(e[n],t[n])):e[n]=t[n]}),e}function m(e){return e}function g(e,t,n){return void 0===t&&(t=0),void 0===n&&(n={cancelled:!1}),0===t?e:function(){for(var a=[],r=0;r<arguments.length;r++)a[r]=arguments[r];clearTimeout(i),i=setTimeout(function(){i=void 0,n.cancelled||e.apply(void 0,a)},t)};var i}function v(e,t){return e.replace(/{([^}]+)}/g,function(e,n){return n in t?t[n]:"{"+n+"}"})}function y(e){var t={};return Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),e?q(e)&&e._$$isNormalized?e:q(e)?Object.keys(e).reduce(function(t,n){var i=!0===e[n]?[]:Array.isArray(e[n])||q(e[n])?e[n]:[e[n]];return!1!==e[n]&&(t[n]=b(n,i)),t},t):"string"!=typeof e?(console.warn("[vee-validate] rules must be either a string or an object."),t):e.split("|").reduce(function(e,t){var n=[],i=t.split(":")[0];return d(t,":")&&(n=t.split(":").slice(1).join(":").split(",")),i?(e[i]=b(i,n),e):e},t):t}function b(e,t){var n=Q.getRuleDefinition(e);if(!n)return t;var i={};if(!n.params&&!Array.isArray(t))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(t)&&!n.params)return t;if(!n.params||n.params.length<t.length&&Array.isArray(t))var a,r=t.map(function(e,t){var i,r=null===(i=n.params)||void 0===i?void 0:i[t];return a=r||a,r||(r=a),r});else r=n.params;for(var s=0;s<r.length;s++){var o=r[s],l=o.default;Array.isArray(t)?s in t&&(l=t[s]):o.name in t?l=t[o.name]:1===r.length&&(l=t),o.isTarget&&(l=w(l,o.cast)),"string"==typeof l&&"@"===l[0]&&(l=w(l.slice(1),o.cast)),!c(l)&&o.cast&&(l=o.cast(l)),i[o.name]?(i[o.name]=Array.isArray(i[o.name])?i[o.name]:[i[o.name]],i[o.name].push(l)):i[o.name]=l}return i}function w(e,t){var n=function(n){return n=n[e],t?t(n):n};return n.__locatorRef=e,n}function _(e,t,a){var r,s,o,l,c,u;return void 0===a&&(a={}),n(this,void 0,void 0,function(){var n,d,h,p,f,m;return i(this,function(i){switch(i.label){case 0:return n=null===(r=a)||void 0===r?void 0:r.bails,d=null===(s=a)||void 0===s?void 0:s.skipIfEmpty,[4,k({name:(null===(o=a)||void 0===o?void 0:o.name)||"{field}",rules:y(t),bails:null==n||n,skipIfEmpty:null==d||d,forceRequired:!1,crossTable:(null===(l=a)||void 0===l?void 0:l.values)||{},names:(null===(c=a)||void 0===c?void 0:c.names)||{},customMessages:(null===(u=a)||void 0===u?void 0:u.customMessages)||{}},e,a)];case 1:return h=i.sent(),p=[],f={},m={},h.errors.forEach(function(e){var t=e.msg();p.push(t),f[e.rule]=t,m[e.rule]=e.msg}),[2,{valid:h.valid,errors:p,failedRules:f,regenerateMap:m}]}})})}function k(e,t,a){var r=void 0!==(a=(void 0===a?{}:a).isInitial)&&a;return n(this,void 0,void 0,function(){var n,a,s,o,l,c,u,d;return i(this,function(i){switch(i.label){case 0:return[4,S(e,t)];case 1:if(n=i.sent(),a=n.shouldSkip,s=n.errors,a)return[2,{valid:!s.length,errors:s}];o=Object.keys(e.rules).filter(function(e){return!Q.isRequireRule(e)}),l=o.length,c=0,i.label=2;case 2:return c<l?r&&Q.isLazy(o[c])?[3,4]:(u=o[c],[4,x(e,t,{name:u,params:e.rules[u]})]):[3,5];case 3:if(!(d=i.sent()).valid&&d.error&&(s.push(d.error),e.bails))return[2,{valid:!1,errors:s}];i.label=4;case 4:return c++,[3,2];case 5:return[2,{valid:!s.length,errors:s}]}})})}function S(e,t){return n(this,void 0,void 0,function(){var n,a,s,o,l,c,u,d,h;return i(this,function(i){switch(i.label){case 0:var p;n=Object.keys(e.rules).filter(Q.isRequireRule),a=n.length,s=[],(p=r(t)||""===t)||(p=Array.isArray(t)&&0===t.length),l=(o=p)&&e.skipIfEmpty,c=!1,u=0,i.label=1;case 1:return u<a?(d=n[u],[4,x(e,t,{name:d,params:e.rules[d]})]):[3,4];case 2:if(h=i.sent(),!q(h))throw Error("Require rules has to return an object (see docs)");if(h.required&&(c=!0),!h.valid&&h.error&&(s.push(h.error),e.bails))return[2,{shouldSkip:!0,errors:s}];i.label=3;case 3:return u++,[3,1];case 4:return o&&!c&&!e.skipIfEmpty||!e.bails&&!l?[2,{shouldSkip:!1,errors:s}]:[2,{shouldSkip:!c&&o,errors:s}]}})})}function x(e,t,a){return n(this,void 0,void 0,function(){var n,r,s,o,l;return i(this,function(i){switch(i.label){case 0:if(!(n=Q.getRuleDefinition(a.name))||!n.validate)throw Error("No such validator '"+a.name+"' exists.");return r=n.castValue?n.castValue(t):t,s=function(e,t){if(Array.isArray(e))return e;var n={};return Object.keys(e).forEach(function(i){var a=e[i];a=c(a)?a(t):a,n[i]=a}),n}(a.params,e.crossTable),[4,n.validate(r,s)];case 1:return"string"==typeof(o=i.sent())?(l=G(G({},s||{}),{_field_:e.name,_value_:t,_rule_:a.name}),[2,{valid:!1,error:{rule:a.name,msg:function(){return v(o,l)}}}]):(q(o)||(o={valid:o}),[2,{valid:o.valid,required:o.required,error:o.valid?void 0:C(e,t,n,a.name,s)}])}})})}function C(e,t,n,i,a){var r,s=null!=(r=e.customMessages[i])?r:n.message;r=function(e,t,n){if(t=t.params,!t||0>=t.filter(function(e){return e.isTarget}).length)return{};var i={},a=e.rules[n];for(!Array.isArray(a)&&q(a)&&(a=t.map(function(e){return a[e.name]})),n=0;n<t.length;n++){var r=t[n],s=a[n];c(s)&&(s=s.__locatorRef,i[r.name]=e.names[s]||s,i["_"+r.name+"_"]=e.crossTable[s])}return i}(e,n,i),n=function(e,t,n,i){var a={},r=e.rules[n],s=t.params||[];return r?(Object.keys(r).forEach(function(t,n){var i=r[t];if(!c(i))return{};var o=s[n];if(!o)return{};i=i.__locatorRef,a[o.name]=e.names[i]||i,a["_"+o.name+"_"]=e.crossTable[i]}),{userTargets:a,userMessage:i}):{}}(e,n,i,s),s=n.userTargets;var o=n.userMessage,l=G(G(G(G({},a||{}),{_field_:e.name,_value_:t,_rule_:i}),r),s);return{msg:function(){var t=o||J.defaultMessage,n=e.name;return t="function"==typeof t?t(n,l):v(t,G(G({},l),{_field_:n}))},rule:i}}function T(){ee.$emit("change:locale")}function M(e){if(e.data){var t=e.data;if("model"in t)return t.model;if(e.data.directives)return function(e,t){var n=Array.isArray(e)?e:h(e),i=u(n,t);return-1===i?void 0:n[i]}(e.data.directives,function(e){return"model"===e.name})}}function A(e){var t,n,i,a=M(e);return a?{value:a.value}:(a=(null===(t=O(e))||void 0===t?void 0:t.prop)||"value",null!==(n=e.componentOptions)&&void 0!==n&&n.propsData&&a in e.componentOptions.propsData?{value:e.componentOptions.propsData[a]}:null!==(i=e.data)&&void 0!==i&&i.domProps&&"value"in e.data.domProps?{value:e.data.domProps.value}:void 0)}function R(e){return Array.isArray(e)||void 0===A(e)?function(e){return Array.isArray(e)?e:Array.isArray(e.children)?e.children:e.componentOptions&&Array.isArray(e.componentOptions.children)?e.componentOptions.children:[]}(e).reduce(function(e,t){var n=R(t);return n.length&&e.push.apply(e,n),e},[]):[e]}function O(e){return e.componentOptions?e.componentOptions.Ctor.options.model:null}function L(e,t,n){r(e[t])?e[t]=[n]:l(e[t])&&e[t].fns?((e=e[t]).fns=Array.isArray(e.fns)?e.fns:[e.fns],d(e.fns,n)||e.fns.push(n)):(l(e[t])&&(e[t]=[e[t]]),Array.isArray(e[t])&&!d(e[t],n)&&e[t].push(n))}function I(e,t,n){e.componentOptions?e.componentOptions&&(e.componentOptions.listeners||(e.componentOptions.listeners={}),L(e.componentOptions.listeners,t,n)):(e.data||(e.data={}),r(e.data.on)&&(e.data.on={}),L(e.data.on,t,n))}function E(e,t){var n;return e.componentOptions?(O(e)||{event:"input"}).event:null!==(n=null==t?void 0:t.modifiers)&&void 0!==n&&n.lazy?"change":ne(e)?"input":"change"}function D(e,t){return e.$scopedSlots.default?e.$scopedSlots.default(t)||[]:e.$slots.default||[]}function Y(e){return G(G({},e.flags),{errors:e.errors,classes:e.classes,failedRules:e.failedRules,reset:function(){return e.reset()},validate:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.validate.apply(e,t)},ariaInput:{"aria-invalid":e.flags.invalid?"true":"false","aria-required":e.isRequired?"true":"false","aria-errormessage":"vee_"+e.id},ariaMsg:{id:"vee_"+e.id,"aria-live":e.errors.length?"assertive":"off"}})}function N(e,t){e.initialized||(e.initialValue=t);var n=!!(!e._ignoreImmediate&&e.immediate||e.value!==t&&e.normalizedEvents.length||e._needsValidation||!e.initialized&&void 0===t);if(e._needsValidation=!1,e.value=t,e._ignoreImmediate=!0,n){var i=function(){if(e.immediate||e.flags.validated)return P(e);e.validateSilent()};e.initialized?i():e.$once("hook:mounted",function(){return i()})}}function $(e){return(l(e.mode)?e.mode:K[e.mode])(e)}function P(e){var t=e.validateSilent();return e._pendingValidation=t,t.then(function(n){return t===e._pendingValidation&&(e.applyResult(n),e._pendingValidation=void 0),n})}function F(e){e.$veeOnInput||(e.$veeOnInput=function(t){e.syncValue(t),e.setFlags({dirty:!0,pristine:!1})});var t=e.$veeOnInput;e.$veeOnBlur||(e.$veeOnBlur=function(){e.setFlags({touched:!0,untouched:!1})});var n=e.$veeOnBlur,i=e.$veeHandler,a=$(e);return i&&e.$veeDebounce===e.debounce||(i=g(function(){e.$nextTick(function(){e._pendingReset||P(e),e._pendingReset=!1})},a.debounce||e.debounce),e.$veeHandler=i,e.$veeDebounce=e.debounce),{onInput:t,onBlur:n,onValidate:i}}function j(e){var t=e.$_veeObserver.refs;return e.fieldDeps.reduce(function(e,n){return t[n]?(e.values[n]=t[n].value,e.names[n]=t[n].name,e):e},{names:{},values:{}})}function U(e,t,n){void 0===n&&(n=!0);var i=e.$_veeObserver.refs;if(e._veeWatchers||(e._veeWatchers={}),!i[t]&&n)return e.$once("hook:mounted",function(){U(e,t,!1)});!l(e._veeWatchers[t])&&i[t]&&(e._veeWatchers[t]=i[t].$watch("value",function(){e.flags.validated&&(e._needsValidation=!0,e.validate())}))}function W(e){e.$_veeObserver&&e.$_veeObserver.unobserve(e.id,"observer")}function B(e){e.$_veeObserver&&e.$_veeObserver.observe(e,"observer")}function H(){return G(G({},{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1}),{valid:!0,invalid:!1})}function V(){for(var e=a(p(this.refs),this.observers),t={},n=H(),i={},r=e.length,s=0;s<r;s++){var o=e[s];Array.isArray(o.errors)?(t[o.id]=o.errors,i[o.id]=G({id:o.id,name:o.name,failedRules:o.failedRules},o.flags)):(t=G(G({},t),o.errors),i=G(G({},i),o.fields))}return re.forEach(function(t){var i=t[0];n[i]=e[t[1]](function(e){return e.flags[i]})}),{errors:t,flags:n,fields:i}}t=t&&t.hasOwnProperty("default")?t.default:t;var z,G=function(){return G=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},G.apply(this,arguments)},q=function(e){return null!==e&&e&&"object"==typeof e&&!Array.isArray(e)},Z={},Q=function(){function e(){}return e.extend=function(e,t){var n=function(e){var t;return null!==(t=e.params)&&void 0!==t&&t.length&&(e.params=e.params.map(function(e){return"string"==typeof e?{name:e}:e})),e}(t);Z[e]=Z[e]?f(Z[e],t):G({lazy:!1,computesRequired:!1},n)},e.isLazy=function(e){var t;return!(null===(t=Z[e])||void 0===t||!t.lazy)},e.isRequireRule=function(e){var t;return!(null===(t=Z[e])||void 0===t||!t.computesRequired)},e.getRuleDefinition=function(e){return Z[e]},e}(),J=G({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),X=function(e){J=G(G({},J),e)},K={aggressive:function(){return{on:["input","blur"]}},eager:function(e){return e.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},ee=new t,te=function(){function e(e,t){this.container={},this.locale=e,this.merge(t)}return e.prototype.resolve=function(e,t,n){return this.format(this.locale,e,t,n)},e.prototype.format=function(e,t,n,i){var a,r,s,o,c,u,d,h;return(n=(null===(s=null===(r=null===(a=this.container[e])||void 0===a?void 0:a.fields)||void 0===r?void 0:r[t])||void 0===s?void 0:s[n])||(null===(c=null===(o=this.container[e])||void 0===o?void 0:o.messages)||void 0===c?void 0:c[n]))||(n="{field} is not valid"),t=null!=(h=null===(d=null===(u=this.container[e])||void 0===u?void 0:u.names)||void 0===d?void 0:d[t])?h:t,l(n)?n(t,i):v(n,G(G({},i),{_field_:t}))},e.prototype.merge=function(e){f(this.container,e)},e.prototype.hasRule=function(e){var t,n;return!(null===(n=null===(t=this.container[this.locale])||void 0===t?void 0:t.messages)||void 0===n||!n[e])},e}(),ne=function(e){var t,n=(null===(t=e.data)||void 0===t?void 0:t.attrs)||e.elm;return!("input"!==e.tag||n&&n.type)||"textarea"===e.tag||d("text password search email tel url number".split(" "),null==n?void 0:n.type)},ie=0,ae=t.extend({inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver={refs:{},observe:function(e){this.refs[e.id]=e},unobserve:function(e){delete this.refs[e]}}),this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,default:""},name:{type:String,default:null},mode:{type:[String,Function],default:function(){return J.mode}},rules:{type:[Object,String],default:null},immediate:{type:Boolean,default:!1},bails:{type:Boolean,default:function(){return J.bails}},skipIfEmpty:{type:Boolean,default:function(){return J.skipOptional}},debounce:{type:Number,default:0},tag:{type:String,default:"span"},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},customMessages:{type:Object,default:function(){return{}}}},watch:{rules:{deep:!0,handler:function(e,t){this._needsValidation=!s(e,t)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1},failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var e=this;return Object.keys(this.normalizedRules).reduce(function(t,n){var i=function(e){return Array.isArray(e)?e.filter(c):Object.keys(e).filter(function(t){return c(e[t])}).map(function(t){return e[t]})}(e.normalizedRules[n]).map(function(e){return e.__locatorRef});return t.push.apply(t,i),i.forEach(function(t){U(e,t)}),t},[])},normalizedEvents:function(){var e=this;return($(this).on||[]).map(function(t){return"input"===t?e._inputEventName:t})},isRequired:function(){var e=G(G({},this._resolvedRules),this.normalizedRules);return e=Object.keys(e).some(Q.isRequireRule),this.flags.required=!!e,e},classes:function(){return function(e,t){for(var n={},i=Object.keys(t),a=i.length,s=function(a){a=i[a];var s=e&&e[a]||a,o=t[a];if(r(o)||("valid"===a||"invalid"===a)&&!t.validated)return"continue";"string"==typeof s?n[s]=o:Array.isArray(s)&&s.forEach(function(e){n[e]=o})},o=0;o<a;o++)s(o);return n}(J.classes,this.flags)},normalizedRules:function(){return y(this.rules)}},created:function(){var e=this,t=function(){if(e.flags.validated){var t=e._regenerateMap;if(t){var n=[],i={};Object.keys(t).forEach(function(e){var a=t[e]();n.push(a),i[e]=a}),e.applyResult({errors:n,failedRules:i,regenerateMap:t})}else e.validate()}};ee.$on("change:locale",t),this.$on("hook:beforeDestroy",function(){ee.$off("change:locale",t)})},render:function(e){var t=this;this.registerField();var n=Y(this);return R(n=D(this,n)).forEach(function(e){var n,i,a,r,l;if(J.useConstraintAttrs){var c,u=null===(c=e.data)||void 0===c?void 0:c.attrs;if(d(["input","select","textarea"],e.tag)&&u)if(c={},"required"in u&&!1!==u.required&&Q.getRuleDefinition("required")&&(c.required="checkbox"!==u.type||[!0]),ne(e)){u=G,c=G({},c);var h=null===(l=e.data)||void 0===l?void 0:l.attrs;l={},h&&("email"===h.type&&Q.getRuleDefinition("email")&&(l.email=["multiple"in h]),h.pattern&&Q.getRuleDefinition("regex")&&(l.regex=h.pattern),0<=h.maxlength&&Q.getRuleDefinition("max")&&(l.max=h.maxlength),0<=h.minlength&&Q.getRuleDefinition("min")&&(l.min=h.minlength),"number"===h.type&&(o(h.min)&&Q.getRuleDefinition("min_value")&&(l.min_value=Number(h.min)),o(h.max)&&Q.getRuleDefinition("max_value")&&(l.max_value=Number(h.max)))),l=y(u(c,l))}else l=y(c);else l={}}else l={};s(t._resolvedRules,l)||(t._needsValidation=!0),d(["input","select","textarea"],e.tag)&&(t.fieldName=(null===(i=null===(n=e.data)||void 0===n?void 0:n.attrs)||void 0===i?void 0:i.name)||(null===(r=null===(a=e.data)||void 0===a?void 0:a.attrs)||void 0===r?void 0:r.id)),t._resolvedRules=l,function(e,t){var n=A(t);e._inputEventName=e._inputEventName||E(t,M(t)),N(e,null==n?void 0:n.value);var i=(n=F(e)).onBlur,a=n.onValidate;I(t,e._inputEventName,n.onInput),I(t,"blur",i),e.normalizedEvents.forEach(function(e){I(t,e,a)}),e.initialized=!0}(t,e)}),this.slim&&1>=n.length?n[0]:e(this.tag,n)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(e){var t=this;Object.keys(e).forEach(function(n){t.flags[n]=e[n]})},syncValue:function(e){this.value=e=function(e){var t,n;return e&&("undefined"!=typeof Event&&l(Event)&&e instanceof Event||e&&e.srcElement)?"file"===(e=e.target).type&&e.files?h(e.files):null!==(t=e._vModifiers)&&void 0!==t&&t.number?(t=parseFloat(e.value))!=t?e.value:t:null!==(n=e._vModifiers)&&void 0!==n&&n.trim&&"string"==typeof e.value?e.value.trim():e.value:e}(e),this.flags.changed=this.initialValue!==e},reset:function(){var e=this;this.errors=[],this.initialValue=this.value;var t={untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1};t.required=this.isRequired,this.setFlags(t),this.failedRules={},this.validateSilent(),this._pendingValidation=void 0,this._pendingReset=!0,setTimeout(function(){e._pendingReset=!1},this.debounce)},validate:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(this,void 0,void 0,function(){return i(this,function(t){return 0<e.length&&this.syncValue(e[0]),[2,P(this)]})})},validateSilent:function(){return n(this,void 0,void 0,function(){var e,t;return i(this,function(n){switch(n.label){case 0:return this.setFlags({pending:!0}),e=G(G({},this._resolvedRules),this.normalizedRules),Object.defineProperty(e,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,_(this.value,e,G(G({name:this.name||this.fieldName},j(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return t=n.sent(),this.setFlags({pending:!1,valid:t.valid,invalid:!t.valid}),[2,t]}})})},setErrors:function(e){this.applyResult({errors:e,failedRules:{}})},applyResult:function(e){var t=e.errors,n=e.failedRules;e=e.regenerateMap,this.errors=t,this._regenerateMap=e,this.failedRules=G({},n||{}),this.setFlags({valid:!t.length,passed:!t.length,invalid:!!t.length,failed:!!t.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var e=function(e){return e.vid?e.vid:e.name?e.name:e.id?e.id:e.fieldName?e.fieldName:"_vee_"+ ++ie}(this),t=this.id;!this.isActive||t===e&&this.$_veeObserver.refs[t]||(t!==e&&this.$_veeObserver.refs[t]===this&&this.$_veeObserver.unobserve(t),this.id=e,this.$_veeObserver.observe(this))}}}),re=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],se=0,oe=t.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,default:"span"},vid:{type:String,default:function(){return"obs_"+se++}},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:H(),fields:{}}},created:function(){var e=this;this.id=this.vid,B(this);var t=g(function(t){var n=t.flags,i=t.fields;e.errors=t.errors,e.flags=n,e.fields=i},16);this.$watch(V,t)},activated:function(){B(this)},deactivated:function(){W(this)},beforeDestroy:function(){W(this)},render:function(e){var t=D(this,G(G({},this.flags),{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=t.length?t[0]:e(this.tag,{on:this.$listeners},t)},methods:{observe:function(e,t){var n;void 0===t&&(t="provider"),"observer"===t?this.observers.push(e):this.refs=G(G({},this.refs),((n={})[e.id]=e,n))},unobserve:function(e,t){if(void 0===t&&(t="provider"),"provider"===t)this.refs[e]&&this.$delete(this.refs,e);else{var n=u(this.observers,function(t){return t.id===e});-1!==n&&this.observers.splice(n,1)}},validate:function(e){var t=void 0!==(e=(void 0===e?{}:e).silent)&&e;return n(this,void 0,void 0,function(){return i(this,function(e){switch(e.label){case 0:return[4,Promise.all(a(p(this.refs).filter(function(e){return!e.disabled}).map(function(e){return e[t?"validateSilent":"validate"]().then(function(e){return e.valid})}),this.observers.filter(function(e){return!e.disabled}).map(function(e){return e.validate({silent:t})})))];case 1:return[2,e.sent().every(function(e){return e})]}})})},handleSubmit:function(e){return n(this,void 0,void 0,function(){return i(this,function(t){switch(t.label){case 0:return[4,this.validate()];case 1:return t.sent()&&e?[2,e()]:[2]}})})},reset:function(){return a(p(this.refs),this.observers).forEach(function(e){return e.reset()})},setErrors:function(e){var t=this;Object.keys(e).forEach(function(n){var i=t.refs[n];i&&(n="string"==typeof(n=e[n]||[])?[n]:n,i.setErrors(n))}),this.observers.forEach(function(t){t.setErrors(e)})}}});e.ValidationObserver=oe,e.ValidationProvider=ae,e.configure=function(e){X(e)},e.extend=function(e,t){if(!l(t)&&!l(t.validate)&&!Q.getRuleDefinition(e))throw Error("Extension Error: The validator '"+e+"' must be a function or have a 'validate' method.");"object"==typeof t?Q.extend(e,t):Q.extend(e,{validate:t})},e.localeChanged=T,e.localize=function(e,t){var n;z||(z=new te("en",{}),X({defaultMessage:function(e,t){return z.resolve(e,null==t?void 0:t._rule_,t||{})}})),"string"==typeof e?(z.locale=e,t&&z.merge(((n={})[e]=t,n)),T()):z.merge(e)},e.normalizeRules=y,e.setInteractionMode=function(e,t){if(X({mode:e}),t){if(!l(t))throw Error("A mode implementation must be a function");K[e]=t}},e.validate=_,e.version="3.2.3",e.withValidation=function(e,t){void 0===t&&(t=m);var n,i="options"in e?e.options:e,a=ae.options;a={name:(i.name||"AnonymousHoc")+"WithValidation",props:G({},a.props),data:a.data,computed:G({},a.computed),methods:G({},a.methods),beforeDestroy:a.beforeDestroy,inject:a.inject};var r=(null===(n=null==i?void 0:i.model)||void 0===n?void 0:n.event)||"input";return a.render=function(e){var n;this.registerField();var a=Y(this),s=G({},this.$listeners),o=M(this.$vnode);this._inputEventName=this._inputEventName||E(this.$vnode,o);var l=A(this.$vnode);N(this,null==l?void 0:l.value);var c=(l=F(this)).onBlur,u=l.onValidate;return L(s,r,l.onInput),L(s,"blur",c),this.normalizedEvents.forEach(function(e){L(s,e,u)}),l=(O(this.$vnode)||{prop:"value"}).prop,a=G(G(G({},this.$attrs),((n={})[l]=null==o?void 0:o.value,n)),t(a)),e(i,{attrs:this.$attrs,props:a,on:s},function(e,t){return Object.keys(e).reduce(function(n,i){return e[i].forEach(function(n){n.context||(e[i].context=t,n.data||(n.data={}),n.data.slot=i)}),n.concat(e[i])},[])}(this.$slots,this.$vnode.context))},a},Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).VeeValidateRules={})}(this,function(e){"use strict";var t={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},n={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},i={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},a={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},r=function(e,n){var i=(void 0===n?{}:n).locale,a=void 0===i?"":i;return Array.isArray(e)?e.every(function(e){return r(e,{locale:a})}):a?(t[a]||t.en).test(e):Object.keys(t).some(function(n){return t[n].test(e)})},s={validate:r,params:[{name:"locale"}]},o=function(e,t){var n=(void 0===t?{}:t).locale,i=void 0===n?"":n;return Array.isArray(e)?e.every(function(e){return o(e,{locale:i})}):i?(a[i]||a.en).test(e):Object.keys(a).some(function(t){return a[t].test(e)})},l={validate:o,params:[{name:"locale"}]},c=function(e,t){var n=(void 0===t?{}:t).locale,a=void 0===n?"":n;return Array.isArray(e)?e.every(function(e){return c(e,{locale:a})}):a?(i[a]||i.en).test(e):Object.keys(i).some(function(t){return i[t].test(e)})},u={validate:c,params:[{name:"locale"}]},d=function(e,t){var i=(void 0===t?{}:t).locale,a=void 0===i?"":i;return Array.isArray(e)?e.every(function(e){return d(e,{locale:a})}):a?(n[a]||n.en).test(e):Object.keys(n).some(function(t){return n[t].test(e)})},h={validate:d,params:[{name:"locale"}]},p=function(e,t){var n=void 0===t?{}:t,i=n.min,a=n.max;return Array.isArray(e)?e.every(function(e){return!!p(e,{min:i,max:a})}):Number(i)<=e&&Number(a)>=e},f={validate:p,params:[{name:"min"},{name:"max"}]},m={validate:function(e,t){var n=t.target;return String(e)===String(n)},params:[{name:"target",isTarget:!0}]},g=function(e,t){var n=t.length;if(Array.isArray(e))return e.every(function(e){return g(e,{length:n})});var i=String(e);return/^[0-9]*$/.test(i)&&i.length===n},v={validate:g,params:[{name:"length",cast:function(e){return Number(e)}}]},y={validate:function(e,t){var n=t.width,i=t.height,a=[];e=Array.isArray(e)?e:[e];for(var r=0;r<e.length;r++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(e[r].name))return Promise.resolve(!1);a.push(e[r])}return Promise.all(a.map(function(e){return t=e,a=n,r=i,s=window.URL||window.webkitURL,new Promise(function(e){var n=new Image;n.onerror=function(){return e(!1)},n.onload=function(){return e(n.width===a&&n.height===r)},n.src=s.createObjectURL(t)});var t,a,r,s})).then(function(e){return e.every(function(e){return e})})},params:[{name:"width",cast:function(e){return Number(e)}},{name:"height",cast:function(e){return Number(e)}}]},b={validate:function(e,t){var n=(void 0===t?{}:t).multiple,i=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return n&&!Array.isArray(e)&&(e=String(e).split(",").map(function(e){return e.trim()})),Array.isArray(e)?e.every(function(e){return i.test(String(e))}):i.test(String(e))},params:[{name:"multiple",default:!1}]};function w(e){return null==e}function _(e){return Array.isArray(e)&&0===e.length}function k(e){return"function"==typeof Array.from?Array.from(e):function(e){for(var t=[],n=e.length,i=0;i<n;i++)t.push(e[i]);return t}(e)}function S(e){return _(e)||-1!==[!1,null,void 0].indexOf(e)||!String(e).trim().length}var x=function(e,t){return Array.isArray(e)?e.every(function(e){return x(e,t)}):k(t).some(function(t){return t==e})},C={validate:x},T={validate:function(e,t){return!x(e,t)}},M={validate:function(e,t){var n=new RegExp(".("+t.join("|")+")$","i");return Array.isArray(e)?e.every(function(e){return n.test(e.name)}):n.test(e.name)}},A={validate:function(e){var t=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(e)?e.every(function(e){return t.test(e.name)}):t.test(e.name)}},R={validate:function(e){return Array.isArray(e)?e.every(function(e){return/^-?[0-9]+$/.test(String(e))}):/^-?[0-9]+$/.test(String(e))}},O={validate:function(e,t){var n=t.length;return!w(e)&&("number"==typeof e&&(e=String(e)),e.length||(e=k(e)),e.length===n)},params:[{name:"length",cast:function(e){return Number(e)}}]},L=function(e,t){var n=t.length;return w(e)?0<=n:Array.isArray(e)?e.every(function(e){return L(e,{length:n})}):String(e).length<=n},I={validate:L,params:[{name:"length",cast:function(e){return Number(e)}}]},E=function(e,t){var n=t.max;return!w(e)&&""!==e&&(Array.isArray(e)?0<e.length&&e.every(function(e){return E(e,{max:n})}):Number(e)<=n)},D={validate:E,params:[{name:"max",cast:function(e){return Number(e)}}]},Y={validate:function(e,t){var n=new RegExp(t.join("|").replace("*",".+")+"$","i");return Array.isArray(e)?e.every(function(e){return n.test(e.type)}):n.test(e.type)}},N=function(e,t){var n=t.length;return!w(e)&&(Array.isArray(e)?e.every(function(e){return N(e,{length:n})}):String(e).length>=n)},$={validate:N,params:[{name:"length",cast:function(e){return Number(e)}}]},P=function(e,t){var n=t.min;return!w(e)&&""!==e&&(Array.isArray(e)?0<e.length&&e.every(function(e){return P(e,{min:n})}):Number(e)>=n)},F={validate:P,params:[{name:"min",cast:function(e){return Number(e)}}]},j=/^[٠١٢٣٤٥٦٧٨٩]+$/,U=/^[0-9]+$/,W={validate:function(e){function t(e){var t=String(e);return U.test(t)||j.test(t)}return Array.isArray(e)?e.every(t):t(e)}},B=function(e,t){var n=t.regex;return Array.isArray(e)?e.every(function(e){return B(e,{regex:n})}):n.test(String(e))},H={validate:B,params:[{name:"regex",cast:function(e){return"string"==typeof e?new RegExp(e):e}}]},V={validate:function(e,t){var n=(void 0===t?{allowFalse:!0}:t).allowFalse,i={valid:!1,required:!0};return w(e)||_(e)||!1===e&&!n||(i.valid=!!String(e).trim().length),i},params:[{name:"allowFalse",default:!0}],computesRequired:!0},z={validate:function(e,t){var n,i=t.target,a=t.values;return(n=a&&a.length?(Array.isArray(a)||"string"!=typeof a||(a=[a]),a.some(function(e){return e==String(i).trim()})):!S(i))?{valid:!S(e),required:n}:{valid:!0,required:n}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},G={validate:function(e,t){var n=t.size;if(isNaN(n))return!1;var i=1024*n;if(!Array.isArray(e))return e.size<=i;for(var a=0;a<e.length;a++)if(e[a].size>i)return!1;return!0},params:[{name:"size",cast:function(e){return Number(e)}}]};e.alpha=s,e.alpha_dash=l,e.alpha_num=u,e.alpha_spaces=h,e.between=f,e.confirmed=m,e.digits=v,e.dimensions=y,e.email=b,e.excluded=T,e.ext=M,e.image=A,e.integer=R,e.is={validate:function(e,t){return e===t.other},params:[{name:"other"}]},e.is_not={validate:function(e,t){return e!==t.other},params:[{name:"other"}]},e.length=O,e.max=I,e.max_value=D,e.mimes=Y,e.min=$,e.min_value=F,e.numeric=W,e.oneOf=C,e.regex=H,e.required=V,e.required_if=z,e.size=G,Object.defineProperty(e,"__esModule",{value:!0})});var tns=function(){var e=window,t=e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame||e.msRequestAnimationFrame||function(e){return setTimeout(e,16)},n=window,i=n.cancelAnimationFrame||n.mozCancelAnimationFrame||function(e){clearTimeout(e)};function a(){for(var e,t,n,i=arguments[0]||{},a=1,r=arguments.length;a<r;a++)if(null!==(e=arguments[a]))for(t in e)i!==(n=e[t])&&void 0!==n&&(i[t]=n);return i}function r(e){return 0<=["true","false"].indexOf(e)?JSON.parse(e):e}function s(e,t,n,i){if(i)try{e.setItem(t,n)}catch(e){}return n}function o(){var e=document,t=e.body;return t||((t=e.createElement("body")).fake=!0),t}var l=document.documentElement;function c(e){var t="";return e.fake&&(t=l.style.overflow,e.style.background="",e.style.overflow=l.style.overflow="hidden",l.appendChild(e)),t}function u(e,t){e.fake&&(e.remove(),l.style.overflow=t,l.offsetHeight)}function d(e,t,n,i){"insertRule"in e?e.insertRule(t+"{"+n+"}",i):e.addRule(t,n,i)}function h(e){return("insertRule"in e?e.cssRules:e.rules).length}function p(e,t,n){for(var i=0,a=e.length;i<a;i++)t.call(n,e[i],i)}var f="classList"in document.createElement("_"),m=f?function(e,t){return e.classList.contains(t)}:function(e,t){return 0<=e.className.indexOf(t)},g=f?function(e,t){m(e,t)||e.classList.add(t)}:function(e,t){m(e,t)||(e.className+=" "+t)},v=f?function(e,t){m(e,t)&&e.classList.remove(t)}:function(e,t){m(e,t)&&(e.className=e.className.replace(t,""))};function y(e,t){return e.hasAttribute(t)}function b(e,t){return e.getAttribute(t)}function w(e){return void 0!==e.item}function _(e,t){if(e=w(e)||e instanceof Array?e:[e],"[object Object]"===Object.prototype.toString.call(t))for(var n=e.length;n--;)for(var i in t)e[n].setAttribute(i,t[i])}function k(e,t){e=w(e)||e instanceof Array?e:[e];for(var n=(t=t instanceof Array?t:[t]).length,i=e.length;i--;)for(var a=n;a--;)e[i].removeAttribute(t[a])}function S(e){for(var t=[],n=0,i=e.length;n<i;n++)t.push(e[n]);return t}function x(e,t){"none"!==e.style.display&&(e.style.display="none")}function C(e,t){"none"===e.style.display&&(e.style.display="")}function T(e){return"none"!==window.getComputedStyle(e).display}function M(e){if("string"==typeof e){var t=[e],n=e.charAt(0).toUpperCase()+e.substr(1);["Webkit","Moz","ms","O"].forEach(function(i){"ms"===i&&"transform"!==e||t.push(i+n)}),e=t}for(var i=document.createElement("fakeelement"),a=(e.length,0);a<e.length;a++){var r=e[a];if(void 0!==i.style[r])return r}return!1}function A(e,t){var n=!1;return/^Webkit/.test(e)?n="webkit"+t+"End":/^O/.test(e)?n="o"+t+"End":e&&(n=t.toLowerCase()+"end"),n}var R=!1;try{var O=Object.defineProperty({},"passive",{get:function(){R=!0}});window.addEventListener("test",null,O)}catch(e){}var L=!!R&&{passive:!0};function I(e,t,n){for(var i in t){var a=0<=["touchstart","touchmove"].indexOf(i)&&!n&&L;e.addEventListener(i,t[i],a)}}function E(e,t){for(var n in t){var i=0<=["touchstart","touchmove"].indexOf(n)&&L;e.removeEventListener(n,t[n],i)}}function D(){return{topics:{},on:function(e,t){this.topics[e]=this.topics[e]||[],this.topics[e].push(t)},off:function(e,t){if(this.topics[e])for(var n=0;n<this.topics[e].length;n++)if(this.topics[e][n]===t){this.topics[e].splice(n,1);break}},emit:function(e,t){t.type=e,this.topics[e]&&this.topics[e].forEach(function(n){n(t,e)})}}}Object.keys||(Object.keys=function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t}),"remove"in Element.prototype||(Element.prototype.remove=function(){this.parentNode&&this.parentNode.removeChild(this)});var Y=function(e){e=a({container:".slider",mode:"carousel",axis:"horizontal",items:1,gutter:0,edgePadding:0,fixedWidth:!1,autoWidth:!1,viewportMax:!1,slideBy:1,center:!1,controls:!0,controlsPosition:"top",controlsText:["prev","next"],controlsContainer:!1,prevButton:!1,nextButton:!1,nav:!0,navPosition:"top",navContainer:!1,navAsThumbnails:!1,arrowKeys:!1,speed:300,autoplay:!1,autoplayPosition:"top",autoplayTimeout:5e3,autoplayDirection:"forward",autoplayText:["start","stop"],autoplayHoverPause:!1,autoplayButton:!1,autoplayButtonOutput:!0,autoplayResetOnVisibility:!0,animateIn:"tns-fadeIn",animateOut:"tns-fadeOut",animateNormal:"tns-normal",animateDelay:!1,loop:!0,rewind:!1,autoHeight:!1,responsive:!1,lazyload:!1,lazyloadSelector:".tns-lazy-img",touch:!0,mouseDrag:!1,swipeAngle:15,nested:!1,preventActionWhenRunning:!1,preventScrollOnTouch:!1,freezable:!0,onInit:!1,useLocalStorage:!0,nonce:!1},e||{});var n=document,l=window,f={ENTER:13,SPACE:32,LEFT:37,RIGHT:39},w={},R=e.useLocalStorage;if(R){var O=navigator.userAgent,L=new Date;try{(w=l.localStorage)?(w.setItem(L,L),R=w.getItem(L)==L,w.removeItem(L)):R=!1,R||(w={})}catch(O){R=!1}R&&(w.tnsApp&&w.tnsApp!==O&&["tC","tPL","tMQ","tTf","t3D","tTDu","tTDe","tADu","tADe","tTE","tAE"].forEach(function(e){w.removeItem(e)}),localStorage.tnsApp=O)}var N=w.tC?r(w.tC):s(w,"tC",function(){var e=document,t=o(),n=c(t),i=e.createElement("div"),a=!1;t.appendChild(i);try{for(var r,s="(10px * 10)",l=["calc"+s,"-moz-calc"+s,"-webkit-calc"+s],d=0;d<3;d++)if(r=l[d],i.style.width=r,100===i.offsetWidth){a=r.replace(s,"");break}}catch(e){}return t.fake?u(t,n):i.remove(),a}(),R),$=w.tPL?r(w.tPL):s(w,"tPL",function(){var e,t=document,n=o(),i=c(n),a=t.createElement("div"),r=t.createElement("div"),s="";a.className="tns-t-subp2",r.className="tns-t-ct";for(var l=0;l<70;l++)s+="<div></div>";return r.innerHTML=s,a.appendChild(r),n.appendChild(a),e=Math.abs(a.getBoundingClientRect().left-r.children[67].getBoundingClientRect().left)<2,n.fake?u(n,i):a.remove(),e}(),R),P=w.tMQ?r(w.tMQ):s(w,"tMQ",function(){if(window.matchMedia||window.msMatchMedia)return!0;var e,t=document,n=o(),i=c(n),a=t.createElement("div"),r=t.createElement("style"),s="@media all and (min-width:1px){.tns-mq-test{position:absolute}}";return r.type="text/css",a.className="tns-mq-test",n.appendChild(r),n.appendChild(a),r.styleSheet?r.styleSheet.cssText=s:r.appendChild(t.createTextNode(s)),e=window.getComputedStyle?window.getComputedStyle(a).position:a.currentStyle.position,n.fake?u(n,i):a.remove(),"absolute"===e}(),R),F=w.tTf?r(w.tTf):s(w,"tTf",M("transform"),R),j=w.t3D?r(w.t3D):s(w,"t3D",function(e){if(!e)return!1;if(!window.getComputedStyle)return!1;var t,n=document,i=o(),a=c(i),r=n.createElement("p"),s=9<e.length?"-"+e.slice(0,-9).toLowerCase()+"-":"";return s+="transform",i.insertBefore(r,null),r.style[e]="translate3d(1px,1px,1px)",t=window.getComputedStyle(r).getPropertyValue(s),i.fake?u(i,a):r.remove(),void 0!==t&&0<t.length&&"none"!==t}(F),R),U=w.tTDu?r(w.tTDu):s(w,"tTDu",M("transitionDuration"),R),W=w.tTDe?r(w.tTDe):s(w,"tTDe",M("transitionDelay"),R),B=w.tADu?r(w.tADu):s(w,"tADu",M("animationDuration"),R),H=w.tADe?r(w.tADe):s(w,"tADe",M("animationDelay"),R),V=w.tTE?r(w.tTE):s(w,"tTE",A(U,"Transition"),R),z=w.tAE?r(w.tAE):s(w,"tAE",A(B,"Animation"),R),G=l.console&&"function"==typeof l.console.warn,q=["container","controlsContainer","prevButton","nextButton","navContainer","autoplayButton"],Z={};if(q.forEach(function(t){if("string"==typeof e[t]){var i=e[t],a=n.querySelector(i);if(Z[t]=i,!a||!a.nodeName)return void(G&&console.warn("Can't find",e[t]));e[t]=a}}),!(e.container.children.length<1)){var Q=e.responsive,J=e.nested,X="carousel"===e.mode;if(Q){0 in Q&&(e=a(e,Q[0]),delete Q[0]);var K={};for(var ee in Q){var te=Q[ee];te="number"==typeof te?{items:te}:te,K[ee]=te}Q=K,K=null}if(X||function e(t){for(var n in t)X||("slideBy"===n&&(t[n]="page"),"edgePadding"===n&&(t[n]=!1),"autoHeight"===n&&(t[n]=!1)),"responsive"===n&&e(t[n])}(e),!X){e.axis="horizontal",e.slideBy="page",e.edgePadding=!1;var ne=e.animateIn,ie=e.animateOut,ae=e.animateDelay,re=e.animateNormal}var se,oe,le="horizontal"===e.axis,ce=n.createElement("div"),ue=n.createElement("div"),de=e.container,he=de.parentNode,pe=de.outerHTML,fe=de.children,me=fe.length,ge=In(),ve=!1;Q&&Xn(),X&&(de.className+=" tns-vpfix");var ye,be,we,_e,ke,Se,xe,Ce,Te=e.autoWidth,Me=Nn("fixedWidth"),Ae=Nn("edgePadding"),Re=Nn("gutter"),Oe=Dn(),Le=Nn("center"),Ie=Te?1:Math.floor(Nn("items")),Ee=Nn("slideBy"),De=e.viewportMax||e.fixedWidthViewportWidth,Ye=Nn("arrowKeys"),Ne=Nn("speed"),$e=e.rewind,Pe=!$e&&e.loop,Fe=Nn("autoHeight"),je=Nn("controls"),Ue=Nn("controlsText"),We=Nn("nav"),Be=Nn("touch"),He=Nn("mouseDrag"),Ve=Nn("autoplay"),ze=Nn("autoplayTimeout"),Ge=Nn("autoplayText"),qe=Nn("autoplayHoverPause"),Ze=Nn("autoplayResetOnVisibility"),Qe=(xe=Nn("nonce"),Ce=document.createElement("style"),xe&&Ce.setAttribute("nonce",xe),document.querySelector("head").appendChild(Ce),Ce.sheet?Ce.sheet:Ce.styleSheet),Je=e.lazyload,Xe=e.lazyloadSelector,Ke=[],et=Pe?(ke=function(){if(Te||Me&&!De)return me-1;var t=Me?"fixedWidth":"items",n=[];if((Me||e[t]<me)&&n.push(e[t]),Q)for(var i in Q){var a=Q[i][t];a&&(Me||a<me)&&n.push(a)}return n.length||n.push(0),Math.ceil(Me?De/Math.min.apply(null,n):Math.max.apply(null,n))}(),Se=X?Math.ceil((5*ke-me)/2):4*ke-me,Se=Math.max(ke,Se),Yn("edgePadding")?Se+1:Se):0,tt=X?me+2*et:me+et,nt=!(!Me&&!Te||Pe),it=Me?xi():null,at=!X||!Pe,rt=le?"left":"top",st="",ot="",lt=Me?function(){return Le&&!Pe?me-1:Math.ceil(-it/(Me+Re))}:Te?function(){for(var e=0;e<tt;e++)if(ye[e]>=-it)return e}:function(){return Le&&X&&!Pe?me-1:Pe||X?Math.max(0,tt-Math.ceil(Ie)):tt-1},ct=Rn(Nn("startIndex")),ut=ct,dt=(An(),0),ht=Te?null:lt(),pt=e.preventActionWhenRunning,ft=e.swipeAngle,mt=!ft||"?",gt=!1,vt=e.onInit,yt=new D,bt=" tns-slider tns-"+e.mode,wt=de.id||(_e=window.tnsId,window.tnsId=_e?_e+1:1,"tns"+window.tnsId),_t=Nn("disable"),kt=!1,St=e.freezable,xt=!(!St||Te)&&Jn(),Ct=!1,Tt={click:Ei,keydown:function(e){e=Ui(e);var t=[f.LEFT,f.RIGHT].indexOf(e.keyCode);0<=t&&(0===t?qt.disabled||Ei(e,-1):Zt.disabled||Ei(e,1))}},Mt={click:function(e){if(gt){if(pt)return;Li()}for(var t=Wi(e=Ui(e));t!==Kt&&!y(t,"data-nav");)t=t.parentNode;if(y(t,"data-nav")){var n=an=Number(b(t,"data-nav")),i=Me||Te?n*me/tn:n*Ie;Ii(Yt?n:Math.min(Math.ceil(i),me-1),e),rn===n&&(dn&&Pi(),an=-1)}},keydown:function(e){e=Ui(e);var t=n.activeElement;if(y(t,"data-nav")){var i=[f.LEFT,f.RIGHT,f.ENTER,f.SPACE].indexOf(e.keyCode),a=Number(b(t,"data-nav"));0<=i&&(0===i?0<a&&ji(Xt[a-1]):1===i?a<tn-1&&ji(Xt[a+1]):Ii(an=a,e))}}},At={mouseover:function(){dn&&(Yi(),hn=!0)},mouseout:function(){hn&&(Di(),hn=!1)}},Rt={visibilitychange:function(){n.hidden?dn&&(Yi(),fn=!0):fn&&(Di(),fn=!1)}},Ot={keydown:function(e){e=Ui(e);var t=[f.LEFT,f.RIGHT].indexOf(e.keyCode);0<=t&&Ei(e,0===t?-1:1)}},Lt={touchstart:zi,touchmove:Gi,touchend:qi,touchcancel:qi},It={mousedown:zi,mousemove:Gi,mouseup:qi,mouseleave:qi},Et=Yn("controls"),Dt=Yn("nav"),Yt=!!Te||e.navAsThumbnails,Nt=Yn("autoplay"),$t=Yn("touch"),Pt=Yn("mouseDrag"),Ft="tns-slide-active",jt="tns-slide-cloned",Ut="tns-complete",Wt={load:function(e){oi(Wi(e))},error:function(e){var t;t=Wi(e),g(t,"failed"),li(t)}},Bt="force"===e.preventScrollOnTouch;if(Et)var Ht,Vt,zt=e.controlsContainer,Gt=e.controlsContainer?e.controlsContainer.outerHTML:"",qt=e.prevButton,Zt=e.nextButton,Qt=e.prevButton?e.prevButton.outerHTML:"",Jt=e.nextButton?e.nextButton.outerHTML:"";if(Dt)var Xt,Kt=e.navContainer,en=e.navContainer?e.navContainer.outerHTML:"",tn=Te?me:Qi(),nn=0,an=-1,rn=Ln(),sn=rn,on="tns-nav-active",ln="Carousel Page ",cn=" (Current Slide)";if(Nt)var un,dn,hn,pn,fn,mn="forward"===e.autoplayDirection?1:-1,gn=e.autoplayButton,vn=e.autoplayButton?e.autoplayButton.outerHTML:"",yn=["<span class='tns-visually-hidden'>"," animation</span>"];if($t||Pt)var bn,wn,_n={},kn={},Sn=!1,xn=le?function(e,t){return e.x-t.x}:function(e,t){return e.y-t.y};Te||Mn(_t||xt),F&&(rt=F,st="translate",j?(st+=le?"3d(":"3d(0px, ",ot=le?", 0px, 0px)":", 0px)"):(st+=le?"X(":"Y(",ot=")")),X&&(de.className=de.className.replace("tns-vpfix","")),function(){(Yn("gutter"),ce.className="tns-outer",ue.className="tns-inner",ce.id=wt+"-ow",ue.id=wt+"-iw",""===de.id&&(de.id=wt),bt+=$||Te?" tns-subpixel":" tns-no-subpixel",bt+=N?" tns-calc":" tns-no-calc",Te&&(bt+=" tns-autowidth"),bt+=" tns-"+e.axis,de.className+=bt,X?((se=n.createElement("div")).id=wt+"-mw",se.className="tns-ovh",ce.appendChild(se),se.appendChild(ue)):ce.appendChild(ue),Fe)&&((se||ue).className+=" tns-ah");if(he.insertBefore(ce,de),ue.appendChild(de),p(fe,function(e,t){g(e,"tns-item"),e.id||(e.id=wt+"-item"+t),!X&&re&&g(e,re),_(e,{"aria-hidden":"true",tabindex:"-1"})}),et){for(var t=n.createDocumentFragment(),i=n.createDocumentFragment(),a=et;a--;){var r=a%me,s=fe[r].cloneNode(!0);if(g(s,jt),k(s,"id"),i.insertBefore(s,i.firstChild),X){var o=fe[me-1-r].cloneNode(!0);g(o,jt),k(o,"id"),t.appendChild(o)}}de.insertBefore(t,de.firstChild),de.appendChild(i),fe=de.children}}(),function(){if(!X)for(var t=ct,n=ct+Math.min(me,Ie);t<n;t++){var i=fe[t];i.style.left=100*(t-ct)/Ie+"%",g(i,ne),v(i,re)}if(le&&($||Te?(d(Qe,"#"+wt+" > .tns-item","font-size:"+l.getComputedStyle(fe[0]).fontSize+";",h(Qe)),d(Qe,"#"+wt,"font-size:0;",h(Qe))):X&&p(fe,function(e,t){var n;e.style.marginLeft=(n=t,N?N+"("+100*n+"% / "+tt+")":100*n/tt+"%")})),P){if(U){var a=se&&e.autoHeight?Wn(e.speed):"";d(Qe,"#"+wt+"-mw",a,h(Qe))}a=$n(e.edgePadding,e.gutter,e.fixedWidth,e.speed,e.autoHeight),d(Qe,"#"+wt+"-iw",a,h(Qe)),X&&(a=le&&!Te?"width:"+Pn(e.fixedWidth,e.gutter,e.items)+";":"",U&&(a+=Wn(Ne)),d(Qe,"#"+wt,a,h(Qe))),a=le&&!Te?Fn(e.fixedWidth,e.gutter,e.items):"",e.gutter&&(a+=jn(e.gutter)),X||(U&&(a+=Wn(Ne)),B&&(a+=Bn(Ne))),a&&d(Qe,"#"+wt+" > .tns-item",a,h(Qe))}else{X&&Fe&&(se.style[U]=Ne/1e3+"s"),ue.style.cssText=$n(Ae,Re,Me,Fe),X&&le&&!Te&&(de.style.width=Pn(Me,Re,Ie));a=le&&!Te?Fn(Me,Re,Ie):"";Re&&(a+=jn(Re)),a&&d(Qe,"#"+wt+" > .tns-item",a,h(Qe))}if(Q&&P)for(var r in Q){r=parseInt(r);var s=Q[r],o=(a="",""),c="",u="",f="",m=Te?null:Nn("items",r),y=Nn("fixedWidth",r),b=Nn("speed",r),w=Nn("edgePadding",r),_=Nn("autoHeight",r),k=Nn("gutter",r);U&&se&&Nn("autoHeight",r)&&"speed"in s&&(o="#"+wt+"-mw{"+Wn(b)+"}"),("edgePadding"in s||"gutter"in s)&&(c="#"+wt+"-iw{"+$n(w,k,y,b,_)+"}"),X&&le&&!Te&&("fixedWidth"in s||"items"in s||Me&&"gutter"in s)&&(u="width:"+Pn(y,k,m)+";"),U&&"speed"in s&&(u+=Wn(b)),u&&(u="#"+wt+"{"+u+"}"),("fixedWidth"in s||Me&&"gutter"in s||!X&&"items"in s)&&(f+=Fn(y,k,m)),"gutter"in s&&(f+=jn(k)),!X&&"speed"in s&&(U&&(f+=Wn(b)),B&&(f+=Bn(b))),f&&(f="#"+wt+" > .tns-item{"+f+"}"),(a=o+c+u+f)&&Qe.insertRule("@media (min-width: "+r/16+"em) {"+a+"}",Qe.cssRules.length)}}(),Hn();var Cn=Pe?X?function(){var e=dt,t=ht;e+=Ee,t-=Ee,Ae?(e+=1,t-=1):Me&&(Oe+Re)%(Me+Re)&&(t-=1),et&&(t<ct?ct-=me:ct<e&&(ct+=me))}:function(){if(ht<ct)for(;dt+me<=ct;)ct-=me;else if(ct<dt)for(;ct<=ht-me;)ct+=me}:function(){ct=Math.max(dt,Math.min(ht,ct))},Tn=X?function(){var e,t,n,i,a,r,s,o,l,c,u;ki(de,""),U||!Ne?(Mi(),Ne&&T(de)||Li()):(e=de,t=rt,n=st,i=ot,a=Ci(),r=Ne,s=Li,o=Math.min(r,10),l=0<=a.indexOf("%")?"%":"px",a=a.replace(l,""),c=Number(e.style[t].replace(n,"").replace(i,"").replace(l,"")),u=(a-c)/r*o,setTimeout(function a(){r-=o,c+=u,e.style[t]=n+c+l+i,0<r?setTimeout(a,o):s()},o)),le||Zi()}:function(){Ke=[];var e={};e[V]=e[z]=Li,E(fe[ut],e),I(fe[ct],e),Ai(ut,ne,ie,!0),Ai(ct,re,ne),V&&z&&Ne&&T(de)||Li()};return{version:"2.9.3",getInfo:Xi,events:yt,goTo:Ii,play:function(){Ve&&!dn&&($i(),pn=!1)},pause:function(){dn&&(Pi(),pn=!0)},isOn:ve,updateSliderHeight:fi,refresh:Hn,destroy:function(){if(Qe.disabled=!0,Qe.ownerNode&&Qe.ownerNode.remove(),E(l,{resize:Zn}),Ye&&E(n,Ot),zt&&E(zt,Tt),Kt&&E(Kt,Mt),E(de,At),E(de,Rt),gn&&E(gn,{click:Fi}),Ve&&clearInterval(un),X&&V){var t={};t[V]=Li,E(de,t)}Be&&E(de,Lt),He&&E(de,It);var i=[pe,Gt,Qt,Jt,en,vn];for(var a in q.forEach(function(t,n){var a="container"===t?ce:e[t];if("object"==typeof a&&a){var r=!!a.previousElementSibling&&a.previousElementSibling,s=a.parentNode;a.outerHTML=i[n],e[t]=r?r.nextElementSibling:s.firstElementChild}}),q=ne=ie=ae=re=le=ce=ue=de=he=pe=fe=me=oe=ge=Te=Me=Ae=Re=Oe=Ie=Ee=De=Ye=Ne=$e=Pe=Fe=Qe=Je=ye=Ke=et=tt=nt=it=at=rt=st=ot=lt=ct=ut=dt=ht=ft=mt=gt=vt=yt=bt=wt=_t=kt=St=xt=Ct=Tt=Mt=At=Rt=Ot=Lt=It=Et=Dt=Yt=Nt=$t=Pt=Ft=Ut=Wt=be=je=Ue=zt=Gt=qt=Zt=Ht=Vt=We=Kt=en=Xt=tn=nn=an=rn=sn=on=ln=cn=Ve=ze=mn=Ge=qe=gn=vn=Ze=yn=un=dn=hn=pn=fn=_n=kn=bn=Sn=wn=xn=Be=He=null,this)"rebuild"!==a&&(this[a]=null);ve=!1},rebuild:function(){return Y(a(e,Z))}}}function Mn(e){e&&(je=We=Be=He=Ye=Ve=qe=Ze=!1)}function An(){for(var e=X?ct-et:ct;e<0;)e+=me;return e%me+1}function Rn(e){return e=e?Math.max(0,Math.min(Pe?me-1:me-Ie,e)):0,X?e+et:e}function On(e){for(null==e&&(e=ct),X&&(e-=et);e<0;)e+=me;return Math.floor(e%me)}function Ln(){var e,t=On();return e=Yt?t:Me||Te?Math.ceil((t+1)*tn/me-1):Math.floor(t/Ie),!Pe&&X&&ct===ht&&(e=tn-1),e}function In(){return l.innerWidth||n.documentElement.clientWidth||n.body.clientWidth}function En(e){return"top"===e?"afterbegin":"beforeend"}function Dn(){var e=Ae?2*Ae-Re:0;return function e(t){if(null!=t){var i,a,r=n.createElement("div");return t.appendChild(r),a=(i=r.getBoundingClientRect()).right-i.left,r.remove(),a||e(t.parentNode)}}(he)-e}function Yn(t){if(e[t])return!0;if(Q)for(var n in Q)if(Q[n][t])return!0;return!1}function Nn(t,n){if(null==n&&(n=ge),"items"===t&&Me)return Math.floor((Oe+Re)/(Me+Re))||1;var i=e[t];if(Q)for(var a in Q)n>=parseInt(a)&&t in Q[a]&&(i=Q[a][t]);return"slideBy"===t&&"page"===i&&(i=Nn("items")),X||"slideBy"!==t&&"items"!==t||(i=Math.floor(i)),i}function $n(e,t,n,i,a){var r="";if(void 0!==e){var s=e;t&&(s-=t),r=le?"margin: 0 "+s+"px 0 "+e+"px;":"margin: "+e+"px 0 "+s+"px 0;"}else if(t&&!n){var o="-"+t+"px";r="margin: 0 "+(le?o+" 0 0":"0 "+o+" 0")+";"}return!X&&a&&U&&i&&(r+=Wn(i)),r}function Pn(e,t,n){return e?(e+t)*tt+"px":N?N+"("+100*tt+"% / "+n+")":100*tt/n+"%"}function Fn(e,t,n){var i;if(e)i=e+t+"px";else{X||(n=Math.floor(n));var a=X?tt:n;i=N?N+"(100% / "+a+")":100/a+"%"}return i="width:"+i,"inner"!==J?i+";":i+" !important;"}function jn(e){var t="";return!1!==e&&(t=(le?"padding-":"margin-")+(le?"right":"bottom")+": "+e+"px;"),t}function Un(e,t){var n=e.substring(0,e.length-t).toLowerCase();return n&&(n="-"+n+"-"),n}function Wn(e){return Un(U,18)+"transition-duration:"+e/1e3+"s;"}function Bn(e){return Un(B,17)+"animation-duration:"+e/1e3+"s;"}function Hn(){if(Yn("autoHeight")||Te||!le){var e=de.querySelectorAll("img");p(e,function(e){var t=e.src;Je||(t&&t.indexOf("data:image")<0?(e.src="",I(e,Wt),g(e,"loading"),e.src=t):oi(e))}),t(function(){di(S(e),function(){be=!0})}),Yn("autoHeight")&&(e=ci(ct,Math.min(ct+Ie-1,tt-1))),Je?Vn():t(function(){di(S(e),Vn)})}else X&&Ti(),Gn(),qn()}function Vn(){if(Te&&1<me){var e=Pe?ct:me-1;!function t(){var n=fe[e].getBoundingClientRect().left,i=fe[e-1].getBoundingClientRect().right;Math.abs(n-i)<=1?zn():setTimeout(function(){t()},16)}()}else zn()}function zn(){le&&!Te||(mi(),Te?(it=xi(),St&&(xt=Jn()),ht=lt(),Mn(_t||xt)):Zi()),X&&Ti(),Gn(),qn()}function Gn(){if(gi(),ce.insertAdjacentHTML("afterbegin",'<div class="tns-liveregion tns-visually-hidden" aria-live="polite" aria-atomic="true">slide <span class="current">'+ai()+"</span>  of "+me+"</div>"),we=ce.querySelector(".tns-liveregion .current"),Nt){var t=Ve?"stop":"start";gn?_(gn,{"data-action":t}):e.autoplayButtonOutput&&(ce.insertAdjacentHTML(En(e.autoplayPosition),'<button type="button" data-action="'+t+'">'+yn[0]+t+yn[1]+Ge[0]+"</button>"),gn=ce.querySelector("[data-action]")),gn&&I(gn,{click:Fi}),Ve&&($i(),qe&&I(de,At),Ze&&I(de,Rt))}if(Dt){if(Kt)_(Kt,{"aria-label":"Carousel Pagination"}),p(Xt=Kt.children,function(e,t){_(e,{"data-nav":t,tabindex:"-1","aria-label":ln+(t+1),"aria-controls":wt})});else{for(var n="",i=Yt?"":'style="display:none"',a=0;a<me;a++)n+='<button type="button" data-nav="'+a+'" tabindex="-1" aria-controls="'+wt+'" '+i+' aria-label="'+ln+(a+1)+'"></button>';n='<div class="tns-nav" aria-label="Carousel Pagination">'+n+"</div>",ce.insertAdjacentHTML(En(e.navPosition),n),Kt=ce.querySelector(".tns-nav"),Xt=Kt.children}if(Ji(),U){var r=U.substring(0,U.length-18).toLowerCase(),s="transition: all "+Ne/1e3+"s";r&&(s="-"+r+"-"+s),d(Qe,"[aria-controls^="+wt+"-item]",s,h(Qe))}_(Xt[rn],{"aria-label":ln+(rn+1)+cn}),k(Xt[rn],"tabindex"),g(Xt[rn],on),I(Kt,Mt)}Et&&(zt||qt&&Zt||(ce.insertAdjacentHTML(En(e.controlsPosition),'<div class="tns-controls" aria-label="Carousel Navigation" tabindex="0"><button type="button" data-controls="prev" tabindex="-1" aria-controls="'+wt+'">'+Ue[0]+'</button><button type="button" data-controls="next" tabindex="-1" aria-controls="'+wt+'">'+Ue[1]+"</button></div>"),zt=ce.querySelector(".tns-controls")),qt&&Zt||(qt=zt.children[0],Zt=zt.children[1]),e.controlsContainer&&_(zt,{"aria-label":"Carousel Navigation",tabindex:"0"}),(e.controlsContainer||e.prevButton&&e.nextButton)&&_([qt,Zt],{"aria-controls":wt,tabindex:"-1"}),(e.controlsContainer||e.prevButton&&e.nextButton)&&(_(qt,{"data-controls":"prev"}),_(Zt,{"data-controls":"next"})),Ht=yi(qt),Vt=yi(Zt),_i(),zt?I(zt,Tt):(I(qt,Tt),I(Zt,Tt))),Kn()}function qn(){if(X&&V){var t={};t[V]=Li,I(de,t)}Be&&I(de,Lt,e.preventScrollOnTouch),He&&I(de,It),Ye&&I(n,Ot),"inner"===J?yt.on("outerResized",function(){Qn(),yt.emit("innerLoaded",Xi())}):(Q||Me||Te||Fe||!le)&&I(l,{resize:Zn}),Fe&&("outer"===J?yt.on("innerLoaded",ui):_t||ui()),si(),_t?ni():xt&&ti(),yt.on("indexChanged",hi),"inner"===J&&yt.emit("innerLoaded",Xi()),"function"==typeof vt&&vt(Xi()),ve=!0}function Zn(e){t(function(){Qn(Ui(e))})}function Qn(t){if(ve){"outer"===J&&yt.emit("outerResized",Xi(t)),ge=In();var i,a=oe,r=!1;Q&&(Xn(),(i=a!==oe)&&yt.emit("newBreakpointStart",Xi(t)));var s,o,l,c,u=Ie,f=_t,m=xt,y=Ye,b=je,w=We,_=Be,k=He,S=Ve,T=qe,M=Ze,A=ct;if(i){var R=Me,O=Fe,L=Ue,D=Le,Y=Ge;if(!P)var N=Re,$=Ae}if(Ye=Nn("arrowKeys"),je=Nn("controls"),We=Nn("nav"),Be=Nn("touch"),Le=Nn("center"),He=Nn("mouseDrag"),Ve=Nn("autoplay"),qe=Nn("autoplayHoverPause"),Ze=Nn("autoplayResetOnVisibility"),i&&(_t=Nn("disable"),Me=Nn("fixedWidth"),Ne=Nn("speed"),Fe=Nn("autoHeight"),Ue=Nn("controlsText"),Ge=Nn("autoplayText"),ze=Nn("autoplayTimeout"),P||(Ae=Nn("edgePadding"),Re=Nn("gutter"))),Mn(_t),Oe=Dn(),le&&!Te||_t||(mi(),le||(Zi(),r=!0)),(Me||Te)&&(it=xi(),ht=lt()),(i||Me)&&(Ie=Nn("items"),Ee=Nn("slideBy"),(o=Ie!==u)&&(Me||Te||(ht=lt()),Cn())),i&&_t!==f&&(_t?ni():function(){if(kt){if(Qe.disabled=!1,de.className+=bt,Ti(),Pe)for(var e=et;e--;)X&&C(fe[e]),C(fe[tt-e-1]);if(!X)for(var t=ct,n=ct+me;t<n;t++){var i=fe[t],a=t<ct+Ie?ne:re;i.style.left=100*(t-ct)/Ie+"%",g(i,a)}ei(),kt=!1}}()),St&&(i||Me||Te)&&(xt=Jn())!==m&&(xt?(Mi(Ci(Rn(0))),ti()):(function(){if(Ct){if(Ae&&P&&(ue.style.margin=""),et)for(var e="tns-transparent",t=et;t--;)X&&v(fe[t],e),v(fe[tt-t-1],e);ei(),Ct=!1}}(),r=!0)),Mn(_t||xt),Ve||(qe=Ze=!1),Ye!==y&&(Ye?I(n,Ot):E(n,Ot)),je!==b&&(je?zt?C(zt):(qt&&C(qt),Zt&&C(Zt)):zt?x(zt):(qt&&x(qt),Zt&&x(Zt))),We!==w&&(We?(C(Kt),Ji()):x(Kt)),Be!==_&&(Be?I(de,Lt,e.preventScrollOnTouch):E(de,Lt)),He!==k&&(He?I(de,It):E(de,It)),Ve!==S&&(Ve?(gn&&C(gn),dn||pn||$i()):(gn&&x(gn),dn&&Pi())),qe!==T&&(qe?I(de,At):E(de,At)),Ze!==M&&(Ze?I(n,Rt):E(n,Rt)),i){if(Me===R&&Le===D||(r=!0),Fe!==O&&(Fe||(ue.style.height="")),je&&Ue!==L&&(qt.innerHTML=Ue[0],Zt.innerHTML=Ue[1]),gn&&Ge!==Y){var F=Ve?1:0,j=gn.innerHTML,U=j.length-Y[F].length;j.substring(U)===Y[F]&&(gn.innerHTML=j.substring(0,U)+Ge[F])}}else Le&&(Me||Te)&&(r=!0);if((o||Me&&!Te)&&(tn=Qi(),Ji()),(s=ct!==A)?(yt.emit("indexChanged",Xi()),r=!0):o?s||hi():(Me||Te)&&(si(),gi(),ii()),o&&!X&&function(){for(var e=ct+Math.min(me,Ie),t=tt;t--;){var n=fe[t];ct<=t&&t<e?(g(n,"tns-moving"),n.style.left=100*(t-ct)/Ie+"%",g(n,ne),v(n,re)):n.style.left&&(n.style.left="",g(n,re),v(n,ne)),v(n,ie)}setTimeout(function(){p(fe,function(e){v(e,"tns-moving")})},300)}(),!_t&&!xt){if(i&&!P&&(Ae===$&&Re===N||(ue.style.cssText=$n(Ae,Re,Me,Ne,Fe)),le)){X&&(de.style.width=Pn(Me,Re,Ie));var W=Fn(Me,Re,Ie)+jn(Re);c=h(l=Qe)-1,"deleteRule"in l?l.deleteRule(c):l.removeRule(c),d(Qe,"#"+wt+" > .tns-item",W,h(Qe))}Fe&&ui(),r&&(Ti(),ut=ct)}i&&yt.emit("newBreakpointEnd",Xi(t))}}function Jn(){if(!Me&&!Te)return me<=(Le?Ie-(Ie-1)/2:Ie);var e=Me?(Me+Re)*me:ye[me],t=Ae?Oe+2*Ae:Oe+Re;return Le&&(t-=Me?(Oe-Me)/2:(Oe-(ye[ct+1]-ye[ct]-Re))/2),e<=t}function Xn(){for(var e in oe=0,Q)(e=parseInt(e))<=ge&&(oe=e)}function Kn(){!Ve&&gn&&x(gn),!We&&Kt&&x(Kt),je||(zt?x(zt):(qt&&x(qt),Zt&&x(Zt)))}function ei(){Ve&&gn&&C(gn),We&&Kt&&C(Kt),je&&(zt?C(zt):(qt&&C(qt),Zt&&C(Zt)))}function ti(){if(!Ct){if(Ae&&(ue.style.margin="0px"),et)for(var e="tns-transparent",t=et;t--;)X&&g(fe[t],e),g(fe[tt-t-1],e);Kn(),Ct=!0}}function ni(){if(!kt){if(Qe.disabled=!0,de.className=de.className.replace(bt.substring(1),""),k(de,["style"]),Pe)for(var e=et;e--;)X&&x(fe[e]),x(fe[tt-e-1]);if(le&&X||k(ue,["style"]),!X)for(var t=ct,n=ct+me;t<n;t++){var i=fe[t];k(i,["style"]),v(i,ne),v(i,re)}Kn(),kt=!0}}function ii(){var e=ai();we.innerHTML!==e&&(we.innerHTML=e)}function ai(){var e=ri(),t=e[0]+1,n=e[1]+1;return t===n?t+"":t+" to "+n}function ri(e){null==e&&(e=Ci());var t,n,i,a=ct;if(Le||Ae?(Te||Me)&&(n=-(parseFloat(e)+Ae),i=n+Oe+2*Ae):Te&&(n=ye[ct],i=n+Oe),Te)ye.forEach(function(e,r){r<tt&&((Le||Ae)&&e<=n+.5&&(a=r),.5<=i-e&&(t=r))});else{if(Me){var r=Me+Re;Le||Ae?(a=Math.floor(n/r),t=Math.ceil(i/r-1)):t=a+Math.ceil(Oe/r)-1}else if(Le||Ae){var s=Ie-1;if(Le?(a-=s/2,t=ct+s/2):t=ct+s,Ae){var o=Ae*Ie/Oe;a-=o,t+=o}a=Math.floor(a),t=Math.ceil(t)}else t=a+Ie-1;a=Math.max(a,0),t=Math.min(t,tt-1)}return[a,t]}function si(){if(Je&&!_t){var e=ri();e.push(Xe),ci.apply(null,e).forEach(function(e){if(!m(e,Ut)){var t={};t[V]=function(e){e.stopPropagation()},I(e,t),I(e,Wt),e.src=b(e,"data-src");var n=b(e,"data-srcset");n&&(e.srcset=n),g(e,"loading")}})}}function oi(e){g(e,"loaded"),li(e)}function li(e){g(e,Ut),v(e,"loading"),E(e,Wt)}function ci(e,t,n){var i=[];for(n||(n="img");e<=t;)p(fe[e].querySelectorAll(n),function(e){i.push(e)}),e++;return i}function ui(){var e=ci.apply(null,ri());t(function(){di(e,fi)})}function di(e,n){return be?n():(e.forEach(function(t,n){!Je&&t.complete&&li(t),m(t,Ut)&&e.splice(n,1)}),e.length?void t(function(){di(e,n)}):n())}function hi(){si(),gi(),ii(),_i(),function(){if(We&&(rn=0<=an?an:Ln(),an=-1,rn!==sn)){var e=Xt[sn],t=Xt[rn];_(e,{tabindex:"-1","aria-label":ln+(sn+1)}),v(e,on),_(t,{"aria-label":ln+(rn+1)+cn}),k(t,"tabindex"),g(t,on),sn=rn}}()}function pi(e,t){for(var n=[],i=e,a=Math.min(e+t,tt);i<a;i++)n.push(fe[i].offsetHeight);return Math.max.apply(null,n)}function fi(){var e=Fe?pi(ct,Ie):pi(et,me),t=se||ue;t.style.height!==e&&(t.style.height=e+"px")}function mi(){ye=[0];var e=le?"left":"top",t=le?"right":"bottom",n=fe[0].getBoundingClientRect()[e];p(fe,function(i,a){a&&ye.push(i.getBoundingClientRect()[e]-n),a===tt-1&&ye.push(i.getBoundingClientRect()[t]-n)})}function gi(){var e=ri(),t=e[0],n=e[1];p(fe,function(e,i){t<=i&&i<=n?y(e,"aria-hidden")&&(k(e,["aria-hidden","tabindex"]),g(e,Ft)):y(e,"aria-hidden")||(_(e,{"aria-hidden":"true",tabindex:"-1"}),v(e,Ft))})}function vi(e){return e.nodeName.toLowerCase()}function yi(e){return"button"===vi(e)}function bi(e){return"true"===e.getAttribute("aria-disabled")}function wi(e,t,n){e?t.disabled=n:t.setAttribute("aria-disabled",n.toString())}function _i(){if(je&&!$e&&!Pe){var e=Ht?qt.disabled:bi(qt),t=Vt?Zt.disabled:bi(Zt),n=ct<=dt,i=!$e&&ht<=ct;n&&!e&&wi(Ht,qt,!0),!n&&e&&wi(Ht,qt,!1),i&&!t&&wi(Vt,Zt,!0),!i&&t&&wi(Vt,Zt,!1)}}function ki(e,t){U&&(e.style[U]=t)}function Si(e){return null==e&&(e=ct),Te?(Oe-(Ae?Re:0)-(ye[e+1]-ye[e]-Re))/2:Me?(Oe-Me)/2:(Ie-1)/2}function xi(){var e=Oe+(Ae?Re:0)-(Me?(Me+Re)*tt:ye[tt]);return Le&&!Pe&&(e=Me?-(Me+Re)*(tt-1)-Si():Si(tt-1)-ye[tt-1]),0<e&&(e=0),e}function Ci(e){var t;if(null==e&&(e=ct),le&&!Te)if(Me)t=-(Me+Re)*e,Le&&(t+=Si());else{var n=F?tt:Ie;Le&&(e-=Si()),t=100*-e/n}else t=-ye[e],Le&&Te&&(t+=Si());return nt&&(t=Math.max(t,it)),t+(!le||Te||Me?"px":"%")}function Ti(e){ki(de,"0s"),Mi(e)}function Mi(e){null==e&&(e=Ci()),de.style[rt]=st+e+ot}function Ai(e,t,n,i){var a=e+Ie;Pe||(a=Math.min(a,tt));for(var r=e;r<a;r++){var s=fe[r];i||(s.style.left=100*(r-ct)/Ie+"%"),ae&&W&&(s.style[W]=s.style[H]=ae*(r-e)/1e3+"s"),v(s,t),g(s,n),i&&Ke.push(s)}}function Ri(e,t){at&&Cn(),(ct!==ut||t)&&(yt.emit("indexChanged",Xi()),yt.emit("transitionStart",Xi()),Fe&&ui(),dn&&e&&0<=["click","keydown"].indexOf(e.type)&&Pi(),gt=!0,Tn())}function Oi(e){return e.toLowerCase().replace(/-/g,"")}function Li(e){if(X||gt){if(yt.emit("transitionEnd",Xi(e)),!X&&0<Ke.length)for(var t=0;t<Ke.length;t++){var n=Ke[t];n.style.left="",H&&W&&(n.style[H]="",n.style[W]=""),v(n,ie),g(n,re)}if(!e||!X&&e.target.parentNode===de||e.target===de&&Oi(e.propertyName)===Oi(rt)){if(!at){var i=ct;Cn(),ct!==i&&(yt.emit("indexChanged",Xi()),Ti())}"inner"===J&&yt.emit("innerLoaded",Xi()),gt=!1,ut=ct}}}function Ii(e,t){if(!xt)if("prev"===e)Ei(t,-1);else if("next"===e)Ei(t,1);else{if(gt){if(pt)return;Li()}var n=On(),i=0;if("first"===e?i=-n:"last"===e?i=X?me-Ie-n:me-1-n:("number"!=typeof e&&(e=parseInt(e)),isNaN(e)||(t||(e=Math.max(0,Math.min(me-1,e))),i=e-n)),!X&&i&&Math.abs(i)<Ie){var a=0<i?1:-1;i+=dt<=ct+i-me?me*a:2*me*a*-1}ct+=i,X&&Pe&&(ct<dt&&(ct+=me),ht<ct&&(ct-=me)),On(ct)!==On(ut)&&Ri(t)}}function Ei(e,t){if(gt){if(pt)return;Li()}var n;if(!t){for(var i=Wi(e=Ui(e));i!==zt&&[qt,Zt].indexOf(i)<0;)i=i.parentNode;var a=[qt,Zt].indexOf(i);0<=a&&(n=!0,t=0===a?-1:1)}if($e){if(ct===dt&&-1===t)return void Ii("last",e);if(ct===ht&&1===t)return void Ii("first",e)}t&&(ct+=Ee*t,Te&&(ct=Math.floor(ct)),Ri(n||e&&"keydown"===e.type?e:null))}function Di(){un=setInterval(function(){Ei(null,mn)},ze),dn=!0}function Yi(){clearInterval(un),dn=!1}function Ni(e,t){_(gn,{"data-action":e}),gn.innerHTML=yn[0]+e+yn[1]+t}function $i(){Di(),gn&&Ni("stop",Ge[1])}function Pi(){Yi(),gn&&Ni("start",Ge[0])}function Fi(){dn?(Pi(),pn=!0):($i(),pn=!1)}function ji(e){e.focus()}function Ui(e){return Bi(e=e||l.event)?e.changedTouches[0]:e}function Wi(e){return e.target||l.event.srcElement}function Bi(e){return 0<=e.type.indexOf("touch")}function Hi(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Vi(){return r=kn.y-_n.y,s=kn.x-_n.x,t=Math.atan2(r,s)*(180/Math.PI),i=!1,90-(n=ft)<=(a=Math.abs(90-Math.abs(t)))?i="horizontal":a<=n&&(i="vertical"),i===e.axis;var t,n,i,a,r,s}function zi(e){if(gt){if(pt)return;Li()}Ve&&dn&&Yi(),Sn=!0,wn&&(i(wn),wn=null);var t=Ui(e);yt.emit(Bi(e)?"touchStart":"dragStart",Xi(e)),!Bi(e)&&0<=["img","a"].indexOf(vi(Wi(e)))&&Hi(e),kn.x=_n.x=t.clientX,kn.y=_n.y=t.clientY,X&&(bn=parseFloat(de.style[rt].replace(st,"")),ki(de,"0s"))}function Gi(e){if(Sn){var n=Ui(e);kn.x=n.clientX,kn.y=n.clientY,X?wn||(wn=t(function(){!function e(n){if(mt){if(i(wn),Sn&&(wn=t(function(){e(n)})),"?"===mt&&(mt=Vi()),mt){!Bt&&Bi(n)&&(Bt=!0);try{n.type&&yt.emit(Bi(n)?"touchMove":"dragMove",Xi(n))}catch(e){}var a=bn,r=xn(kn,_n);if(!le||Me||Te)a+=r,a+="px";else a+=F?r*Ie*100/((Oe+Re)*tt):100*r/(Oe+Re),a+="%";de.style[rt]=st+a+ot}}else Sn=!1}(e)})):("?"===mt&&(mt=Vi()),mt&&(Bt=!0)),("boolean"!=typeof e.cancelable||e.cancelable)&&Bt&&e.preventDefault()}}function qi(n){if(Sn){wn&&(i(wn),wn=null),X&&ki(de,""),Sn=!1;var a=Ui(n);kn.x=a.clientX,kn.y=a.clientY;var r=xn(kn,_n);if(Math.abs(r)){if(!Bi(n)){var s=Wi(n);I(s,{click:function e(t){Hi(t),E(s,{click:e})}})}X?wn=t(function(){if(le&&!Te){var e=-r*Ie/(Oe+Re);e=0<r?Math.floor(e):Math.ceil(e),ct+=e}else{var t=-(bn+r);if(t<=0)ct=dt;else if(t>=ye[tt-1])ct=ht;else for(var i=0;i<tt&&t>=ye[i];)t>ye[ct=i]&&r<0&&(ct+=1),i++}Ri(n,r),yt.emit(Bi(n)?"touchEnd":"dragEnd",Xi(n))}):mt&&Ei(n,0<r?-1:1)}}"auto"===e.preventScrollOnTouch&&(Bt=!1),ft&&(mt="?"),Ve&&!dn&&Di()}function Zi(){(se||ue).style.height=ye[ct+Ie]-ye[ct]+"px"}function Qi(){var e=Me?(Me+Re)*me/Oe:me/Ie;return Math.min(Math.ceil(e),me)}function Ji(){if(We&&!Yt&&tn!==nn){var e=nn,t=tn,n=C;for(tn<nn&&(e=tn,t=nn,n=x);e<t;)n(Xt[e]),e++;nn=tn}}function Xi(e){return{container:de,slideItems:fe,navContainer:Kt,navItems:Xt,controlsContainer:zt,hasControls:Et,prevButton:qt,nextButton:Zt,items:Ie,slideBy:Ee,cloneCount:et,slideCount:me,slideCountNew:tt,index:ct,indexCached:ut,displayIndex:An(),navCurrentIndex:rn,navCurrentIndexCached:sn,pages:tn,pagesCached:nn,sheet:Qe,isOn:ve,event:e||{}}}G&&console.warn("No slides found in",e.container)};return Y}();Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null},hasLHSMenu:{type:Boolean,required:!1,default:!0}},template:'\n        <div :class="[header.isMenuOpen ? \'menuOpen\' : \'\']">\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid" :class="[!hasLHSMenu ? \'noLHSMenu\' : \'\']">\n                <yuno-header-v2 \n                    @userInfo="onUserInfo" \n                    @isMini="onMini" \n                    v-if="loginStatus && hasPageHeader"\n                >\n                </yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(e){this.$emit("onUserInfo",e)},onMini(e){this.isMiniSidebar=e}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(e,t){e!==t&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(e,t){"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(e){this.updateActiveOrg(e.id)},orgUpdated(e){const t=e?.response?.data;201===t?.code?(sessionStorage.clear(),window.location.reload(!0)):t?.message&&console.log(t.message)},updateActiveOrg(e){this.$buefy.loading.open();const t={apiURL:YUNOCommon.config.academy("activeOrg",!1),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:e},callback:!0,callbackFunc:e=>this.orgUpdated(e)};this.dispatchData("postData",t)},dispatchData(e,t){this.$store.dispatch(e,t)},init(){if("org-admin"===this.userInfo.data.role){const e=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=e}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e}}}),Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()})},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const e=sessionStorage.getItem("isLHSMenu");null===e||this.isMobile?this.reduce=!1:(this.reduce="true"===e,this.sidebarToggle(!0))},sidebarToggle(e){e||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout(()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)},n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},extractSlugFromURL(e){const t=e.replace(/\/$/,"").split("/");""===t[t.length-1]&&t.pop();return t[t.length-1]},manageCurrentPage(e){const t=e=>e.replace(/\/$/,""),n=t(window.location.origin+window.location.pathname);e.forEach(e=>{e.items.forEach(e=>{e.is_active=n===t(e.url);let i=!1;e.sub_items.forEach(a=>{a.is_active=n===t(a.url),a.is_active&&a.parent_id===e.id&&(i=!0)}),e.is_expended=!!i})})},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},gotPostLoginMenu(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";e?t=e.response.data.data:(t=this.header.data,this.header.success=!0),this.manageCurrentPage(t),this.header.data=t,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(e){const t={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,i={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPostLoginMenu(e)}};this.$store.dispatch("fetchData",i)},manageOrgAdmin(e){const{host:t}=YUNOCommon.config,{has_org:n,org_id:i}=e;null===sessionStorage.getItem("activeOrg")&&(n?i.length>1?(window.location.href=`${t()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(i[0].id)):window.location.href=`${t()}/create-organization-account`)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";if(e?t=e.response.data.data:(t=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(t.role),this.userRole.data=t.role,this.userProfile.data=t,this.userProfile.success=!0,t.role,"Learner"===t.role&&this.fetchReferralCode(),"Learner"===t.role&&"pending"===t.is_signup_completed){const e=localStorage.getItem("userState");window.location.pathname+window.location.search!==e&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout(()=>{localStorage.removeItem("skipSignUp")},10))}t.role,this.$emit("userInfo",t)}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},getStorage(){const e=this.storage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.header.data=e.menu}this.loginStatus()},setStorage(){const e=this.storage,t={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(e.name+"V"+e.version,JSON.stringify(t))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const e=this.storage;sessionStorage.removeItem(e.name+"V"+e.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(e){if(this.generateCode.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;Event.$emit("fetchReferralCode")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("postData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(e,t){0!==t.sub_items.length&&e.preventDefault(),"generate-code"===t.slug&&e.preventDefault(),"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.referralCode.data=t}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},gotCode(e){if(this.moduleWithoutTab.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;this.fetchReferralCode()}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotCode(t)}};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Referral":"Referral Earnings",isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)}}}),Vue.component("yuno-course-search-bar",{props:["data","options"],template:'\n        <div class="hasSearchBar">\n            <template v-if="userInfo.loading">\n                <b-skeleton height="31px"></b-skeleton>\n            </template>\n            <div class="searchBarWrapper" v-if="userInfo.success && userInfo.data.role === \'Learner\'">\n                <validation-observer  \n                    tag="div" \n                    ref="searchObserver" \n                    v-slot="{ handleSubmit, invalid }">\n                    <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                        <b-field class="searchFieldWrapper">\n                            <validation-provider \n                                tag="div"\n                                class="searchField"\n                                :customMessages="{ isNotBlank: errorMsg.subject }"\n                                :rules="{required:true, isNotBlank:categories.selected}" \n                                v-slot="{ errors, classes }">\n                                <b-autocomplete\n                                    :class="classes"\n                                    v-model="categories.current"\n                                    :data="categories.data"\n                                    autocomplete="courseSearch"\n                                    :loading="categories.isLoading"\n                                    placeholder="Search..."\n                                    @typing="searchOnTyping"\n                                    @select="onSelect($event)"\n                                    :clearable="true"\n                                >\n                                    <template slot-scope="props">\n                                        <template v-if="props.option.course_url">\n                                            <div class="suggestion courseBlock">\n                                                <figure>\n                                                    <div class="imageWrapper">\n                                                        <img :src="props.option.imageurl" :alt="props.option.title">\n                                                    </div>\n                                                    <figcaption>\n                                                        <p class="courseTitle">{{ props.option.title }}</p>\n                                                        <p class="courseDetail">\n                                                            <span class="caption">Course</span>\n                                                            <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                        </p>\n                                                    </figcaption>\n                                                </figure>\n                                            </div>\n                                        </template>\n                                        <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                            <div class="suggestion categoryBlock">\n                                                <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                <p class="courseDetail">\n                                                    <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                </p>\n                                            </div>\n                                        </template>\n                                        <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                            <div class="suggestion categoryBlock">\n                                                <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                <p class="courseDetail">\n                                                    <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                </p>\n                                            </div>\n                                        </template>\n                                    </template>\n                                </b-autocomplete>\n                            </validation-provider>\n                            <div class="ctaWrapper">\n                                <b-button\n                                    native-type="submit"\n                                    class="doSearch">\n                                    <span class="material-icons-outlined">search</span>\n                                </b-button>  \n                            </div>\n                        </b-field>\n                    </form>\n                </validation-observer>\n            </div>\n        </div>\n    ',data:()=>({errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},popularSearch:[]}),computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","header","module","searchSuggestions"])},async created(){},mounted(){},methods:{onSelect(e){e&&(e.course_url?window.location.href=e.course_url:e.course_count&&(this.categories.selected=e,this.payload.search=e.id,this.searchParams.category=[e.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],e.parent_cat_slug&&(this.searchParams.category=[e.parent_cat_id],this.searchParams.category_level_1=[e.category_level_1],this.searchParams.category_level_2=[e.id]),this.initForm()))},gotCourseSuggestions(e){if(this.categories.isLoading=!1,200===e.response?.data?.code){const{course:t,category:n,sub_category:i}=e.response.data.data;n&&this.categories.data.push(...n),i&&this.categories.data.push(...i),t&&this.categories.data.push(...t)}},fetchCourseSuggestions(e){const t=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",e),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(e){return t.gotCourseSuggestions(e)}};this.$store.dispatch("fetchData",n)},searchOnTyping:_.debounce(function(e){e.length>2?(this.categories.isLoading=!0,this.fetchCourseSuggestions(e)):this.categories.data=[]},700),initForm(){const e=this.categories.selected;e.category_id,e.category,e.categorySlug;void 0===this.$props.hassearchbar&&(window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)))}}});const YUNOInsightsCard=(jQuery,{insightsCard:function(){Vue.component("yuno-insights-card",{props:["data","options"],template:'\n                <section class="insightsData">\n                    <div class="container">\n                        <div class="row">\n                            <div class="col-12 col-md-4"\n                                v-for="(card, i) in data.cards"\n                                :key="i">\n                                <div class="insightCard" :class="[card.customClass]">\n                                    <span :class="[card.iconType]"> {{card.icon}} </span>\n                                    <div class="value">\n                                        <span class="labelBig">{{ card.label }}</span>\n                                        <span class="labelSmall">{{ card.helper }}</span>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </section>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOFeaturesList=(jQuery,{featuresList:function(){Vue.component("yuno-features-list",{props:["data","options"],template:'\n                <section class="featuresList">\n                    <div class="container">\n                        <h2 \n                            class="sectionTitle" \n                            :class="[data.desciption === undefined || data.desciption === \'\' ? \'gapBtm\' : \'\']"\n                        >\n                            {{data.title}}\n                        </h2>\n                        <p class="description" v-if="data.desciption !== undefined && data.desciption !== \'\'">{{data.desciption}}</p>\n                        <ul class="cardsGrid">\n                            <li\n                                v-for="(card, i) in data.cards"\n                                :key="i" \n                                :class="card.customClass">\n                                <figure class="cardImgWrapper">\n                                    <img :src="card.img" :alt="card.title">\n                                </figure>\n                                <h3 class="cardCaption">{{card.title}}</h3>\n                                <ul class="checkList">\n                                    <li v-for="(item, j) in card.list" :key="j">\n                                        {{item}}\n                                    </li>\n                                </ul>\n                            </li>\n                        </ul>\n                    </div>\n                </section>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOReviewCard=(jQuery,{reviewCard:function(){Vue.component("star-rating",VueStarRating.default),Vue.component("yuno-review-card",{props:["dataid","options"],template:'\n            <section id="reviews" class="reviewSection" :class="[options.viewType !== undefined ? options.viewType : \'\', options.customClass]">\n                <template v-if="instructorReviews.loading && !isLoadMore">\n                    <div class="seactionHeader">\n                        <h3 v-if="options.title === undefined || options.title !== false" class="sectionTitle" :class="options.title !== undefined ? options.titleClass : \'\'">\n                            <template v-if="options.title !== undefined">\n                                {{options.title}}\n                            </template>\n                            <template v-else>\n                                Reviews <span class="helper"></span>\n                            </template>\n                        </h3>\n                        <div class="filterWrap" v-if="options.sorting && options.sorting !== \'featured\'">\n                            <b-dropdown\n                                v-model="sortBy.selected"\n                                aria-role="list"\n                                @change="onSorting($event)"\n                                class="filterMenu">\n                                <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">\n                                    <span>{{sortBy.selected}}</span>\n                                    <b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n                                </button>\n                                <template v-for="(item, itemIndex) in sortBy.data">\n                                    <b-dropdown-item \n                                        :value="item.slug" \n                                        :key="itemIndex"\n                                        @click="sortingItems(item.label)"\n                                        aria-role="listitem">\n                                        <span>{{item.label}}</span>\n                                    </b-dropdown-item>\n                                </template>\n                            </b-dropdown> \n                        </div>\n                    </div>\n                    <template\n                        v-if="options.viewType === \'gridView\'">\n                        <div class="reviewCardGrid">\n                            <article class="reviewCard" v-for="(review, reviewIndex) in 3" :key="reviewIndex">\n                                <figure class="thumbnail">\n                                    <b-skeleton height="250px"></b-skeleton>\n                                </figure>\n                                <figure class="userImg">\n                                    <b-skeleton circle width="60px" height="60px"></b-skeleton>\n                                    <figcaption>\n                                        <b-skeleton class="reviewerName" width="40%" height="20%" :animated="true"></b-skeleton>\n                                        <b-skeleton class="reviewDate" width="30%" height="20%" :animated="true"></b-skeleton>\n                                    </figcaption>\n                                </figure>\n                                <div class="cardInfo">\n                                    <div class="reviewContent">\n                                        <b-skeleton active></b-skeleton>\n                                    </div>\n                                </div>\n                            </article>\n                        </div>\n                    </template>\n                    <template\n                        v-else>\n                    >\n                        <article class="reviewCard">\n                            <figure class="userImg">\n                                <b-skeleton circle width="60px" height="60px"></b-skeleton>\n                                <figcaption>\n                                    <b-skeleton class="reviewerName" width="40%" height="20%" :animated="true"></b-skeleton>\n                                    <b-skeleton class="reviewDate" width="30%" height="20%" :animated="true"></b-skeleton>\n                                </figcaption>\n                            </figure>\n                            <div class="starRating">\n                                <b-skeleton  width="30%" height="20%" :animated="true"></b-skeleton>\n                            </div>\n                            <div class="reviewContent">\n                                <b-skeleton height="80px"></b-skeleton>\n                            </div>\n                        </article>\n                        <article class="reviewCard">\n                            <figure class="userImg">\n                                <b-skeleton circle width="60px" height="60px"></b-skeleton>\n                                <figcaption>\n                                    <b-skeleton class="reviewerName" width="40%" height="20%" :animated="true"></b-skeleton>\n                                    <b-skeleton class="reviewDate" width="30%" height="20%" :animated="true"></b-skeleton>\n                                </figcaption>\n                            </figure>\n                            <div class="starRating">\n                                <b-skeleton  width="30%" height="20%" :animated="true"></b-skeleton>\n                            </div>\n                            <div class="reviewContent">\n                                <b-skeleton height="80px"></b-skeleton>\n                            </div>\n                        </article>\n                    </template>\n                </template>\n                <template v-if="instructorReviews.success">\n                    <template v-if="instructorReviews.error === null">  \n                        <div class="seactionHeader" :class="[options.hasBasedOn ? \'hasBasedOn\' : \'\']">\n                            <h3 v-if="options.title === undefined || options.title !== false" class="sectionTitle" :class="options.title !== undefined ? options.titleClass : \'\'">\n                                <template v-if="options.title !== undefined">\n                                    {{options.title}}\n                                </template>\n                                <template v-else>\n                                    Reviews <span class="helper">({{instructorReviews.count}})</span>\n                                </template>\n                            </h3>\n                            <div class="filterWrap" v-if="options.sorting && options.sorting !== \'featured\'">\n                                <b-dropdown\n                                    v-model="sortBy.selected"\n                                    aria-role="list"\n                                    @change="onSorting($event)"\n                                    class="filterMenu">\n                                    <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">\n                                        <span>{{sortBy.selected}}</span>\n                                        <b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n                                    </button>\n                                    <template v-for="(item, itemIndex) in sortBy.data">\n                                        <b-dropdown-item \n                                            :value="item.slug" \n                                            :key="itemIndex"\n                                            @click="sortingItems(item.label)"\n                                            aria-role="listitem">\n                                            <span>{{item.label}}</span>\n                                        </b-dropdown-item>\n                                    </template>\n                                </b-dropdown> \n                            </div>\n                        </div>\n                        <p class="basedon" v-if="options.hasBasedOn"><a :href="options.externalURL" target="_blank">Based on {{instructorReviews.total_review}} reviews</a></p>\n                        <div class="overallStars" v-if="options.overallStars">\n                            <star-rating :rating="Number(instructorReviews.reviews_avg_rating)" active-color="#F9B600" :read-only="true" :round-start-rating="false" :star-size="30" :show-rating="false"></star-rating>\n                            <div class="vue-star-rating-rating-text">{{ instructorReviews.reviews_avg_rating }}</div>\n                        </div>\n                        <template v-if="options.viewType === undefined">\n                            <article class="reviewCard" v-for="(review, reviewIndex) in instructorReviews.data" :key="reviewIndex">\n                                <figure class="userImg">\n                                    <img v-if="review.image !== \'\'" :src="review.image" :alt="\'Review by\' + review.name">\n                                    <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>\n                                    <figcaption>\n                                        <h3 class="reviewerName">{{review.name}}</h3>\n                                        <div class="reviewDate">{{ review.date }}</div>\n                                    </figcaption>\n                                </figure>\n                                <div class="starRating">\n                                    <div class="stars">\n                                        <star-rating :rating="review.rating" :read-only="true" :increment="0.01" :star-size="25" :show-rating="false"></star-rating>\n                                    </div>\n                                </div>\n                                <div class="reviewContent">\n                                    {{ review.review_text }}\n                                </div>\n                            </article>\n                        </template>\n                        <template v-else-if="options.viewType === \'listView\'">\n                            <article class="reviewCard" v-for="(review, reviewIndex) in instructorReviews.data" :key="reviewIndex">\n                                <figure class="userImg">\n                                    <img v-if="review.image !== \'\'" :src="review.image" :alt="\'Review by\' + review.name">\n                                    <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>\n                                    <figcaption>\n                                        <h3 class="reviewerName">{{review.name}} <a class="watchVideo" v-if="review.video_testimonial !== \'\'" @click.prevent="videoTestimonialModal(review)" :href="review.video_testimonial"><b-tag rounded>Watch Video</b-tag></a></h3>\n                                        <div class="reviewDate">{{ review.date }}</div>\n                                        <div class="starRating">\n                                            <star-rating active-color="#F9B600" :rating="review.rating" :read-only="true" :increment="0.01" :star-size="14" :show-rating="false"></star-rating>\n                                        </div>\n                                        <div class="reviewContent">\n                                            {{ review.review_text }}\n                                        </div>\n                                        <div class="verified" v-if="review.verified_enrolment">\n                                            <b-tooltip label="Yuno has verified that the student enrolled in the instructor\'s classes"\n                                                type="is-dark"\n                                                multilined\n                                                position="is-top">\n                                                <b-tag rounded>Verified Enrolment</b-tag>\n                                            </b-tooltip>\n                                        </div>\n                                    </figcaption>\n                                </figure>\n                            </article>\n                        </template>\n                        <template v-else-if="options.viewType === \'gridView\'">\n                            <div class="reviewCardGrid">\n                                <article class="reviewCard" v-for="(review, reviewIndex) in instructorReviews.data" :key="reviewIndex">\n                                    <figure class="thumbnail">\n                                        <a href="#" @click.prevent="videoTestimonialModal(review)"><img :src="getThumbnail(review.video_testimonial)" :alt="\'Review by\' + review.name"></a>\n                                    </figure>\n                                    <figure class="userImg">\n                                        <img v-if="review.image !== \'\'" :src="review.image" :alt="\'Review by\' + review.name">\n                                        <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>\n                                        <figcaption>\n                                            <h3 class="reviewerName">{{review.name}} \n                                                <a class="watchVideo" v-if="false" @click.prevent="videoTestimonialModal(review)" :href="review.video_testimonial"><b-tag rounded>Watch Video</b-tag></a>\n                                            </h3>\n                                            <div class="reviewDate">{{ review.date }}</div>\n                                            <div class="starRating">\n                                                <div class="stars">\n                                                    <star-rating active-color="#F9B600" :rating="review.rating" :read-only="true" :increment="0.01" :star-size="18" :show-rating="false"></star-rating>\n                                                </div>\n                                            </div>\n                                        </figcaption>\n                                    </figure>\n                                    <div class="cardInfo">\n                                        <div class="reviewContent">\n                                            {{ review.review_text }}\n                                        </div>\n                                        <div class="verified" v-if="review.verified_enrolment">\n                                            <b-tooltip label="Yuno has verified that the student enrolled in the instructor\'s classes"\n                                                type="is-dark"\n                                                multilined\n                                                position="is-top">\n                                                <b-tag rounded>Verified Enrolment</b-tag>\n                                            </b-tooltip>\n                                        </div>\n                                    </div>\n                                </article>\n                            </div>\n                        </template>\n                        <template v-if="options.hasShowMore !== undefined && options.hasShowMore">\n                            <div class="showMore"><a href="#" @click.prevent="showmore()">Show more reviews</a></div>\n                        </template>\n                        <template v-else>\n                            <div class="d-flex justify-content-center" :class="options.loadMoreParentClass !== undefined ? options.loadMoreParentClass : \'\'" v-if="instructorReviews.currentCount !== instructorReviews.count">\n                                <b-button \n                                    class="yunoPrimaryCTA wired big"\n                                    @click="loadMore()"\n                                    :loading="isLoadMore ? true : false"\n                                    :disabled="isLoadMore ? true : false">\n                                    See more reviews\n                                </b-button>\n                            </div>\n                        </template>\n                    </template>\n                    <template v-else>    \n                        <section id="reviews" class="reviewSection">\n                            <div class="seactionHeader">\n                                <h3 class="sectionTitle">\n                                    Reviews\n                                </h3>\n                            </div>\n                            <article class="yunoCardWide gapTop15">\n                                <div class="yunoCardBody">\n                                    <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                                </div>\n                            </article>\n                        </section>\n                    </template>\n                </template>\n                <b-modal \n                    :active.sync="video.modal" \n                    :width="800" \n                    :can-cancel="[\'escape\', \'x\']"\n                    @close="videoTestimonialClose" \n                    class="yunoModal lightTheme">\n                        <template v-if="video.modal">\n                            <div class="modalHeader">\n                                <h2 class="modalTitle">{{video.data.name}} - Testimonial</h2>\n                            </div>\n                            <div class="modalBody">\n                                \n                                    <div class="videoLPPlayer">\n                                        <iframe width="800" height="450" :src="video.data.video_testimonial + \'?autoplay=1\'" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>\n                                    </div>\n                                \n                            </div>\n                        </template>\n                </b-modal>\n                <b-modal \n                    :active.sync="showmoreModal.modal" \n                    :width="800" \n                    :can-cancel="[\'escape\', \'x\']"\n                    :on-cancel="showmoreClose"\n                    class="yunoModal loginSignupModal">\n                        <template v-if="showmoreModal.modal">\n                            <div class="modalBody reviewsList">\n                                <template v-if="allReviews.loading">\n                                    <div class="loaderWrapper big">\n                                        <div class="smallLoader"></div>\n                                    </div>\n                                </template>\n                                <template v-if="allReviews.success">\n                                    <div class="reviewsHeader">\n                                        <h2>Rated 5-Star on Google</h2>\n                                        <div class="ratingWrapper">\n                                            <star-rating :rating="Number(allReviews.reviews_avg_rating)" active-color="#F9B600" :read-only="true" :round-start-rating="false" :star-size="24" :show-rating="false"></star-rating>\n                                            <div class="vue-star-rating-rating-text">{{ allReviews.reviews_avg_rating }}</div>\n                                        </div>\n                                        <p><a :href="options.externalURL" target="_blank">Based on {{allReviews.total_review}} reviews</a></p>\n                                        <a href="#" v-if="video.isActive" class="backToList" @click.prevent="backToList()"><span class="material-icons">arrow_back_ios_new</span></a>\n                                    </div>\n                                    <div class="videoWrapper" \n                                        v-if="video.isActive" \n                                        :class="[video.isActive ? \'slide-in-right\' : \'\', video.isSlideActive ? \'slide-out-right\' : \'\']">\n                                        <div class="videoLPPlayer">\n                                            <iframe width="800" height="450" :src="video.data.video_testimonial + \'?autoplay=1\'" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>\n                                        </div>\n                                    </div>\n                                    <div v-if="showmoreModal.isActive">\n                                        <article class="reviewCard" v-for="(review, reviewIndex) in allReviews.data" :key="reviewIndex">\n                                            <figure class="userImg">\n                                                <img v-if="review.image !== \'\'" :src="review.image" :alt="\'Review by\' + review.name">\n                                                <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>\n                                                <figcaption>\n                                                    <h3 class="reviewerName">{{review.name}} <a class="watchVideo" v-if="review.video_testimonial !== \'\'" @click.prevent="showVideo(review)" :href="review.video_testimonial"><b-tag rounded>Watch Video</b-tag></a></h3>\n                                                    <div class="reviewDate">{{ readableDate(review.created_at) }}</div>\n                                                    <div class="starRating">\n                                                        <star-rating active-color="#F9B600" :rating="Number(review.value)" :read-only="true" :round-start-rating="false" :star-size="14" :show-rating="false"></star-rating>\n                                                    </div>\n                                                    <div class="reviewContent">\n                                                        {{ review.review_text }}\n                                                    </div>\n                                                    <div class="verified" v-if="review.verified_enrolment">\n                                                        <b-tooltip label="Yuno has verified that the student enrolled in the instructor\'s classes"\n                                                            type="is-dark"\n                                                            multilined\n                                                            position="is-top">\n                                                            <b-tag rounded>Verified Enrolment</b-tag>\n                                                        </b-tooltip>\n                                                    </div>\n                                                </figcaption>\n                                            </figure>\n                                        </article>\n                                        <div class="d-flex justify-content-center" v-if="allReviews.currentCount !== allReviews.count">\n                                            <b-button \n                                                class="yunoPrimaryCTA wired big"\n                                                @click="loadMore(true)"\n                                                :loading="isLoadMore ? true : false"\n                                                :disabled="isLoadMore ? true : false">\n                                                See more reviews\n                                            </b-button>\n                                        </div>\n                                    </div>\n                                </template>\n                            </div>\n                        </template>\n                </b-modal>\n            </section>\n            ',data:()=>({isLoadMore:!1,sortBy:{selected:"Newest First",data:[{label:"Newest First",slug:"newest"},{label:"Highest Rated First",slug:"highest"},{label:"Oldest First",slug:"oldest"},{label:"Lowest Rated First",slug:"lowest"}]},video:{modal:!1,data:[],allReviews:!1,isActive:!1,isSlideActive:!1},showmoreModal:{modal:!1,data:[],isActive:!0,scrollTop:""}}),computed:{...Vuex.mapState(["instructorReviews","userProfile","allReviews"]),emptyStates:()=>({title:"Reviews not found",state:"dataNotFound",description:"This instructor doesn't have any reviews yet.",maxheight:!1})},created(){},mounted(){this.setUpReviewtab()},methods:{getThumbnail:e=>"https://i.ytimg.com/vi/"+YUNOCommon.getFromString(e,/embed\/(.*)/)+"/hqdefault.jpg",limitCharacter(e,t){let n=e.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(n=n.replace(/\s\s+/g," "),n.length>t){const e=n.lastIndexOf(" ",t-1);return n.substring(0,e)+"..."}return n},readableDate:e=>moment(e).format("MMM DD, YYYY"),showmore(){this.showmoreModal.modal=!0,0===this.allReviews.data.length&&this.fetchAllReviews()},showmoreClose(){this.showmoreModal.data=[]},videoTestimonialClose(){this.video.data=[]},backToList(){this.video.isSlideActive=!0,setTimeout(()=>{this.video.isActive=!1,this.video.data=[],this.video.isSlideActive=!1,this.showmoreModal.isActive=!0,setTimeout(()=>{document.querySelector(".reviewsList").scrollTop=this.showmoreModal.scrollTop},100)},500)},showVideo(e){this.showmoreModal.scrollTop="",this.video.data=e,this.video.isActive=!0,this.showmoreModal.isActive=!1,this.showmoreModal.scrollTop=document.querySelector(".reviewsList").scrollTop},videoTestimonialModal(e){this.video.data=e,this.video.modal=!0,this.showmoreModal.modal=!1,this.showmoreModal.data=[]},sortingItems(e){this.sortBy.selected=e},onSorting(e){const t=this.instructorReviews;t.sort=e,t.offset=0,t.data=[],this.fetchReviews()},loadMore(e){this.isLoadMore=!0,void 0!==e&&e?this.fetchAllReviews():this.fetchReviews()},setUpReviewtab(){const e=this.instructorReviews;e.count="",e.currentCount="",e.limit=this.$props.options.limit,e.offset=0,e.sort="newest",this.fetchReviews()},structuredData(e){const t=document.createElement("script");t.setAttribute("type","application/ld+json");let n=document.createTextNode(JSON.stringify(e));t.appendChild(n),document.head.appendChild(t)},gotReviews(e){if(this.isLoadMore=!1,void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){let t=e.response.data,n={"@context":"https://schema.org","@type":"LocalBusiness",name:"Yuno Learning",image:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/assets/images/yuno_logo.png",telephone:"+917411781928",priceRange:"₹₹₹₹",address:{"@type":"PostalAddress",streetAddress:"Yuno Learning Global Pvt. Ltd. 006-Upper Ground Floor, MGF Metropolis Mall",addressLocality:"Gurugram",addressRegion:"Haryana",postalCode:"122002",addressCountry:"IN"},aggregateRating:{"@type":"AggregateRating",ratingValue:Number(t.reviews_avg_rating),reviewCount:t.count},review:[]};const i=this.instructorReviews,a=i.data;i.count=t.count,i.currentCount=a.length,i.offset=a.length,i.rating=Number(t.reviews_avg_rating),i.total_review=void 0!==t.total_review?t.total_review:0,i.reviews_avg_rating=void 0!==t.reviews_avg_rating?t.reviews_avg_rating:0;for(let e=0;e<a.length;e++){const t=a[e];let i={"@type":"Review",author:{"@type":"Person",name:t.name},datePublished:t.created_at,description:t.review_text,name:this.userProfile.data.first_name,reviewRating:{"@type":"Rating",bestRating:"5",ratingValue:t.value,worstRating:"1"}};t.rating=Number(t.value),t.date=moment(t.created_at).format("MMM DD, YYYY"),n.review.push(i)}this.structuredData(n)}},fetchReviews(){const e=this,t=this.instructorReviews;let n="",i="",a="";i="channel"!==this.$props.options.type&&this.$props.options.type,a=!!this.$props.options.sorting&&this.$props.options.sorting,n=YUNOCommon.config.reviewAPI(i,this.$props.dataid,this.$props.options.view,t.limit,t.offset,a);const r={apiURL:n,module:"gotData",store:"instructorReviews",pushData:!0,callback:!0,callbackFunc:function(t){return e.gotReviews(t)}};this.$store.dispatch("fetchData",r)},gotAllReviews(e){if(this.isLoadMore=!1,void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){let t=e.response.data;const n=this.allReviews,i=n.data;n.count=t.count,n.currentCount=i.length,n.offset=i.length,n.total_review=t.total_review,n.reviews_avg_rating=t.reviews_avg_rating}},fetchAllReviews(){const e=this,t={apiURL:YUNOCommon.config.reviewAPI("learner",0,"list-view",this.allReviews.limit,this.allReviews.offset,"all"),module:"gotData",store:"allReviews",pushData:!0,callback:!0,callbackFunc:function(t){return e.gotAllReviews(t)}};this.$store.dispatch("fetchData",t)}}})}});Vue.component("yuno-hero-banner-v2",{props:["data","options"],template:'\n        <section class="heroBanner">\n            <div class="container">\n                <div class="wrapper">\n                    <div class="innerWrapper">\n                        <div class="starRating" v-if="data.rating.isActive">\n                            <div class="iconsWrapper">\n                                <span class="material-icons">star</span>\n                                <span class="material-icons">star</span>\n                                <span class="material-icons">star</span>\n                                <span class="material-icons">star</span>\n                                <span class="material-icons">star</span>\n                            </div>\n                            <p class="smallCaption">{{ data.rating.label }}</p>\n                        </div>\n                        <h1 class="largestTitle">{{ data.title }}</h1>\n                        <validation-observer \n                            tag="div" \n                            class="searchBarWrapper"\n                            ref="searchObserver" \n                            v-slot="{ handleSubmit, invalid }">\n                            <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                                <b-field class="searchFieldWrapper">\n                                    <validation-provider \n                                        tag="div"\n                                        class="searchField"\n                                        :customMessages="{ isNotBlank: errorMsg.subject }"\n                                        :rules="{required:true, isNotBlank:categories.selected}" \n                                        v-slot="{ errors, classes }">\n                                        <b-autocomplete\n                                            :class="classes"\n                                            v-model="categories.current"\n                                            :data="categories.data"\n                                            autocomplete="courseSearch"\n                                            :loading="categories.isLoading"\n                                            placeholder="Search for..."\n                                            @typing="searchOnTyping"\n                                            @select="onSelect($event)"\n                                            :clearable="true"\n                                        >\n                                            <template slot-scope="props">\n                                                <template v-if="props.option.course_url">\n                                                    <div class="suggestion courseBlock">\n                                                        <figure>\n                                                            <div class="imageWrapper" v-if="false">\n                                                                <img :src="props.option.imageurl" :alt="props.option.title">\n                                                            </div>\n                                                            <figcaption>\n                                                                <p class="courseTitle">{{ props.option.title }}</p>\n                                                                <p class="courseDetail">\n                                                                    <span class="caption">Course</span>\n                                                                    <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                                </p>\n                                                            </figcaption>\n                                                        </figure>\n                                                    </div>\n                                                </template>\n                                                <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                                    <div class="suggestion categoryBlock">\n                                                        <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                        <p class="courseDetail">\n                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                        </p>\n                                                    </div>\n                                                </template>\n                                                <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                                    <div class="suggestion categoryBlock">\n                                                        <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                        <p class="courseDetail">\n                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                        </p>\n                                                    </div>\n                                                </template>\n                                            </template>\n                                        </b-autocomplete>\n                                    </validation-provider>\n                                    <div class="ctaWrapper">\n                                        <b-button\n                                            native-type="submit"\n                                            class="doSearch">\n                                            <span class="material-icons-outlined">search</span>\n                                        </b-button>  \n                                    </div>\n                                </b-field>\n                            </form>\n                        </validation-observer>\n                        <div class="categoriesWrapper" :class="{ \'has-scroll\': scrollState.isScrollable }">\n                            <b-button class="prev" @click="handlePrevClick">\n                                <span class="material-icons">chevron_left</span>\n                            </b-button>\n                            <ul class="categories">\n                                <template v-if="categoryList.loading">\n                                    <li v-for="(category, i) in 4" :key="i">\n                                        <b-skeleton active width="220px" height="66px"></b-skeleton>\n                                    </li>\n                                </template>\n                                <template v-else-if="categoryList.success && categoryList.error === null">\n                                    <li v-for="(category, i) in categoryList.data" :key="i" >\n                                        <a :href="category.url" class="inner">{{ category.name }} <sup v-if="false">®</sup></a>\n                                    </li>\n                                </template>\n                            </ul>\n                            <b-button class="next" @click="handleNextClick">\n                                <span class="material-icons">chevron_right</span>\n                            </b-button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n    ',data(){return{heroStyle:{"background-image":"url("+this.$props.data.bgImg+")"},categories:{data:[],selected:null,current:"",isLoading:!1},errorMsg:{subject:"Please select the subject from list"},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},scrollState:{currentPosition:0,scrollAmount:200,visibleItems:4,totalItems:0,isScrollable:!1}}},computed:{...Vuex.mapState(["searchSuggestions","categoryList"]),isScrollable(){const e=this.$el?.querySelector(".categories");return!!e&&e.scrollWidth>e.clientWidth}},async created(){},mounted(){this.fetchCategories(),this.$watch("categoryList.data",()=>{this.$nextTick(()=>{this.initializeScroll(),this.checkScrollable()})},{immediate:!0}),window.addEventListener("resize",this.checkScrollable)},beforeDestroy(){const e=this.$el.querySelector(".categories");e&&e.removeEventListener("scroll",this.updateButtonStates),window.removeEventListener("resize",this.checkScrollable)},methods:{initForm(){const e=this.categories.selected,t={filter:"category",id:e.category_id,label:e.category,parent_id:0,slug:e.categorySlug},n={filter:"category"};void 0===this.$props.hassearchbar?window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)):Event.$emit("initHeaderSearch",t,n)},gotCourseSuggestions(e){if(this.categories.isLoading=!1,200===e.response?.data?.code){const{course:t,category:n,sub_category:i}=e.response.data.data;n&&this.categories.data.push(...n),i&&this.categories.data.push(...i),t&&this.categories.data.push(...t)}},fetchCourseSuggestions(e){const t=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",e),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(e){return t.gotCourseSuggestions(e)}};this.$store.dispatch("fetchData",n)},searchOnTyping:_.debounce(function(e){e.length>2?(this.categories.isLoading=!0,this.categories.data=[],this.fetchCourseSuggestions(e)):this.categories.data=[]},700),onSelect(e){e&&(e.course_url?window.location.href=e.course_url:e.course_count&&(this.categories.selected=e,this.payload.search=e.id,this.searchParams.category=[e.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],e.parent_cat_slug&&(this.searchParams.category=[e.parent_cat_id],this.searchParams.category_level_1=[e.category_level_1],this.searchParams.category_level_2=[e.id]),this.initForm()))},gotCategories(e){const{code:t,data:n}=e.response?.data||{};200===t&&(this.categoryList.data=[{id:5426,name:"Duolingo",is_checked:!1,excerpt:"fgg",desciption:"",image:"https://dev.yunolearning.com/wp-content/uploads/2023/11/duolingo-1-768x432.png",url:"https://dev.yunolearning.com/./duolingo/",slug:"duolingo"},{id:5590,name:"French",is_checked:!1,excerpt:"Join our live online French classes for personalized learning. Improve your French skills with expert tutors. Enroll now for interactive sessions!",desciption:"Live French Online Classes with Expert Tutors\r\nJoin our live online French classes for personalized learning. Improve your French skills with expert tutors. Enroll now for interactive sessions!\r\nonline French classes\r\nonline French course\r\nFrench Online Classes\r\nFrench courses online\r\nLearn French online\r\n\r\nWhy should you learn French?\r\nLearning French offers distinct advantages across various age groups and professional paths. \r\nFrench for Kids:\r\nFor children, early exposure to French through engaging methods like interactive games, songs, and stories fosters cognitive development and cultural awareness. Utilizing French lessons online can make this process even more accessible and enjoyable. \r\n\r\nPrograms such as DELF Junior are specifically designed to introduce young learners to language proficiency in a structured, enjoyable manner. These certifications not only build foundational language skills but also cultivate a lifelong appreciation for linguistic diversity and cultural richness.\r\nFrench for Students:\r\nFor students, acquiring French language skills significantly enhances academic and career opportunities. Mastery of French allows students to access prestigious educational institutions in French-speaking countries and engage deeply with French literature, philosophy, and sciences. \r\n\r\nStarting with French courses online, students can progress from basic to advanced proficiency levels. The DELF and DALF certifications serve as internationally recognized benchmarks of language competency. Additionally, assessments like TEF are available for those seeking to further their academic pursuits in French-speaking environments, positioning them competitively in the global academic arena.\r\nFrench for Working Professionals:\r\nProfessionals across various fields benefit greatly from learning French, as it broadens their career horizons and enhances their ability to participate in international markets. Whether starting with essential communication skills or advancing to specialized business French, professionals can tailor their learning through French courses online. \r\n\r\nCertifications such as DELF Pro and TFI focus on assessing practical workplace communication, ensuring professionals are well-prepared for diverse business scenarios. Engaging in French lessons online not only opens doors to opportunities in French-speaking regions but also enriches cross-cultural interactions, making it a strategic asset in today’s interconnected world.\r\nLevels in French\r\nUnderstanding the different levels of French proficiency is essential for learners to set realistic goals and measure their progress. The Common European Framework of Reference for Languages (CEFR) provides a widely recognized standard for language proficiency, dividing it into six levels from basic to proficient. Here’s a breakdown of these levels, what learners can expect to achieve at each stage, and the corresponding French tests they can take.\r\nBeginner Levels\r\nA1 - Breakthrough\r\n\r\nDescription: At this level, learners can understand and use familiar everyday expressions and very basic phrases aimed at satisfying concrete needs. They can introduce themselves and others and can ask and answer questions about personal details.\r\nSkills: Basic understanding and usage of common phrases, introducing oneself, asking simple questions.\r\nRelevant Tests: DELF A1\r\nA2 - Waystage\r\n\r\nDescription: Learners can understand sentences and frequently used expressions related to areas of most immediate relevance (e.g., basic personal and family information, shopping, local geography, employment). They can communicate in simple and routine tasks.\r\nSkills: Simple interactions on familiar topics, basic communication in routine tasks.\r\nRelevant Tests: DELF A2\r\nIntermediate Levels\r\nB1 - Threshold\r\n\r\nDescription: At this level, learners can understand the main points of clear standard input on familiar matters regularly encountered in work, school, leisure, etc. They can deal with most situations likely to arise while traveling in an area where the language is spoken.\r\nSkills: Handling day-to-day situations, producing simple connected text on topics of personal interest.\r\nRelevant Tests: DELF B1, TEF (Test d'Évaluation de Français)\r\nB2 - Vantage\r\n\r\nDescription: Learners can understand the main ideas of complex text on both concrete and abstract topics, including technical discussions in their field of specialization. They can interact with a degree of fluency and spontaneity.\r\nSkills: Understanding complex texts, engaging in fluent conversations on various topics.\r\nRelevant Tests: DELF B2, TEF, TCF (Test de Connaissance du Français)\r\nAdvanced Levels\r\nC1 - Effective Operational Proficiency\r\n\r\nDescription: At this level, learners can understand a wide range of demanding, longer texts, and recognize implicit meaning. They can express ideas fluently and spontaneously without much obvious searching for expressions.\r\nSkills: Producing clear, well-structured, detailed text on complex subjects, understanding a wide range of texts.\r\nRelevant Tests: DALF C1\r\nC2 - Mastery\r\n\r\nDescription: Learners can understand with ease virtually everything heard or read. They can summarize information from different spoken and written sources, reconstructing arguments and accounts in a coherent presentation. They can express themselves spontaneously, very fluently, and precisely.\r\nSkills: Understanding virtually everything heard or read, expressing ideas fluently and precisely.\r\nRelevant Tests: DALF C2\r\nFAQ’s \r\nWhat are the benefits of learning French online?\r\n\r\nLearning French online offers immense flexibility, allowing you to study from anywhere at your own pace. Online platforms provide diverse resources like interactive videos, audio lessons, and live tutoring, catering to different learning styles. Whether you're starting as a beginner or seeking advanced proficiency, there are tailored French courses online to suit your needs. \r\n\r\nWhat types of French lessons online are available for beginners?\r\n\r\nBeginners can access a wide range of French lessons online that focus on fundamental vocabulary, grammar, and everyday phrases. These lessons often include interactive exercises, pronunciation guides, and basic conversational practice. \r\n\r\nHow can I choose the right online French course for my level?\r\n\r\nTo choose the appropriate French course online, start by evaluating your current proficiency level. Many online platforms offer placement tests to help you determine if you're at a beginner (A1, A2), intermediate (B1, B2), or advanced (C1, C2) level. Once you know your level, select courses that align with your learning goals and offer structured progression through the different stages of language acquisition.\r\n\r\nAre there specialized French courses online for children?\r\n\r\nYes, there are numerous French lessons online specifically designed for children. These courses use fun and engaging methods like games, songs, and stories to teach French. They focus on basic vocabulary and simple sentences to help kids start communicating early. Programs like DELF Junior cater to young learners, providing age-appropriate assessments that measure their progress in a supportive environment. \r\n\r\nWhat online resources are available for advanced French learners?\r\n\r\nAdvanced learners can find specialized French courses online that delve into complex grammar, advanced vocabulary, and nuanced conversational skills. These resources often include literature analysis, business French, and preparation for advanced proficiency tests like DALF C1 and C2. \r\n\r\nCan I prepare for French proficiency tests through online courses?\r\n\r\nAbsolutely, many French courses online are designed to help you prepare for various French proficiency tests like DELF, DALF, TEF, and TCF. These courses offer practice exams, targeted exercises, and test-taking strategies to improve your performance. They cover all aspects of the language, including listening, reading, writing, and speaking, ensuring that you are well-prepared to achieve your certification goals.\r\n\r\nHow do online French lessons enhance learning compared to traditional methods?\r\n\r\nFrench lessons online provide interactive and multimedia-rich content, which can be more engaging than traditional classroom methods. They offer flexibility, allowing you to learn at your own pace and revisit challenging topics as needed.\r\n",image:"https://dev.yunolearning.com/wp-content/uploads/2024/06/French-category-image-768x432.png",url:"https://dev.yunolearning.com/./french/",slug:"french"},{id:4722,name:"PTE",is_checked:!1,excerpt:"Ready to conquer the PTE exam? Grasp exam formats, excel in listening, speaking, reading, writing. Get started and succeed in your PTE journey",desciption:"The full form of PTE is the Pearson Test of English. This test is owned by Pearson Group. It is recognized as one of the preferred choices by educational institutes across the world. PTE exam scores are recognized in various regions, including Asia, Europe, the USA, Canada, Australia, and New Zealand. PTE offers the advantage of rapid score reporting, with results typically available within 48 hours of completing the exam. Additionally, being a computer-based test, PTE minimizes the potential for errors.\r\n\r\nThe PTE Academic score report comprises two types of scoring: the overall score and communicative skills scores in listening, reading,  speaking, and writing.\r\nThe overall score, which falls within the range of 10-90 points, reflects the candidate's performance across all test sections, determined by their responses to various exam tasks. If you are preparing for the PTE exam, don't forget to check out the PTE exam date 2023 in advance.",image:"https://dev.yunolearning.com/wp-content/uploads/2022/09/3_PTE-768x511.jpg",url:"https://dev.yunolearning.com/./pte/",slug:"pte"},{id:5227,name:"TOEFL",is_checked:!1,excerpt:"TOEFL IBT made easy! Access expert strategies for reading, listening, writing, and speaking. Begin your journey to TOEFL success now. Let's excel today!",desciption:'"Embark on a comprehensive live, online TOEFL preparation journey with our team of expert trainers, designed to help you achieve your desired score without leaving the comfort of your home. Our personalized and interactive approach covers all test sections, including listening, reading, writing, and speaking. Engage in an array of learning activities, practice tests, and targeted exercises to enhance your language skills and master the strategies essential for success.\r\n\r\nReceive continuous feedback, support, and guidance to identify your strengths and weaknesses, making improvement a priority. Our flexible course scheduling accommodates your busy lifestyle, while our small group sizes ensure individualized attention and customized learning experiences. In addition, benefit from one-on-one sessions with trainers to address doubts and hone your speaking skills.\r\n\r\nWith a stable internet connection and a laptop or desktop, you can access our cutting-edge platform and resources that foster a dynamic and engaging learning environment. Trust our expert trainers to help you reach your TOEFL goals from the convenience and safety of your own home."\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t',image:"https://dev.yunolearning.com/wp-content/uploads/2023/05/7_TOEFL Category Page-768x512.jpeg",url:"https://dev.yunolearning.com/./toefl/",slug:"toefl"},{id:3060,name:"IELTS",is_checked:!1,excerpt:"Get top-notch IELTS online coaching! Achieve your goals with expert guidance. Enroll now with the best coaching center for IELTS.",desciption:"<h2>How to prepare for IELTS</h2><p>\r\nIELTS, the International English Language Testing System, is one of the major English tests in the world. One needs it if one has to study, work, or settle down in an English-speaking country. It evaluates one’s level of English by testing one in four skills of the language – Reading, Writing, Listening, and Speaking. One gets a band score based on one’s performance. There are two versions of it - IELTS Academic and IELTS General Training. You should take the IELTS Academic. Both can be either pen-and-paper tests or computer-based. Both can be either pen-and-paper tests or computer-based.</p><p>One gets a score of 9 if one is an excellent user. According to the level of one’s English in a particular skill, one can get 8.5, 8, 7.5, 7, 6.5, and so on. Therefore, one should know what band score one requires and whether one has to take the IELTS Academic or IELTS General exam. The IELTS Academic is taken if one wishes to go abroad to pursue undergraduate or higher education, and the IELTS General Training is taken if one wants to emigrate or get a work visa to an English-speaking country.</p><p>Before taking the test, one should check one’s readiness for it through live classes and prepare accordingly. If the level of English is not what is needed to get the desired score, then one must work on grammar first.</p><p>The IELTS exam tests four skills - Listening, Reading, Writing and Speaking. Listening, Reading, and Writing tests are taken together, whereas the Speaking test is a one-to-one interaction with the examiner. It can be on the same day or one or two days before or after the other three. The Listening test lasts for 30 minutes, the Reading and Writing tests for 60 minutes each, and the Speaking test lasts for 11 to 15 minutes.</p><p>Before taking the test one must become familiar with the question types of each module and the strategy to score a higher band score in each. Seeking help from the Best IELTS Online Coaching can be highly beneficial in this case.</p>",image:"https://dev.yunolearning.com/wp-content/uploads/2020/10/IELTS.jpg",url:"https://dev.yunolearning.com/./ielts/",slug:"ielts"},{id:3084,name:"English Speaking",is_checked:!1,excerpt:"Enhance speaking skills with our online English classes. Join live coaching sessions for effective training! Enroll with confidence",desciption:"<h2>English Speaking Online Classes</h2><p>\r\nLearn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.</p>\r\n<h2>Instructors who really care</h2><p>You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from software. You want personalized attention from your instructor. We understand that. So we have designed our Spoken English Classes Online and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors and Live English speaking classes anywhere else.</p><p>You don’t only get to practice, but you acquire English language skills for life\r\nThe way we have designed our program is that you get to practice English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our Online English speaking classes focus on four different areas: pronunciation, grammar, fluency, and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!</p>\r\n<h2>So immersive that you’d want more classes</h2>\r\n<p>We believe that there’s no better way to learn than from real instructors. However, instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in English coaching classes online and the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% Satisfaction</p>\r\n<h2>Affordable pricing</h2>\r\n<p>So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality.</p>",image:"https://dev.yunolearning.com/wp-content/uploads/2020/10/EnglishSpeaking.jpg",url:"https://dev.yunolearning.com/./english-speaking/",slug:"english-speaking"},{id:5227,name:"TOEFL",is_checked:!1,excerpt:"TOEFL IBT made easy! Access expert strategies for reading, listening, writing, and speaking. Begin your journey to TOEFL success now. Let's excel today!",desciption:'"Embark on a comprehensive live, online TOEFL preparation journey with our team of expert trainers, designed to help you achieve your desired score without leaving the comfort of your home. Our personalized and interactive approach covers all test sections, including listening, reading, writing, and speaking. Engage in an array of learning activities, practice tests, and targeted exercises to enhance your language skills and master the strategies essential for success.\r\n\r\nReceive continuous feedback, support, and guidance to identify your strengths and weaknesses, making improvement a priority. Our flexible course scheduling accommodates your busy lifestyle, while our small group sizes ensure individualized attention and customized learning experiences. In addition, benefit from one-on-one sessions with trainers to address doubts and hone your speaking skills.\r\n\r\nWith a stable internet connection and a laptop or desktop, you can access our cutting-edge platform and resources that foster a dynamic and engaging learning environment. Trust our expert trainers to help you reach your TOEFL goals from the convenience and safety of your own home."\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t',image:"https://dev.yunolearning.com/wp-content/uploads/2023/05/7_TOEFL Category Page-768x512.jpeg",url:"https://dev.yunolearning.com/./toefl/",slug:"toefl"},{id:3060,name:"IELTS",is_checked:!1,excerpt:"Get top-notch IELTS online coaching! Achieve your goals with expert guidance. Enroll now with the best coaching center for IELTS.",desciption:"<h2>How to prepare for IELTS</h2><p>\r\nIELTS, the International English Language Testing System, is one of the major English tests in the world. One needs it if one has to study, work, or settle down in an English-speaking country. It evaluates one’s level of English by testing one in four skills of the language – Reading, Writing, Listening, and Speaking. One gets a band score based on one’s performance. There are two versions of it - IELTS Academic and IELTS General Training. You should take the IELTS Academic. Both can be either pen-and-paper tests or computer-based. Both can be either pen-and-paper tests or computer-based.</p><p>One gets a score of 9 if one is an excellent user. According to the level of one’s English in a particular skill, one can get 8.5, 8, 7.5, 7, 6.5, and so on. Therefore, one should know what band score one requires and whether one has to take the IELTS Academic or IELTS General exam. The IELTS Academic is taken if one wishes to go abroad to pursue undergraduate or higher education, and the IELTS General Training is taken if one wants to emigrate or get a work visa to an English-speaking country.</p><p>Before taking the test, one should check one’s readiness for it through live classes and prepare accordingly. If the level of English is not what is needed to get the desired score, then one must work on grammar first.</p><p>The IELTS exam tests four skills - Listening, Reading, Writing and Speaking. Listening, Reading, and Writing tests are taken together, whereas the Speaking test is a one-to-one interaction with the examiner. It can be on the same day or one or two days before or after the other three. The Listening test lasts for 30 minutes, the Reading and Writing tests for 60 minutes each, and the Speaking test lasts for 11 to 15 minutes.</p><p>Before taking the test one must become familiar with the question types of each module and the strategy to score a higher band score in each. Seeking help from the Best IELTS Online Coaching can be highly beneficial in this case.</p>",image:"https://dev.yunolearning.com/wp-content/uploads/2020/10/IELTS.jpg",url:"https://dev.yunolearning.com/./ielts/",slug:"ielts"},{id:3084,name:"English Speaking",is_checked:!1,excerpt:"Enhance speaking skills with our online English classes. Join live coaching sessions for effective training! Enroll with confidence",desciption:"<h2>English Speaking Online Classes</h2><p>\r\nLearn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.</p>\r\n<h2>Instructors who really care</h2><p>You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from software. You want personalized attention from your instructor. We understand that. So we have designed our Spoken English Classes Online and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors and Live English speaking classes anywhere else.</p><p>You don’t only get to practice, but you acquire English language skills for life\r\nThe way we have designed our program is that you get to practice English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our Online English speaking classes focus on four different areas: pronunciation, grammar, fluency, and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!</p>\r\n<h2>So immersive that you’d want more classes</h2>\r\n<p>We believe that there’s no better way to learn than from real instructors. However, instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in English coaching classes online and the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% Satisfaction</p>\r\n<h2>Affordable pricing</h2>\r\n<p>So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality.</p>",image:"https://dev.yunolearning.com/wp-content/uploads/2020/10/EnglishSpeaking.jpg",url:"https://dev.yunolearning.com/./english-speaking/",slug:"english-speaking"},{id:5227,name:"TOEFL",is_checked:!1,excerpt:"TOEFL IBT made easy! Access expert strategies for reading, listening, writing, and speaking. Begin your journey to TOEFL success now. Let's excel today!",desciption:'"Embark on a comprehensive live, online TOEFL preparation journey with our team of expert trainers, designed to help you achieve your desired score without leaving the comfort of your home. Our personalized and interactive approach covers all test sections, including listening, reading, writing, and speaking. Engage in an array of learning activities, practice tests, and targeted exercises to enhance your language skills and master the strategies essential for success.\r\n\r\nReceive continuous feedback, support, and guidance to identify your strengths and weaknesses, making improvement a priority. Our flexible course scheduling accommodates your busy lifestyle, while our small group sizes ensure individualized attention and customized learning experiences. In addition, benefit from one-on-one sessions with trainers to address doubts and hone your speaking skills.\r\n\r\nWith a stable internet connection and a laptop or desktop, you can access our cutting-edge platform and resources that foster a dynamic and engaging learning environment. Trust our expert trainers to help you reach your TOEFL goals from the convenience and safety of your own home."\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t',image:"https://dev.yunolearning.com/wp-content/uploads/2023/05/7_TOEFL Category Page-768x512.jpeg",url:"https://dev.yunolearning.com/./toefl/",slug:"toefl"},{id:3060,name:"IELTS",is_checked:!1,excerpt:"Get top-notch IELTS online coaching! Achieve your goals with expert guidance. Enroll now with the best coaching center for IELTS.",desciption:"<h2>How to prepare for IELTS</h2><p>\r\nIELTS, the International English Language Testing System, is one of the major English tests in the world. One needs it if one has to study, work, or settle down in an English-speaking country. It evaluates one’s level of English by testing one in four skills of the language – Reading, Writing, Listening, and Speaking. One gets a band score based on one’s performance. There are two versions of it - IELTS Academic and IELTS General Training. You should take the IELTS Academic. Both can be either pen-and-paper tests or computer-based. Both can be either pen-and-paper tests or computer-based.</p><p>One gets a score of 9 if one is an excellent user. According to the level of one’s English in a particular skill, one can get 8.5, 8, 7.5, 7, 6.5, and so on. Therefore, one should know what band score one requires and whether one has to take the IELTS Academic or IELTS General exam. The IELTS Academic is taken if one wishes to go abroad to pursue undergraduate or higher education, and the IELTS General Training is taken if one wants to emigrate or get a work visa to an English-speaking country.</p><p>Before taking the test, one should check one’s readiness for it through live classes and prepare accordingly. If the level of English is not what is needed to get the desired score, then one must work on grammar first.</p><p>The IELTS exam tests four skills - Listening, Reading, Writing and Speaking. Listening, Reading, and Writing tests are taken together, whereas the Speaking test is a one-to-one interaction with the examiner. It can be on the same day or one or two days before or after the other three. The Listening test lasts for 30 minutes, the Reading and Writing tests for 60 minutes each, and the Speaking test lasts for 11 to 15 minutes.</p><p>Before taking the test one must become familiar with the question types of each module and the strategy to score a higher band score in each. Seeking help from the Best IELTS Online Coaching can be highly beneficial in this case.</p>",image:"https://dev.yunolearning.com/wp-content/uploads/2020/10/IELTS.jpg",url:"https://dev.yunolearning.com/./ielts/",slug:"ielts"},{id:3084,name:"English Speaking",is_checked:!1,excerpt:"Enhance speaking skills with our online English classes. Join live coaching sessions for effective training! Enroll with confidence",desciption:"<h2>English Speaking Online Classes</h2><p>\r\nLearn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.</p>\r\n<h2>Instructors who really care</h2><p>You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from software. You want personalized attention from your instructor. We understand that. So we have designed our Spoken English Classes Online and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors and Live English speaking classes anywhere else.</p><p>You don’t only get to practice, but you acquire English language skills for life\r\nThe way we have designed our program is that you get to practice English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our Online English speaking classes focus on four different areas: pronunciation, grammar, fluency, and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!</p>\r\n<h2>So immersive that you’d want more classes</h2>\r\n<p>We believe that there’s no better way to learn than from real instructors. However, instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in English coaching classes online and the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% Satisfaction</p>\r\n<h2>Affordable pricing</h2>\r\n<p>So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality.</p>",image:"https://dev.yunolearning.com/wp-content/uploads/2020/10/EnglishSpeaking.jpg",url:"https://dev.yunolearning.com/./english-speaking/",slug:"english-speaking"}])},fetchCategories(){const e={apiURL:YUNOCommon.config.categoriesAPi("?isAll=true"),module:"gotData",store:"categoryList",callback:!0,addToModule:!1,callbackFunc:e=>this.gotCategories(e)};this.$store.dispatch("fetchData",e)},initializeScroll(){const e=this.$el.querySelector(".categories");e&&(this.scrollState.totalItems=this.categoryList.data.length,e.addEventListener("scroll",this.updateButtonStates),this.updateButtonStates())},updateButtonStates(){const e=this.$el.querySelector(".prev"),t=this.$el.querySelector(".next"),n=this.$el.querySelector(".categories");if(e&&t&&n){const i=n.scrollWidth>n.clientWidth;e.disabled=!i||n.scrollLeft<=0;const a=n.scrollWidth-n.clientWidth;t.disabled=!i||Math.ceil(n.scrollLeft)>=a}},handlePrevClick(){const e=this.$el.querySelector(".categories");if(e){const t=Math.min(this.scrollState.scrollAmount,e.scrollLeft);e.scrollBy({left:-t,behavior:"smooth"})}},handleNextClick(){const e=this.$el.querySelector(".categories");if(e){const t=e.scrollWidth-e.clientWidth-e.scrollLeft,n=Math.min(this.scrollState.scrollAmount,t);e.scrollBy({left:n,behavior:"smooth"})}},checkScrollable(){const e=this.$el?.querySelector(".categories");e&&(this.scrollState.isScrollable=e.scrollWidth>e.clientWidth)}}});const YUNOCategories=(jQuery,{categories:function(){Vue.component("yuno-categories",{props:["data"],template:'\n                <section id="yunoCategories" class="yunoCategories">\n                    <div class="container">\n                        <h1 class="sectionTitle">{{data.title}}</h1>\n                        <div class="row categoryWrapper">\n                            <div \n                                v-for="(category, categoryIndex) in data.data.slice(0, 3)"\n                                :key="categoryIndex" \n                                class="col-12 col-md-12"\n                                :class="setColumns">\n                                <article class="categoryCard">\n                                    <figure class="cardImg">\n                                        <img width="348" height="196" :src="category.image + imgParameter" :alt="category.name">\n                                    </figure>\n                                    <div class="cardBody">\n                                        <h2 class="cardTitle">{{category.name}}</h2>\n                                        <p class="cardDescription">{{setDescription(category.desciption)}}</p>\n                                    </div>\n                                    <div class="cardFooter">\n                                        <b-button tag="a"\n                                            :href="category.url"\n                                            class="yunoPrimaryCTA wired small">\n                                            <span class="makeIndent">To explore our {{category.name}} channel </span>Learn more\n                                        </b-button>\n                                    </div>\n                                </article>\n                            </div>\n                        </div>\n                    </div>\n                </section>\n            ',computed:{imgParameter:()=>YUNOCommon.config.addVerion(!0),setColumns:{get(){let e="";switch(this.$props.data.data.length){case 2:e="col-lg-6";break;case 3:case 4:e="col-lg-4";break;default:this.$props.data.data.length>4&&(e="col-lg-4")}return e}}},methods:{removeTagsAndTruncate(e,t=200){const n=e.replace(/<\/[^>]+>/g," ").replace(/<[^>]+>/g,"");return n.length>t?n.substr(0,t)+"...":n},setDescription(e){return this.removeTagsAndTruncate(e)}}})}}),YUNOCarouselList=(jQuery,{carouselList:function(){Vue.component("yuno-carousel-list",{props:["data"],template:'\n                <section id="yunoCarouselList" class="yunoCarouselList">\n                    <div class="container">\n                        <h2 class="sectionTitle">{{data.title}}</h2>\n                        <div class="carouselListControls" id="carouselListControls">\n                            <button type="button" data-controls="prev" tabindex="-1" aria-controls="tns1"><i class="fa fa-long-arrow-left" aria-hidden="true"></i></button>\n                            <button type="button" data-controls="next" tabindex="-1" aria-controls="tns1"><i class="fa fa-long-arrow-right" aria-hidden="true"></i></button>\n                        </div>\n                        <div id="carouselList" class="carouselList">\n                            <div \n                                class="carouselCard"\n                                v-for="(item, carouselIndex) in data.data"\n                                :key="carouselIndex">\n                                <div class="innerWrap">\n                                    <figure class="cardImg">\n                                        <img width="253" height="140" :src="item.MediaURL + imgParameter" :alt="item.ProductTitle">\n                                    </figure>\n                                    <div class="cardBody">\n                                        <h3 class="cardTitle"><span v-html="item.ProductTitle"></span></h3>\n                                        <p class="cardPrice">&#8377; {{item.UnitPrice}}</p>\n                                    </div>\n                                    <div class="cardFooter">\n                                        <a :href="item.CourseLink">View Details</a>\n                                    </div>\n                                </div>  \n                            </div>\n                        </div>\n                    </div>\n                </section>\n            ',data:()=>({}),computed:{imgParameter:()=>YUNOCommon.config.addVerion(!0)},mounted(){tns({container:"#carouselList",controlsContainer:"#carouselListControls",loop:!1,responsive:{500:{items:1},768:{items:2},992:{items:3},1200:{items:4}},swipeAngle:!1,speed:400,nav:!1,mouseDrag:!0,nonce:yunoNonce})}})}}),YUNOInstructorList=(jQuery,{instructorList:function(){Vue.component("yuno-instructor-list",{props:["data"],template:'\n                <div>\n                    <section id="yunoInstructorList" class="yunoInstructorList">\n                        <div class="container">\n                            <h2 class="sectionTitle">{{data.title}}</h2>\n                            <p class="sectionDes">{{data.description}}</p>\n                            <div class="sliderWrapper">\n                                <ul id="instructorCarousel" class="userList">\n                                    <li \n                                        class="item"\n                                        v-for="(list, instructorIndex) in data.data"\n                                        :key="instructorIndex">\n                                        <a :href="list.url" target="_blank">\n                                            <figure class="userImg">\n                                                <img :src="list.image + imgParameter" :alt="list.name">\n                                                <figcaption>\n                                                    <h3 class="username">{{list.name}}</h3>\n                                                    <h4 class="experience" v-if="false">{{list.experience}}</h4>\n                                                    <div class="stars">\n                                                        <star-rating :rating="Number(list.rating)" active-color="#F9B600" :read-only="true" :round-start-rating="false" :star-size="18" :show-rating="false"></star-rating>\n                                                        <div class="vue-star-rating-rating-text" v-if="list.rating !== \'0\'">{{ list.rating }}</div>\n                                                    </div>\n                                                </figcaption>\n                                            </figure>\n                                            <p class="description" v-if="list.about !== \'\'">{{ getAbout(list.about) + \'...\' }}</p>\n                                        </a>\n                                    </li>\n                                </ul>\n                                <div class="carouselListControls">\n                                    <button class="prev" type="button" data-controls="prev" tabindex="-1" aria-controls="tns1">\n                                        <span class="material-icons-outlined">chevron_left</span>\n                                    </button>\n                                    <button class="next" type="button" data-controls="next" tabindex="-1" aria-controls="tns1">\n                                        <span class="material-icons-outlined">chevron_right</span>\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n                    </section>\n                </div> \n            ',data:()=>({}),computed:{imgParameter:()=>YUNOCommon.config.addVerion(!0)},mounted(){this.initSlider()},methods:{getAbout:e=>YUNOCommon.removeTagsFromString(e).substring(0,100),initSlider(){tns({container:"#instructorCarousel",controlsContainer:".carouselListControls",loop:!0,responsive:{500:{items:1},768:{items:2},992:{items:3},1200:{items:3}},swipeAngle:!1,speed:400,nav:!1,mouseDrag:!0,autoplay:!0,autoplayTimeout:6e3,nonce:yunoNonce})}}})}});Vue.component("yuno-partner-list",{props:["data"],template:'\n        <div>\n            <section id="yunoPartnerList" class="yunoPartnerList">\n                <div class="container">\n                    <h2 class="sectionTitle">{{data.title}}</h2>\n                    <div class="sliderWrapper">\n                        <ul id="partnerCarousel" class="userList">\n                            <li \n                                class="item"\n                                v-for="(list, i) in data.items"\n                                :key="i"\n                                >\n                                    <figure class="imgWrapper">\n                                        <img :src="list.image + imgParameter" :alt="list.name">\n                                    </figure>\n                            </li>\n                        </ul>\n                        <div id="partnerCarouselControls" class="partnerCarouselControls">\n                            <button class="prev" type="button" data-controls="prev" tabindex="-1" aria-controls="tns1">\n                                <span class="material-icons-outlined">chevron_left</span>\n                            </button>\n                            <button class="next" type="button" data-controls="next" tabindex="-1" aria-controls="tns1">\n                                <span class="material-icons-outlined">chevron_right</span>\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </section>\n        </div> \n    ',data:()=>({}),computed:{imgParameter:()=>YUNOCommon.config.addVerion(!0)},mounted(){this.initSlider()},methods:{getAbout:e=>YUNOCommon.removeTagsFromString(e).substring(0,100),initSlider(){tns({container:"#partnerCarousel",controlsContainer:".partnerCarouselControls",loop:!0,responsive:{500:{items:1},768:{items:2},992:{items:3},1200:{items:3}},swipeAngle:!1,speed:400,nav:!1,mouseDrag:!0,autoplay:!0,autoplayTimeout:6e3,nonce:yunoNonce,nonce:yunoNonce})}}});const YUNOWhyLearn=(jQuery,{whyLearn:function(){Vue.component("yuno-why-learn",{props:["data"],template:'\n                <section id="yunoWhyLearn" class="yunoWhyLearn">\n                    <div class="container">\n                        <h2 class="sectionTitle" :class="[data.description !== undefined ? \'hasDescription\' : \'\']">{{data.title}}</h2>\n                        <p v-if="data.description !== undefined" class="description">{{ data.description }}</p>\n                        <ul class="yunoTiles">\n                            <li \n                                v-for="(tile, tileIndex) in data.list"\n                                :key="tileIndex"\n                                :class="tile.icon">\n                                <div class="innerWrapper">\n                                    <span :class="[tile.iconType, \'icon\']">{{tile.icon}}</span>\n                                    <div class="info">\n                                        <h3>{{tile.title}}</h3>\n                                        <p>{{tile.description}}</p>\n                                    </div>\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                </section>\n            ',data:()=>({}),mounted(){}})}}),YUNOHomeHero=(jQuery,{homeHero:function(){Vue.component("yuno-home-hero",{props:["data","imageOnly","customClass"],template:'\n                <section id="homeHeroCarousel" class="homeHeroCarousel">\n                    <template v-if="imageOnly !== undefined && imageOnly">\n                        <div \n                            v-for="(slide, slideIndex) in data.list"\n                            :key="slideIndex"\n                            class="yunoHero noCaption"\n                            :class="customClass !== undefined ? customClass : \'noClass\'"\n                            :style="{backgroundImage: \'url(\' + slide.img + imgParameter + \')\'}">\n                        </div>\n                    </template>\n                    <template v-else>\n                        <div\n                            v-for="(slide, slideIndex) in data.list" \n                            :key="slideIndex"\n                            class="yunoHero" \n                            :style="{backgroundImage: \'url(\' + slide.img + imgParameter + \')\'}">\n                            <div class="container">\n                                <div class="row">\n                                    <div class="col-12 col-md-7 col-lg-7">\n                                        <div class="heroCopy">\n                                            <small class="helper">{{slide.category}}</small>\n                                            <h2 class="title">{{slide.title}}</h2>\n                                            <h3 class="subTitle">{{slide.subTitle}}</h3>\n                                            <b-button tag="a"\n                                                :href="slide.ctaURL"\n                                                class="yunoSecondaryCTA">\n                                                {{slide.cta}}\n                                            </b-button>  \n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </template>\n                </section>\n            ',data:()=>({}),computed:{imgParameter:()=>YUNOCommon.config.addVerion(!0)},async created(){},mounted(){tns({container:"#homeHeroCarousel",loop:!0,responsive:{500:{items:1},768:{items:1},992:{items:1},1200:{items:1}},swipeAngle:!1,speed:400,nav:!1,mouseDrag:!0,autoplay:!0,nav:!1,navPosition:"bottom",autoplayTimeout:6e3,nonce:yunoNonce})},methods:{}})}});Vue.component("yuno-app-banner",{props:{data:{type:Object,required:!0}},template:'\n        <div class="yunoAppV2">\n            <div class="container">\n                <figure>\n                    <div class="imgWrapper">\n                        <img :src="data.screenshot" :alt="data.title" width="349" height="374">\n                    </div>\n                    <figcaption>\n                        <div class="wrapper">\n                            <h2 class="largestTitle">{{ data.title }}</h2>\n                            <p class="smallerCaption">{{ data.subtitle }}</p>\n                            <ul class="list-unstyled">\n                                <li>\n                                    <img :src="data.qrCode" alt="Yuno Android APP QR Code" width="96" height="96">\n                                </li>\n                                <li>\n                                    <a :href="data.androidApp" target="_blank"><img :src="data.appCTA" alt="Yuno Android APP Store" width="180" height="52"></a>\n                                </li>                                       \n                            </ul>\n                        </div>\n                    </figcaption>\n                </figure>\n            </div>\n        </div>\n    ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}});const YUNOHome=(jQuery,{home:function(){YUNOCategories.categories(),YUNOCarouselList.carouselList(),YUNOInstructorList.instructorList(),YUNOWhyLearn.whyLearn(),YUNOHomeHero.homeHero(),YUNOReviewCard.reviewCard(),YUNOInsightsCard.insightsCard(),YUNOFeaturesList.featuresList(),Vue.component("yuno-home",{template:'\n                <yuno-page-grid\n                    @onUserInfo="onUserInfo"\n                >\n                    <template v-slot:main>\n                        <yuno-hero-banner-v2 v-if="!user.isLoggedin" :data="heroData"></yuno-hero-banner-v2>\n                        <yuno-home-hero v-if="user.isLoggedin && userInfo.error === null" :data="homeHero"></yuno-home-hero>\n                        <div class="container" v-if="instructorReviews.error === null">\n                            <yuno-review-card :dataid="0" :options="reviewsOptions"></yuno-review-card>\n                        </div>\n                        <yuno-partner-list :data="partnerList"></yuno-partner-list>\n                        <yuno-insights-card v-if="false" :data="insightsCards"></yuno-insights-card>\n                        <template v-if="isOtherModuleLoading">\n                            <section id="yunoCategories" class="yunoCategories">\n                                <div class="container">\n                                    <div class="row categoryWrapper">\n                                        <div \n                                            v-for="(category, categoryIndex) in 3"\n                                            :key="categoryIndex" \n                                            class="col-12 col-md-4"\n                                        >\n                                            <article class="categoryCard">\n                                                <figure class="cardImg">\n                                                    <b-skeleton height="196px"></b-skeleton>\n                                                </figure>\n                                                <div class="cardBody">\n                                                    <h2 class="cardTitle"><b-skeleton active></b-skeleton></h2>\n                                                </div>\n                                            </article>\n                                        </div>\n                                    </div>\n                                </div>\n                            </section>\n                        </template>\n                        <template v-if="isOtherModuleReady">\n                            <yuno-categories :data="homeCategories"></yuno-categories>\n                            <yuno-carousel-list v-if="false" :data="homeCarouselList"></yuno-carousel-list>\n                            <yuno-instructor-list :data="instructorslList"></yuno-instructor-list>\n                            <yuno-features-list :data="whyLiveClasses"></yuno-features-list>\n                            <yuno-app-banner :data="appBanner"></yuno-app-banner>\n                            <yuno-why-learn :data="whyLearn"></yuno-why-learn>\n                        </template>\n                    </template>\n                </yuno-page-grid>\n            ',data(){return{marginTop:15,isMiniSidebar:!1,appBanner:{screenshot:this.$store.state.themeURL+"/assets/images/yunoApp.webp",qrCode:this.$store.state.themeURL+"/assets/images/appQR.webp",appCTA:this.$store.state.themeURL+"/assets/images/googlePlayCTA.webp",androidApp:"https://play.google.com/store/apps/details?id=com.yunolearning.learn&pli=1",title:"Learn Anytime, Anywhere with the Yuno App",subtitle:"Available on Android"},partnerList:{title:"Trusted by Leading Companies",items:[{image:this.$store.state.themeURL+"/assets/images/image_2023_04_03T10_34_51_664Z.png",name:"Upgrad"},{image:this.$store.state.themeURL+"/assets/images/image_2023_04_03T10_34_20_504Z.png",name:"Imarticus"},{image:this.$store.state.themeURL+"/assets/images/image_2023_04_03T10_33_53_966Z.png",name:"EagleiAdvisor"},{image:this.$store.state.themeURL+"/assets/images/image_2023_04_03T10_36_45_965Z.png",name:"Code Quotient"},{image:this.$store.state.themeURL+"/assets/images/logo-xornor.png",name:"Xornor"},{image:this.$store.state.themeURL+"/assets/images/ishyaLogo.jpg",name:"Ishya Foundation"},{image:this.$store.state.themeURL+"/assets/images/aaryanOverseas.png",name:"Aaryan"}]},searchbarOptions:{isActive:!0,data:[]},whyLiveClasses:{title:"Benefits of Personalized Live Classes",cards:[{img:this.$store.state.themeURL+"/assets/images/Small-group-classes.jpg",title:"Small group classes",customClass:"hasRightBorder",list:["You can choose your instructor and the specific batch time that fits your schedule","Because the classes are in really small groups, you will receive personalised attention from your instructor","Small group classes are the most affordable way for you to access personalized learning"]},{img:this.$store.state.themeURL+"/assets/images/1-to-1-Classes.jpg",title:"1 to 1 Classes",customClass:"",list:["You can choose your instructor and the specific class time that fits your schedule","Since you are the only student in the class, you can work with your instructor to decide your class schedule","Although more expensive than group classes, 1-to-1 classes are the most personalized option on Yuno"]}]},insightsCards:{cards:[{iconType:"material-icons-outlined",icon:"school",label:"40,000+",helper:"Students",customClass:"students"},{iconType:"material-icons-outlined",icon:"devices",label:"125,000+",helper:"live classes",customClass:"liveClasses"},{iconType:"material-icons-outlined",icon:"how_to_reg",label:"87%",helper:"attendence",customClass:"attendence"}]},reviewsOptions:{title:"Rated 5 Stars on Google",titleClass:"fatSize",type:"toprated",view:"list-view",sorting:"featured",limit:3,loadMoreParentClass:"marginBtm30",viewType:"gridView",hasShowMore:!0,hasBasedOn:!0,externalURL:"https://www.google.com/search?q=yuno+learning&sxsrf=ALiCzsaa3y8Tp4vq4iSyUOlNKIzl1srhxg%3A1665188984807&ei=eMRAY8jVMOOtqtsPwd-5sAc&ved=0ahUKEwjI8bCvsM_6AhXjlmoFHcFvDnYQ4dUDCA4&uact=5&oq=yuno+learning&gs_lcp=Cgdnd3Mtd2l6EAMyBAgjECcyBAgjECcyBQgAEIAEMgUIABCABDIGCAAQHhAWMgYIABAeEBYyBggAEB4QFjIFCAAQhgM6CggAEEcQ1gQQsAM6DQgAEEcQ1gQQsAMQyQNKBAhBGABKBAhGGABQxQNYxQNgpAdoAnABeACAAa4BiAGuAZIBAzAuMZgBAKABAcgBCMABAQ&sclient=gws-wiz#lrd=0x390fedb7a6342f61:0xd0865bda29b46987,1,,,",overallStars:!0,customClass:"yunoHomeReviews"},heroData:{title:"Learn from top-rated instructors in small groups or 1-on-1",rating:{isActive:!0,label:"Rated 5-Star on Google"},bgImg:this.$store.state.themeURL+"/assets/images/herov2BG.png",categories:[{label:"English Speaking",url:"/english-speaking/"},{label:"IELTS",url:"/ielts/"},{label:"PTE",url:"/pte/"},{label:"TOEFL",url:"/toefl/"},{label:"Duolingo",url:"/duolingo/"}]},homeSignin:{category:"general",productcode:"",leadstatus:"",content_type:"",content_id:""}}},computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","footer","homeCategories","homeCarouselList","instructorslList","whyLearn","homeHero","instructorReviews","module"]),isPageLoading:{get(){return this.userInfo.loading||this.header.loading||this.footer.loading}},isPageReady:{get(){let e="";return e=this.user.isLoggedin?this.header.success&&this.footer.success&&this.userInfo.success:this.header.success&&this.footer.success,e}},isOtherModuleLoading:{get(){return this.homeCategories.loading||this.instructorslList.loading}},isOtherModuleReady:{get(){return this.homeCategories.success&&this.instructorslList.success}},isUserLoading:{get(){return this.userInfo.loading}},isUserReady:{get(){let e="";return e=!this.user.isLoggedin||this.userInfo.success,e}}},async created(){this.loginStatus(),this.emitEvents()},mounted(){},methods:{gotUserInfo(e){void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code&&e.response.data.data},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},loginStatus(){0!==Number(isLoggedIn)?(this.user.isLoggedin=!0,this.fetchUserInfo()):this.user.isLoggedin=!1},searchBar(){return!(!this.user.isLoggedin||"Learner"!==this.userRole.data)},onLogin(e){},onUserInfo(e){this.fetchCategories(),this.fetchFeaturedInstructor()},onMini(e){this.isMiniSidebar=e},onMenuLoaded(){},emitEvents(){Event.$on("checkLoggedInState",e=>{e?Event.$on("gotUserRole",(e,t)=>{Event.$on("gotUserMenu",()=>{this.fetchCategories(),this.fetchFeaturedInstructor()})}):(this.fetchCategories(),this.fetchFeaturedInstructor())})},structuredData(e){const t=document.createElement("script");t.setAttribute("type","application/ld+json");let n=document.createTextNode(JSON.stringify(e));t.appendChild(n),document.head.appendChild(t)},fetchFooter(){const e={apiURL:YUNOCommon.config.footerAPI(),module:"gotData",store:"footer",callback:!1};this.$store.dispatch("fetchData",e)},categoriesRichSnippet(){let e=this.homeCategories.data,t={"@context":"https://schema.org","@type":"Dataset",name:"Top Courses on Yuno",description:"Explore the most popular subject categories on Yuno. Each category offers multiple courses to choose from and each course consists of personalised live classes",hasPart:[],license:"https://creativecommons.org/licenses/by/4.0/",creator:{"@type":"Organization",name:"Yuno Learning"}};for(var n=0;n<e.length;n++){let i={"@type":"Dataset",url:e[n].url,name:e[n].name,description:e[n].desciption,image:e[n].image,license:"https://creativecommons.org/licenses/by/4.0/",creator:{"@type":"Organization",name:"Yuno Learning"}};t.hasPart.push(i)}this.structuredData(t)},gotCategories(e){void 0!==e.response&&void 0!==e.response.data&&e.response.data.code},fetchCategories(){const e=this,t={apiURL:YUNOCommon.config.categoriesAPi(),module:"gotData",store:"homeCategories",callback:!0,callbackFunc:function(t){return e.gotCategories(t)}};this.$store.dispatch("fetchData",t)},featuredCoursesRichSnippet(){let e=this.homeCarouselList.data,t={"@context":"https://schema.org","@type":"ItemList",name:this.homeCarouselList.title,itemListElement:[]};for(var n=0;n<e.length;n++){let i={"@type":"ListItem",position:parseInt(n+1),item:{"@type":"Product",url:e[n].CourseLink,name:e[n].ProductTitle,image:e[n].MediaURL,offers:{"@type":"Offer",url:e[n].CourseLink,priceCurrency:"INR",price:e[n].UnitPrice}}};t.itemListElement.push(i)}this.structuredData(t)},gotFeaturedCourses(e){void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code&&this.featuredCoursesRichSnippet()},fetchFeaturedCourses(){const e=this,t={apiURL:YUNOCommon.config.featuredCoursesAPi(),module:"gotData",store:"homeCarouselList",callback:!0,callbackFunc:function(t){return e.gotFeaturedCourses(t)}};this.$store.dispatch("fetchData",t)},fetchFeaturedInstructor(){const e={apiURL:YUNOCommon.config.featuredInstructorAPi("home"),module:"gotData",store:"instructorslList",callback:!1};this.$store.dispatch("fetchData",e)}}})}});