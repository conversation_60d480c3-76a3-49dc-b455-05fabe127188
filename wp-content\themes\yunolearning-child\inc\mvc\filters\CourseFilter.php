<?php
namespace V4;

/**
 * Class CourseFilter
 * Handles Course-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */

class CourseFilter extends Filter
{

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('locale');
        $this->loadModel('locale');
    }
    
   /**
     * Generates a course week duration filter based on search text and selected duration range.
     *
     * @since 1.0.0
     * @access public
     * @param string $searchText The search text to filter courses
     * @param int $weekDurationMin The minimum duration in weeks
     * @param int $weekDurationMax The maximum duration in weeks
     * @return array|false Returns a filter array with course week duration data or false if no data is found
     */
    public function generateCourseSearchWeekDurationFilter($searchText, $weekDurationMin, $weekDurationMax)
    {
        $query = [
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => !empty($searchText) ? [
                        'bool' => [
                            'must' => [
                                [
                                    'multi_match' => [
                                        'query' => $searchText,
                                        'fields' => [
                                            'data.details.title^50',
                                            'data.details.course_category.name^5',
                                            'data.details.course_category.sub_category.name^4',
                                            'data.details.course_category.sub_category.sub_category.name^3',
                                            'data.details.short_description^2',
                                            'data.details.post_description^1'
                                        ],
                                        'type' => 'phrase_prefix'
                                    ]
                                ],
                                [
                                    'term' => [
                                        'data.details.is_enable' => true
                                    ]
                                ]
                            ]
                        ]
                    ] : [
                        "match_all" => (object)[]
                    ]
                ]
            ],
            'aggs' => [
                'nested_data_details' => [
                    'nested' => [
                        'path' => 'data.details'
                    ],
                    'aggs' => [
                        'min_duration_weeks' => [
                            'min' => [
                                'field' => 'data.details.duration_weeks'
                            ]
                        ],
                        'max_duration_weeks' => [
                            'max' => [
                                'field' => 'data.details.duration_weeks'
                            ]
                        ]
                    ]
                ]
            ],
            '_source' => false
        ];

        $courseDataResponse = $this->es->customQuery($query, 'course', ['size' => 0]);

        if ($courseDataResponse['status_code'] == 200) {

            $rangeMin = $courseDataResponse['body']['aggregations']['nested_data_details']['min_duration_weeks']['value'];
            $rangeMax = $courseDataResponse['body']['aggregations']['nested_data_details']['max_duration_weeks']['value'];

            $rangeData = [
                "ui_control_type" => "SLIDER",
                "is_active" => true,
                "title" => "Total Duration",
                "placeholder" => "Select Duration",
                "tooltip" => "Course duration in weeks",
                "filter" => "week_duration",
                "selected" => ["min" => $weekDurationMin, "max" => $weekDurationMax],
                "min" => $rangeMin,
                "max" => $rangeMax
            ];

            return $rangeData;
        }

        return false;
    }

    /**
     * Generates a course price filter based on search text, price code, and price range.
     *
     * @since 1.0.0
     * @access public
     * @param string $searchText The search text to filter courses
     * @param string $priceCode The price code to filter courses
     * @param int $priceMin The minimum price
     * @param int $priceMax The maximum price
     * @return array|false Returns a filter array with course price data or false if no data is found
     */
    public function generateCourseSearchPriceFilter($searchText, $priceCode, $priceMin, $priceMax)
    {
        $currency = $this->loadModel('locale')->getCurrency($priceCode);

        $query = [
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => !empty($searchText) ? [
                        'bool' => [
                            'must' => [
                                [
                                    'multi_match' => [
                                        'query' => $searchText,
                                        'fields' => [
                                            'data.details.title^50',
                                            'data.details.course_category.name^5',
                                            'data.details.course_category.sub_category.name^4',
                                            'data.details.course_category.sub_category.sub_category.name^3',
                                            'data.details.short_description^2',
                                            'data.details.post_description^1'
                                        ],
                                        'type' => 'phrase_prefix'
                                    ]
                                ],
                                [
                                    'term' => [
                                        'data.details.is_enable' => true
                                    ]
                                ]
                            ]
                        ]
                    ] : [
                        "match_all" => (object)[]
                    ]
                ]
            ],
            'aggs' => [
                'nested_data_details_prices' => [
                    'nested' => [
                        'path' => 'data.details.prices'
                    ],
                    'aggs' => [
                        'prices_by_currency' => [
                            'terms' => [
                                'field' => 'data.details.prices.code'
                            ],
                            'aggs' => [
                                'min_one_to_one_price' => [
                                    'min' => [
                                        'field' => 'data.details.prices.1_to_1_price'
                                    ]
                                ],
                                'max_one_to_one_price' => [
                                    'max' => [
                                        'field' => 'data.details.prices.1_to_1_price'
                                    ]
                                ],
                                'min_group_price' => [
                                    'min' => [
                                        'field' => 'data.details.prices.group_price'
                                    ]
                                ],
                                'max_group_price' => [
                                    'max' => [
                                        'field' => 'data.details.prices.group_price'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'nested_data_details_main' => [
                    'nested' => [
                        'path' => 'data.details'
                    ],
                    'aggs' => [
                        'min_one_to_one_price' => [
                            'min' => [
                                'field' => 'data.details.one_to_one_price'
                            ]
                        ],
                        'max_one_to_one_price' => [
                            'max' => [
                                'field' => 'data.details.one_to_one_price'
                            ]
                        ],
                        'min_group_price' => [
                            'min' => [
                                'field' => 'data.details.group_price'
                            ]
                        ],
                        'max_group_price' => [
                            'max' => [
                                'field' => 'data.details.group_price'
                            ]
                        ]
                    ]
                ]
            ],
            '_source' => false
        ];

        $courseDataResponse = $this->es->customQuery($query, 'course', ['size' => 0]);

        if ($courseDataResponse['status_code'] == 200) {
            $aggregations = $courseDataResponse['body']['aggregations'];
            $mainPrices = $aggregations['nested_data_details_main'];
            $currencyPrices = $aggregations['nested_data_details_prices']['prices_by_currency']['buckets'];

            // Find the selected currency's price range
            $selectedCurrencyPrices = null;
            foreach ($currencyPrices as $currencyPrice) {
                if ($currencyPrice['key'] === $priceCode) {
                    $selectedCurrencyPrices = $currencyPrice;
                    break;
                }
            }

            //convert main prices to currency selected price
            $exchangeRate = $this->loadModel('locale')->getExchangeRate($priceCode);
            $mainPrices['min_one_to_one_price']['value'] = $mainPrices['min_one_to_one_price']['value'] / $exchangeRate;
            $mainPrices['max_one_to_one_price']['value'] = $mainPrices['max_one_to_one_price']['value'] / $exchangeRate;
            $mainPrices['min_group_price']['value'] = $mainPrices['min_group_price']['value'] / $exchangeRate;
            $mainPrices['max_group_price']['value'] = $mainPrices['max_group_price']['value'] / $exchangeRate;

            // Use currency-specific prices if available, otherwise fall back to main prices
            $priceRange = [
                "ui_control_type" => "SLIDER",
                "is_active" => true,
                "title" => "Price",
                "placeholder" => "Select Price",
                "tooltip" => "Course price",
                "filter" => "price",
                "selected" => [
                    "currency_code" => $priceCode,
                    "min" => $priceMin,
                    "max" => $priceMax
                ],
                "currency" => $currency,
                "min" => floor(min($mainPrices['min_one_to_one_price']['value'], $mainPrices['min_group_price']['value'], $selectedCurrencyPrices['min_one_to_one_price']['value'], $selectedCurrencyPrices['min_group_price']['value'])),
                "max" => ceil(max($mainPrices['max_one_to_one_price']['value'], $mainPrices['max_group_price']['value'], $selectedCurrencyPrices['max_one_to_one_price']['value'], $selectedCurrencyPrices['max_group_price']['value']))
            ];

            return $priceRange;
        }

        return false;
    }

    /**
     * Generates personalization filter data for courses based on search text and selected personalization.
     *
     * @since 1.0.0
     * @access public
     * @param string $searchText The search text to filter courses
     * @param string $selectedPersonalization The selected personalization type (all, one_to_one, one_to_many)
     * @return array|false Returns formatted personalization filter data or false if no data is found
     */
    public function generateCourseSearchPersonalizationFilter($personalization = [])
    {
        return [
            "ui_control_type" => "CHECKBOX",
            "is_active" => true,
            "title" => "Personalization",
            "placeholder" => "Personalization",
            "tooltip" => "Personalization",
            "filter" => "personalization",
            "multiple" => true,
            "selected" => $personalization,
            "items" => [
                /*[
                    "slug" => "all",
                    "label" => "All",
                    "filter" => "personalization",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "all_inclusive"
                    ],
                    "subtitle" => "",
                    "count" => 0 // Count will be updated dynamically by frontend
                ],*/
                [
                    "slug" => "one_to_many",
                    "label" => "Group Classes",
                    "filter" => "personalization",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "groups"
                    ],
                    "subtitle" => "You'll be among a group of learners in the live classes",
                    "count" => 0 // Count will be updated dynamically by frontend
                ],
                [
                    "slug" => "one_to_one",
                    "label" => "1-on-1 classes",
                    "filter" => "personalization",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "safety_divider"
                    ],
                    "subtitle" => "You'll be the only learner, along with your instructor, in the live classes",
                    "count" => 0 // Count will be updated dynamically by frontend
                ]
            ]
        ];
    }

    /**
     * Generates teaching mode filter data for courses.
     *
     * @since 1.0.0
     * @access public
     * @param string $teachingMode The selected teaching mode (all, online, in_person)
     * @return array Returns formatted teaching mode filter data
     */
    public function generateCourseSearchTeachingModeFilter($teachingMode = [])
    {
        return [
            "ui_control_type" => "CHECKBOX",
            "is_active" => true,
            "title" => "Teaching Mode",
            "placeholder" => "Teaching Mode",
            "tooltip" => "Teaching Mode",
            "filter" => "teaching_mode",
            "multiple" => true,
            "selected" => $teachingMode,
            "items" => [
                /*[
                    "slug" => "all",
                    "label" => "All",
                    "filter" => "teaching_mode",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "all_inclusive"
                    ],
                    "subtitle" => "",
                    "count" => 0 // Count will be updated dynamically by frontend
                ],*/
                [
                    "slug" => "online",
                    "label" => "Only online",
                    "filter" => "teaching_mode",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "desktop_windows"
                    ],
                    "subtitle" => "You will be taught online in a live class",
                    "count" => 0 // Count will be updated dynamically by frontend
                ],
                [
                    "slug" => "in-person",
                    "label" => "Only in-person",
                    "filter" => "teaching_mode",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "people_outline"
                    ],
                    "subtitle" => "You'll be taught in-person at a physical location",
                    "count" => 0 // Count will be updated dynamically by frontend
                ]
            ]
        ];
    }

    /**
     * Generates availability filter data for courses based on class time slots.
     *
     * @since 1.0.0
     * @access public
     * @param string|array $selectedTimeSlots The selected time slots (default: empty array)
     * @return array Returns formatted availability filter data
     */
    public function generateCourseSearchAvailabilityFilter($timeSlots = [])
    {
        $selectedTimeSlots = is_array($timeSlots) ? $timeSlots : [$timeSlots];

        return [
            "ui_control_type" => "CHECKBOX",
            "is_active" => true,
            "title" => "I'm available",
            "placeholder" => "Class start time between...",
            "tooltip" => "Select your preferred class time slots",
            "filter" => "availability",
            "selected" => $selectedTimeSlots,
            "multiple" => true,
            "items" => [
                [
                    "slug" => "anytime",
                    "label" => "Anytime",
                    "filter" => "availability",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "schedule"
                    ],
                    "subtitle" => "Available between 6am - 11pm",
                    "interval" => "6am - 11pm",
                    "count" => 0 // Count will be updated dynamically by frontend
                ],
                [
                    "slug" => "morning",
                    "label" => "Morning",
                    "filter" => "availability",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "wb_twilight"
                    ],
                    "subtitle" => "Available between 6am - 12pm",
                    "interval" => "6am - 12pm",
                    "count" => 0 // Count will be updated dynamically by frontend
                ],
                [
                    "slug" => "afternoon",
                    "label" => "Afternoon",
                    "filter" => "availability",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "wb_sunny"
                    ],
                    "subtitle" => "Available between 12pm - 4pm",
                    "interval" => "12pm - 4pm",
                    "count" => 0 // Count will be updated dynamically by frontend
                ],
                [
                    "slug" => "evening",
                    "label" => "Evening",
                    "filter" => "availability",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "nights_stay"
                    ],
                    "subtitle" => "Available between 4pm - 8pm",
                    "interval" => "4pm - 8pm",
                    "count" => 0 // Count will be updated dynamically by frontend
                ],
                [
                    "slug" => "night",
                    "label" => "Night",
                    "filter" => "availability",
                    "icon" => [
                        "type" => "material-icons",
                        "code" => "dark_mode"
                    ],
                    "subtitle" => "Available between 8pm - 11pm",
                    "interval" => "8pm - 11pm",
                    "count" => 0 // Count will be updated dynamically by frontend
                ]
            ]
        ];
    }

    /**
     * Generates category filter data for courses based on search text.
     * Returns a filter array containing categories.
     *
     * @since 1.0.0
     * @access public
     * @param string $searchText The search text to filter courses
     * @param array $categorieslevel1 Array of selected category IDs
     * @return array|false Returns a filter array with categories or false if no data is found
     */
    public function generateCourseSearchCategoryFilter($searchText, $parentCategories = [])
    {
        // First query to get category IDs
        $categoryQuery = [
            'size' => 0,
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => !empty($searchText) ? [
                        'bool' => [
                            'must' => [
                                [
                                    'multi_match' => [
                                        'query' => $searchText,
                                        'fields' => [
                                            'data.details.title^50',
                                            'data.details.course_category.name^5',
                                            'data.details.course_category.sub_category.name^4',
                                            'data.details.course_category.sub_category.sub_category.name^3',
                                            'data.details.short_description^2',
                                            'data.details.post_description^1'
                                        ],
                                        'type' => 'phrase_prefix'
                                    ]
                                ],
                                [
                                    'term' => [
                                        'data.details.is_enable' => true
                                    ]
                                ]
                            ]
                        ]
                    ] : [
                        "match_all" => (object)[]
                    ]
                ]
            ],
            'aggs' => [
                'unique_course_main_categories' => [
                    'nested' => [
                        'path' => 'data.details.course_category'
                    ],
                    'aggs' => [
                        'unique_main_category_ids' => [
                            'terms' => [
                                'field' => 'data.details.course_category.id',
                                'size' => 9999
                            ],
                            'aggs' => [
                                'unique_names' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.name.keyword',
                                        'size' => 9999
                                    ]
                                ],
                                'unique_slugs' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.slug',
                                        'size' => 9999
                                    ]
                                ],
                                'unique_parent_ids' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.parent_id',
                                        'size' => 9999
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            '_source' => false
        ];

        $categoryResponse = $this->es->customQuery($categoryQuery, 'course', ['size' => 0]);

        if ($categoryResponse['status_code'] != 200) {
            return false;
        }

        // Extract category IDs
        $categories = [];
        $buckets = $categoryResponse['body']['aggregations']['unique_course_main_categories']['unique_main_category_ids']['buckets'];

        foreach ($buckets as $bucket) {
            $id = $bucket['key'];
            $name = $bucket['unique_names']['buckets'][0]['key'] ?? '';
            $slug = $bucket['unique_slugs']['buckets'][0]['key'] ?? '';
            $parentId = $bucket['unique_parent_ids']['buckets'][0]['key'] ?? null;

            if (!empty($name) && $parentId !== null) {
                $categories[] = [
                    'id' => $id,
                    'slug' => $slug,
                    'name' => $name,
                    'parent_id' => $parentId,
                    'count' => $bucket['doc_count']
                ];
            }
        }

        // Return filter array
        return [
            "ui_control_type" => "RADIO",
            "is_active" => true,
            "title" => "Category",
            "placeholder" => "Select Category",
            "tooltip" => "Course category",
            "filter" => "category",
            "selected" => $parentCategories,
            "multiple" => false,
            "items" => array_map(function ($category) {
                return [
                    "id" => $category['id'],
                    "slug" => $category['slug'],
                    "label" => $category['name'],
                    "filter" => "category",
                    "count" => $category['count'],
                    "parent_id" => $category['parent_id']
                ];
            }, $categories)
        ];
    }

    /**
     * Generates category level 1 filter data for courses based on search text.
     * Returns a filter array containing level 1 categories.
     *
     * @since 1.0.0
     * @access public
     * @param string $searchText The search text to filter courses
     * @param array $categorieslevel1 Array of selected level 1 category IDs
     * @return array|false Returns a filter array with level 1 categories or false if no data is found
     */
    public function generateCourseSearchCategoryLevel1Filter($searchText, $categorieslevel1 = [])
    {
        $subcategoryQuery = [
            'size' => 0,
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => !empty($searchText) ? [
                        'bool' => [
                            'must' => [
                                [
                                    'multi_match' => [
                                        'query' => $searchText,
                                        'fields' => [
                                            'data.details.title^50',
                                            'data.details.course_category.name^5',
                                            'data.details.course_category.sub_category.name^4',
                                            'data.details.course_category.sub_category.sub_category.name^3',
                                            'data.details.short_description^2',
                                            'data.details.post_description^1'
                                        ],
                                        'type' => 'phrase_prefix'
                                    ]
                                ],
                                [
                                    'term' => [
                                        'data.details.is_enable' => true
                                    ]
                                ]
                            ]
                        ]
                    ] : [
                        "match_all" => (object)[]
                    ]
                ]
            ],
            'aggs' => [
                'unique_sub_categories' => [
                    'nested' => [
                        'path' => 'data.details.course_category.sub_category'
                    ],
                    'aggs' => [
                        'unique_sub_category_ids' => [
                            'terms' => [
                                'field' => 'data.details.course_category.sub_category.id',
                                'size' => 9999
                            ],
                            'aggs' => [
                                'sub_category_name' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.sub_category.name.keyword',
                                        'size' => 9999
                                    ]
                                ],
                                'sub_category_slug' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.sub_category.slug',
                                        'size' => 9999
                                    ]
                                ],
                                'parent_category_id' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.sub_category.parent_id',
                                        'size' => 9999
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            '_source' => false
        ];

        $subcategoryResponse = $this->es->customQuery($subcategoryQuery, 'course', ['size' => 0]);

        if ($subcategoryResponse['status_code'] != 200) {
            return false;
        }

        // Process subcategories
        $subcategories = [];
        $buckets = $subcategoryResponse['body']['aggregations']['unique_sub_categories']['unique_sub_category_ids']['buckets'];

        foreach ($buckets as $bucket) {
            $id = $bucket['key'];
            $name = $bucket['sub_category_name']['buckets'][0]['key'] ?? '';
            $slug = $bucket['sub_category_slug']['buckets'][0]['key'] ?? '';
            $parentId = $bucket['parent_category_id']['buckets'][0]['key'] ?? null;

            if (!empty($name) && $parentId !== null) {
                $subcategories[] = [
                    'id' => $id,
                    'slug' => $slug,
                    'name' => $name,
                    'parent_id' => $parentId,
                    'count' => $bucket['doc_count']
                ];
            }
        }

        // Return filter array
        return [
            "ui_control_type" => "CHECKBOX",
            "is_active" => true,
            "title" => "Category Level 1",
            "placeholder" => "Select Category Level 1",
            "tooltip" => "Select a level 1 course category",
            "filter" => "category_level_1",
            "selected" => $categorieslevel1,
            "multiple" => true,
            "items" => array_map(function ($subcategory) {
                return [
                    "id" => $subcategory['id'],
                    "slug" => $subcategory['slug'],
                    "label" => $subcategory['name'],
                    "filter" => "category_level_1",
                    "count" => $subcategory['count'],
                    "parent_id" => $subcategory['parent_id']
                ];
            }, $subcategories)
        ];
    }

    /**
     * Generates category level 2 filter data for courses based on search text.
     * Returns a filter array containing level 2 categories (subcategories).
     *
     * @since 1.0.0
     * @access public
     * @param string $searchText The search text to filter courses
     * @param array $categoryLevel2IDs Array of selected level 2 category IDs
     * @return array|false Returns a filter array with level 2 categories or false if no data is found
     */
    public function generateCourseSearchCategoryLevel2Filter($searchText, $categoryLevel2IDs = [])
    {
        $subsubcategoryQuery = [
            'size' => 0,
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => !empty($searchText) ? [
                        'bool' => [
                            'must' => [
                                [
                                    'multi_match' => [
                                        'query' => $searchText,
                                        'fields' => [
                                            'data.details.title^50',
                                            'data.details.course_category.name^5',
                                            'data.details.course_category.sub_category.name^4',
                                            'data.details.course_category.sub_category.sub_category.name^3',
                                            'data.details.short_description^2',
                                            'data.details.post_description^1'
                                        ],
                                        'type' => 'phrase_prefix'
                                    ]
                                ],
                                [
                                    'term' => [
                                        'data.details.is_enable' => true
                                    ]
                                ]
                            ]
                        ]
                    ] : [
                        "match_all" => (object)[]
                    ]
                ]
            ],
            'aggs' => [
                'unique_sub_sub_categories' => [
                    'nested' => [
                        'path' => 'data.details.course_category.sub_category.sub_category'
                    ],
                    'aggs' => [
                        'unique_sub_sub_category_ids' => [
                            'terms' => [
                                'field' => 'data.details.course_category.sub_category.sub_category.id',
                                'size' => 9999
                            ],
                            'aggs' => [
                                'sub_category_name' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.sub_category.sub_category.name.keyword',
                                        'size' => 9999
                                    ]
                                ],
                                'sub_category_slug' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.sub_category.sub_category.slug',
                                        'size' => 9999
                                    ]
                                ],
                                'parent_category_id' => [
                                    'terms' => [
                                        'field' => 'data.details.course_category.sub_category.sub_category.parent_id',
                                        'size' => 9999
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            '_source' => false
        ];

        $subsubcategoryResponse = $this->es->customQuery($subsubcategoryQuery, 'course');

        if ($subsubcategoryResponse['status_code'] != 200) {
            return false;
        }

        // Process subcategories
        $subsubcategories = [];
        $buckets = $subsubcategoryResponse['body']['aggregations']['unique_sub_sub_categories']['unique_sub_sub_category_ids']['buckets'];

        foreach ($buckets as $bucket) {
            $id = $bucket['key'];
            $name = $bucket['sub_category_name']['buckets'][0]['key'] ?? '';
            $slug = $bucket['sub_category_slug']['buckets'][0]['key'] ?? '';
            $parentId = $bucket['parent_category_id']['buckets'][0]['key'] ?? null;

            if (!empty($name) && $parentId !== null) {
                $subsubcategories[] = [
                    'id' => $id,
                    'slug' => $slug,
                    'name' => $name,
                    'parent_id' => $parentId,
                    'count' => $bucket['doc_count']
                ];
            }
        }

        // Return filter array
        return [
            "ui_control_type" => "CHECKBOX",
            "is_active" => true,
            "title" => "Category Level 2",
            "placeholder" => "Select Category Level 2",
            "tooltip" => "Select a level 2 course category",
            "filter" => "category_level_2",
            "selected" => $categoryLevel2IDs,
            "multiple" => true,
            "items" => array_map(function ($subsubcategory) {
                return [
                    "id" => $subsubcategory['id'],
                    "slug" => $subsubcategory['slug'],
                    "label" => $subsubcategory['name'],
                    "filter" => "category_level_2",
                    "count" => $subsubcategory['count'],
                    "parent_id" => $subsubcategory['parent_id']
                ];
            }, $subsubcategories)
        ];
    }

    /**
     * Generates academy filter data for courses based on search text.
     * Returns a filter array containing academies.
     *
     * @since 1.0.0
     * @access public
     * @param string $searchText The search text to filter courses
     * @param array $selectedAcademyIds Array of selected academy IDs
     * @return array|false Returns a filter array with academies or false if no data is found
     */
    public function generateCourseSearchAcademyFilter($searchText, $selectedAcademyIds = [])
    {
        // First query to get unique academy IDs from courses
        $academyQuery = [
            'size' => 0,
            'query' => [
                'nested' => [
                    'path' => 'data.details',
                    'query' => !empty($searchText) ? [
                        'bool' => [
                            'must' => [
                                [
                                    'multi_match' => [
                                        'query' => $searchText,
                                        'fields' => [
                                            'data.details.title^50',
                                            'data.details.course_category.name^5',
                                            'data.details.course_category.sub_category.name^4',
                                            'data.details.course_category.sub_category.sub_category.name^3',
                                            'data.details.short_description^2',
                                            'data.details.post_description^1'
                                        ],
                                        'type' => 'phrase_prefix'
                                    ]
                                ],
                                [
                                    'term' => [
                                        'data.details.is_enable' => true
                                    ]
                                ]
                            ]
                        ]
                    ] : [
                        "match_all" => (object)[]
                    ]
                ]
            ],
            'aggs' => [
                'unique_academies' => [
                    'nested' => [
                        'path' => 'data.details'
                    ],
                    'aggs' => [
                        'academy_ids' => [
                            'terms' => [
                                'field' => 'data.details.academies',
                                'size' => 9999
                            ]
                        ]
                    ]
                ]
            ],
            '_source' => false
        ];

        $academyResponse = $this->es->customQuery($academyQuery, 'course', ['size' => 0]);

        if ($academyResponse['status_code'] != 200) {
            return false;
        }

        // Extract academy IDs and their counts
        $academyIds = [];
        $academyCounts = [];
        $buckets = $academyResponse['body']['aggregations']['unique_academies']['academy_ids']['buckets'];

        foreach ($buckets as $bucket) {
            $academyIds[] = $bucket['key'];
            $academyCounts[$bucket['key']] = $bucket['doc_count'];
        }

        $academies = [];
        if (!empty($academyIds)) {

            // Get academy details using AcademyModel
            $academiesQuery = [
                'custom' => [
                    'query' => [
                        'terms' => [
                            'data.details.record_id' => $academyIds
                        ]
                    ]
                ]
            ];

            $academiesData = $this->loadModel('academy')->getAcademies($academiesQuery, ['schema' => 'Refer#Academy_Minimal']);

            // Process academies and combine with counts

            foreach ($academiesData['data'] as $academy) {
                $academies[] = [
                    'id' => $academy['id'],
                    'name' => $academy['name'],
                    'count' => $academyCounts[$academy['id']] ?? 0
                ];
            }
        }
        // Return filter array
        return [
            "ui_control_type" => "CHECKBOX",
            "is_active" => true,
            "title" => "Academy",
            "placeholder" => "Select Academy",
            "tooltip" => "Select a course academy",
            "filter" => "academy",
            "selected" => $selectedAcademyIds,
            "multiple" => true,
            "items" => $academies
        ];
    }
}
