<?php

namespace V4;

/**
 * BatchController
 *
 * This class handles batch-related functionalities.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class BatchController extends Controller
{
    /**
     * BatchController Constructor
     *
     * Initializes the controller by loading required libraries and models 
     * needed for batch-related functionalities.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadModel('batch');
        $this->loadLibary('validate');
    }
    /**
     * Get a batch by ID
     *
     * Retrieves all info of batch from the es and returns them in a JSON format.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function getBatch($request)
    {
        try {
            $batchId = $request['batchId'];
            $batch = $this->batch->getBatch($batchId);
            if ($batch) {
                $this->response->success('GET_SUCCESS', $batch, ['message' => 'Batch retrieved successfully']);
            } else {
                $this->response->error('GET_FAIL', ['message' => 'Batch not found']);
            }
        } catch (\Exception $e) {
            $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
            return;
        }
    }

    public function addBatch($request)
    {
        try {
            ynLog("createBatch - Request: " . json_encode($request), 'createBatch');
            // Parse JSON payload
            $data = json_decode($request->get_body(), true);
            ynLog("createBatch - Data: " . json_encode($data), 'createBatch');
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Invalid JSON payload']);
            }
            // Define all required fields with expected type or pattern
            $validationChecks = [
                'title'                => 'string',
                'course_id'            => 'array',
                'instructor_id'        => 'numeric',
                'created_by'           => 'numeric',
                'start_date'           => 'string', // Can add regex for ISO date if needed
                'end_date'             => 'string',
                'class_days'           => 'array',
                'class_time'           => 'string',
                'personalisation' => '/^(one_to_many|one_to_one)$/',
                'teaching_mode'        => '/^(online|in_person)$/',
                // 'place_id'             => 'numeric',
                // 'classroom_id'         => 'numeric',
                'vacancy'            => 'numeric',
                'is_locked'            => 'boolean',
                'created_by'           => 'numeric'
            ];

            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($request, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Call the model's function
            $batchId = $this->batchModel->createBatchPost($data);
            ynLog("createBatch - Batch ID: " . $batchId, 'createBatch');

            if (!$batchId) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to create batch']);
            } else {
                ynLog("createBatch - Batch created with ID: $batchId", 'createBatch');
                return $this->response->success('POST_INSERT', ['batch_id' => $batchId], ['message' => 'Batch created successfully']);
            }
        } catch (\Exception $e) {
            // Handle any exceptions that occur
            return $this->response->error('POST_INSERT_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Update a batch by ID
     *
     * Updates the batch with the given ID and returns the updated batch in JSON format.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function updBatch($request)
    {
        try {
            ynLog("updateBatch - Request: " . json_encode($request), 'updBatch');

            // Parse incoming JSON payload
            $data = json_decode($request->get_body(), true);
            ynLog("updateBatch - Data: " . json_encode($data), 'updBatch');

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('POST_UPDATE_FAIL', ['message' => 'Invalid JSON payload']);
            }

            // Validate required key for update
            if (empty($data['batch_db_id']) || !is_numeric($data['batch_db_id'])) {
                return $this->response->error('POST_UPDATE_FAIL', ['message' => 'Invalid or missing batch_db_id']);
            }

            $post_id = (int) $data['batch_db_id'];

            // Check if the post exists and is of type 'batch'
            if (get_post_type($post_id) !== 'batch') {
                return $this->response->error('POST_UPDATE_FAIL', ['message' => "Post ID $post_id is not a valid batch"]);
            }

            // Exclude keys we don't want to store as post meta
            $excluded_keys = ['batch_db_id'];

            // Update post meta fields
            foreach ($data as $key => $value) {
                if (in_array($key, $excluded_keys)) continue;

                update_post_meta($post_id, $key, maybe_serialize($value));
            }

            ynLog("updateBatch - Updated batch post_id: $post_id", 'updBatch');

            return $this->response->success(
                'POST_UPDATE',
                ['batch_id' => $post_id],
                ['message' => 'Batch updated successfully']
            );
        } catch (\Exception $e) {
            ynLog("updateBatch - Exception: " . $e->getMessage(), 'updBatch');
            return $this->response->error('POST_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }
    /**
     * Delete a batch by ID
     *
     * Deletes the batch with the given ID and returns a success message.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function deleteBatch($request)
    {
        try {
            ynLog("deleteBatch - Request: " . json_encode($request), 'deleteBatch');

            $batchId = $request['batchId'];
            ynLog("deleteBatch - Batch ID: $batchId", 'deleteBatch');

            // Validate batch ID
            if (empty($batchId) || !is_numeric($batchId)) {
                return $this->response->error('DELETE_FAIL', ['message' => 'Invalid batch ID']);
            }

            // Call the model's function to delete the batch
            $result = $this->batch->deleteBatch($batchId);
            ynLog("deleteBatch - Result: " . json_encode($result), 'deleteBatch');

            if ($result) {
                return $this->response->success('DELETE_SUCCESS', [], ['message' => 'Batch deleted successfully']);
            } else {
                return $this->response->error('DELETE_FAIL', ['message' => 'Failed to delete batch']);
            }
        } catch (\Exception $e) {
            ynLog("deleteBatch - Exception: " . $e->getMessage(), 'deleteBatch');
            return $this->response->error('DELETE_FAIL', ['message' => $e->getMessage()]);
        }
    }
    /**
     * Get all batches
     *
     * Retrieves all batches and returns them in a JSON format.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function getBatches($request)
    {
        try {
            ynLog("getAllBatches - Request: " . json_encode($request), 'getAllBatches');
            // Extract query string parameters
            $userId = isset($_GET['user_id']) ? (int) $_GET['user_id'] : 0;
            $instructorId = isset($_GET['instructor_id']) ? (int) $_GET['instructor_id'] : 0;
            $courseId = isset($_GET['course_id']) ? (int) $_GET['course_id'] : 0;
            $academyId = isset($_GET['academy_id']) ? (int) $_GET['academy_id'] : 0;
            $limit = isset($_GET['limit']) ? (int) $_GET['limit'] : 10; // Default limit
            $offset = isset($_GET['offset']) ? (int) $_GET['offset'] : 0; // Default offset
            $viewType = isset($request['viewType']) ? $request['viewType'] : 'all';
            $classDaysTime  = isset($_GET['class_days_time']) ? $_GET['class_days_time'] : ''; // e.g., 'Mon-Wed 5pm'
            $personalisation = isset($_GET['personalisation']) ? $_GET['personalisation'] : ''; // e.g., 'IELTS'
            $onlyEnrollable = isset($_GET['only_enrollable']) ? (bool) $_GET['only_enrollable'] : false;
            $onlyLocked     = isset($_GET['only_locked']) ? (bool) $_GET['only_locked'] : false;
            $activeBatch = isset($_GET['active_batch']) ? $_GET['active_batch'] : 'all'; // e.g., 'active', 'inactive', 'all'
            $teachingMode = isset($_GET['teaching_mode']) ? $_GET['teaching_mode'] : 'all'; // e.g., 'online', 'in_person', 'all'

            // Validation rules
            $validationChecks = [
                'user_id' => 'numeric',                  // Must be numeric
                'course_id' => 'numeric',                // Must be numeric
                'limit' => 'numeric',                    // Must be numeric
            ];

            // Perform validation on each field
            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($request, $key, $type);
                if (is_wp_error($result)) {
                    return $result; // Return the error immediately if validation fails
                }
            }

            if ($userId <= 0) {
                return $this->response->error('USER_ID_FAIL', ['message' => 'Invalid User']);
            }

            // Fetch User Role
            $this->loadModel('user');
            $role = $this->userModel->getUserRole($userId);

            // Role-Based Validation
            if ($this->userModel->checkRole($role, $this->userModel->yn_Org_Admin) !== false) {
                if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $referrals !== 'all') {
                    return $this->response->error('ACCESS_DENIED', ['message' => 'You are not allowed to use these filters']);
                }
            } else if ($this->userModel->checkRole($role, $this->userModel->yn_Yuno_Admin) !== false) {
                // Yuno Admin - full access, no restrictions
            } else {
                return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
            }
            $mustConditions = [];
            $mustNotConditions = [];

            // Instructor filter
            if (!empty($instructorId)) {
                $mustConditions[] = [
                    "match_phrase" => [
                        "data.details.batch_details.instructor_id" => $instructorId
                    ]
                ];
            }

            // Org Admin (batch reference)
            if (!empty($org_admin_id) && !empty($batchReferenceArray)) {
                $mustConditions[] = [
                    "terms" => [
                        "data.details.batch_details.batch_post_id" => $batchReferenceArray
                    ]
                ];
            }

            // Academy filter
            if (!empty($academyId)) {
                $mustConditions[] = [
                    "term" => [
                        "data.details.academies" => $academyId
                    ]
                ];
            }

            // Course ID filter
            if (!empty($courseId)) {
                $mustConditions[] = [
                    "match_phrase" => [
                        "data.details.batch_details.course_id" => $courseId
                    ]
                ];
            }

            // Personalisation
            if ($personalisation !== "all") {
                $mustConditions[] = [
                    "match" => [
                        "data.details.batch_details.personalisation" => $personalisation === "one_to_one" ? 1 : 0
                    ]
                ];
            }
            // Class Mode
            if ($teachingMode !== "all") {
                $mustConditions[] = [
                    "match" => [
                        "data.details.batch_details.mode" => $teachingMode === "in_person" ? 1 : 0
                    ]
                ];
            }

            // Locked Batch
            if ($onlyLocked !== "all") {
                $mustConditions[] = [
                    "match" => [
                        "data.details.batch_details.locked_batch" => $onlyLocked === "locked" ? 1 : 0
                    ]
                ];
            }

            // Active Batch
            if ($activeBatch !== "all") {
                $mustConditions[] = [
                    "match" => [
                        "data.details.batch_details.active_batch" => $activeBatch === "active" ? 1 : 0
                    ]
                ];
            }

            // Enrollable Status (default = 'enrollable')
            if ($onlyEnrollable !== "all") {
                $mustConditions[] = [
                    "range" => [
                        "data.details.batch_details.enrollment_counter" => [
                            "gt" => 0
                        ]
                    ]
                ];
            }

            // Batch Days (array)
            if (!empty($batch_days)) {
                $dayConditions = [];
                foreach ($batch_days as $day) {
                    $dayConditions[] = [
                        "match" => [
                            "data.details.batch_details.days_of_week" => $day
                        ]
                    ];
                }
                $mustConditions[] = ["bool" => ["should" => $dayConditions]];
            }

            // Batch Time (array)
            if (!empty($batch_time)) {
                $timeConditions = [];
                foreach ($batch_time as $time) {
                    $timeConditions[] = [
                        "match" => [
                            "data.details.batch_details.batch_type" => $time
                        ]
                    ];
                }
                $mustConditions[] = ["bool" => ["should" => $timeConditions]];
            }

            // Call the model's function to get all batches
            $batches = $this->batch->getBatches($request);
            ynLog("getAllBatches - Batches: " . json_encode($batches), 'getAllBatches');

            if ($batches) {
                return $this->response->success('GET_SUCCESS', $batches, ['message' => 'Batches retrieved successfully']);
            } else {
                return $this->response->error('GET_FAIL', ['message' => 'No batches found']);
            }
        } catch (\Exception $e) {
            ynLog("getAllBatches - Exception: " . $e->getMessage(), 'getAllBatches');
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    }
}
