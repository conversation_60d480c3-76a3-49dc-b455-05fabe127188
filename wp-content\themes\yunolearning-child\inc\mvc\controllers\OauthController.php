<?php
namespace V4;

use \Exception;
use \WP_User;
use \Google_Client;
use \Google_Service_Oauth2;
use \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use \Aws\CognitoIdentity\CognitoIdentityClient;
use \Aws\Exception\AwsException;
use \Utility;
use \UserElasticSearch;

/**
 * OauthController Class
 * 
 * Handles OAuth-related controller logic for authentication flows.
 */
class OauthController extends Controller {
    
    // Constants for credentials types
    private const CREDENTIALS_TYPE_IDENTITY_POOL = 'identity_pool';
    private const CREDENTIALS_TYPE_USER_POOL = 'user_pool';
    private const CREDENTIALS_TYPE_AUTOMATION = 'automation';
    private const CREDENTIALS_TYPE_VIRTUAL_IDENTITY = 'virtual_identity';
    
    // Constants for auth references
    private const AUTH_REF_GOOGLE = 'google';
    private const AUTH_REF_VIRTUAL_CLASSROOM = 'virtual-classroom';
    private const AUTH_REF_AUTOMATION = 'automation';
    private const AUTH_REF_APPLE = 'apple';
    
    // Constants for cookie names
    private const COOKIE_NAME = "yuno_user_login_id";
    
    // Constants for user roles
    private const ROLE_INSTRUCTOR = 'instructor';
    private const ROLE_LEARNER = 'learner';
    private const ROLE_ORG_ADMIN = 'org-admin';
    
    // NEW: State parameter structure constants (matching loginForm.js signIn object)
    private const stateParameterStructure = [
        'mobile' => '',
        'categoryURL' => '',
        'productCode' => '',
        'leadStatus' => '',
        'variant' => '',
        'utmSource' => '',
        'utmCampaign' => '',
        'utmMedium' => '',
        'adGroupID' => '',
        'adContent' => '',
        'utmTerm' => '',
        'gclid' => '',
        'content' => [
            'type' => '',
            'id' => ''
        ],
        'landingPage' => [
            'url' => '',
            'title' => ''
        ],
        'referralUrl' => '',
        'redirectUrl' => '',
        'courseToBeMap' => '',
        'orgDetails' => [
            'type' => 'login',
            'orgId' => '',
            'orgUrl' => '',
            'phone' => ''
        ],
        'loginDetails' => [
            'role' => ''
        ]
    ];
    
    // NEW: LocalStorage simulation constants (matching auth.js localStorage keys)
    private const localStorageKeys = [
        'paymentState' => 'paymentContextData',
        'userState' => 'userNavigationState', 
        'isReferrer' => 'referrerContextFlag',
        'isQuiz' => 'quizContextFlag',
        'skipSignUp' => 'skipSignupFlag',
        'userSignUp' => 'userSignupStatus',
        'oldUserState' => 'backupNavigationState'
    ];
    
    // NEW: Role dashboard mapping (exactly matching auth.js roleDashboardMap)
    private const roleDashboardMap = [
        'Instructor' => '/instructor',
        'Learner' => '/learner'
    ];
    
    /**
     * @var OauthModel The OAuth model instance
     */
    protected $oauthModel;
    
    /**
     * @var Logger The Logger model instance
     */
    protected $logger;
    
    /**
     * @var Response The response library instance
     */
    protected $response;
    
    /**
     * @var Common The common library instance
     */
    protected $common;
    
    /**
     * @var Validate The validate library instance
     */
    protected $validate;
    
    /**
     * Constructor to initialize the OauthController
     */
    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadLibary('logger');

        $this->loadModel('oauth');
        $this->loadModel('org');
    }
    
    /**
     * Helper function to safely handle potential WP_Error objects
     * Used to prevent "Object of class WP_Error could not be converted to string" errors
     */
    public function safeValue($var) {
        if (is_wp_error($var)) {
            return 'wp_error:' . $var->get_error_message();
        }
        return $var;
    }
    
    /**
     * Helper function to ensure email always has a value
     * 
     * @param string $email The email address to check
     * @param string $sub_id The user's subject ID
     * @return string A valid email address
     */
    public function getSafeEmail($email, $sub_id) {
        // If email is empty and we have a sub_id, generate a placeholder email
        if (empty($email) && !empty($sub_id)) {
            return $sub_id . '@cognito.user';
        }

        // If email contains cognito.user domain and we have a real email in the database
        if (!empty($sub_id) && strpos($email, '@cognito.user') !== false) {
            // Check if we already have this user and they have a real email
            $users = get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);

            if (!empty($users)) {
                $user_email = $users[0]->user_email;
                // If the user has a real email (not a cognito.user one), use it
                if (strpos($user_email, '@cognito.user') === false) {
                    return $user_email;
                }
            }
        }

        return $email;
    }
    
    /**
     * Helper method to get current datetime
     * 
     * @return string Current datetime in MySQL format
     */
    private function getCurrentDateTime(): string {
        return date("Y-m-d H:i:s");
    }
    
    /**
     * Helper method to get current user ID safely
     * 
     * @return int Current user ID or 0 if not logged in
     */
    private function getCurrentUserId(): int {
        return get_current_user_id() ?: 0;
    }
    
    /**
     * Extracts and processes the state parameter from the query string
     * 
     * @return object The decoded state parameter as a PHP object
     * @throws Exception If state parameter is invalid or missing
     */
    public function processStateParameter() {
        parse_str($_SERVER['QUERY_STRING'], $parsedArray);
        return json_decode(urldecode($parsedArray['state']));
    }
    
    /**
     * Decodes JWT token payload
     * 
     * @param array $response The authentication response containing the token
     * @return array The decoded payload as an associative array
     * @throws Exception If token decoding fails
     */
    public function decodeTokenPayload($response) {
        try {
            // Determine which token to use based on credentials_type
            $token = '';
        if (!empty($response['credentials_type']) && $response['credentials_type'] == self::CREDENTIALS_TYPE_IDENTITY_POOL) {
                if (empty($response['google_id_token'])) {
                    echo "<h1>Error: Missing Token</h1>";
                    echo "<p>Missing google_id_token in response</p>";
                    echo "<p>Response data: <pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre></p>";
                    error_log("Missing google_id_token in response: " . json_encode($response), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                $token = $response['google_id_token'];
        } else {
                if (empty($response['id_token'])) {
                    echo "<h1>Error: Missing Token</h1>";
                    echo "<p>Missing id_token in response</p>";
                    echo "<p>Response data: <pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre></p>";
                    error_log("Missing id_token in response: " . json_encode($response), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    die();
                }
                $token = $response['id_token'];
            }
            
            // Split the JWT token into its component parts
            $tokenParts = explode('.', $token);
            if (count($tokenParts) !== 3) {
                echo "<h1>Error: Invalid Token Format</h1>";
                echo "<p>Invalid token format - does not contain 3 parts (header, payload, signature)</p>";
                echo "<p>Parts count: " . count($tokenParts) . "</p>";
                echo "<p>Token: " . substr($token, 0, 50) . "...</p>";
                error_log("Invalid token format - does not contain 3 parts: " . substr($token, 0, 50), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                die();
            }
            
            // Get the payload part
            $payloadBase64 = $tokenParts[1];
            if (empty($payloadBase64)) {
                echo "<h1>Error: Empty Token Payload</h1>";
                echo "<p>Token payload part is empty</p>";
                error_log("Empty payload part in token", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                die();
            }
            
            // Properly handle base64url decoding (JWT uses base64url, not standard base64)
            $payloadBase64 = str_replace(['-', '_'], ['+', '/'], $payloadBase64);
            $payloadBase64 = preg_replace('/[^A-Za-z0-9\+\/=]/', '', $payloadBase64);
            $payloadBase64 = str_pad($payloadBase64, strlen($payloadBase64) % 4, '=', STR_PAD_RIGHT);
            
            // Decode the base64 string
            $payload = base64_decode($payloadBase64);
            if ($payload === false) {
                echo "<h1>Error: Base64 Decode Failed</h1>";
                echo "<p>Failed to base64 decode token payload</p>";
                echo "<p>Payload Base64: " . substr($payloadBase64, 0, 50) . "...</p>";
                error_log("Failed to base64 decode token payload: " . substr($payloadBase64, 0, 50), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                die();
            }
            
            // Parse the JSON
            $decodedPayload = json_decode($payload, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo "<h1>Error: JSON Decode Failed</h1>";
                echo "<p>Failed to JSON decode token payload: " . json_last_error_msg() . "</p>";
                echo "<p>Raw payload: " . htmlspecialchars(substr($payload, 0, 100)) . "...</p>";
                error_log("Failed to JSON decode token payload: " . json_last_error_msg() . ", Payload: " . substr($payload, 0, 100), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                die();
            }
            
            // Additional logging for token contents
            error_log("Token successfully decoded. Contains fields: " . implode(", ", array_keys($decodedPayload)), 
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
                
            return $decodedPayload;
        } catch (Exception $e) {
            // Log the error
            error_log("Token decode error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            
            // Display error and die
            echo "<h1>Error: Token Decode Exception</h1>";
            echo "<p>Error Message: " . $e->getMessage() . "</p>";
            echo "<p>File: " . $e->getFile() . " (Line: " . $e->getLine() . ")</p>";
            echo "<p>Stack Trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
            die();
        }
    }
    
    /**
     * Gets user ID from authentication details
     * 
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param object $stateArray The state parameter object
     * @return int The user ID if found, 0 otherwise
     * @throws Exception If authentication validation fails
     */
    public function getUserIdFromAuth($email, $sub_id, $stateArray) {
        $users_by_email = get_user_by('email', $email);
        $user_id = $users_by_email ? $users_by_email->ID : 0;
        if (!$user_id && !empty($sub_id) && (!isset($stateArray->org_details->auth_ref) || $stateArray->org_details->auth_ref !== "google")) {
            $users = get_users([
                'meta_key' => 'cognito_sub_id',
                'meta_value' => $sub_id,
                'number' => 1,
            ]);
            $user_id = !empty($users) ? $users[0]->ID : 0;
        }
        if (!empty($stateArray->org_details) && $stateArray->org_details->auth_ref == "google") {
            if (!$user_id) {
                error_log("Switch account error: No user found with email $email", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                wp_redirect(home_url('/login-error/?error=' . urlencode("Account switching failed. No account found with this email address.")));
                exit;
            }
        }
        return $user_id;
    }
    
    /**
     * Updates user meta data with authentication details
     * 
     * @param int $user_id The user ID
     * @param array $response The authentication response
     * @param array $decodedPayload The decoded JWT payload
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param object $org_details Organization details
     * @param string $uemailid The email ID to use
     * @return void
     */
    public function updateUserMetaData($user_id, $response, $decodedPayload, $email, $sub_id, $org_details, $uemailid) {
        global $datetime;
        
        $id_token = $response['id_token'];
        $token_parts = explode('.', $id_token);
        $payload = base64_decode($token_parts[1]);
        $user_details = json_decode($payload, true);
        $sub_id = $user_details['sub'];

        // Store additional user details from the response in user meta
        if ($user_id) {
            try {
                // Use the model for creating auth data array
                $user_meta_data = $this->oauthModel->createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
            } catch (\Exception $e) {
                error_log("Auth data creation error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(),
                    3, ABSPATH . "error-logs/cognito-custom-errors.log");
                update_user_meta($user_id, 'user_details_id_token', $user_details);
                update_user_meta($user_id, 'user_data_cognito_response', $user_details);
                if (function_exists('create_yuno_auth_data_array')) {
                    $user_meta_data = create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
                    update_user_meta($user_id, 'yuno_user_auth_data', $user_meta_data);
                }
            }
        }

        $post_user_refresh_token = $response['refresh_token'] ?? "";
        $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
        $yuno_user_name_arr = explode(" ", $yuno_user_name ?? '');
        $yuno_user_fisrtname = isset($yuno_user_name_arr[0]) ? sanitize_user($yuno_user_name_arr[0]) : '';
        $yuno_user_lastname = isset($yuno_user_name_arr[1]) ? sanitize_user($yuno_user_name_arr[1]) : '';
        if (empty($yuno_user_lastname)) {
            $yuno_user_lastname = !empty($yuno_user_fisrtname) ? $yuno_user_fisrtname : "k";
        }
        if (!empty($response['access_token'])) {
            update_user_meta($user_id, 'yuno_user_access_token', $response['access_token']);
        }
        if (!empty($post_user_refresh_token)) {
            update_user_meta($user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
        }
        if (!empty(strtotime("+1 hour"))) {
            update_user_meta($user_id, 'cognito_token_expiry', strtotime("+1 hour"));
        }
        $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
        $yuno_user_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : null;
        $id = isset($decodedPayload['sub']) ? $decodedPayload['sub'] : null;
        if (!empty($response['id_token'])) {
            update_user_meta($user_id, 'yuno_user_id_token', $response['id_token']);
        }
        if (!empty($_GET['code'])) {
            update_user_meta($user_id, 'yuno_user_authentication_code', $_GET['code']);
        }
        $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
        if (empty($existing_sub_id)) {
            update_user_meta($user_id, 'cognito_sub_id', $sub_id);
            error_log("Setting initial cognito_sub_id for user: Email: $email, sub_id: $sub_id",
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
        } else if ($existing_sub_id !== $sub_id) {
            error_log("Alternative sub_id detected: Email: $email, New sub_id: $sub_id, Existing sub_id: $existing_sub_id",
                3, ABSPATH . "error-logs/cognito-custom-errors.log");
            $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
            if (empty($alt_sub_ids)) {
                $alt_sub_ids = array();
            }
            if (!in_array($sub_id, $alt_sub_ids)) {
                $alt_sub_ids[] = $sub_id;
                update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
            }
        }
        update_user_meta($user_id, 'googleplus_access_token', $id);
        
        // CRITICAL FIX: Only update googleplus_profile_img if this is NOT an Apple ID login
        // Detect if this is an Apple ID authentication
        $isAppleId = false;
        
        // Method 1: Check org_details auth_ref
        if (!empty($org_details) && isset($org_details->auth_ref) && $org_details->auth_ref === self::AUTH_REF_APPLE) {
            $isAppleId = true;
        }
        
        // Method 2: Check identities in user details (most reliable)
        if (!$isAppleId && isset($user_details['identities']) && is_array($user_details['identities'])) {
            foreach ($user_details['identities'] as $identity) {
                if (isset($identity['providerName']) && $identity['providerName'] === 'SignInWithApple') {
                    $isAppleId = true;
                    break;
                }
            }
        }
        
        // Method 3: Check cognito username prefix
        if (!$isAppleId && isset($user_details['cognito:username'])) {
            $isAppleId = strpos($user_details['cognito:username'], 'signinwithapple_') === 0;
        }
        
        // Only update googleplus_profile_img if this is NOT an Apple ID login
        if (!$isAppleId) {
            update_user_meta($user_id, 'googleplus_profile_img', $picture);
            error_log("OAuth Meta Update: Updated googleplus_profile_img for Google login", 3, ABSPATH . "error-logs/oauth-meta-updates.log");
        } else {
            error_log("OAuth Meta Update: Skipped googleplus_profile_img update for Apple ID login", 3, ABSPATH . "error-logs/oauth-meta-updates.log");
        }
        
        update_user_meta($user_id, 'yuno_display_name', $yuno_user_name);
        update_user_meta($user_id, 'yuno_first_name', $yuno_user_fisrtname);
        update_user_meta($user_id, 'yuno_last_name', $yuno_user_lastname);
        update_user_meta($user_id, 'yuno_gplus_email', $uemailid);
        update_user_meta($user_id, 'yuno_gplus_rgdate', $datetime);
    }
    
    /**
     * Gets authentication response based on the provided auth code and state
     * 
     * @param string $authCode The authorization code from OAuth provider
     * @param object $stateArray The decoded state parameter
     * @return array The authentication response
     * @throws Exception If authentication fails
     */
    public function getAuthResponse($authCode, $stateArray) {
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $auth_ref = isset($org_details->auth_ref) ? $org_details->auth_ref : '';
        $org_id = isset($org_details->org_id) ? $org_details->org_id : 0;

        if (!empty($org_details) && $auth_ref == self::AUTH_REF_GOOGLE) {
            return $this->processAccountSwitch($authCode);
        } elseif (!empty($org_details) && $auth_ref == self::AUTH_REF_VIRTUAL_CLASSROOM) {
            return $this->processVirtualAccountSwitch($authCode, $org_id);
        } elseif (!empty($org_details) && $auth_ref == self::AUTH_REF_AUTOMATION) {
            return ["credentials_type" => self::CREDENTIALS_TYPE_AUTOMATION, "id_token" => $authCode];
        } else {
            parse_str($_SERVER['QUERY_STRING'], $parsedArray);
            $stateArray = json_decode(urldecode($parsedArray['state']));
            return $this->oauthModel->getCognitoAccessToken($authCode);
        }
    }

    /**
     * Main OAuth authentication handler that processes the auth code and manages user signup/login
     * 
     * @param string $authCode The authentication code from the OAuth provider
     * @return void
     */
    public function handleOauthCallback($authCode) {
        try {
            $stateArray = $this->processStateParameter();

            $response = $this->getAuthResponse($authCode, $stateArray);

            if (isset($response['error'])) {
                $error_message = $response['error'];
                wp_redirect(home_url('/login-error/?error=' . urlencode($error_message)));
                exit;
            }

            $decodedPayload = $this->decodeTokenPayload($response);
            $email = $decodedPayload['email'] ?? null;
            $sub_id = $decodedPayload['sub'] ?? null;

            $uemailid = $email = $this->getSafeEmail($email, $sub_id);
            $UEmail = $email;

            $user_id = 0;
            if ($response['credentials_type'] != self::CREDENTIALS_TYPE_AUTOMATION) {
                // First check if user exists based on email
                $user_id = $this->getUserIdFromAuth($email, $sub_id, $stateArray);
            } else {
                $user_id = $_GET['user_id'];
                $user_info = get_userdata($user_id);
                if ($user_info) {
                    $uemailid = $user_info->user_email;
                    $email = $user_info->user_email;
                }
            }

            // Process existing user or register new user
            if ($user_id) {
                $this->handleExistingUser($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id);
            } else {
                // Handle new user registration
                $this->handleNewUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail);
            }
        } catch (Exception $e) {
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $request = ["user_id" => $user_id ?? 0];
            $user = ["user_id" => $user_id ?? 0];
            $this->logger->writeLog("error", "ES", "login | signup", $message, $request, []);
            wp_redirect(home_url('/login-error/?error=' . urlencode("Authentication error occurred. Please try again.")));
            exit();
        }
    }

    // NEW API ENDPOINT METHOD - Added to support V4 REST API authentication flow
    // This replaces the /auth page template with a proper API endpoint
    /**
     * Handles OAuth authentication callback via V4 REST API endpoint
     * This method replaces the traditional /auth page template approach
     * 
     * @param WP_REST_Request $request The REST request object containing OAuth parameters
     * @return void
     */
    public function handleAuthCallbackAPI($request) {
        // Extract the authorization code from the REST request
        $authCode = $request->get_param('code');
        
        // Validate that we have the required authorization code
        if (empty($authCode)) {
            error_log("API OAuth Callback Error: Missing authorization code", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            wp_redirect(home_url('/login-error/?error=' . urlencode('Missing authorization code')));
            exit;
        }
        
        // This ensures existing code that relies on $_GET continues to work
        foreach ($request->get_params() as $key => $value) {
            $_GET[$key] = $value;
        }
        
        // Also ensure $_SERVER['QUERY_STRING'] is available for state processing
        if (empty($_SERVER['QUERY_STRING'])) {
            $_SERVER['QUERY_STRING'] = http_build_query($request->get_params());
        }
        
        // Log the API endpoint usage for debugging
        error_log("API OAuth Callback: Processing auth code via V4 endpoint - Params populated", 3, ABSPATH . "error-logs/cognito-custom-info.log");
        
        // Reuse the existing authentication logic - now with $_GET properly populated
        // This ensures all existing functionality (Google, Cognito, Apple, etc.) continues to work
        $this->handleOauthCallback($authCode);
        
        // Exit to prevent any further processing
        exit;
    }
    // END NEW API ENDPOINT METHOD

    /**
     * Handles authentication flow for existing users
     * 
     * @param int $user_id The user ID
     * @param array $response The authentication response
     * @param array $decodedPayload The decoded JWT payload
     * @param object $stateArray The state parameter object
     * @param string $uemailid The user's email ID
     * @param string $sub_id The user's subject ID
     * @return void
     * @throws Exception If user processing fails
     */
    public function handleExistingUser($user_id, $response, $decodedPayload, $stateArray, $uemailid, $sub_id) {
        global $datetime, $cookie_name;
        $signupDetail = get_user_meta($user_id, 'is_signup_complete', true);
        $courseToBeMap = null;
        if (isset($stateArray->course_to_be_map)) {
            $courseToBeMap = $stateArray->course_to_be_map;
        }
        if ($signupDetail != 1) {
            if ($courseToBeMap) {
                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                if (empty($currentCourses)) {
                    $currentCourses = [];
                }
                if (!in_array($courseToBeMap, $currentCourses)) {
                    $currentCourses[] = $courseToBeMap;
                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                }
            }
        }
        $new_password = $uemailid . '###987654';
        $ups = wp_set_password($new_password, $user_id);
        $authToken = "";
        $mobile_web_token = "";
        if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
            $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
            $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
        } else {
            $token_result = create_jwt_token($user_id);
            if (is_wp_error($token_result)) {
                error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                $authToken = "";
                $mobile_web_token = "";
            } else {
                $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                $authToken = "Bearer " . $auth_token;
                $mobile_web_token = $auth_token;
            }
        }
        $users = get_users(array('meta_key' => 'cognito_sub_id', 'meta_value' => $sub_id, 'number' => 1));
        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $this->updateUserMetaData($user_id, $response, $decodedPayload, $uemailid, $sub_id, $org_details, $uemailid);
        parse_str($_SERVER['QUERY_STRING'], $parsedArray);
        $stateArray = json_decode(urldecode($parsedArray['state']));

        $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
        $content = isset($stateArray->content) ? $stateArray->content : '';
        $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
        $yuno_redirect_url = $stateArray->redirect_url;
        $contentType = '';
        if (!empty($content)) {
            $contentType = isset($content->type) ? $content->type : '';
            $contentId = isset($content->id) ? $content->id : '';
            $webinarPrivateClassArray = array("privateClass", "webinar");
            $allContentArray = array("privateClass", "webinar");
            if (!empty($contentType) && !empty($contentId) && in_array($contentType, $allContentArray)) {
                if (!empty($contentType) && in_array($contentType, $webinarPrivateClassArray)) {
                    try {
                        direct_user_enrollment_in_class($contentId, $user_id);
                    } catch (Exception $e) {
                        error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }
                }
                $current_user_type = get_user_meta($user_id, 'current_user_type', true);
            }
        }
        if (!empty($org_details)) {
            $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
            $org_user_mode = isset($org_details->type) ? $org_details->type : '';
            $org_phone = isset($org_details->phone) ? $org_details->phone : "";
            $decoded_value = base64_decode($org_encoded);
            $decoded_val = explode("@@@", $decoded_value);
            $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
            $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
            $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
            $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
            $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
            if (!empty($org_id) && $org_id != 0) {
                $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                $org_action = "add";
                if (!empty($details_from_org_objects)) {
                    if (array_key_exists($org_id, $details_from_org_objects)) {
                        $org_action = "update";
                    }
                }
                $details = [
                    'user_id' => $user_id,
                    'datetime' => $datetime,
                    'type' => $org_user_mode,
                    'org_id' => $org_id,
                    'org_action' => $org_action,
                    'crm_id' => $crm_id,
                    'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                    "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                    "programs" => isset($org_details->programs) ? $org_details->programs : '',
                    'parents' => isset($org_details->parents) ? json_encode($org_details->parents, true) : ''
                ];
                try {
                    signin_signedup_update_org_users_object($details);
                } catch (Exception $e) {
                }
            }
        }
        $arguments = ["user_id" => $user_id, "user_existance" => true];
        $this->saveUserToElasticsearch($arguments);
        $cookie_name = self::COOKIE_NAME;
        if (!isset($_COOKIE[$cookie_name])) {
            setcookie($cookie_name, $user_id, time() + 7 * 24 * 60 * 60, "/");
        }
        $site_url = site_url();
        $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
        try {
            user_last_login_time($user_id, $userLeadId);
        } catch (Exception $e) {
        }
        // =============================================================================
        // CRITICAL FIX: APPLE ID SIGNUP INTENT DETECTION (for existing users)
        // =============================================================================
        
        wp_clear_auth_cookie();
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        
        // CRITICAL FIX: Check if this is Apple ID user coming from signup tab
        // This handles the case where existing users use Apple ID from signup tab
        $isAppleIdSignup = $this->isAppleIdSignupIntent($stateArray, $response);
        
        if ($isAppleIdSignup) {
            // Force redirect to signup page regardless of completion status
            error_log("APPLE ID EXISTING USER: Detected signup intent, forcing signup redirect", 3, ABSPATH . "error-logs/apple-signup-fix.log");
            
            // Determine appropriate signup page based on role
            $role = $this->detectUserRole($user_id, $stateArray);
            if ($role === 'Instructor') {
                $finalRedirectUrl = home_url('/instructor-sign-up');
                error_log("APPLE ID EXISTING USER: Redirecting instructor to /instructor-sign-up", 3, ABSPATH . "error-logs/apple-signup-fix.log");
            } else {
                $finalRedirectUrl = home_url('/sign-up');
                error_log("APPLE ID EXISTING USER: Redirecting learner to /sign-up", 3, ABSPATH . "error-logs/apple-signup-fix.log");
            }
        } else {
            // Use normal auth.js-based redirect determination
            $finalRedirectUrl = $this->determineRedirectUrl($user_id, $stateArray);
        }
        
        // Handle org resources redirect first (if needed)
        $args = [
            "org_redirect_url" => $org_redirect_url,
            "org_encoded" => $org_encoded,
            "mobile_web_token" => $mobile_web_token,
            "user_id" => $user_id,
            "yuno_redirect_url" => $yuno_redirect_url
        ];
        try {
            $this->redirectToYunoResources($args);
        } catch (Exception $e) {
            // Continue with normal redirect if org resources redirect fails
        }

        // Use our auth.js-based redirect URL instead of hardcoded home_url('/')
        wp_redirect($finalRedirectUrl);
        exit();
    }

    /**
     * Handles authentication flow for new user registration
     * 
     * @param int $user_id The user ID (usually 0 for new users)
     * @param array $response The authentication response
     * @param array $decodedPayload The decoded JWT payload
     * @param object $stateArray The state parameter object
     * @param string $email The user's email
     * @param string $sub_id The user's subject ID
     * @param string $uemailid The user's email ID to use
     * @param string $UEmail The original email
     * @return void
     * @throws Exception If user registration fails
     */
    public function handleNewUser($user_id, $response, $decodedPayload, $stateArray, $email, $sub_id, $uemailid, $UEmail) {
        global $datetime, $cookie_name;
        if ($response['credentials_type'] != self::CREDENTIALS_TYPE_VIRTUAL_IDENTITY) {
            // Use Cognito sub_id from decodedPayload if available, otherwise use provided sub_id
            if (isset($decodedPayload['cognito_sub_id']) && !empty($decodedPayload['cognito_sub_id'])) {
                $sub_id = $decodedPayload['cognito_sub_id'];
            }
            
            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $username_parts = explode('@', $email);
                $yuno_user_name = sanitize_user(array_shift($username_parts));
            } else {
                $yuno_user_name = $sub_id;
            }

            $user_email = $this->getSafeEmail($email, $sub_id);
            $random_password = $user_email . '###987654';
            if ($user_id == 0) {
                $user_id = wp_create_user($yuno_user_name, $random_password, $user_email);
                if (is_wp_error($user_id)) {
                    $alt_username = 'user_' . $sub_id;
                    $user_id = wp_create_user($alt_username, $random_password, $user_email);
                    if (is_wp_error($user_id)) {
                        wp_redirect(home_url('/login-error/?error=' . urlencode("Registration failed. Please contact support.")));
                        exit;
                    }
                }
                update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                
                if (isset($decodedPayload['sub']) && $decodedPayload['sub'] !== $sub_id) {
                    update_user_meta($user_id, 'google_sub_id', $decodedPayload['sub']);
                }
            }
            
            // Determine the display name for consistency
            if (isset($decodedPayload['name']) && !empty($decodedPayload['name'])) {
                $display_name = $decodedPayload['name'];
            } else {
                // Create a display name from the email
                $email_parts = explode('@', $email);
                $display_name = ucwords(str_replace('.', ' ', $email_parts[0]));
            }
            update_user_meta($user_id, 'yuno_display_name', $display_name);
            wp_update_user(['ID' => $user_id, 'display_name' => $display_name]);

            $picture = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : null;
            parse_str($_SERVER['QUERY_STRING'], $parsedArray);
            $stateArray = json_decode(urldecode($parsedArray['state']));

            $org_details = isset($stateArray->org_details) ? $stateArray->org_details : '';
            $usr_role = isset($stateArray->login_details->role) ? $stateArray->login_details->role : '';
            $login_details = isset($stateArray->login_details) ? $stateArray->login_details : '';
            $role = self::ROLE_LEARNER;
            if (!empty($login_details)) {
                $role = !empty($login_details->role) ? $login_details->role : self::ROLE_LEARNER;
                $u = new WP_User($user_id);
                if ($role == self::ROLE_INSTRUCTOR) {
                    $u->remove_role('SEO Manager');
                    $u->add_role('um_instructor');
                } else if ($role == self::ROLE_ORG_ADMIN) {
                    $u->remove_role('SEO Manager');
                    update_user_meta($user_id, 'profile_privacy', "public");
                    update_user_meta($user_id, 'is_signup_complete', true);
                    $u->add_role('um_org-admin');
                    on_role_change_custion_callback($user_id, 'um_org-admin');
                }
            }
            $courseToBeMap = isset($stateArray->course_to_be_map) ? $stateArray->course_to_be_map : null;
            if ($courseToBeMap) {
                $currentCourses = get_user_meta($user_id, 'course_to_be_map', true);
                if (empty($currentCourses)) {
                    $currentCourses = [];
                }
                if (!in_array($courseToBeMap, $currentCourses)) {
                    $currentCourses[] = $courseToBeMap;
                    update_user_meta($user_id, 'course_to_be_map', $currentCourses);
                }
            } else {
                $currentCourses = [];
                update_user_meta($user_id, 'course_to_be_map', $currentCourses);
            }
            if (!empty($org_details)) {
                error_log("Org Details: " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                    $this->safeValue(isset($org_details->org_id) ? $org_details->org_id : 'undefined') .
                    ", sub_id: " . $this->safeValue($sub_id) .
                    " already registered user\n", 3, ABSPATH . "error-logs/m-custom-errors.log");
                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                $decoded_value = base64_decode($org_encoded);
                $decoded_val = explode("@@@", $decoded_value);
                $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : "";
                if (!empty($org_id) && $org_id != 0) {
                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                    $org_action = "add";
                    if (!empty($details_from_org_objects)) {
                        if (array_key_exists($org_id, $details_from_org_objects)) {
                            $org_action = "update";
                        }
                    }
                    $details = [
                        'user_id' => $user_id,
                        'datetime' => $datetime,
                        'type' => $org_user_mode,
                        'org_id' => $org_id,
                        'org_action' => $org_action,
                        'crm_id' => $crm_id,
                        'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                        "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                        "programs" => isset($org_details->programs) ? $org_details->programs : '',
                        'parents' => isset($org_details->parents) ? json_encode($org_details->parents, JSON_UNESCAPED_SLASHES) : ''
                    ];
                    signin_signedup_update_org_users_object($details);
                }
            }
            $user_obj = [
                "name" => $display_name,
                "email" => $uemailid,
                "image" => $picture
            ];
            $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
            $basic_details = [
                "locale" => "",
                "registration_date" => date("Y-m-d H:i:s"),
                "last_login_time" => date("Y-m-d H:i:s"),
                "zoho_lead_id" => $userLeadId
            ];
            $arguments = [
                "user" => $user_obj,
                "basic_details" => $basic_details,
                "role" => $role,
                "user_id" => $user_id,
                "user_existance" => false
            ];
            $this->saveUserToElasticsearch($arguments);
            $authToken = "";
            $mobile_web_token = "";
            if (!empty($_COOKIE["CURRENT_USER_TOKEN"])) {
                $authToken = "Bearer " . $_COOKIE["CURRENT_USER_TOKEN"];
                $mobile_web_token = $_COOKIE["CURRENT_USER_TOKEN"];
            } else {
                $token_result = create_jwt_token($user_id);
                if (is_wp_error($token_result)) {
                    error_log("JWT token creation error: " . $token_result->get_error_message(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    $authToken = "";
                    $mobile_web_token = "";
                } else {
                    $auth_token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
                    $authToken = "Bearer " . $auth_token;
                    $mobile_web_token = $auth_token;
                }
            }
            $this->updateUserMetaData($user_id, $response, $decodedPayload, $email, $sub_id, $org_details, $uemailid);
            $mobile = isset($stateArray->mobile) ? $stateArray->mobile : '';
            $categoryURL = isset($stateArray->categoryURL) ? $stateArray->categoryURL : 'general';
            $productCode = isset($stateArray->productCode) ? $stateArray->productCode : '';
            $leadStatus = isset($stateArray->leadStatus) ? $stateArray->leadStatus : '';
            $variant = isset($stateArray->variant) ? $stateArray->variant : '';
            $utmSource = isset($stateArray->utmSource) ? $stateArray->utmSource : '';
            $utmCampaign = isset($stateArray->utmCampaign) ? $stateArray->utmCampaign : '';
            $utmMedium = isset($stateArray->utmMedium) ? $stateArray->utmMedium : '';
            $adGroupID = isset($stateArray->adGroupID) ? $stateArray->adGroupID : '';
            $adContent = isset($stateArray->adContent) ? $stateArray->adContent : '';
            $utmTerm = isset($stateArray->utmTerm) ? $stateArray->utmTerm : '';
            $gclid = isset($stateArray->gclid) ? $stateArray->gclid : '';
            $content = isset($stateArray->content) ? $stateArray->content : '';
            $yuno_referral_url = isset($stateArray->referral_url) ? $stateArray->referral_url : '';
            $yuno_redirect_url = $stateArray->redirect_url;
            $landing_page = isset($stateArray->landing_page) ? $stateArray->landing_page : '';
            $landing_page_url = "";
            $landing_page_title = "";
            if (!empty($landing_page)) {
                $landing_page_url = isset($landing_page->url) ? $landing_page->url : '';
                $landing_page_title = isset($landing_page->title) ? $landing_page->title : '';
                update_user_meta($user_id, 'Yuno_Landing_Page_Info', [$landing_page_url, $landing_page_title]);
            }
            if (!empty($yuno_redirect_url)) {
                update_user_meta($user_id, 'redirect_url', $yuno_redirect_url);
            }
            if (empty($mobile)) {
                if ($org_phone != 0) {
                    $mobile = $org_phone;
                }
            }
            if ($mobile != '' && $mobile != false) {
                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
            }
            $contentType = '';
            if (!empty($content)) {
                $contentType = isset($content->type) ? $content->type : '';
                if (!empty($contentType)) {
                    update_user_meta($user_id, 'xycontentType', $contentType);
                }
                $contentId = isset($content->id) ? $content->id : '';
                $webinarPrivateClassArray = array("privateClass", "webinar", "learning_content", "collection", "course", "category", "quiz", "writing_task", "document", "demo_class_link", "blog", "article", "video", "ebook");
                if (!empty($contentType) && !empty($contentId) && in_array($contentType, $webinarPrivateClassArray)) {
                    try {
                        direct_user_enrollment_in_class($contentId, $user_id);
                    } catch (Exception $e) {
                        error_log("Enrollment error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }
                }
            }
            //first arrival
            if (!empty($org_details)) {
                $org_encoded = isset($org_details->org_id) ? $org_details->org_id : '';
                $org_user_mode = isset($org_details->type) ? $org_details->type : '';
                $org_phone = isset($org_details->phone) ? $org_details->phone : "";
                $decoded_value = base64_decode($org_encoded);
                $decoded_val = explode("@@@", $decoded_value);
                $org_id_ref = isset($decoded_val[0]) ? $decoded_val[0] : '';
                $datetime = $decoded_val[1]; // need to verify it's storage place
                $org_id = !empty($org_id_ref) ? $org_id_ref : 0;
                $redirect_url = get_post_meta($org_id, 'org_redirect_url', true);
                $org_redirect_url = isset($org_details->org_url) ? $org_details->org_url : $redirect_url;
                $crm_id = isset($org_details->crm_id) ? $org_details->crm_id : $org_details->org_id;
                update_user_meta($user_id, 'user_registration_org_url', $org_redirect_url);
                if (!empty($org_id) && $org_id != 0) {
                    $details_from_org_objects = get_user_meta($user_id, 'details_from_org', true);
                    $org_action = "add";
                    if (!empty($details_from_org_objects)) {
                        if (array_key_exists($org_id, $details_from_org_objects)) {
                            $org_action = "update";
                        }
                    }
                    $details = [
                        'user_id' => $user_id,
                        'datetime' => $datetime,
                        'type' => $org_user_mode,
                        'org_id' => $org_id,
                        'crm_id' => $crm_id,
                        'business_unit' => isset($org_details->business_unit) ? $org_details->business_unit : '',
                        "cohort" => isset($org_details->cohort) ? $org_details->cohort : '',
                        "programs" => isset($org_details->programs) ? $org_details->programs : '',
                        'parents' => isset($org_details->parents) ? json_encode($org_details->parents, JSON_UNESCAPED_SLASHES) : ''
                    ];
                    signin_signedup_update_org_users_object($details);
                }
            }
            if (empty($mobile)) {
                if ($org_phone != 0) {
                    $mobile = $org_phone;
                }
            }
            if ($mobile != '' && $mobile != false) {
                update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
            }
            if ($categoryURL != '' && $categoryURL != false) {
                $categoryURL = str_replace("/", "", $categoryURL);
                if (strtolower($categoryURL) == "nocategory") {
                    update_user_meta($user_id, 'Home_Page_Signup_Form', true);
                    $categoryURL = '';
                } else if (strtolower($categoryURL) == "general") {
                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                    $categoryURL = '';
                } else {
                    update_user_meta($user_id, 'Category_URL_For_Signup', [strtolower($categoryURL)]);
                }
            }
            if ($productCode != '' && $productCode != false) {
                update_user_meta($user_id, 'Yuno_Product_Code', $productCode);
            }
            if ($leadStatus != '' && $leadStatus != false) {
                update_user_meta($user_id, 'Yuno_Lead_Status', $leadStatus);
            }
            if ($variant != '' && $variant != false) {
                update_user_meta($user_id, 'Yuno_Variant', $variant);
            }
            if ($utmSource != '' && $utmSource != false) {
                update_user_meta($user_id, 'Yuno_UTM_Source', $utmSource);
            }
            if ($utmCampaign != '' && $utmCampaign != false) {
                update_user_meta($user_id, 'Yuno_UTM_Campaign', $utmCampaign);
            }
            if ($utmMedium != '' && $utmMedium != false) {
                update_user_meta($user_id, 'Yuno_UTM_Medium', $utmMedium);
            }
            if ($adGroupID != '' && $adGroupID != false) {
                update_user_meta($user_id, 'Yuno_Ad_Group_ID', $adGroupID);
            }
            if ($adContent != '' && $adContent != false) {
                update_user_meta($user_id, 'Yuno_Ad_Content', $adContent);
            }
            if ($utmTerm != '' && $utmTerm != false) {
                update_user_meta($user_id, 'Yuno_UTM_Term', $utmTerm);
            }
            if ($gclid != '' && $gclid != false) {
                update_user_meta($user_id, 'Yuno_GCLID', $gclid);
            }
            error_log("utm_params in stateeee arrayy" . date("Y-m-d H:i:s") . " === " . json_encode($stateArray, JSON_UNESCAPED_SLASHES) . "\n\n", 3, ABSPATH . "error-logs/utmparams.log");
            $data = [
                'data' => [
                    'data' => [
                        'details' => [
                            'user_id' => $user_id,
                            'utm_params' => [
                                'YL_medium' => $utmMedium,
                                'YL_lead_source' => $utmSource,
                                'YL_keyword' => $utmMedium,
                                'YL_campaign' => $utmCampaign,
                                'YL_ad_group' => $adGroupID,
                                'YL_ad_content' => $adContent
                            ]
                        ]
                    ]
                ]
            ];
            \UserElasticSearch::update_signedup("utm-params", $data);
            insert_notification($user_id);
            /*END*/
            //email notification send to new user
            if ($landing_page_url != site_url('/ielts/become-an-instructor/') && $usr_role != 'org-admin') {
                try {
                    email_notification('WELCOME_NEW_USER', $user_id);
                } catch (Exception $e) {
                    error_log("Email notification error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
            }
            $userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
            try {
                user_last_login_time($user_id, $userLeadId);
            } catch (Exception $e) {
                error_log("Login time update error: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            }
            if (!empty($content)) {
                $webinarPrivateClassArray = array("privateClass", "webinar");
            }
            // =============================================================================
            // CRITICAL FIX: Use EXACT same redirect logic as Google login for Apple ID
            // This ensures Apple ID follows the same flow based on stateArray and completion status
            // =============================================================================
            
            wp_clear_auth_cookie();
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);
            update_user_meta($user_id, 'user_source', "yuno");
            
            // Use the EXACT same auth.js-based redirect determination as existing users
            // This ensures new users get the proper signup flow based on their completion status
            $finalRedirectUrl = $this->determineRedirectUrl($user_id, $stateArray);
            
            // Handle org resources redirect first (if needed)
            $args = [
                "org_redirect_url" => $org_redirect_url,
                "org_encoded" => $org_encoded,
                "mobile_web_token" => $mobile_web_token,
                "user_id" => $user_id,
                "yuno_redirect_url" => $yuno_redirect_url
            ];
            //yuno_resources_redirection($args);
            error_log("Cognito NEW USER redirect (same as Google): " . date("Y-m-d H:i:s") . "\n" . " === email === " .
                $this->safeValue($UEmail) . ", sub_id: " . $this->safeValue($sub_id) .
                " NEW USER being redirected to: " . $finalRedirectUrl . "\n", 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            
            // Use auth.js-based redirect URL exactly like Google login
            wp_redirect($finalRedirectUrl);
            exit();
        } else {
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL);
            die("exit");
        }
        wp_redirect($redirect_u);
    }

    /**
     * Processes account switching based on the provided authentication code.
     *
     * @param string $authCode The authentication code from OAuth provider
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function processAccountSwitch($authCode)
    {
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            
            // Debug mode check - add ?debug=1 to URL to see detailed errors
            $debug_mode = isset($_GET['debug']) && $_GET['debug'] == 1;
            
            try {
            $client = new \Google_Client();
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
                
                // Fetch token with better error handling
                try {
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
                    

                    
                    // Check if token array is valid
                    if (!is_array($token)) {
                        echo "<h1>Error: Invalid Token Response</h1>";
                        echo "<p>Expected an array but got: " . gettype($token) . "</p>";
                        if ($token instanceof \Exception) {
                            echo "<p>Exception message: " . $token->getMessage() . "</p>";
                        } else {
                            echo "<p>Response: " . htmlspecialchars(print_r($token, true)) . "</p>";
                        }
                        die();
                    }
                    
                    // Check for error in token response
                    if (isset($token['error'])) {
                        echo "<h1>Error: OAuth Token Error</h1>";
                        echo "<p>Error: " . htmlspecialchars($token['error']) . "</p>";
                        if (isset($token['error_description'])) {
                            echo "<p>Description: " . htmlspecialchars($token['error_description']) . "</p>";
                        }
                        
                        // Add special handling for invalid_client error
                        if ($token['error'] === 'invalid_client') {
                            // Get the client ID (obscured for security)
                            $clientId = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                            $obscuredClientId = substr($clientId, 0, 5) . '...' . substr($clientId, -5);
                            
                            echo "<h2>Client Credentials Issue</h2>";
                            echo "<p>The 'invalid_client' error indicates that your OAuth client credentials (client ID and/or client secret) are invalid or not properly configured.</p>";
                            
                            echo "<h3>Troubleshooting Steps:</h3>";
                            echo "<ol>";
                            echo "<li>Check that AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID is correct (currently configured as: <code>" . $obscuredClientId . "</code>)</li>";
                            echo "<li>Verify AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET is correct</li>";
                            echo "<li>Make sure the client ID and secret are for the right Google project</li>";
                            echo "<li>Check that the OAuth consent screen is properly configured in Google Cloud Console</li>";
                            echo "<li>Verify the redirect URI (" . AWS_COGNITO_OAUTH_APP_REDIRECT_URL . ") is registered in the Google API Console</li>";
                            echo "<li>Ensure the application has the necessary OAuth scopes enabled</li>";
                            echo "</ol>";
                        }
                        
                        echo "<p>Full response: <pre>" . htmlspecialchars(json_encode($token, JSON_PRETTY_PRINT)) . "</pre></p>";
                        die();
                    }
                    
                    // Check for required token fields
                    if (!isset($token['access_token'])) {
                        echo "<h1>Error: Missing Access Token</h1>";
                        echo "<p>The OAuth response did not include an access_token.</p>";
                        echo "<p>Full response: <pre>" . htmlspecialchars(json_encode($token, JSON_PRETTY_PRINT)) . "</pre></p>";
                        die();
                    }
                    
                    if (!isset($token['id_token'])) {
                        echo "<h1>Error: Missing ID Token</h1>";
                        echo "<p>The OAuth response did not include an id_token.</p>";
                        echo "<p>Full response: <pre>" . htmlspecialchars(json_encode($token, JSON_PRETTY_PRINT)) . "</pre></p>";
                        die();
                    }
                    
                } catch (\Exception $e) {
                    echo "<h1>Error: Token Fetch Failed</h1>";
                    echo "<p>Failed to fetch access token with authorization code.</p>";
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "<p>Auth code: " . substr($authCode, 0, 10) . "...</p>";
                    echo "<p>Stack trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
                    die();
                }
                
                // Now safely set the access token and continue
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
                
                if (empty($google_id_token)) {
                    echo "<h1>Error: Empty Token</h1>";
                    echo "<p>Empty google_id_token received from OAuth response</p>";
                    die();
                }
                
                // Safely parse the JWT token with error checking at each step
                $token_parts = explode('.', $google_id_token);
                if (count($token_parts) !== 3) {
                    echo "<h1>Error: Invalid Token Format</h1>";
                    echo "<p>Expected 3 parts but got " . count($token_parts) . "</p>";
                    die();
                }
                
                // Check if payload part is properly formatted
                $payload_base64 = $token_parts[1];
                if (empty($payload_base64)) {
                    echo "<h1>Error: Empty Payload</h1>";
                    echo "<p>Token payload part is empty</p>";
                    die();
                }
                
                // Properly handle base64url decoding (JWT uses base64url, not standard base64)
                $payload_base64 = str_replace(['-', '_'], ['+', '/'], $payload_base64);
                $payload_base64 = preg_replace('/[^A-Za-z0-9\+\/=]/', '', $payload_base64);
                $payload_base64 = str_pad($payload_base64, strlen($payload_base64) % 4, '=', STR_PAD_RIGHT);
                
                $payload = base64_decode($payload_base64);
                if ($payload === false) {
                    echo "<h1>Error: Base64 Decode Failed</h1>";
                    echo "<p>Failed to decode token payload from base64</p>";
                    die();
                }
                
                $user_details = json_decode($payload, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    echo "<h1>Error: JSON Decode Failed</h1>";
                    echo "<p>" . json_last_error_msg() . "</p>";
                    echo "<p>Payload: " . htmlspecialchars(substr($payload, 0, 100)) . "...</p>";
                    die();
                }
                
                // Now extract email and sub_id with error checking
                $email = isset($user_details['email']) ? $user_details['email'] : null;
                $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;
                
                if (empty($email)) {
                    echo "<h1>Error: No Email Found</h1>";
                    echo "<p>No email address found in token payload</p>";
                    echo "<p>Token payload: <pre>" . json_encode($user_details, JSON_PRETTY_PRINT) . "</pre></p>";
                    die();
                }
                
            $google_oauth = new \Google_Service_Oauth2($client);
                
                try {
            $google_account_info = $google_oauth->userinfo->get();
                    
                    // Compare emails to make sure they match
                    $oauth_email = $google_account_info->email;
                    if ($email !== $oauth_email) {
                        echo "<h1>Warning: Email Mismatch</h1>";
                        echo "<p>Token email ($email) differs from OAuth email ($oauth_email)</p>";
                        $email = $oauth_email;
                    }
                    
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
                } catch (\Exception $e) {
                    echo "<h1>Error: Google OAuth API Failed</h1>";
                    echo "<p>Error Message: " . $e->getMessage() . "</p>";
                    echo "<p>Unable to retrieve user information from Google OAuth API</p>";
                    die();
                }

            $token_parts = explode('.', $google_id_token);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;
            if (email_exists($email) == false) {
                $params = ["email" => $email, "name" => $name, "picture" => $picture, "google_id_token" => $google_id_token, "sub_id" => $sub_id];
                $response = $this->switchAccountFirstArrival($params);

                $decodedPayload = $this->decodeTokenPayload($response);
                if (isset($response['cognito_sub_id']) && !empty($response['cognito_sub_id'])) {
                    $decodedPayload['cognito_sub_id'] = $response['cognito_sub_id'];
                    $sub_id = $response['cognito_sub_id'];
                }
                
                $stateArray = $this->processStateParameter();
                $this->handleNewUser(0, $response, $decodedPayload, $stateArray, $email, $sub_id, $email, $email);
                return;
            } else {
                // Found user by email - only use email for lookup
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;

                if (empty($user_id)) {
                    // This shouldn't happen as we already checked email_exists, but just in case
                    error_log("Switch account error: email_exists true but get_user_by returned null for email $email",
                        3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        
                        if ($debug_mode) {
                            echo "<h1>Switch Account Error</h1>";
                            echo "<p>Failed to find user with email: $email (email_exists says it exists but get_user_by returns null)</p>";
                            exit;
                        }
                        
                    throw new Exception("Failed to find user with email: $email");
                }

                // Store sub_id in user meta for reference only
                if (!empty($sub_id) && !empty($user_id)) {
                    $existing_sub_id = get_user_meta($user_id, 'cognito_sub_id', true);
                    if (empty($existing_sub_id)) {
                        // Only set cognito_sub_id if it's not already set (first registration)
                        update_user_meta($user_id, 'cognito_sub_id', $sub_id);
                    } else if ($existing_sub_id !== $sub_id) {
                        // If sub_id is different, store it as an alternative ID without changing the main one
                        $alt_sub_ids = get_user_meta($user_id, 'alt_cognito_sub_ids', true);
                        if (empty($alt_sub_ids)) {
                            $alt_sub_ids = array();
                        }
                        if (!in_array($sub_id, $alt_sub_ids)) {
                            $alt_sub_ids[] = $sub_id;
                            update_user_meta($user_id, 'alt_cognito_sub_ids', $alt_sub_ids);
                        }
                    }
                }

                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    // Rest of the authentication code remains the same...
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => self::CREDENTIALS_TYPE_USER_POOL, "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                        error_log("AWS Cognito Error in refresh token auth: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                        
                        echo "<h1>AWS Cognito Error</h1>";
                        echo "<p>Error Message: " . $e->getMessage() . "</p>";
                        echo "<p>Error Code: " . $e->getAwsErrorCode() . "</p>";
                        echo "<p>User ID: " . $user_id . "</p>";
                        echo "<p>Email: " . $email . "</p>";
                        
                        // We'll continue with the identity pool fallback anyway
                        echo "<p>Attempting identity pool fallback authentication...</p>";
                        
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => self::CREDENTIALS_TYPE_IDENTITY_POOL, "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
                            error_log($message, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                            
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger_result = $this->logger->writeLog("error", "ES", "login | signup", $message, $request, $data);
                            
                        echo "<h1>AWS Identity Pool Error</h1>";
                        echo "<p>Error Message: " . $e->getMessage() . "</p>";
                        echo "<p>Error Code: " . $e->getAwsErrorCode() . "</p>";
                        echo "<p>User ID: " . $user_id . "</p>";
                        echo "<p>Email: " . $email . "</p>";
                        die();
                    }
                }
            }
            return $response;
            } catch (\Exception $e) {
                // Log the error for debugging
                $message = "Switch Account Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
                error_log($message, 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                
                $request = ["auth_code" => $authCode];
                $user = [];
                $logger_result = $this->logger->writeLog($logtype, $module, $action, $message, $request, $data);
                
                // Display detailed error information on screen
                echo "<h1>Account Switching Error</h1>";
                echo "<p><strong>Error Message:</strong> " . $e->getMessage() . "</p>";
                echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
                echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
                echo "<p><strong>Stack Trace:</strong></p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
                die();
            }
        }
        
        // If no auth code provided, show error
        echo "<h1>Authentication Error</h1>";
        echo "<p>No authorization code provided.</p>";
        die();
    }

    /**
     * Switches the account on the first arrival based on the provided parameters.
     *
     * @param array $params The parameters for switching the account.
     * @throws Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function switchAccountFirstArrival($params)
    {
        $logtype = "error";
        $module = "ES";
        $action = "header - login | signup";
        $data = [];
        
        // Debug mode check - add ?debug=1 to URL to see detailed errors
        $debug_mode = isset($_GET['debug']) && $_GET['debug'] == 1;
        
        $clients = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
            'version' => 'latest',
            'region' => 'ap-south-1', // e.g., us-east-1
            'credentials' => [
                'key' => AWS_COGNITO_IAM_USER_KEY,
                'secret' => AWS_COGNITO_IAM_USER_SECRET,
            ],
        ]);
        
        $cognito_sub_id = null;
        
        //not found user
        try {
            // Extract or generate a sub_id if needed
            $token_parts = explode('.', $params['google_id_token']);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            $sub_id = isset($user_details['sub']) ? $user_details['sub'] : null;

            // First, try to get the user to check if they exist in Cognito
            try {
                $userResult = $clients->adminGetUser([
                    'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                    'Username' => $params['email'],
                ]);
                
                // User exists in Cognito, get the Cognito sub_id
                $userAttributes = $userResult->get('UserAttributes');
                foreach ($userAttributes as $attribute) {
                    if ($attribute['Name'] === 'sub') {
                        $cognito_sub_id = $attribute['Value'];
                        error_log("Retrieved existing Cognito sub_id: $cognito_sub_id", 3, ABSPATH . "error-logs/cognito-custom-info.log");
                        break;
                    }
                }
                
                // User exists in Cognito, no need to create
                error_log("User already exists in Cognito: " . $params['email'], 3, ABSPATH . "error-logs/cognito-custom-info.log");
                
                // Check if they are in the right group
                try {
                    $groupsResult = $clients->adminListGroupsForUser([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'Username' => $params['email'],
                    ]);
                    
                    $inCorrectGroup = false;
                    foreach ($groupsResult['Groups'] as $group) {
                        if ($group['GroupName'] === AWS_COGNITO_USER_GROUP_NAME) {
                            $inCorrectGroup = true;
                            break;
                        }
                    }
                    
                    // Add to group if not already in it
                    if (!$inCorrectGroup) {
                        $clients->adminAddUserToGroup([
                            'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                            'Username' => $params['email'],
                            'GroupName' => AWS_COGNITO_USER_GROUP_NAME,
                        ]);
                    }
                } catch (\Exception $e) {
                    // Just log the error but continue
                    error_log("Error checking user groups: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                }
                
            } catch (\Aws\CognitoIdentityProvider\Exception\CognitoIdentityProviderException $e) {
                // User doesn't exist, create them
                if ($e->getAwsErrorCode() === 'UserNotFoundException') {
                    // Create user in Cognito
                    $result = $clients->adminCreateUser([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'Username' => $params['email'],
                        'UserAttributes' => [
                            [
                                'Name' => 'email',
                                'Value' => $params['email'],
                            ],
                            [
                                'Name' => 'email_verified',
                                'Value' => 'false', // Mark the email as verified
                            ],
                            [
                                'Name' => 'name',
                                'Value' => $params['name'],
                            ],
                            [
                                'Name' => 'picture',
                                'Value' => $params['picture'],
                            ],
                            // Remove the sub ID attribute as it's non-mutable in Cognito
                        ],
                        'TemporaryPassword' => 'TempPassword1!', // Optionally set a temporary password
                        'MessageAction' => 'SUPPRESS', // Use 'SUPPRESS' if you don't want to send an email
                    ]);
                    
                    // Step 2: Add the user to the specified group
                    $group_username = $result['User']['Username'];
                    $groupResult = $clients->adminAddUserToGroup([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'Username' => $group_username,
                        'GroupName' => AWS_COGNITO_USER_GROUP_NAME,
                    ]);
                    
                    // Retrieve the Cognito-generated sub_id for the newly created user
                    try {
                        $userResult = $clients->adminGetUser([
                            'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                            'Username' => $params['email'],
                        ]);
                        
                        $userAttributes = $userResult->get('UserAttributes');
                        foreach ($userAttributes as $attribute) {
                            if ($attribute['Name'] === 'sub') {
                                $cognito_sub_id = $attribute['Value'];
                                error_log("Retrieved newly created Cognito sub_id: $cognito_sub_id", 3, ABSPATH . "error-logs/cognito-custom-info.log");
                                break;
                            }
                        }
                    } catch (\Exception $e) {
                        error_log("Error retrieving Cognito sub_id after user creation: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
                    }
                } else {
                    // Some other error occurred
                    throw $e;
                }
            }
        } catch (\Aws\Exception\AwsException $e) {
            // Output error message if something goes wrong
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $request = ["email" => $params['email']];
            $user = ["email" => $params['email']];
            $logger_result = $this->logger->writeLog($logtype, $module, $action, $message, $request, $data);
            
            // Display error to user instead of exiting silently
            echo "<h1>Error: Cognito User Creation Failed</h1>";
            echo "<p>We encountered an error while creating your user account.</p>";
            echo "<p>Please try again or contact support if the issue persists.</p>";
            
            if ($debug_mode) {
                echo "<p>Error details: " . $e->getMessage() . "</p>";
                echo "<p>Error code: " . $e->getAwsErrorCode() . "</p>";
                echo "<p>Request ID: " . $e->getAwsRequestId() . "</p>";
            }
            
            error_log("Cognito User Creation Failed: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            exit();
        }
        
        // Now get identity credentials
        try {
            $identityClient = new \Aws\CognitoIdentity\CognitoIdentityClient([
                'version' => 'latest',
                'region' => 'ap-south-1', // e.g., us-east-1
                'credentials' => [
                    'key' => AWS_COGNITO_IAM_USER_KEY,
                    'secret' => AWS_COGNITO_IAM_USER_SECRET,
                ],
            ]);
            $results = $identityClient->getId([
                'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                'Logins' => [
                    'accounts.google.com' => $params['google_id_token'],
                ],
            ]);
            $identityId = $results['IdentityId'];
            $credentialsResult = $identityClient->getCredentialsForIdentity([
                'IdentityId' => $identityId,
                'Logins' => [
                    'accounts.google.com' => $params['google_id_token'],
                ],
            ]);
            $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
            $secretKey = $credentialsResult['Credentials']['SecretKey'];
            $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                            $response = [
                    "google_id_token" => $params['google_id_token'], 
                    "id_token" => $sessionToken, 
                    "access_token" => $accessKeyId, 
                    "refresh_token" => $secretKey, 
                    "credentials_type" => self::CREDENTIALS_TYPE_IDENTITY_POOL, 
                    "user_existence" => false,
                    "cognito_sub_id" => $cognito_sub_id
                ];
        } catch (\Aws\Exception\AwsException $e) {
            $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            $request = ["email" => $params['email']];
            $user = ["google_id_token" => $params['google_id_token']];
            $logger_result = $this->logger->writeLog($logtype, $module, $action, $message, $request, $data);
            
            // Display error to user instead of exiting silently
            echo "<h1>Error: Cognito Identity Failed</h1>";
            echo "<p>We encountered an error while setting up your account credentials.</p>";
            echo "<p>Please try again or contact support if the issue persists.</p>";
            
            if ($debug_mode) {
                echo "<p>Error details: " . $e->getMessage() . "</p>";
                echo "<p>Error code: " . $e->getAwsErrorCode() . "</p>";
                echo "<p>Request ID: " . $e->getAwsRequestId() . "</p>";
            }
            
            error_log("Cognito Identity Failed: " . $e->getMessage(), 3, ABSPATH . "error-logs/cognito-custom-errors.log");
            exit();
        }
        
        // Log successful account creation
        error_log("New user account created successfully: " . $params['email'], 3, ABSPATH . "error-logs/cognito-custom-success.log");
        return $response;
    }

    /**
     * Processes virtual account switching for Google Meet integration.
     *
     * @param string $authCode The authentication code from OAuth provider
     * @param int $org_id The organization ID
     * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
     * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
     */
    public function processVirtualAccountSwitch($authCode, $org_id)
    {
        date_default_timezone_set('Asia/Kolkata');
        if (!empty($authCode)) {
            $logtype = "error";
            $module = "ES";
            $action = "header - login | signup";
            $data = [];
            $client = new \Google_Client();
            $client->setClientId(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID);
            $client->setClientSecret(AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET);
            $client->setRedirectUri(AWS_COGNITO_OAUTH_APP_REDIRECT_URL);
            $client->addScope("email");
            $client->addScope("profile");
            $client->addScope("openid");
            $client->addScope("https://www.googleapis.com/auth/calendar");
            $client->addScope("https://www.googleapis.com/auth/drive");
            $client->addScope("https://www.googleapis.com/auth/calendar.events");
            $client->addScope("https://www.googleapis.com/auth/admin.reports.audit.readonly");
            $token = $client->fetchAccessTokenWithAuthCode($authCode);
            $client->setAccessToken($token['access_token']);
            $google_id_token = $token['id_token'];
            $google_oauth = new \Google_Service_Oauth2($client);
            $google_account_info = $google_oauth->userinfo->get();
            $email = $google_account_info->email;
            $name = $google_account_info->name;
            $picture = $google_account_info->picture;
            if (strpos($email, "@gmail.com") !== false) {
              wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
              die("exit");
            }

            if (email_exists($email) == false) {
                // Get the current logged-in user's ID
                $user_id = get_current_user_id();
                // update_user_meta($user_id, 'google_meet_vc', "true");
                // update_user_meta($user_id, 'ins_meet_permission', "true");
                // update_user_meta($user_id, 'zoom_vc', "false");
                // update_user_meta($user_id, 'zoom_user_status',"yuno-licenced");
                // update_user_meta($user_id, 'zoom_refresh_token', "test");
                // Example: Adding or updating the "meet" data with org_id = 0, academy_id = 0
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'] ?? "",
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                // Query Elasticsearch to retrieve the plan
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);

                // Access specific values
                $returnStatus = $data['error']['status'];
                if ($returnStatus == "UNAUTHENTICATED") {
                  wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                  die("exit");
                }

                $this->oauthModel->saveVirtualAuthAccess($user_id,$meet_entry);
                $response = ["google_id_token" => $google_id_token, "id_token" => $google_id_token, "access_token" => $token['access_token'], "refresh_token" => $token['refresh_token'], "credentials_type" => self::CREDENTIALS_TYPE_VIRTUAL_IDENTITY, "user_existence" => false];
            } else {
                //found user
                $users = get_user_by('email', $email);
                $user_id = $users->ID ?? 0;
                // update_user_meta($user_id, 'google_meet_vc', "true");
                // update_user_meta($user_id, 'ins_meet_permission', "true");
                // update_user_meta($user_id, 'zoom_vc', "false");
                // update_user_meta($user_id, 'zoom_user_status',"yuno-licenced");
                // update_user_meta($user_id, 'zoom_refresh_token', "test");
                // Example: Adding or updating the "meet" data with org_id = 0, academy_id = 0
                $meet_entry = [
                  'org_id' => $org_id,
                  'academies' => get_post_meta($org_id, 'academies', true),
                  'virtual_classroom' => [
                      'meet' => [
                          'access_token' => $token['access_token'],
                          'refresh_token' => $token['refresh_token'],
                          'id_token' => $google_id_token,
                          'token_type' => $token['token_type'],
                          'expires_in' => time() + $token['expires_in'],
                          'email' => $email,
                          'name' => $name,
                          'scope' => $token['scope']
                      ]
                  ]
                ];
                $user_id = get_current_user_id();
                // Query Elasticsearch to retrieve the plan
                $url = GOOGLE_MEET_API_URL;
                $headers = [
                  "Authorization: Bearer " .$token['access_token'],
                ];
                $curlPost = '';
                $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');

                // Decode JSON into associative array
                $data = json_decode($return['response'], true);

                // Access specific values
                $returnStatus = $data['error']['status'];
                if ($returnStatus == "UNAUTHENTICATED") {
                  wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=false");
                  die("exit");
                }

                $this->oauthModel->saveVirtualAuthAccess($user_id,$meet_entry);
                $get_yuno_user_refresh_token = get_user_meta($user_id, 'yuno_user_refresh_token', true);
                try {
                    $client_object = new \Aws\CognitoIdentityProvider\CognitoIdentityProviderClient([
                        'version' => 'latest',
                        'region' => 'ap-south-1', // e.g., us-east-1
                        'credentials' => [
                            'key' => AWS_COGNITO_IAM_USER_KEY,
                            'secret' => AWS_COGNITO_IAM_USER_SECRET,
                        ],
                    ]);
                    // Rest of the authentication code remains the same...
                    $resultant = $client_object->adminInitiateAuth([
                        'UserPoolId' => AWS_COGNITO_USER_POOL_ID,
                        'AuthFlow' => 'REFRESH_TOKEN_AUTH',
                        'ClientId' => AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                        'AuthParameters' => [
                            'REFRESH_TOKEN' => $get_yuno_user_refresh_token,
                        ],
                    ]);
                    // Extract the access token from the response
                    $accessToken = $resultant->get('AuthenticationResult')['AccessToken'];
                    // Optionally, you can also retrieve the new ID token and refresh token
                    $idToken = $resultant->get('AuthenticationResult')['IdToken'];
                    //$newRefreshToken = $result->get('AuthenticationResult')['RefreshToken'];
                    $response = ["google_id_token" => $google_id_token, "id_token" => $idToken, "access_token" => $accessToken, "credentials_type" => self::CREDENTIALS_TYPE_VIRTUAL_IDENTITY, "user_existence" => true];
                } catch (\Aws\Exception\AwsException $e) {
                    // Handle the error
                    //echo "Error: " . $e->getMessage();
                    try {
                        $clients = new \Aws\CognitoIdentity\CognitoIdentityClient([
                            'version' => 'latest',
                            'region' => 'ap-south-1', // e.g., us-east-1
                            'credentials' => [
                                'key' => AWS_COGNITO_IAM_USER_KEY,
                                'secret' => AWS_COGNITO_IAM_USER_SECRET,
                            ],
                        ]);
                        $result = $clients->getId([
                            'IdentityPoolId' => AWS_COGNITO_IDENTITY_POOL_ID,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $identityId = $result['IdentityId'];
                        $credentialsResult = $clients->getCredentialsForIdentity([
                            'IdentityId' => $identityId,
                            'Logins' => [
                                'accounts.google.com' => $google_id_token,
                            ],
                        ]);
                        $accessKeyId = $credentialsResult['Credentials']['AccessKeyId'];
                        $secretKey = $credentialsResult['Credentials']['SecretKey'];
                        $sessionToken = $credentialsResult['Credentials']['SessionToken'];
                        $response = ["google_id_token" => $google_id_token, "id_token" => $sessionToken, "access_token" => $accessKeyId, "refresh_token" => $secretKey, "credentials_type" => self::CREDENTIALS_TYPE_VIRTUAL_IDENTITY, "user_existence" => true];
                    } catch (\Aws\Exception\AwsException $e) {
                        //echo $e->getMessage();
                        $message = "Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine(); // Optionally, display a user-friendly message to the user
                        $request = ["email" => $email];
                        $user = ["google_id_token" => $google_id_token];
                        $logger_result = $this->logger->writeLog("error", "ES", "login | signup", $message, $request, $data);
                        exit();
                    }
                }
            }
            wp_redirect(YUNO_OAUTH_APP_PROFILE_URL."?org_id=".$org_id."&is_connected=true");
            die("exit");
            return $response;
        }
    }

    /**
     * Saves the user data in Elasticsearch based on the given parameters.
     *
     * @param array $params An associative array containing the following keys:
     *                      - user_existance: A boolean indicating if the user exists or not.
     *                      - user_id: The ID of the user.
     *                      - role: The role of the user.
     *                      - user: The user object.
     *                      - basic_details: The basic details of the user.
     * @return void
     * @throws Exception If Elasticsearch operation fails
     */
    public function saveUserToElasticsearch($params)
    {
      if ($params['user_existance'] === true) {
        $curlPost['data'] = [
          "data" => [
              "details" => [
                  "user_id" => $params['user_id'],
              ],
          ],
      ];
      \UserElasticSearch::update_signedup("login", $curlPost);
      }
      else {
        $location_obj = [
          "country" => "",
          "pin_code" => "",
          "flat_house_number" => "",
          "street" => "",
          "landmark" => "",
          "city" => "",
          "state" => "",
          "address_type" => "",
      ];
	  $region_obj = [
		"country" => [
			"id"=> null,
			"name" => "",
			"code" => ""
		],
		"timezone" => "",
		"currency" => [
			"code" => "",
			"name" => "",
			"symbol" => "",
			"symbol_html" => ""
		],
		"language" => [
			"name" => "",
			"native" => "",
			"code" => ""
		]
      ];
      $utm_params = [
          "YL_medium" => "",
          "YL_lead_source" => "",
          "YL_keyword" => "",
          "YL_campaign" => "",
          "YL_ad_group" => "",
          "YL_ad_content" => "",
      ];
      $curlPost['data'] = [
          "data" => [
              "details" => [
                  "user_id" => $params['user_id'],
                  "event_type" => "signedup",
                  "event_label" => "User signed up",
                  "role" => $params['role'],
                  "user" => $params['user'],
                  "basic_details" => $params['basic_details'],
                  "location" => $location_obj,
		  "region" => $region_obj,
                  "utm_params" => $utm_params,
              ],
              "@timestamp" => date("Y-m-d H:i:s"),
          ],
      ];
      \UserElasticSearch::create_signedup("login", $curlPost);
      }
    }

    /**
     * Redirects the user to Yuno resources based on the provided parameters.
     *
     * @param array $params An array containing the following parameters:
     *                      - 'org_redirect_url' (string): The URL to redirect the user to.
     *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
     *                      - 'user_id' (int): The ID of the user.
     *                      - 'mobile_web_token' (string): The mobile web token.
     *                      - 'yuno_redirect_url' (string): The Yuno redirect URL.
     *
     * @return void
     */
    public function redirectToYunoResources($params)
    {
      if (!empty($params['org_redirect_url'])) {
        if (empty($params['org_encoded'])) {
        //if (is_mobile_web_device()) {
            update_user_meta($params['user_id'], 'user_source', "other");
            $app_redirected_url = $params['org_redirect_url']."?user_id=".$params['user_id']."&yuno_token=".$params['mobile_web_token'];
            wp_redirect($app_redirected_url);
            die();
        //}
        } else {
            // Check if the 'org_encoded' variable is empty
            if (empty($params['org_encoded'])) {
                // If 'org_encoded' is empty, append the 'user_id' parameter to the 'org_redirect_url'
                $redirected_url = $params['org_redirect_url'] . "?user_id=" . $params['user_id'];
            }
            else {
                // If 'org_encoded' is not empty, append both 'user_id' and 'yuno_token' parameters to the 'org_redirect_url'
                $redirected_url = $params['org_redirect_url']. "?user_id=" . $params['user_id']."&yuno_token=".$params['mobile_web_token'];
            }
            wp_redirect($redirected_url);
            die();
        }
      }
      $yuno_redirect_url =  $params['yuno_redirect_url'];
      if (!empty($yuno_redirect_url)) {
        wp_redirect($yuno_redirect_url);
        die("exited");
      }
    }

    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     *
     * If the access token is expired, it refreshes the token and saves the new token
     * to the user meta.
     *
     * @param int $user_id The WordPress user ID.
     * @param int $org_id The org ID.
     * @return string The Google Meet access token.
     * @throws \Exception If an error occurs while retrieving or refreshing the token.
     */
    public function getGoogleMeetAccessToken($user_id, $org_id) {
        try {
            return $this->oauthModel->getGoogleMeetAccessToken($user_id, $org_id);
        } catch (\Exception $e) {
            // Log error
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['user_id' => $user_id],
                'data' => []
            ];
            $this->logger->writeLog($logDetails['logtype'], $logDetails['module'], $logDetails['action'], $logDetails['message'], $logDetails['request'], $logDetails['data']);
            return "invalid token";
        }
    }

    /**
     * Checks if a user has the required permissions for virtual classroom.
     * 
     * @param int $userId The user ID to check.
     * @return bool True if the user has the required permissions, false otherwise.
     */
    public function checkUserVirtualClassroomPermissions($userId) {
        $meta_key = 'virtual_classroom_data';
        $data = get_user_meta($userId, $meta_key, true);
        $org_id = (int)get_user_meta($userId, 'active_org', true) ?? 0;
        $has_required_scopes = false;
        if (isset($data['data']) && is_array($data['data'])) {
            foreach ($data['data'] as $item) {
                if (isset($item['virtual_classroom']['meet'])) {
                    $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                    $required_scopes = ['https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events'];
                    $scopes = explode(' ', $filtered_virtual_classroom['scope'] ?? '');
                    $has_required_scopes = !array_diff($required_scopes, $scopes);
                    break;
                }
            }
        }
        if ($has_required_scopes) {
            return true;
        } else {
            return false;
        }
    }
    
    // =============================================================================
    // NEW: STEP 1 - State Parameter Processing & LocalStorage Simulation Functions
    // =============================================================================
    
    /**
     * Simulates localStorage.getItem() functionality using state parameters
     * NOTE: Since localStorage is client-side only, we'll use the state parameter data
     * and derive localStorage-equivalent values from the state context
     * 
     * @param object $stateArray Decoded OAuth state parameter
     * @param string $key LocalStorage key to simulate
     * @return mixed Value equivalent to localStorage.getItem(key) or null
     */
    private function getLocalStorageSimulation($stateArray, $key) {
        switch ($key) {
            case 'paymentState':
                // In auth.js: localStorage.getItem('paymentState')
                // For server-side, we'll check if there's payment context in state
                if (isset($stateArray->payment_context)) {
                    return json_encode($stateArray->payment_context);
                }
                return null;
                
            case 'userState': 
                // In auth.js: localStorage.getItem('userState')
                // This represents the original page the user was on before OAuth
                // COMPLETE BACKEND-ONLY SOLUTION: Replicate loginForm.js setSigninProps() logic
                
                // Method 1: Direct userState in state parameter (would require frontend change)
                if (isset($stateArray->userState) && !empty($stateArray->userState)) {
                    error_log("AUTH.JS LOGIC: userState found in state parameter: " . $stateArray->userState, 3, ABSPATH . "error-logs/auth-js-logic.log");
                    return $stateArray->userState;
                }
                
                // Method 2: PERFECT REPLICATION - Extract userState using exact loginForm.js logic
                // loginForm.js setSigninProps(): 
                // if (landingPage.redirect_url !== "") {
                //     localStorage.setItem('userState', landingPage.redirect_url);
                // } else {
                //     localStorage.setItem('userState', window.location.pathname + window.location.search);
                // }
                
                // Check if redirect_url exists (equivalent to landingPage.redirect_url)
                if (isset($stateArray->redirect_url) && !empty($stateArray->redirect_url)) {
                    $redirectUrl = $stateArray->redirect_url;
                    $parsedUrl = parse_url($redirectUrl);
                    $userStatePath = ($parsedUrl['path'] ?? '/');
                    
                    if (!empty($parsedUrl['query'])) {
                        $userStatePath .= '?' . $parsedUrl['query'];
                    }
                    
                    error_log("AUTH.JS LOGIC: userState from redirect_url (matching landingPage.redirect_url logic): $userStatePath", 3, ABSPATH . "error-logs/auth-js-logic.log");
                    return $userStatePath;
                }
                
                // Fallback: Extract from landing_page.url (equivalent to window.location.pathname + window.location.search)
                if (isset($stateArray->landing_page->url) && !empty($stateArray->landing_page->url)) {
                    $landingUrl = $stateArray->landing_page->url;
                    $parsedUrl = parse_url($landingUrl);
                    $userStatePath = ($parsedUrl['path'] ?? '/');
                    
                    // Add query parameters if they exist
                    if (!empty($parsedUrl['query'])) {
                        $userStatePath .= '?' . $parsedUrl['query'];
                    }
                    
                    error_log("AUTH.JS LOGIC: userState from landing_page.url (matching window.location.pathname logic): $userStatePath", 3, ABSPATH . "error-logs/auth-js-logic.log");
                    return $userStatePath;
                }
                
                error_log("AUTH.JS LOGIC: userState extraction failed - manageGuestLink will use dashboard fallback", 3, ABSPATH . "error-logs/auth-js-logic.log");
                return null;
                
            case 'isReferrer':
                // In auth.js: localStorage.getItem('isReferrer') 
                // Check if this is a referral flow
                return !empty($stateArray->referral_url) ? 'true' : null;
                
            case 'isQuiz':
                // In auth.js: localStorage.getItem('isQuiz')
                // Check if this is a quiz flow
                $contentType = $stateArray->content->type ?? '';
                return ($contentType === 'quiz') ? 'true' : null;
                
            case 'skipSignUp':
                // In auth.js: localStorage.getItem('skipSignUp')
                // Check if signup should be skipped (usually for org flows)
                $orgType = $stateArray->org_details->type ?? '';
                return ($orgType === 'skip_signup' || !empty($stateArray->skip_signup)) ? 'true' : null;
                
            default:
                return null;
        }
    }
    
    /**
     * Processes state parameter to extract all necessary context
     * Exactly replicates the data that auth.js gets from OAuth state + localStorage
     * 
     * @param object $stateArray Decoded OAuth state parameter  
     * @return array Processed context data matching auth.js expectations
     */
    private function processStateContext($stateArray) {
        return [
            // Direct state parameter data (from loginForm.js)
            'mobile' => $stateArray->mobile ?? '',
            'categoryURL' => $stateArray->categoryURL ?? '',
            'productCode' => $stateArray->productCode ?? '',
            'leadStatus' => $stateArray->leadStatus ?? '',
            'utmSource' => $stateArray->utmSource ?? '',
            'utmCampaign' => $stateArray->utmCampaign ?? '',
            'utmMedium' => $stateArray->utmMedium ?? '',
            'adGroupID' => $stateArray->adGroupID ?? '',
            'adContent' => $stateArray->adContent ?? '',
            'utmTerm' => $stateArray->utmTerm ?? '',
            'gclid' => $stateArray->gclid ?? '',
            'content' => [
                'type' => $stateArray->content->type ?? '',
                'id' => $stateArray->content->id ?? ''
            ],
            'landingPage' => [
                'url' => $stateArray->landing_page->url ?? '',
                'title' => $stateArray->landing_page->title ?? ''
            ],
            'referralUrl' => $stateArray->referral_url ?? '',
            'redirectUrl' => $stateArray->redirect_url ?? '',
            'courseToBeMap' => $stateArray->course_to_be_map ?? '',
            'orgDetails' => [
                'type' => $stateArray->org_details->type ?? 'login',
                'orgId' => $stateArray->org_details->org_id ?? '',
                'orgUrl' => $stateArray->org_details->org_url ?? '',
                'phone' => $stateArray->org_details->phone ?? ''
            ],
            'loginDetails' => [
                'role' => $stateArray->login_details->role ?? ''
            ],
            
            // LocalStorage simulation data
            'localStorageSimulation' => [
                'paymentState' => $this->getLocalStorageSimulation($stateArray, 'paymentState'),
                'userState' => $this->getLocalStorageSimulation($stateArray, 'userState'),
                'isReferrer' => $this->getLocalStorageSimulation($stateArray, 'isReferrer'),
                'isQuiz' => $this->getLocalStorageSimulation($stateArray, 'isQuiz'),
                'skipSignUp' => $this->getLocalStorageSimulation($stateArray, 'skipSignUp')
            ]
        ];
    }
    
    /**
     * Gets role dashboard URL exactly matching auth.js roleDashboardMap
     * 
     * @param string $role User role (Instructor, Learner, etc.)
     * @return string Dashboard URL
     */
    private function getRoleDashboardUrl($role) {
        // Exactly matching auth.js: const roleDashboardMap = { Instructor: "/instructor", Learner: "/learner" };
        $dashboardUrl = self::roleDashboardMap[$role] ?? '/';
        return home_url($dashboardUrl);
    }
    
    // =============================================================================
    // NEW: STEP 2 - User Status Detection Functions (matching auth.js)
    // =============================================================================
    
    /**
     * Gets user signup status by fetching user information
     * Replicates the data that auth.js gets from fetchUserSignUpStatus API call
     * 
     * @param int $userId WordPress user ID
     * @param object $stateArray Decoded OAuth state parameter (for role from login_details)
     * @return array User signup status data matching auth.js expectations
     */
    private function getUserSignupStatus($userId, $stateArray = null) {
        // Get user basic data
        $userData = get_userdata($userId);
        $userRoles = $userData->roles ?? ['subscriber'];
        
        // CRITICAL FIX: Use role from state parameter first (auth.js behavior)
        // This fixes the instructor signup redirect issue
        $role = 'Learner'; // Default role

        // First check the state parameter for intended role (matching auth.js)
        if ($stateArray && isset($stateArray->login_details->role)) {
            $stateRole = $stateArray->login_details->role;
            
            // CRITICAL DEBUG: Track role detection
            error_log("Auth.js Logic: State role detected: '$stateRole'", 3, ABSPATH . "error-logs/auth-js-logic.log");
            
            // CRITICAL FIX: Handle both lowercase and uppercase instructor role
            if ($stateRole === 'instructor' || $stateRole === 'Instructor') {
                $role = 'Instructor';
                error_log("Auth.js Logic: Role set to Instructor based on state parameter", 3, ABSPATH . "error-logs/auth-js-logic.log");
            } elseif ($stateRole === 'org-admin') {
                $role = 'org-admin';
                error_log("Auth.js Logic: Role set to org-admin based on state parameter", 3, ABSPATH . "error-logs/auth-js-logic.log");
            } else {
                error_log("Auth.js Logic: State role '$stateRole' not recognized as instructor/org-admin", 3, ABSPATH . "error-logs/auth-js-logic.log");
            }
        } else {
            // CRITICAL DEBUG: Check what's actually in login_details
            if ($stateArray && isset($stateArray->login_details)) {
                error_log("Auth.js Logic: login_details exists but role is missing or null: " . json_encode($stateArray->login_details), 3, ABSPATH . "error-logs/auth-js-logic.log");
            } else {
                error_log("Auth.js Logic: login_details does not exist in state parameter", 3, ABSPATH . "error-logs/auth-js-logic.log");
            }
            
            error_log("Auth.js Logic: No state role found, checking WordPress roles: " . json_encode($userRoles), 3, ABSPATH . "error-logs/auth-js-logic.log");
        }
        
        // CRITICAL FALLBACK: Always check WordPress roles for existing users (moved outside the else block)
        // This fixes the issue where existing instructors don't have role in state parameter
        if ($role === 'Learner') {
            if (in_array('um_instructor', $userRoles)) {
                $role = 'Instructor';
                error_log("Auth.js Logic: Role set to Instructor based on WordPress role um_instructor", 3, ABSPATH . "error-logs/auth-js-logic.log");
            } elseif (in_array('um_org-admin', $userRoles)) {
                $role = 'org-admin';
                error_log("Auth.js Logic: Role set to org-admin based on WordPress role um_org-admin", 3, ABSPATH . "error-logs/auth-js-logic.log");
            }
        }
        
        // CRITICAL ADDITIONAL FALLBACK: Check URL patterns for instructor intent
        if ($role === 'Learner' && $stateArray && isset($stateArray->landing_page->url)) {
            $landingUrl = $stateArray->landing_page->url;
            if (strpos($landingUrl, 'instructor') !== false || strpos($landingUrl, 'teach') !== false) {
                $role = 'Instructor';
                error_log("Auth.js Logic: Role set to Instructor based on landing page URL pattern: $landingUrl", 3, ABSPATH . "error-logs/auth-js-logic.log");
            }
        }
        
        // Get signup completion status - CRITICAL FIX: Handle instructor-specific completion logic
        $isSignupComplete = get_user_meta($userId, 'is_signup_complete', true);
        $signupStatus = 'pending'; // Default to pending
        
        // For instructors, check the instructor-specific completion step (matching UserController API logic)
        if ($role === 'Instructor') {
            $isCompletedStep3 = get_user_meta($userId, 'is_completed_step_3', true);
            if ($isCompletedStep3 && $isCompletedStep3 === "yes") {
                $signupStatus = "completed";
                error_log("Auth.js Logic: Instructor signup completed - is_completed_step_3 = yes", 3, ABSPATH . "error-logs/auth-js-logic.log");
            } else {
                $signupStatus = "pending";
                error_log("Auth.js Logic: Instructor signup pending - is_completed_step_3 = " . ($isCompletedStep3 ?: 'empty'), 3, ABSPATH . "error-logs/auth-js-logic.log");
            }
        } else {
            // For non-instructors, use the general signup completion check
            $signupStatus = ($isSignupComplete == 1 || $isSignupComplete === "true" || $isSignupComplete === true) ? 'completed' : 'pending';
            error_log("Auth.js Logic: Non-instructor signup status - is_signup_complete = " . ($isSignupComplete ?: 'empty') . ", status = $signupStatus", 3, ABSPATH . "error-logs/auth-js-logic.log");
        }
        
        // Get current state data
        $hasPhoneNumber = !empty(get_user_meta($userId, 'yuno_gplus_mobile', true));
        $signupCategory = get_user_meta($userId, 'Category_URL_For_Signup', true) ?: [];
        
        // Get organization data for org-admin role
        $hasOrg = false;
        $orgIds = [];
        if ($role === 'org-admin') {
            $orgDetails = get_user_meta($userId, 'details_from_org', true);
            if (!empty($orgDetails) && is_array($orgDetails)) {
                $hasOrg = true;
                foreach ($orgDetails as $orgId => $orgData) {
                    $orgIds[] = ['id' => $orgId];
                }
            }
        }
        
        // Get account status
        $accountStatus = 'active';
        if ($role === 'Instructor') {
            $instructorStatus = get_user_meta($userId, 'instructor_status', true);
            if ($instructorStatus === 'de-active' || $instructorStatus === 'deactive') {
                $accountStatus = 'de-active';
            }
        }
        
        // Get redirect URL from user meta or org settings
        $redirectUrl = '';
        $activeOrg = get_user_meta($userId, 'active_org', true);
        if (!empty($activeOrg)) {
            $redirectUrl = get_post_meta($activeOrg, 'org_redirect_url', true);
        }
        
        return [
            'code' => 200,
            'data' => [
                'is_signup_completed' => $signupStatus,
                'role' => $role,
                'account_status' => $accountStatus,
                'signup_category' => $signupCategory,
                'current_state' => [
                    'has_phone_number' => $hasPhoneNumber,
                    'redirect_url' => $redirectUrl
                ],
                'has_org' => $hasOrg,
                'org_id' => $orgIds,
                'is_lead_created' => true // Assuming lead is created during signup
            ]
        ];
    }
    
    /**
     * Handles payment state exactly matching auth.js isPaymentState function
     * 
     * @param string $paymentState JSON string of payment state data
     * @param array $signupStatus User signup status data
     * @return string Redirect URL for payment flow
     */
    private function handlePaymentState($paymentState, $signupStatus) {
        // Parse payment state JSON (exactly matching auth.js)
        $paymentData = json_decode($paymentState, true);
        
        if (empty($paymentData) || !isset($paymentData['redirectURL'])) {
            return home_url('/');
        }
        
        // Add signup status to payment data (exactly matching auth.js)
        $paymentData['signUp'] = $signupStatus;
        
        // In auth.js this would update localStorage, but we'll handle it in redirect
        // localStorage.setItem('paymentState', JSON.stringify(parseIt));
        
        return home_url($paymentData['redirectURL']);
    }
    
    /**
     * Manages guest link handling exactly matching auth.js manageGuestLink function
     * 
     * @param string $url The URL to check and process
     * @param string $dashboardUrl Default dashboard URL (full URL, not just path)
     * @return string Final redirect URL
     */
    private function manageGuestLink($url, $dashboardUrl) {
        // CRITICAL FIX: Ensure we have full URLs to work with
        error_log("MANAGE GUEST LINK: Input URL: '$url', Dashboard URL: '$dashboardUrl'", 3, ABSPATH . "error-logs/auth-js-logic.log");
        
        // If URL is empty, try to get it from session as backup
        if (empty($url)) {
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            $sessionUrl = $_SESSION['oauth_original_url'] ?? '';
            if (!empty($sessionUrl)) {
                $url = $sessionUrl;
                error_log("MANAGE GUEST LINK: Using URL from session: '$url'", 3, ABSPATH . "error-logs/auth-js-logic.log");
                // Clear the session variable after use
                unset($_SESSION['oauth_original_url']);
            }
        }
        
        // Exactly matching auth.js manageGuestLink logic
        if (strpos($url, "open") !== false) {
            $finalUrl = home_url($url);
            error_log("MANAGE GUEST LINK: 'open' detected, redirecting to: $finalUrl", 3, ABSPATH . "error-logs/auth-js-logic.log");
            return $finalUrl;
        } elseif (strpos($url, "invite") !== false) {
            $finalUrl = home_url($url);
            error_log("MANAGE GUEST LINK: 'invite' detected, redirecting to: $finalUrl", 3, ABSPATH . "error-logs/auth-js-logic.log");
            return $finalUrl;
        } else {
            error_log("MANAGE GUEST LINK: No special pattern detected, using dashboard URL: $dashboardUrl", 3, ABSPATH . "error-logs/auth-js-logic.log");
            return $dashboardUrl;
        }
    }
    
    /**
     * Handles org-admin flow exactly matching auth.js manageOrgAdmin function
     * 
     * @param array $userData User data from getUserSignupStatus
     * @param array $stateContext Processed state context data
     * @return string Redirect URL for org-admin
     */
    private function handleOrgAdminFlow($userData, $stateContext) {
        $hasOrg = $userData['has_org'] ?? false;
        $orgIds = $userData['org_id'] ?? [];
        
        // CRITICAL FIX: Properly extract hasAcademy from landingPage data exactly matching auth.js
        // Auth.js: const landingPage = JSON.parse(sessionStorage.getItem('landingPage'));
        // Auth.js: const hasAcademy = landingPage.hasAcademy;
        
        $hasAcademy = false;
        
        // Method 1: Check if hasAcademy is directly available in landing page data
        if (isset($stateContext['landingPage']['hasAcademy'])) {
            $hasAcademy = (bool) $stateContext['landingPage']['hasAcademy'];
            error_log("AUTH.JS LOGIC: hasAcademy found in landingPage data: " . ($hasAcademy ? 'true' : 'false'), 3, ABSPATH . "error-logs/auth-js-logic.log");
        }
        // Method 2: Check landing page URL for academy indicators (fallback)
        elseif (isset($stateContext['landingPage']['url'])) {
            $landingPageUrl = $stateContext['landingPage']['url'];
            $hasAcademy = (strpos($landingPageUrl, 'academy') !== false);
            error_log("AUTH.JS LOGIC: hasAcademy determined from URL '$landingPageUrl': " . ($hasAcademy ? 'true' : 'false'), 3, ABSPATH . "error-logs/auth-js-logic.log");
        }
        // Method 3: Check if current page contains academy references (last resort)
        else {
            $currentUrl = $_SERVER['REQUEST_URI'] ?? '';
            $hasAcademy = (strpos($currentUrl, 'academy') !== false);
            error_log("AUTH.JS LOGIC: hasAcademy determined from current URL '$currentUrl': " . ($hasAcademy ? 'true' : 'false'), 3, ABSPATH . "error-logs/auth-js-logic.log");
        }
        
        if (!$hasOrg) {
            // No org - create organization account with hasAcademy parameter exactly matching auth.js
            // Auth.js: window.location.href = `${host()}/create-organization-account?has_academy=${hasAcademy}`;
            error_log("AUTH.JS LOGIC: manageOrgAdmin - No org, redirecting to create-organization-account with has_academy=" . ($hasAcademy ? 'true' : 'false'), 3, ABSPATH . "error-logs/auth-js-logic.log");
            return home_url("/create-organization-account?has_academy=" . ($hasAcademy ? 'true' : 'false'));
        } elseif (count($orgIds) > 1) {
            // Multiple orgs - select organization
            error_log("AUTH.JS LOGIC: manageOrgAdmin - Multiple orgs, redirecting to select-an-organization", 3, ABSPATH . "error-logs/auth-js-logic.log");
            return home_url('/select-an-organization');
        } else {
            // Single org - CRITICAL FIX: Implement fetchOrgInfo + gotOrgInfo logic
            $orgId = $orgIds[0]['id'];
            error_log("AUTH.JS LOGIC: manageOrgAdmin - Single org found: $orgId, fetching org info", 3, ABSPATH . "error-logs/auth-js-logic.log");
            
            // Replicate fetchOrgInfo + gotOrgInfo functionality
            return $this->processOrgInfoAndRedirect($orgId);
        }
    }
    
    /**
     * Replicates auth.js fetchOrgInfo + gotOrgInfo functionality
     * Fetches organization data and determines redirect based on academy status
     * 
     * @param int $orgId Organization ID
     * @return string Final redirect URL based on org data
     */
    private function processOrgInfoAndRedirect($orgId) {
        try {
            // STEP 1: fetchOrgInfo equivalent - Get organization data
            // This replicates: YUNOCommon.config.generic("org", false, false, orgID)
            
            error_log("AUTH.JS LOGIC: fetchOrgInfo equivalent - Loading organization model for org ID: $orgId", 3, ABSPATH . "error-logs/auth-js-logic.log");
            
            // Load the OrgModel to fetch organization data (loaded in constructor)
            // Note: $orgModel property is dynamically created by loadModel() method
            /** @var \V4\OrgModel $orgModel */
            $orgData = $this->orgModel->getOrganization($orgId, ['schema' => 'Organization']);
            
            if (!$orgData) {
                error_log("AUTH.JS LOGIC: fetchOrgInfo - Failed to fetch org data for ID: $orgId", 3, ABSPATH . "error-logs/auth-js-logic.log");
                // Fallback to default enrollments page if org data fetch fails
                return home_url('/enrollments');
            }
            
            error_log("AUTH.JS LOGIC: fetchOrgInfo - Successfully fetched org data", 3, ABSPATH . "error-logs/auth-js-logic.log");
            
            // STEP 2: gotOrgInfo equivalent - Process the fetched data
            return $this->processGotOrgInfo($orgData);
            
        } catch (Exception $e) {
            error_log("AUTH.JS LOGIC: processOrgInfoAndRedirect ERROR: " . $e->getMessage(), 3, ABSPATH . "error-logs/auth-js-logic.log");
            // Fallback to default enrollments page on error
            return home_url('/enrollments');
        }
    }
    
    /**
     * Replicates auth.js gotOrgInfo functionality exactly
     * Processes organization data and determines final redirect based on academies
     * 
     * @param array $orgData Organization data from API
     * @return string Final redirect URL
     */
    private function processGotOrgInfo($orgData) {
        // Exactly matching auth.js gotOrgInfo logic:
        // const academies = data.yuno_academy_subscription.academies;
        // if (academies.length > 0) {
        //     window.location.href = `${host()}/enrollments/`;
        // } 
        // // else {
        // //     window.location.href = "/create-new-academy";
        // // }
        
        $academies = $orgData['yuno_academy_subscription']['academies'] ?? [];
        
        error_log("AUTH.JS LOGIC: gotOrgInfo - Checking academies count: " . count($academies), 3, ABSPATH . "error-logs/auth-js-logic.log");
        error_log("AUTH.JS LOGIC: gotOrgInfo - Academies data: " . json_encode($academies), 3, ABSPATH . "error-logs/auth-js-logic.log");
        
        if (count($academies) > 0) {
            error_log("AUTH.JS LOGIC: gotOrgInfo - Academies found, redirecting to /enrollments/", 3, ABSPATH . "error-logs/auth-js-logic.log");
            return home_url('/enrollments/');
        } else {
            error_log("AUTH.JS LOGIC: gotOrgInfo - No academies found, redirecting to /enrollments/ (commented create-new-academy logic)", 3, ABSPATH . "error-logs/auth-js-logic.log");
            // The commented else block in auth.js suggests this should redirect to create-new-academy
            // But since it's commented out, we follow the current behavior and go to enrollments
            return home_url('/enrollments/');
        }
    }
    
    // =============================================================================
    // NEW: STEP 3 - Redirect Determination Functions (exactly matching auth.js)
    // =============================================================================
    
    /**
     * Handles pending signup status exactly matching auth.js statusPending function
     * 
     * @param array $userData User signup status data
     * @param array $stateContext Processed state context data 
     * @return string Redirect URL for pending status
     */
    private function handleStatusPending($userData, $stateContext) {
        // Extract localStorage simulation data
        $paymentState = $stateContext['localStorageSimulation']['paymentState'];
        $skipSignUpState = $stateContext['localStorageSimulation']['skipSignUp'];
        $userState = $stateContext['localStorageSimulation']['userState'];
        $isReferrer = $stateContext['localStorageSimulation']['isReferrer'];
        $isQuiz = $stateContext['localStorageSimulation']['isQuiz'];
        
        // Calculate getState exactly matching auth.js
        $getState = empty($paymentState) && $skipSignUpState !== 'true';
        
        // Helper function for signup redirect exactly matching auth.js
        $getSignupRedirectUrl = function() use ($userData) {
            $signUpPath = ($userData['role'] === "Instructor") ? "/instructor-sign-up" : "/sign-up";
            
            // CRITICAL DEBUG: Track signup redirect decision
            error_log("Auth.js Logic: Signup redirect - User role: '{$userData['role']}', Path: '$signUpPath'", 3, ABSPATH . "error-logs/auth-js-logic.log");
            
            return home_url($signUpPath);
        };
        
        // Check phone number requirement (exactly matching auth.js)
        if (!$userData['current_state']['has_phone_number']) {
            return $getSignupRedirectUrl();
        }
        
        // Check referrer or quiz in progress (exactly matching auth.js)
        if ($isReferrer || $isQuiz) {
            if ($userState) {
                return $userState;
            }
            return $getSignupRedirectUrl();
        }
        
        // Check if sign-up is required based on state and userState (exactly matching auth.js)
        if ($getState && $userState) {
            return $getSignupRedirectUrl();
        }
        
        // Check signup category requirement (exactly matching auth.js)
        $signupCategory = $userData['signup_category'] ?? [];
        if (empty($paymentState) && $skipSignUpState !== 'true' && !empty($signupCategory)) {
            return $getSignupRedirectUrl();
        }
        
        // Check payment state exists (exactly matching auth.js)
        if (!empty($paymentState)) {
            return $this->handlePaymentState($paymentState, $userData);
        }
        
        // Check if sign-up is skipped (exactly matching auth.js)
        if ($skipSignUpState === 'true' && $userState) {
            return home_url($userState);
        }
        
        // Default fallback
        $role = $userData['role'] ?? 'Learner';
        return $this->getRoleDashboardUrl($role);
    }
    
    /**
     * Handles completed signup status exactly matching auth.js statusComplete function
     * 
     * @param array $userData User signup status data
     * @param array $stateContext Processed state context data
     * @param int $userId WordPress user ID
     * @return string Redirect URL for completed status
     */
    private function handleStatusComplete($userData, $stateContext, $userId) {
        // Extract data exactly matching auth.js statusComplete
        $paymentState = $stateContext['localStorageSimulation']['paymentState'];
        $isReferrer = $stateContext['localStorageSimulation']['isReferrer'];
        $isQuiz = $stateContext['localStorageSimulation']['isQuiz'];
        $userState = $stateContext['localStorageSimulation']['userState'];
        
        $role = $userData['role'] ?? 'Learner';
        $accountStatus = $userData['account_status'] ?? 'active';
        $currentState = $userData['current_state'] ?? [];
        
        // Get dashboard URL exactly matching auth.js
        $dashboardUrl = $this->getRoleDashboardUrl($role);
        
        // Handle deactivated instructor accounts (exactly matching auth.js)
        if ($role === "Instructor" && $accountStatus === "de-active") {
            return home_url('/account-disabled');
        }
        
        // Redirect if isReferrer or isQuiz exists (exactly matching auth.js)
        if ($isReferrer || $isQuiz) {
            if ($userState) {
                return $userState;
            }
            return $dashboardUrl;
        }
        
        // Handle payment state (exactly matching auth.js)
        if (!empty($paymentState)) {
            return $this->handlePaymentState($paymentState, $userData);
        }
        
        // Handle userState or default redirection (exactly matching auth.js)
        if ($userState) {
            if ($userState === "/account-disabled/") {
                // Build redirect URL with user_id and token
                $authToken = get_user_meta($userId, 'CURRENT_USER_JWT_TOKEN', true);
                $redirectUrl = $currentState['redirect_url'] ?? '';
                
                if (!empty($redirectUrl)) {
                    return $redirectUrl . "/?user_id={$userId}&yuno_token={$authToken}";
                }
                return $dashboardUrl;
            } else {
                return $this->manageGuestLink($userState, $dashboardUrl);
            }
        } else {
            // Default redirect with org redirect URL if available
            $authToken = get_user_meta($userId, 'CURRENT_USER_JWT_TOKEN', true);
            $redirectUrl = $currentState['redirect_url'] ?? '';
            
            if (!empty($redirectUrl)) {
                return $redirectUrl . "/?user_id={$userId}&yuno_token={$authToken}";
            }
            return $dashboardUrl;
        }
    }
    
    /**
     * Detects if this is an Apple ID user coming from signup tab
     * 
     * @param object $stateArray Decoded OAuth state parameter
     * @param array $response OAuth response containing tokens
     * @return bool True if this is Apple ID signup intent
     */
    private function isAppleIdSignupIntent($stateArray, $response) {
        // Get user details from the token (where identity information is stored)
        $isAppleId = false;
        
        try {
            // Extract user details from id_token
            $id_token = $response['id_token'];
            $token_parts = explode('.', $id_token);
            $payload = base64_decode($token_parts[1]);
            $user_details = json_decode($payload, true);
            
            // Method 1: Check identities in user details (most reliable)
            if (isset($user_details['identities'])) {
                foreach ($user_details['identities'] as $identity) {
                    if (isset($identity['providerName']) && $identity['providerName'] === 'SignInWithApple') {
                        $isAppleId = true;
                        break;
                    }
                }
            }
            
            // Method 2: Check cognito username prefix
            if (!$isAppleId && isset($user_details['cognito:username'])) {
                $isAppleId = strpos($user_details['cognito:username'], 'signinwithapple_') === 0;
            }
            
        } catch (Exception $e) {
            error_log("APPLE ID DETECTION ERROR: " . $e->getMessage(), 3, ABSPATH . "error-logs/apple-signup-fix.log");
            return false;
        }
        
        if (!$isAppleId) {
            return false; // Not Apple ID, no special handling needed
        }
        
        // Check if coming from signup context
        $isSignupContext = false;
        
        // Method 1: Check landing page URL for signup indicators
        if (isset($stateArray->landing_page->url)) {
            $landingUrl = $stateArray->landing_page->url;
            $isSignupContext = (strpos($landingUrl, '/login/') !== false || 
                               strpos($landingUrl, 'signup') !== false ||
                               strpos($landingUrl, 'sign-up') !== false);
        }
        
        // Method 2: Check org_details type (if set by frontend)
        if (!$isSignupContext && isset($stateArray->org_details->type)) {
            $isSignupContext = ($stateArray->org_details->type === 'signup');
        }
        
        // Method 3: Assume signup intent for Apple ID from login page (fallback)
        if (!$isSignupContext && isset($stateArray->landing_page->url)) {
            $landingUrl = $stateArray->landing_page->url;
            $isSignupContext = (strpos($landingUrl, '/login') !== false);
        }
        
        $result = $isAppleId && $isSignupContext;
        error_log("APPLE ID INTENT CHECK: isAppleId=$isAppleId, isSignupContext=$isSignupContext, result=$result", 3, ABSPATH . "error-logs/apple-signup-fix.log");
        
        return $result;
    }
    
    /**
     * Detects user role from state parameter or WordPress roles
     * 
     * @param int $userId WordPress user ID
     * @param object $stateArray Decoded OAuth state parameter
     * @return string User role (Learner, Instructor, org-admin)
     */
    private function detectUserRole($userId, $stateArray) {
        // First check state parameter
        if (isset($stateArray->login_details->role)) {
            $stateRole = $stateArray->login_details->role;
            if ($stateRole === 'instructor' || $stateRole === 'Instructor') {
                return 'Instructor';
            } elseif ($stateRole === 'org-admin') {
                return 'org-admin';
            }
        }
        
        // Check WordPress user roles
        $userData = get_userdata($userId);
        $userRoles = $userData->roles ?? [];
        
        if (in_array('um_instructor', $userRoles)) {
            return 'Instructor';
        } elseif (in_array('um_org-admin', $userRoles)) {
            return 'org-admin';
        }
        
        return 'Learner'; // Default
    }

    /**
     * Main redirect determination function exactly matching auth.js flow
     * This is the central function that determines where to redirect the user
     * 
     * @param int $userId WordPress user ID
     * @param object $stateArray Decoded OAuth state parameter
     * @return string Final redirect URL
     */
    private function determineRedirectUrl($userId, $stateArray) {
        // Debug logging - START
        error_log("Auth.js Logic: Starting redirect determination for user_id: $userId", 3, ABSPATH . "error-logs/auth-js-logic.log");
        
        // CRITICAL DEBUG: Show complete state parameter
        error_log("Auth.js Logic: COMPLETE STATE PARAMETER: " . json_encode($stateArray, JSON_PRETTY_PRINT), 3, ABSPATH . "error-logs/auth-js-logic.log");
        
        // Process state context (Step 1)
        $stateContext = $this->processStateContext($stateArray);
        error_log("Auth.js Logic: State context processed - " . json_encode($stateContext, JSON_PRETTY_PRINT), 3, ABSPATH . "error-logs/auth-js-logic.log");
        
        // Get user signup status (Step 2) - CRITICAL FIX: Pass state array for role detection
        $userStatusResponse = $this->getUserSignupStatus($userId, $stateArray);
        error_log("Auth.js Logic: User status response - " . json_encode($userStatusResponse, JSON_PRETTY_PRINT), 3, ABSPATH . "error-logs/auth-js-logic.log");
        
        if ($userStatusResponse['code'] !== 200) {
            // Error getting user status - redirect to default dashboard
            error_log("Auth.js Logic: Error getting user status, redirecting to home", 3, ABSPATH . "error-logs/auth-js-logic.log");
            return home_url('/');
        }
        
        $userData = $userStatusResponse['data'];
        $signupStatus = $userData['is_signup_completed'] ?? 'pending';
        
        error_log("Auth.js Logic: User signup status: $signupStatus, role: " . ($userData['role'] ?? 'undefined'), 3, ABSPATH . "error-logs/auth-js-logic.log");
        
        // Route to appropriate handler exactly matching auth.js gotSignUpDetail switch statement
        switch ($signupStatus) {
            case "pending":
                $redirectUrl = $this->handleStatusPending($userData, $stateContext);
                error_log("Auth.js Logic: Pending status - redirecting to: $redirectUrl", 3, ABSPATH . "error-logs/auth-js-logic.log");
                return $redirectUrl;
                
            case "completed":
                // Handle org-admin special case exactly matching auth.js
                if ($userData['role'] === "org-admin") {
                    $redirectUrl = $this->handleOrgAdminFlow($userData, $stateContext);
                    error_log("Auth.js Logic: Org-admin flow - redirecting to: $redirectUrl", 3, ABSPATH . "error-logs/auth-js-logic.log");
                    return $redirectUrl;
                } else {
                    $redirectUrl = $this->handleStatusComplete($userData, $stateContext, $userId);
                    error_log("Auth.js Logic: Completed status - redirecting to: $redirectUrl", 3, ABSPATH . "error-logs/auth-js-logic.log");
                    return $redirectUrl;
                }
                
            default:
                // Fallback for any other status
                $role = $userData['role'] ?? 'Learner';
                $redirectUrl = $this->getRoleDashboardUrl($role);
                error_log("Auth.js Logic: Default fallback - redirecting to: $redirectUrl", 3, ABSPATH . "error-logs/auth-js-logic.log");
                return $redirectUrl;
        }
    }
} 
