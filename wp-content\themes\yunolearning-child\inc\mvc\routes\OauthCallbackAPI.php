<?php
/**
 * OAuth Callback API Routes
 * 
 * Purpose: Handle OAuth authentication callbacks from providers (Google, Cognito, Apple, etc.)
 */

return [
    "/auth/callback" => [
        "controller" => "OauthController",
        "methods" => [
            "GET" => [
                "callback" => "handleAuthCallbackAPI",
                "auth" => false
            ]
        ]
    ]
];
// This URL should be configured in OAuth provider settings and wp-config.php 