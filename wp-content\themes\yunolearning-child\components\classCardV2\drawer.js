Vue.component("yuno-classCard-drawer", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
    <div class="cardContent">
        <template v-if="userRole.data == 'Learner'">
			<yuno-learner-drawer
				:data="data"
			>
			</yuno-learner-drawer>
		</template>
		<template v-else-if="userRole.data == 'Instructor'">
			<yuno-instructor-drawer
				:data="data"
			>
			</yuno-instructor-drawer>
		</template>
    </div>
    `,
  data() {
    return {};
  },
  computed: {
    ...Vuex.mapState(["userInfo", "userRole", "videoList"]),
  },
  methods: {},
});
