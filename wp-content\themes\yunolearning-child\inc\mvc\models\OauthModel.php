<?php
namespace V4;

/**
 * OauthModel model
 */

class OauthModel extends Model {
    
    // Class constants for auth providers
    private const AUTH_PROVIDER_COGNITO = 'COGNITO';
    private const AUTH_PROVIDER_GOOGLE = 'GOOGLE';
    private const AUTH_PROVIDER_VIRTUAL_CLASSROOM = 'VIRTUAL_CLASSROOM';
    private const AUTH_PROVIDER_AUTOMATION = 'AUTOMATION';
    private const AUTH_PROVIDER_APPLE = 'APPLE';
    
    // Class constants for token types
    private const TOKEN_TYPE_BEARER = 'Bearer';
    private const TOKEN_TYPE_BEARER_UPPER = 'BEARER';
    
    // Class constants for auth references
    private const AUTH_REF_GOOGLE = 'google';
    private const AUTH_REF_VIRTUAL_CLASSROOM = 'virtual-classroom';
    private const AUTH_REF_AUTOMATION = 'automation';
    private const AUTH_REF_APPLE = 'apple';
    
    /**
     * @var mixed Schema object for data validation
     */
    protected $schema;
    
    /**
     * @var UserModel User model instance
     */
    protected $userModel;
    
    /**
     * Constructor to initialize the OauthModel
     */
    function __construct()
    {
        parent::__construct();
        $this->loadLibary('schema');
        $this->loadModel('user');
    }
    /**
     * Retrieves the Oauth details.
     */
    public function getOauth($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $userArgs = array(
                'auth_email'      => 'yuno_gplus_email',
                'access_token'    => 'yuno_user_access_token',
                'refresh_token'   => 'yuno_user_refresh_token',
                'expires_in'      => 'session_tokens',
                'id_token'        => 'yuno_user_id_token'
            );

            $userMetaData = array();
            $userID = $query['id'];
            foreach ($userArgs as $key => $metaKey) {
                $userMetaData[$key] = get_user_meta($userID, $metaKey, true);
                if (is_array($userMetaData) && !empty($userMetaData)) {
                    $userData = $userMetaData;
                }
            }
        }
        else {
            return false;
        }
        if (is_array($userData) && !empty($userData)) {
            $oauthResponse = array(
                'app' => self::AUTH_PROVIDER_COGNITO,
                'yuno_user_id' => $this->load->subData("user","getUser",$query['id'],['schema' => 'User_Minimal']),
                'auth_email' => $userData['auth_email'] ?? '',
                'token_type' => self::TOKEN_TYPE_BEARER,
                'access_token' => $userData['access_token'] ?? '',
                'refresh_token' => $userData['refresh_token'] ?? '',
                'expires_in' => $userData['expires_in'] ?? 0,
                'scope' => 'meeting:read meeting:write user:read user:write recording:write recording:read report:read:admin',
                'id_token' => $userData['id_token'] ?? '',
            );
            return $this->schema->validate($oauthResponse, 'Oauth', $filter);
        }
        return false;
    }

    /**
     * Logs an error using WP_Structured_Logger.
     */
    public function logError($logDetails) {
        $logger = \WP_Structured_Logger::get_instance();
        $logger->custom_log(
            $logDetails['logtype'],
            $logDetails['module'],
            $logDetails['action'],
            $logDetails['message'],
            $logDetails['user'],
            $logDetails['request'],
            $logDetails['data']
        );
    }

    /**
     * Saves the authentication access token in the database.
     */
    function saveAuthAccessToken($params) {
        global $wpdb;
  
        $tokenData = [
            'id_token' => $params['id_token'],
            'access_token' => $params['access_token'],
            'refresh_token' => $params['refresh_token'],
            'token_expiry' => $params['token_expiry'],
            'auth_code' => $params['auth_code'],
            'user_id' => $params['user_id'],
            'resource' => $params['resource'],
        ];
  
        $tokenTable = $wpdb->prefix . 'user_tokens';
  
        try {
            $wpdb->insert($tokenTable, $tokenData);
            if ($wpdb->last_error) {
                throw new \Exception($wpdb->last_error);
            }
        } catch (\Exception $e) {
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'header - login | signup',
                'message' => $message,
                'user' => ['user_id' => $params['user_id']],
                'request' => ['id_token' => $params['id_token']],
                'data' => []
            ];
            $this->logError($logDetails);
            exit('An error occurred while saving the authentication token.');
        }
    }
    
    function getCognitoAccessToken($authCode)
    {
        if (!empty($authCode)) {
            $url = \AWS_COGNITO_DOMAIN . '/oauth2/token';
            $data = [
                'grant_type' => 'authorization_code',
                'client_id' => \AWS_COGNITO_OAUTH_APP_CLIENT_ID,
                'client_secret' => \AWS_COGNITO_OAUTH_APP_CLIENT_SECRET,
                'code' => $authCode,
                // OLD REDIRECT URI - COMMENTED FOR API ENDPOINT IMPLEMENTATION  
                // 'redirect_uri' => \AWS_COGNITO_OAUTH_APP_REDIRECT_URL,
                // NEW API ENDPOINT REDIRECT URI - USING wp-config.php constant (now updated)
                'redirect_uri' => \AWS_COGNITO_OAUTH_APP_REDIRECT_URL,
            ];

            $options = [
                'http' => [
                    'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method' => 'POST',
                    'content' => http_build_query($data),
                ],
            ];
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            if ($result === false) {
                $error = error_get_last();
                $message = "Error in Cognito response: " . ($error['message'] ?? 'Unknown error');
                $request = $user = [];
                $data = ["data" => $http_response_header ?? []];
                $logger = \WP_Structured_Logger::get_instance();
                $logger->custom_log("error", "ES", "header - login | signup", $message, $user, $request, $data);
                exit($message);
            }

            $response = json_decode($result, true);
            return $response;
        }
        return false;
    }

    /**
     * Saves virtual authentication access data.
     */
    function saveVirtualAuthAccess($user_id,$new_entry) {
        try {
            $meta_key = 'virtual_classroom_data';
            $existing_data = get_user_meta($user_id, $meta_key, true);
  
            if (empty($existing_data)) {
                $existing_data = ['data' => []];
            }
  
            $entry_exists = false;
  
            foreach ($existing_data['data'] as $key => $entry) {
                if ($entry['org_id'] == $new_entry['org_id']) {
                    $existing_data['data'][$key] = array_merge($existing_data['data'][$key], $new_entry);
                    $entry_exists = true;
                    break;
                }
            }
  
            foreach ($existing_data['data'] as $key => $entry) {
                $existing_data['data'][$key] = $new_entry;
                $entry_exists = true;
                break;
            }
  
            if (!$entry_exists) {
                $existing_data['data'][] = $new_entry;
            }
  
            update_user_meta($user_id, $meta_key, $existing_data);
        } catch (\Exception $e) {
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['org_id' => $new_entry['org_id']],
                'data' => []
            ];
            $this->logError($logDetails);
            exit('An error occurred while saving the authentication token.');
        }
    }

    /**
     * Creates a standardized authentication data array for storing in user meta
     */
    public function createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
        $auth_provider = self::AUTH_PROVIDER_COGNITO;
    if (isset($org_details->auth_ref)) {
        if ($org_details->auth_ref == self::AUTH_REF_GOOGLE) {
            $auth_provider = self::AUTH_PROVIDER_GOOGLE;
        } else if ($org_details->auth_ref == self::AUTH_REF_VIRTUAL_CLASSROOM) {
            $auth_provider = self::AUTH_PROVIDER_VIRTUAL_CLASSROOM;
        } else if ($org_details->auth_ref == self::AUTH_REF_AUTOMATION) {
            $auth_provider = self::AUTH_PROVIDER_AUTOMATION;
        } else if ($org_details->auth_ref == self::AUTH_REF_APPLE || strpos($user_details['cognito:username'] ?? '', 'signinwithapple_') === 0) {
            $auth_provider = self::AUTH_PROVIDER_APPLE;
        } else {
            $auth_provider = strtoupper($org_details->auth_ref);
        }
    } else if (isset($user_details['identities']) && is_array($user_details['identities'])) {
        foreach ($user_details['identities'] as $identity) {
            if (isset($identity['providerName'])) {
                if ($identity['providerName'] == 'Google') {
                    $auth_provider = self::AUTH_PROVIDER_GOOGLE;
                } else if ($identity['providerName'] == 'SignInWithApple') {
                    $auth_provider = self::AUTH_PROVIDER_APPLE;
                } else {
                    $auth_provider = strtoupper($identity['providerName']);
                }
                break;
            }
        }
        }

        $user_roles = [];
        $capabilities = get_user_meta($user_id, 'wp_capabilities', true);
        if (is_array($capabilities)) {
            $user_roles = array_keys($capabilities);
        } else {
            $user_roles = ['subscriber'];
        }

        $full_name = isset($decodedPayload['name']) ? $decodedPayload['name'] : '';
        if (empty($full_name)) {
            $full_name = get_user_meta($user_id, 'yuno_display_name', true);
        }
        if (empty($full_name)) {
            $first_name = get_user_meta($user_id, 'yuno_first_name', true);
            $last_name = get_user_meta($user_id, 'yuno_last_name', true);
            if (!empty($first_name) || !empty($last_name)) {
                $full_name = trim($first_name . ' ' . $last_name);
            }
        }

        $image_url = isset($decodedPayload['picture']) ? $decodedPayload['picture'] : get_user_meta($user_id, 'googleplus_profile_img', true);

        $scope = '';
        if (isset($response['scope'])) {
            $scope = $response['scope'];
        }

        update_user_meta($user_id, 'user_details_id_token', $user_details);
        update_user_meta($user_id, 'user_data_cognito_response', $user_details);

        $extracted_data = [
            'sub_id' => $sub_id,
            'auth_code' => isset($_GET['code']) ? $_GET['code'] : '',
            'last_login' => current_time('mysql'),
            'identity_provider' => isset($user_details['identities'][0]['providerName']) ? $user_details['identities'][0]['providerName'] : $auth_provider
        ];

        if (isset($user_details['email_verified'])) {
            $extracted_data['email_verified'] = $user_details['email_verified'];
        }
        if (isset($user_details['cognito:username'])) {
            $extracted_data['cognito_username'] = $user_details['cognito:username'];
        }
        if (isset($user_details['given_name'])) {
            $extracted_data['given_name'] = $user_details['given_name'];
        }
        if (isset($user_details['family_name'])) {
            $extracted_data['family_name'] = $user_details['family_name'];
        }
        if (isset($decodedPayload['iat'])) {
            $extracted_data['issued_at'] = $decodedPayload['iat'];
        }
        if (isset($decodedPayload['exp'])) {
            $extracted_data['expires_at'] = $decodedPayload['exp'];
        }

        update_user_meta($user_id, 'user_extracted_cognito_data', $extracted_data);

        return [
        'app' => $auth_provider,
        'yuno_user_id' => [
            'id' => $user_id,
            'role' => $user_roles,
            'full_name' => $full_name,
            'image_url' => $image_url
        ],
        'auth_email' => $email,
        'token_type' => isset($response['token_type']) ? strtoupper($response['token_type']) : self::TOKEN_TYPE_BEARER_UPPER,
        'access_token' => $response['access_token'] ?? '',
        'refresh_token' => isset($response['refresh_token']) ? $response['refresh_token'] : '',
        'expires_in' => isset($response['expires_in']) ? (string)$response['expires_in'] : (string)strtotime("+1 hour"),
        'scope' => $scope,
        'id_token' => $response['id_token'] ?? ''
    ];
  }

    /**
     * Retrieves the Google Meet access token for a given user ID and org ID.
     */
    public function getGoogleMeetAccessToken($user_id, $org_id) {
        try {
            date_default_timezone_set('Asia/Kolkata');
            $access_token = "";
            $filtered_virtual_classroom = [];

            $meta_key = 'virtual_classroom_data';
            $data = get_user_meta($user_id, $meta_key, true);
            if (count($data) > 0) {
                foreach ($data['data'] as $item) {
                    if (isset($item['virtual_classroom']['meet']) && $item['org_id'] == $org_id) {
                        $filtered_virtual_classroom = $item['virtual_classroom']['meet'];
                        break;
                    }
                }
                $email = get_user_meta($user_id, 'yuno_gplus_email', true);
                $name = get_user_meta($user_id, 'yuno_display_name', true);
                if ($email == $filtered_virtual_classroom['email']) {
                    $g_client_id = \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                    $g_client_secret = \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
                } else {
                    $g_client_id = \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                    $g_client_secret = \AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
                }
                $refresh_token = $filtered_virtual_classroom['refresh_token'];
                $expires_in = $filtered_virtual_classroom['expires_in'];
                $access_token = $filtered_virtual_classroom['access_token'];
                
                $client = new \Google_Client();
                $client->setClientId($g_client_id);
                $client->setClientSecret($g_client_secret);
                $client->setAccessType('offline');

                $client->refreshToken($refresh_token);
                $new_token = $client->getAccessToken();
                if ($new_token) {
                    $org_academies = [];
                    $academies = get_post_meta($org_id, "academies", true);
                    if (is_array($academies)) {
                        $org_academies = $academies;
                    }
                    $meet_entry = [
                        'org_id' => $org_id,
                        'academies' => $org_academies,
                        'virtual_classroom' => [
                            'meet' => [
                                'access_token' => $new_token['access_token'],
                                'refresh_token' => $new_token['refresh_token'],
                                'id_token' => $new_token['id_token'],
                                'token_type' => $new_token['token_type'],
                                'expires_in' => time() + $new_token['expires_in'],
                                'email' => $filtered_virtual_classroom['email'],
                                'name' => $name,
                                'scope' => $new_token['scope']
                            ]
                        ]
                    ];
                    $this->saveVirtualAuthAccess($user_id,$meet_entry);
                    return $new_token['access_token'];
                }
            }
            $url = \GOOGLE_MEET_API_URL;
            $headers = [
                "Authorization: Bearer " .$access_token,
            ];
            $curlPost = '';

            $return = \Utility::curl_request($url, 'GET', $curlPost, $headers, '');
            $data = json_decode($return['response'], true);

            $returnStatus = $data['error']['status'];
            if ($returnStatus == "UNAUTHENTICATED") {
                return "Consent revoked. Please reconnect to virtual classroom in Settings > Account for ".get_the_title($org_id)." to schedule classes.";
            }
            return $access_token;
        } catch (\Exception $e) {
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $user_id],
                'request' => ['user_id' => $user_id],
                'data' => []
            ];
            $logger = \WP_Structured_Logger::get_instance();
            $logger->custom_log($logDetails['logtype'], $logDetails['module'], $logDetails['action'], $logDetails['message'], $logDetails['user'], $logDetails['request'], $logDetails['data']);
            return "invalid token";
        }
    }
}