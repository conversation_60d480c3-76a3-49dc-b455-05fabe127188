Vue.component("yuno-search-location", {
	props: {
		map: {
			type: Object,
			required: false,
			default: null,
		},
	},
	template: `
        <div class="searchLocation">
            <h2 class="subtitle1 pb-2">Search place on Google map</h2>
            <div class="searchContainer">
                <b-field>
                    <b-autocomplete
                        ref="searchInput"
                        v-model="searchQuery"
                        :data="filteredAddress"
                        @input="searchLocation"
                        @select="handleSelectedAddress"
                        placeholder="Search place on Google to add in classroom"
                        custom-class="yunoSearchAddress"
                        :clearable="true">
                            <template slot-scope="props">
                                <div style="display:flex;flex-direction:column;gap:5px">
                                    <span class="main_text">{{ props.option.structured_formatting.main_text }}</span>
                                    <span class="secondary_text">{{ props.option.structured_formatting.secondary_text }}</span>
                                </div>
                            </template>
                            <template #empty>
                                <div class="no-results">No results</div>
                            </template>
                    </b-autocomplete>
                </b-field>
                <div class="cta">
                    <b-button
                        class="yunoPrimaryCTA"
                        :disabled="selectedLocation === null"
                        @click="confirmLocation"
                    >
                        Continue to Add
                    </b-button>
                </div>
            </div>
        </div>
    `,
	data() {
		return {
			searchQuery: "",
			selectedLocation: null,
			searchResults: [],
			placesService: null,
		};
	},
	computed: {
		...Vuex.mapState(["form", "filters", "timeSlots"]),
		wpThemeURL() {
			return this.$store.state.themeURL;
		},
		filteredAddress() {
			return this.searchResults;
		},
	},
	methods: {
		activeOrg() {
			const activeOrg = this.userInfo.data.current_state.org_id;

			if (activeOrg) {
				return activeOrg;
			}
		},
		confirmLocation() {
			this.$emit("confirmLocation");
		},
		searchLocation(value) {
			this.searchQuery = value;
			if (!value) {
				this.searchResults = [];
				return;
			}
			if (window.google && window.google.maps && google.maps.places) {
				const service = new google.maps.places.AutocompleteService();
				service.getPlacePredictions({ input: value }, (predictions, status) => {
					if (status !== google.maps.places.PlacesServiceStatus.OK) {
						this.searchResults = [];
						return;
					}
					this.searchResults = predictions;
				});
			}
		},
		handleSelectedAddress(selected) {
			const isGoogleLoaded = window.google?.maps?.places;
			if (selected && isGoogleLoaded) {
				const placeId = selected.place_id;
				const service = new google.maps.places.PlacesService(this.$props.map);

				service.getDetails({ placeId: placeId }, (place, status) => {
					if (
						status === google.maps.places.PlacesServiceStatus.OK &&
						place.geometry
					) {
						this.$emit("updateSelectedLocation", place);

						// Update selected location
						this.selectedLocation = {
							lat: place.geometry.location.lat(),
							lng: place.geometry.location.lng(),
							address: place.formatted_address,
							placeId: place.place_id,
						};

						// Update search input with formatted address
						this.searchQuery = place.formatted_address;
						this.fetchLocationDetail(placeId);
					}
				});
			} else {
				this.selectedLocation = null;
				this.filters.data = [];
			}
		},
		extractCoordinates(location) {
			return {
				latitude: location.geometry?.location?.lat || 0,
				longitude: location.geometry?.location?.lng || 0,
			};
		},
		extractAddressInfo(components) {
			const findType = (type) =>
				components.find((comp) => comp.types.includes(type));
			const findAllOfType = (type) =>
				components.filter((comp) => comp.types.includes(type));

			const postalCode = findType("postal_code");
			const state = findType("administrative_area_level_1");
			const country = findType("country");
			const locality = findType("locality");
			const neighborhood = findType("neighborhood");
			const colloquialArea = findType("colloquial_area");
			const sublocalityComponents = findAllOfType("sublocality");

			const sortedSublocalities = [...sublocalityComponents].sort((a, b) => {
				const levelA = a.types.find((t) => t.startsWith("sublocality_level_"));
				const levelB = b.types.find((t) => t.startsWith("sublocality_level_"));
				if (!levelA && !levelB) return 0;
				if (!levelA) return 1;
				if (!levelB) return -1;
				return (
					parseInt(levelA.split("_").pop()) - parseInt(levelB.split("_").pop())
				);
			});

			const administrative_area = {};
			for (let i = 1; i <= 7; i++) {
				const match = findType(`administrative_area_level_${i}`);
				administrative_area[`administrative_area_level_${i}`] =
					match?.long_name || null;
			}

			return {
				postal_code: postalCode?.long_name || "",
				state,
				country,
				locality: locality?.long_name || "",
				neighborhood: neighborhood?.long_name || "",
				colloquial_area: colloquialArea?.long_name || "",
				sublocality: {
					sublocality_level_1: sortedSublocalities[0]?.long_name || "",
					sublocality_level_2: sortedSublocalities[1]?.long_name || "",
					sublocality_level_3: sortedSublocalities[2]?.long_name || "",
					sublocality_level_4: sortedSublocalities[3]?.long_name || null,
					sublocality_level_5: sortedSublocalities[4]?.long_name || null,
				},
				administrative_area,
				country: country?.long_name || "",
				country_code: country?.short_name || "",
				state_name: state?.long_name || "",
				state_code: state?.short_name || "",
			};
		},
		updateFormPayload(location, addressInfo, coordinates) {
			const formObj = this.form.payload;

			formObj.postal_code = addressInfo.postal_code;
			formObj.country = addressInfo.country;
			formObj.country_name = addressInfo.country;
			formObj.country_code = addressInfo.country_code;
			formObj.city = addressInfo.locality;
			formObj.state_name = addressInfo.state_name;
			formObj.state_code = addressInfo.state_code;
			formObj.formatted_address = location?.formatted_address;
			formObj.name = location?.name;
			formObj.latitude = coordinates.latitude;
			formObj.longitude = coordinates.longitude;
		},
		updateTimeSlots(weekDays) {
			// If Google returns timeslot data:
			if (weekDays && weekDays.length) {
				// Reset all timeslots first.
				this.timeSlots.data.forEach((slot) => {
					slot.availability = []; // start with empty array for adding new slots
					// When updating with API data, mark the day as available.
					slot.isDayOff = false;
				});

				weekDays.forEach((entry) => {
					// Entry expected as "Day: startTime – endTime" string.
					const firstColonIndex = entry.indexOf(":");
					if (firstColonIndex === -1) return;

					const day = entry.substring(0, firstColonIndex).trim();
					const times = entry.substring(firstColonIndex + 1).trim();
					const [startTime, endTime] = times.split("–").map((t) => t.trim());

					const matchingSlot = this.timeSlots.data.find(
						(slot) => slot.day === day
					);
					if (matchingSlot) {
						matchingSlot.availability.push({
							startTime,
							endTime,
							isOverlapping: false,
							isEndTime: false,
						});
					}
				});
			} else {
				this.timeSlots.data.forEach((slot) => {
					slot.availability = [{ isOverlapping: false, isEndTime: false }];
					slot.isDayOff = true;
				});
			}
		},
		updatePayload(data) {
			const components = data.address_components || [];
			const location = data;
			const formObj = this.form.payload;
			const weekDays = data.opening_hours && data.opening_hours.weekday_text;

			const addressInfo = this.extractAddressInfo(components);
			const coordinates = this.extractCoordinates(location);

			// Store specific values in google_map object

			this.form.payload.google_map = {
				place_id: data.place_id || "",
				types: data.types || [],
				colloquial_area: addressInfo.colloquial_area,
				locality: addressInfo.locality,
				sublocality: addressInfo.sublocality,
				neighborhood: addressInfo.neighborhood,
				postal_code: addressInfo.postal_code,
				administrative_area: addressInfo.administrative_area,
				country: addressInfo.country,
				coordinates: {
					latitude: data.geometry?.location?.lat || 0,
					longitude: data.geometry?.location?.lng || 0,
				},
				opening_hours: data.opening_hours?.weekday_text || [],
			};

			this.updateFormPayload(location, addressInfo, coordinates);
			this.updateTimeSlots(weekDays);
		},
		gotfetchedLocationDetail(options) {
			if (
				options.response !== undefined &&
				options.response.data !== undefined &&
				options.response.status === 200
			) {
				this.filters.data = options.response.data.result;
				let data = this.filters.data;
				this.updatePayload(data);
			}
		},
		fetchLocationDetail(placeId) {
			const options = {
				apiURL: `${this.wpThemeURL}/proxy.php?placeId=${placeId}`,
				module: "gotData",
				store: "filters",
				callback: true,
				callbackFunc: (options) => this.gotfetchedLocationDetail(options),
			};
			this.$store.dispatch("fetchData", options);
		}
	},
	mounted() {
		// Initialize Places Service
		if (window.google && window.google.maps) {
			this.placesService = new google.maps.places.PlacesService(document.createElement('div'));
		}
	},
});

