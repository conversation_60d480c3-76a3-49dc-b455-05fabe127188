$pureRed: #ff0000;
$brightCrimson: #ef4444;
$darkGray: #5f6368;
$semiTransp: rgba(0, 0, 0, 0.6);
$lightGray: #b0b0b0;
$bgtransp: transparent;
$bgPeach: #ffe5cc;
@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
  .cardSection {
    .hasNoClass {
      border: 1px solid $grey;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: $gap15 $gapSmall * 2;
      cursor: pointer;
      &:hover {
        border-color: $primary;
      }
    }
    .favIcon {
      img {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
    }
    .cardContent {
      border: 1px solid $grey;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 15px $gapSmall * 2;
      cursor: pointer;
      &:hover {
        border-color: $primary;
      }
      .cardContentWrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        gap: $gapSmall;

        .actionList {
          &.mobile {
            display: none;
            position: absolute;
            right: 0;
            @media (max-width: 767px) {
              display: block;
            }
          }
        }
        .classStatus {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: $overline;
          font-weight: 500;
          line-height: 14px;
          letter-spacing: 1.5px;
          text-transform: uppercase;
          .dot {
            border-radius: 50%;
            height: 10px;
            width: 10px;
            background: $pureRed;
          }
        }
        .learnerAttendence {
          display: flex;
          align-items: center;
          gap: 7px;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          letter-spacing: 0.25px;
          .progress-wrapper {
            .progress-value {
              @include setFontColor($primaryCopyColor, 0.7);
            }

            .progress {
              border: 1px solid #fff;

              &.is-orange::-webkit-progress-value {
                background: #fc9927;
              }

              &.is-red::-webkit-progress-value {
                background: #ca0813;
              }

              &.is_blue::-webkit-progress-value {
                background: #0000ff;
              }

              &.is-yellow::-webkit-progress-value {
                background: #f0c042;
              }

              &.is-green::-webkit-progress-value {
                background: #008000;
              }

              &.is-lightGreen::-webkit-progress-value {
                background: #669d4f;
              }

              &.is-darkGreen::-webkit-progress-value {
                background: #356b21;
              }
            }
          }
          .bgLightPink {
            background: $primaryVariant;
            color: $brightCrimson;
          }
        }
        .classType {
          text-align: center;
          padding: 2px 6px;
          border-radius: 4px;
        }
        .classType.bgLightBlue {
          background: #cce5ff;
        }
        .classType.lightPeach {
          background-color: $bgPeach;
        }
        .classType.bgLightGreen {
          background: #d4edda;
          color: $onSurfaceVariant;
        }
        .classTitle {
          letter-spacing: 0.15px;
          margin-bottom: 5px;
          color: $onSurface;
        }
        .underline {
          text-decoration-line: underline;
          text-decoration-style: solid;
        }
        .classDetails {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 4px;
          &.addMargin {
            margin-bottom: 5px;
          }
          span {
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            letter-spacing: 0.25px;
          }
          .recordingDuration {
            display: flex;
            align-items: center;
            gap: 4px;
            .playIcon {
              border: 2px solid #5f6368;
              border-radius: 50%;
              color: $darkGray;
              width: 18px;
              height: 18px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
        .userProfile {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
          align-items: center;
          span {
            img {
              border-radius: 50%;
              width: 34px;
              height: 35px;
            }
          }
          .material-icons {
            color: $grey;
            font-size: 36px;
          }
          .userDescription {
            display: flex;
            flex-direction: column;
            gap: 1px;
          }
        }
        .progress-wrapper {
          .progress {
            width: 137px;
            height: 7px;
          }
        }
        .learnerList {
          display: flex;
          li {
            margin-left: -12px;
            z-index: 7;
            .material-icons {
              background-color: white;
              color: #e6e6e6;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 18px;
              height: 24px;
              font-size: 27px;
            }
            img {
              width: 24px;
              height: 24px;
              border-radius: 50%;
            }
          }
        }
        .progressWrapper {
          display: flex;
          align-items: center;
          gap: 6px;
          @media (max-width: 767px) {
            width: 100%;

            .progress {
              width: 100%;
            }
          }
        }
      }
      .buttonWrapper {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: $gapSmall * 2;
        padding-top: $gapSmall;
        width: 100%;
        @media (min-width: 767px) {
          width: 150px;
        }
        .hasFlex {
          display: flex;
          justify-content: start;
          @media (min-width: 767px) {
            justify-content: end;
          }
        }
        .noBold {
          font-weight: 400;
        }
        .cta {
          a {
            width: 100%;
            &.secondaryCTA {
              padding: 10px 20px;
              border-radius: 4px;
              border: 1px solid #d0c4c2;
              background-color: #fff;
              line-height: normal;
              font-size: 16px;
              font-weight: 500;
              color: #201a19;

              &:hover {
                text-decoration: none;
                border-color: #a81e22;
              }
            }
          }
        }
        .academyLabel {
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: start;
          gap: 5px;

          @media (min-width: 767px) {
            justify-content: center;
            gap: 10px;
          }

          .academyLogo {
            width: 24px;
            height: 24px;
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
        .actionList {
          &.desktop {
            @media (max-width: 767px) {
              display: none;
            }
          }
          .dropdown-menu {
            min-width: 100px;
            padding: 0;

            a {
              color: $onSurface;
            }
          }
        }
      }
      .starIcon {
        color: #f9b600;
        font-size: 16px;
      }
    }
    .scheduleModal {
      .yunoModal {
        .modal-content {
          height: 490px !important;
          border: 1px solid $grey !important;
        }

        .modal-background {
          background-color: $bgtransp !important;
        }
        .modalTitle {
          padding: 40px 20px 0 20px;
        }

        .modalBody {
          display: flex;
          flex-grow: 1;
          @media (max-width: 768px) {
            flex-direction: column;
            gap: 20px;
          }

          .classInfoWrapper {
            width: 70%;
            .classInfo {
              display: flex;
              flex-direction: column;
              gap: 6px;
              border-bottom: 1px solid $grey;
              width: 300px;

              .flexDiv {
                display: flex;
                align-items: center;
              }
              .learnerList {
                display: flex;
                align-items: center;
                padding-left: 10px;
                li {
                  margin-left: -12px;
                  z-index: 7;
                  .material-icons {
                    background-color: white;
                    color: #e6e6e6;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 18px;
                    height: 24px;
                    font-size: 27px;
                  }
                  img {
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                  }
                }
              }
              .learnerEnrolled {
                display: flex;
                align-items: baseline;
                gap: 6px;
              }
            }
            .courseInfo {
              display: flex;
              flex-direction: column;
              gap: 6px;

              .academyFavIcon {
                img {
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                }
              }
            }
          }
          .addLearners {
            width: 30%;

            .totalLearners {
              border-bottom: 2px solid $grey;
            }
          }
        }
        .modalFooter {
          padding-bottom: 20px;
          .ctaWrapper {
            gap: 10px;

            .primaryCTA {
              padding: 7px 10px;
              border-radius: 4px;
              border: 1px solid #a81e22;
              background-color: #a81e22;
              line-height: normal;
              font-size: 16px;
              font-weight: 500;
              color: #fff;

              &:hover {
                text-decoration: none;
                background-color: $primaryV1;
              }
            }
            .secondaryCTA {
              padding: 7px 10px;
              border-radius: 4px;
              border: 1px solid #d0c4c2;
              background-color: #fff;
              line-height: normal;
              font-size: 16px;
              font-weight: 500;
              min-width: 80px !important;
              color: #201a19;

              &:hover {
                text-decoration: none !important;
                border-color: #a81e22;
              }
            }
          }
        }
      }
    }
    .yunoSnackbar {
      .snackbar {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 15px;
        background: white;
        width: 400px;
        overflow-y: auto;
      }
      .vue-star-rating[data-v-fde73a0c] {
        justify-content: center !important;
      }
      .yunoInput {
        .textarea {
          &:not([rows]) {
            max-height: 6em !important;
            min-height: 4em !important;
          }
        }
      }
    }
  }
  .yunoSnackbar {
    .snackbar {
      .closeSnackbar {
        .material-icons-outlined {
          font-size: 18px;
        }
      }
    }
    .titleLarge {
      font-size: 24px;
      line-height: normal;
      font-weight: 400;
      margin-bottom: 5px;
    }
    .titleSmall {
      font-size: 16px;
      line-height: normal;
      font-weight: 500;
      margin-bottom: 5px;
    }
    .subtitleSmall {
      font-size: 14px;
      line-height: normal;
      font-weight: 400;
      margin-bottom: 15px;
    }
    .noticeSmall {
      margin-bottom: 10px;
    }
    .noticeTitle {
      font-size: 10px;
      line-height: normal;
      font-weight: 400;
      margin-bottom: 0;
      width: 100%;
      text-transform: uppercase;
      letter-spacing: 1.5px;
    }
    .mappedInstructor {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 15px;
      .imgWrapper {
        flex: 0 0 50px;
        margin-right: 10px;
        img {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          font-size: 0;
          background-color: $whiteBG;
        }
      }
      figcaption {
        flex: 0 0 calc(100% - 60px);
      }
      .insName {
        font-size: 16px;
        line-height: 20px;
        font-weight: 500;
        margin-bottom: 5px;
      }
      .studentCount {
        flex: 0 0 100%;
        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
        margin-bottom: 0;
        margin-top: 10px;
      }
    }
    .mappedInstructor.gapBtm15 {
      margin-bottom: 15px;
    }
    .ctaWrapper {
      margin-top: 15px;
      display: flex;
      .button {
        margin-right: 10px;
      }
    }
    .formWrapper {
      width: 100%;
      position: relative;
      background: #fafafa;
      padding: 10px;
      .innerWrapper {
        background-color: $whiteBG;
        padding: 10px;
        margin-bottom: 15px;
        .groupElement {
          margin: 0;
        }
      }
      .vue-star-rating {
        padding-left: 15px;
      }
      .field.noGap {
        margin: 0;
      }
      .alert {
        height: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .material-icons-outlined {
          font-size: 40px;
          margin-bottom: 10px;
        }
      }
      .ctaWrapper {
        justify-content: right;
        .button {
          margin-right: 0;
        }
      }
      .ctaWrapper.loading {
        position: absolute;
        right: 5px;
        bottom: 54px;
        .button {
          border: 0;
        }
      }
      .checkList {
        .fieldLabel {
          font-size: 14px;
          margin: 0 0 10px;
          color: $semiTransp;
          font-weight: 600;
        }
        .checkboxList {
          display: flex;
          flex-wrap: wrap;
          .field {
            margin-right: 10px;
          }
          .b-checkbox {
            border-radius: 20px;
            padding: 5px 10px;
            height: auto;
            font-size: 12px;
          }
        }
      }
    }
    .formWrapper.noBG {
      background: none;
      padding: 0;
    }
    .formWrapper.gapTop15 {
      padding-top: 15px;
    }
    .starLabel {
      display: flex;
      margin-left: 12px;
      margin-top: 8px;
      li {
        font-size: 12px;
        line-height: normal;
        font-weight: 400;
        margin-bottom: 0;
        flex: 0 0 67px;
        text-align: center;
      }
      li.active {
        visibility: visible;
      }
      li.notActive {
        visibility: hidden;
      }
    }
    .checkboxList {
      .field {
        display: inline-block !important;
        margin-bottom: 2px !important;
        margin-right: 6px !important;
      }
    }
  }
}
.b-sidebar.extraWide {
  .sidebar-content {
    width: 550px !important;
  }
}
.b-sidebar.card-slidebar {
  .sidebar-content {
    width: 450px;
  }
  .sidebar-content.is-light {
    background-color: white !important;
  }
  .academyLogo {
    img {
      width: 16px;
      height: 16px;
      border-radius: 2px;
    }
  }
  .guestLink {
    &.collapse:not(.show) {
      display: block !important;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
    }

    .card-header {
      box-shadow: none;
      padding: 0;
      border: none;
      height: 40px;
      .card-header-title,
      .card-header-icon {
        padding: 0.35rem 1rem;
        a {
          &:hover {
            text-decoration: none;
          }
        }
      }
      .card-header-title {
        font-weight: 500 !important;
        font-size: 14px !important;
        line-height: 18px !important;
        letter-spacing: 0.1px !important;
        padding: 8px;
      }
    }
  }
  .content {
    .urlWrapper {
      display: flex;
      align-items: center;
      border-radius: 4px;
      padding: 8px;
      position: relative;
      border: 1px solid $grey;
      height: 50px;

      .url {
        padding-right: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .copyGuestUrl {
        position: absolute;
        right: 8px;
        background: transparent;
        color: #a81e22;
        border: none;
        font-weight: 500;
        text-decoration: underline;
      }
    }
  }
  .cardContent {
    padding-top: 70px;
    .headline5 {
      font-size: 24px;
      line-height: 28px;
      font-weight: 500;
      margin-bottom: 0;
    }
    .subtitle1 {
      font-size: 16px;
      line-height: 20px;
      font-weight: 500;
      margin-bottom: 0;

      &.noBold {
        font-weight: 400;
      }
    }
    .underline {
      text-decoration-line: underline;
      text-decoration-style: solid;
    }
    .subtitle3 {
      font-size: 12px;
      line-height: 16px;
      font-weight: 400;
      margin-bottom: 0;
    }
    .subtitle2 {
      font-size: 14px;
      line-height: 18px;
      font-weight: 500;
      margin-bottom: 0;

      &.noBold {
        font-weight: 400;
      }
    }
    .subtitle3 {
      font-size: 16px;
      line-height: 18px;
      font-weight: 500;
      margin-bottom: 0;

      &.noBold {
        font-weight: 400;
      }
    }

    .overline {
      font-size: 12px;
      line-height: 14px;
      font-weight: 500;
      margin-bottom: 0;
    }
    .wrapper {
      padding: 25px;
    }
    .cardContentWrapper {
      display: flex;
      flex-direction: column;
      gap: 10px;
      .videoWrapper {
        padding-bottom: $gap15;
        .videoLPPlayer {
          height: 300px;
          iframe {
            width: 100%;
            height: 100%;
          }
        }
      }
      .videoWrapper.loading {
        padding-bottom: 0;
      }
      .classStatus {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 10px;
        line-height: 14px;
        font-weight: 500;
        text-align: left;
        color: $pureRed;
        text-transform: uppercase;
        .dot {
          border: 1px solid;
          border-radius: 50%;
          height: 10px;
          width: 10px;
          background: $pureRed;
        }
      }
      .progress-wrapper {
        .progress {
          width: 137px;
          height: 7px;
        }
      }
      .learnerAttendence {
        display: flex;
        align-items: center;
        gap: 7px;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        letter-spacing: 0.25px;
        .progress-wrapper {
          .progress-value {
            @include setFontColor($primaryCopyColor, 0.7);
          }
          .progress {
            width: 137px;
            height: 7px;

            &.is-orange::-webkit-progress-value {
              background: #fc9927;
            }

            &.is-red::-webkit-progress-value {
              background: #ca0813;
            }

            &.is_blue::-webkit-progress-value {
              background: #0000ff;
            }

            &.is-yellow::-webkit-progress-value {
              background: #f0c042;
            }

            &.is-green::-webkit-progress-value {
              background: #008000;
            }

            &.is-lightGreen::-webkit-progress-value {
              background: #669d4f;
            }

            &.is-darkGreen::-webkit-progress-value {
              background: #356b21;
            }
          }
        }
        .bgLightPink {
          background: $primaryVariant;
          color: $brightCrimson;
        }
      }
      .classType {
        font-size: 10px;
        font-weight: 500;
        line-height: 14px;
        letter-spacing: 1.5px;
        text-align: center;
        padding: 4px 6px;
        border-radius: 4px;

        &.bgLightGreen {
          background: #d4edda;
          color: $onSurfaceVariant;
        }
        &.lightPeach {
          background: $bgPeach;
        }
        &.bgLightPink {
          background: $primaryVariant;
          color: $brightCrimson;
        }
        &.bgLightBlue {
          background: #cce5ff;
        }
      }
      .classTitle {
        font-size: 24px;
        line-height: 28px;
        font-weight: 500;
        margin-bottom: 0;
        color: $onSurface;
      }
      .classDetails {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 4px;
        span {
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          letter-spacing: 0.25px;
        }
        .recordingDuration {
          display: flex;
          align-items: center;
          gap: 4px;
          .playIcon {
            border: 2px solid #5f6368;
            border-radius: 50%;
            color: $darkGray;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
      .closeSidebar {
        margin-left: -8px;
        cursor: pointer;
      }
    }
    .button.yunoPrimaryCTA {
      padding: 10px 20px;
      border-radius: 4px;
      border: 1px solid #a81e22;
      background-color: $primary;
      line-height: normal;
      font-size: 16px;
      font-weight: 400;
      color: $secondaryCopyColor;
      &:hover {
        text-decoration: none;
        background-color: $primaryV1;
      }
    }
    .buttonWrapper {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 20px;
      padding-top: 20px;
      width: 150px;

      .cta {
        a {
          width: 100%;
          &.secondaryCTA {
            padding: 10px 20px;
            border-radius: 4px;
            border: 1px solid #d0c4c2;
            background-color: #fff;
            line-height: normal;
            font-size: 16px;
            font-weight: 500;
            color: #201a19;

            &:hover {
              text-decoration: none !important;
              border-color: #a81e22;
            }
          }
        }
      }
    }
    .courseDetails {
      border-bottom: 1px solid $grey;
      gap: 8px;
      padding: 25px 0;
	  .hasFlex {
		display: flex;
		gap: 8px;
		margin-top: 8px;
	  }
      .academyLogo {
        width: 24px;
        height: 24px;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
    .classReview {
      gap: 10px;
      img {
        border-radius: 50%;
        width: 70px;
      }
      .material-icons.profileIcon {
        background-color: white;
        color: #e6e6e6;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        width: 18px;
        height: 24px;
        font-size: 34px;
      }
      .learnerReviewWrapper {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }
    .userProfile {
      display: flex;
      gap: 10px;
      align-items: center;
      img {
        width: 40px;
        border-radius: 50%;
      }
      img.userImage {
        width: 82px !important;
      }
      .userDescription {
        display: flex;
        flex-direction: column;
        gap: 3px;
      }
    }
    .learnerList {
      display: flex;
      align-items: center;
      li {
        margin-left: -12px;
        z-index: 7;
        .material-icons {
          background-color: white;
          color: #e6e6e6;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 18px;
          height: 24px;
          font-size: 27px;
        }
        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
        }
      }
    }
    .starIcon {
      color: #f9b600;
      font-size: 16px;
    }
    .instructorLearners {
      ul {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 1rem;
          border: 1px solid $grey;
          padding: 10px 10px;

          .learnerProfile {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            img {
              width: 30px;
              height: 30px;
              border-radius: 50%;
            }
          }

          .learnerAttendance {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .progress {
              width: 100px;
              height: 7px;
            }
          }
          .learnerRating {
            display: flex;
            align-items: center;
            gap: 0.3rem;
          }
        }
      }
    }
  }
  .separator {
    margin: 0 4px;
    color: $lightGray;

    @media (max-width: 767px) {
      display: none;
    }
  }
}
.yunoSnackbar {
  .classCheckbox {
    display: inline-block;
    .button {
      padding: 0 8px !important;
      border-radius: 20px !important;
      font-size: 11px !important;
    }
  }
}
.separator {
  margin: 0 4px;
  color: $lightGray;
  display: none;
  @media (min-width: 767px) {
    display: block;
  }
}
@media (max-width: 767px) {
  #app {
    .cardSection {
      .cardContent {
        flex-direction: column;
        .cardContentWrapper {
          .learnerAttendence {
            flex-direction: column;
            align-items: baseline;
          }
          .classDetails {
            flex-direction: column;
            gap: 5px;
            align-items: baseline;
          }
        }
        .buttonWrapper {
          .cta {
            a {
              width: 100%;

              &.secondaryCTA {
                padding: 10px 20px;
                border-radius: 4px;
                border: 1px solid #d0c4c2;
                background-color: #fff;
                line-height: normal;
                font-size: 16px;
                font-weight: 500;
                color: #201a19;

                &:hover {
                  text-decoration: none;
                  border-color: #a81e22;
                }
              }
            }
          }
        }
      }
    }
  }
  .b-sidebar.card-slidebar {
    .sidebar-content {
      width: 100%;
    }
    .cardContent {
      .cardContentWrapper {
        .learnerAttendence {
          flex-direction: column;
          align-items: baseline;
        }
      }
      .buttonWrapper {
        a {
          width: 100%;
        }
      }
    }
  }
}
