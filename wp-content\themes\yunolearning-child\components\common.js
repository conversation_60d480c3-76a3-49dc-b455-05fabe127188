const YUNOCommon = (function ($) {

    const config = {
        errorMsg: {
            common: "An error seems to have occurred. Please try again. ",
            notMapped: "The selected course doesn't mapped with any instructor. Please select other course.",
            enrollmentError: "It seems something went wrong with our servers. Our team has been notified. Please try again later.",
            sesstionExpired: "Your session has been expired. Please login again to resume your session. "
        },
        awsHost: function () {
            let isHost = window.location.hostname

            if (isHost === "localhost") {
                return "https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev"
            };

            if (isHost === "www.yunolearning.com") {
                return "https://api.yunolearning.com"
            };

            if (isHost === "stage.yunolearning.com" || isHost === "dev.yunolearning.com") {
                return "https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev"
            };

            if (isHost === "webcache.googleusercontent.com") {
                return "https://api.yunolearning.com"
            };
        },
        addVerion: function (hasQuery) {
            let version = 1,
                versionStr = "";

            if (hasQuery) {
                versionStr = "?buildVersion=" + version + ""
            } else {
                versionStr = "&buildVersion=" + version + ""
            }

            return versionStr;
        },
        pickHost: function () {
            if (this.host() === "http://localhost") {
                return "https://dev.yunolearning.com"
            } else if (this.host() === "http://*************") {
                return "https://dev.yunolearning.com"
            }  else if (this.host() === "https://www.yunolearning.com") {
                return "https://www.yunolearning.com"
            } else if (this.host() === "https://webcache.googleusercontent.com") {
                return "https://www.yunolearning.com"
            }
             else {
                return this.host();
            }
        },
        laravelHost: function () {
            if (this.host() === "http://localhost") {
                return "https://vcdev.yunolearning.com"
            } else if (this.host() === "https://dev.yunolearning.com") {
                return "https://vcdev.yunolearning.com"
            } else if (this.host() === "https://stage.yunolearning.com") {
                return "https://vcstage.yunolearning.com"
            } else if (this.host() === "https://www.yunolearning.com") {
                return "https://ai-laravel-yxdza.kinsta.app"
            };
        },
        host: function () {
            const getHost = window.location.protocol + '//' + window.location.hostname;
            return getHost;
        },
        footerAPI: function () {
            // const createURL = this.pickHost() + "/wp-json/yuno/v1/misc/footer/info";
            const createURL = this.pickHost() + "/wp-json/yuno/v1/footer";

            return createURL;
        },    
        latesBlogAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/latest/blogs";

            return createURL;
        },
        courseIELTSAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/ielts/short-detail";

            return createURL;
        },
        allCoursesIELTSAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/ielts/detail";

            return createURL;
        },
        headerMenuAPI: function (userRole, categories) {
            let hasCategories = "";

            if (categories !== undefined) {
                hasCategories = "?category=" + categories + "";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/menu/" + userRole + "" + hasCategories + "";

            return createURL;
        },
        headerMenuAPIV2: function (userID, userRole) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/menu/"+ userID +"/" + userRole + "";
            return createURL;
        },
        userRoleAPI: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/" + userID + "/role";

            return createURL;
        },
        userProfileAPI: function (userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/" + userID + "/profile" + hasCache + "";

            return createURL;
        },
        studentResultsAPI: function (type, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/results/" + type + "/" + limit + "/" + offset + "";

            return createURL;
        },
        faqAPI: function (type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/faq/" + type + "";

            return createURL;
        },
        courseAPI: function (courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/getdetail/" + courseID + "/" + userID + "";

            return createURL;
        },
        scheduleAPI: function (courseID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/" + courseID + "/schedule";

            return createURL;
        },
        instructorAPI: function (ownerID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/" + ownerID + "/getUserDetail";

            return createURL;
        },
        updateInstructorDetailAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/updateUserDetail";

            return createURL;
        },
        instructorCoursesAPI: function (instructorId, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/instructor/" + instructorId + "/" + userID + "";

            return createURL;
        },
        instructorBatchAPI: function (instructorId, courseID, userID, state, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/batches/" + instructorId + "/instructor/" + courseID + "/" + userID + "/" + state + "/" + limit + "/" + offset + "";

            return createURL;
        },
        instructorNonBatchesAPI: function (instructorId) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructorSingleProductDetail?instructorID=" + instructorId + "";

            return createURL;
        },
        categoriesAPi: function (query) {
            if (query === undefined) {
                query = "";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/category"+ query +"";

            return createURL;
        },
        featuredCoursesAPi: function (instructorId) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/featuredCourses";

            return createURL;
        },
        featuredInstructorAPi: function (channel) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/featuredInstructor/" + channel + "";

            return createURL;
        },
        batchAPi: function (isAll, id, userID, state) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/batches/" + isAll + "/" + id + "/" + userID + "/" + state + "";

            return createURL;
        },
        classAPi: function (type, instructorId, loggedinUser, criteria, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/classes/" + type + "/" + instructorId + "/" + loggedinUser + "/" + criteria + "/" + limit + "/" + offset + "";

            return createURL;
        },
        signUpAPi: function (userID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/users/" + userID + "/" + type + "";

            return createURL;
        },
        signUpV2APi: function (userID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/users/" + userID + "/" + type + "";

            return createURL;
        },
        isUserSignUpAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/signup/" + userID + "";

            return createURL;
        },
        loginAPi: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/loginWithGoogle";

            return createURL;
        },
        createPaymentAPi: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/create/payment";

            return createURL;
        },
        updatePaymentAPi: function (batchID, courseID, userID, receiptID, dealID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/update/" + batchID + "/" + courseID + "/" + userID + "/" + receiptID + "/" + dealID + "/payment";

            return createURL;
        },
        myLearnersAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/schedule/mylearners/mygroups/" + userID + "";

            return createURL;
        },
        classTitleAPi: function (instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/class-schedule/titles/instructor/" + instructorID + "";

            return createURL;
        },
        addClassTitleAPi: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/add/class/title";

            return createURL;
        },
        createClassAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/create/class/" + userID + "";

            return createURL;
        },
        updateClassAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/update/class/" + userID + "";

            return createURL;
        },
        classesAPi: function (role, userID, type, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/classes/" + role + "/" + userID + "/" + type + "/" + limit + "/" + offset + "";

            return createURL;
        },
        classesByViewAPi: function (view, currentUserID, userID, filterVal, courseID, attendance, state, limit, offset, isCache) {
            let hasCache = "",
                hasCurrentUserID = "",
                hasAttendance = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            if (currentUserID !== undefined && currentUserID !== false) {
                hasCurrentUserID = "/" + currentUserID + ""
            }

            if (attendance !== undefined && attendance !== false) {
                hasAttendance = "/" + attendance + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v2/classes/" + view + "" + hasCurrentUserID + "/" + userID + "/" + filterVal + "/" + courseID + hasAttendance + "/" + state + "/" + limit + "/" + offset + "" + hasCache + "";

            return createURL;
        },
        groupsAPi: function (role, userID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/get/groups/" + role + "/" + userID + "/" + limit + "/" + offset + "";

            return createURL;
        },
        addLearnersToGroupAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/updateUsersInGroup";

            return createURL;
        },
        updateGroupTitleAPI: function (userID, groupID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/updateGroup";

            return createURL;
        },
        createGroupsAPi: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/create/group";

            return createURL;
        },
        crmContacts: function () {
            const createURL = this.awsHost() + "/getCRMContacts";

            return createURL;
        },
        instructorLearnersAPI: function (role, userID, limit, offset, filters) {
            let hasFilter = "";

            if (filters !== false) {
                hasFilter = "?filter=" + filters + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/mylearners/" + role + "/" + userID + "/" + limit + "/" + offset + "" + hasFilter + "";

            return createURL;
        },
        instructorBatchesAPI: function (userID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/batches/" + userID + "/" + type + "";

            return createURL;
        },
        learnerCoursesAPI: function(userID, type, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            };
            
            const createURL = this.pickHost() + "/wp-json/yuno/v1/learner/"+ userID +"/courses/"+ type +""+ hasCache +"";

            return createURL;
        },
        enHeroCardsAPI: function (userID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/english-speaking/detail";

            return createURL;
        },
        classDetailAPI: function (classID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/classDetail/" + classID + "/" + userID + "";

            return createURL;
        },
        classLearnerAPI: function (classID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/classLearnerDetail/" + classID + "/"+ limit +"/"+ offset +"";

            return createURL;
        },
        demoClassEnrollAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/enroll/demo/class";

            return createURL;
        },
        editClassAPI: function (classID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/class/detail/" + classID + "/instructor/" + userID + "";

            return createURL;
        },
        allCoursesAPI: function (filter) {
            let hasFilter = "";

            if (filter !== undefined && filter !== false) {
                hasFilter = "/" + filter;
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/all/course/list"+ hasFilter +"";

            return createURL;
        },
        allBatchesAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/admin/batches/all";

            return createURL;
        },
        enrollmentStatusAPI: function (batchID, courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/enrollment/batch/" + batchID + "/" + courseID + "/" + userID + "/status";

            return createURL;
        },
        generatePaymentLinkAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/create/payment/link";

            return createURL;
        },
        reviewsAPI: function (type, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/reviews/" + type + "/" + userID + "";

            return createURL;
        },
        paymentList: function (filter, userID, type, typeVal, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/payments/" + filter + "/" + userID + "/" + type + "/" + typeVal + "/" + limit + "/" + offset + "";

            return createURL;
        },
        enrollmentList: function (filter, userID, type, typeVal, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/enrollment/" + filter + "/" + userID + "/" + type + "/" + typeVal + "/" + limit + "/" + offset + "";

            return createURL;
        },
        enrollmentsList: function (role, userID, view, instructorID, orgAdminID, referral, learnerID, eStatus, counsellorID, pStatus, limit, offset, apiVersion, courseID) {
            let hasAPIVersion = "";

            if (apiVersion !== undefined && apiVersion !== false) {
                hasAPIVersion = apiVersion;
            } else {
                hasAPIVersion = "v1"
            }


            const createURL = this.pickHost() + "/wp-json/yuno/"+ hasAPIVersion +"/enrollments/" + role + "/" + userID + "/" + view + "/" + instructorID + "/" + orgAdminID + "/" + referral + "/" + learnerID + "/" + eStatus + "/" + counsellorID + "/" + pStatus + "/" + courseID + "/" + limit + "/" + offset + "";

            return createURL;
        },
        paymentsList: function (role, userID, view, days, pStatus, counsellorID, instructorID, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/payment/" + role + "/" + userID + "/" + view + "/" + days + "/" + pStatus + "/" + counsellorID + "/" + instructorID + "/" + limit + "/" + offset + "" + hasCache + "";

            return createURL;
        },
        updatePaymentLinkAPI: function () {
            const createURL = this.awsHost() + "/payments/updatelink";

            return createURL;
        },
        updateLinkAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/updateLink/payment";

            return createURL;
        },
        instructorListAPI: function (userID, type) {
            let hasUserID = "",
                hasType = "";

            if (type !== undefined && type !== false) {
                hasType = "/" + type;
            }

            if (userID !== undefined && userID !== false) {
                hasUserID = "/" + userID + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/list" + hasUserID + ""+ hasType +"";

            return createURL;
        },
        reviewsByTypeAPI: function (channelID, instructorId) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/reviews/" + channelID + "/" + instructorId + "";

            return createURL;
        },
        batchToggleAPI: function (batchid, courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/enrollment/" + batchid + "/" + courseID + "/" + userID + "/status/toggle";

            return createURL;
        },
        changeBatchAPI: function (batchid, courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/changeBatches";

            return createURL;
        },
        blogListAPI: function (limit, offset) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/posts/?per_page=" + limit + "&offset=" + offset + "&_embed";

            return createURL;
        },
        pageAPI: function (postID) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/pages/" + postID + "?_embed";

            return createURL;
        },
        blogAPI: function (postID) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/posts/" + postID + "?_embed";

            return createURL;
        },
        postCategoriesAPI: function (limit) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/categories/?per_page=" + limit + "";

            return createURL;
        },
        courseBatchesAPI: function (courseID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/batches/all/" + courseID + "/0/upcomingOngoing";

            return createURL;
        },
        blogsByCategoryAPI: function (categoryID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/posts?categories=" + categoryID + "&per_page=" + limit + "&offset=" + offset + "&_embed";

            return createURL;
        },
        blogCategoryAPI: function (categoryID) {
            const createURL = this.pickHost() + "/wp-json/wp/v2/categories/" + categoryID + "";

            return createURL;
        },
        settingsAPI: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/notificationsettings/" + userID + "";

            return createURL;
        },
        updateSettingsAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/notificationsettings";

            return createURL;
        },
        addressAPI: function (userID, type, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/" + userID + "/address/" + type + "" + hasCache + "";

            return createURL;
        },
        updateAddressAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/update/user/address";

            return createURL;
        },
        listOfCounsellorsAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/counselor/list";

            return createURL;
        },
        googleContactsAPI: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/getGoogleContacts/" + userID + "";

            return createURL;
        },
        meetingAPI: function (meetingID, state, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/meeting/" + meetingID + "/" + state + "/"+ limit +"/"+ offset +"";

            return createURL;
        },
        participantsAPI: function (participantID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/add/zoom/participants/" + participantID + "";

            return createURL;
        },
        batchesGrid: function (state, userID, instructorID, courseID, classTime, category, groupType, limit, offset) {
            let hasgroupType = "";

            if (groupType !== undefined && groupType !== false) {
                hasgroupType = "/" + groupType + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/batches/" + state + "/" + userID + "/" + instructorID + "/" + courseID + "/" + classTime + "/" + category + ""+ hasgroupType +"/" + limit + "/" + offset + "";

            return createURL;
        },
        mapCoursesAPI: function (state, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/courses/" + state + "/" + limit + "/" + offset + "";

            return createURL;
        },
        updateInstructorCoursesAPI: function (instructorID, courseID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/add/instructor/" + instructorID + "/course/" + courseID + "";

            return createURL;
        },
        relatedCoursesAPI: function (instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/" + instructorID + "/courses";

            return createURL;
        },
        categoryListAPI: function (filters) {
            let hasFilters = "";
    
            if (filters !== undefined) {
                hasFilters = "?filter=" + filters + "";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/all/category/signup"+ hasFilters +"";

            return createURL;
        },
        categoryTaxonomyAPI: function (userID) {
            let hasUserID = "";

            if (userID !== undefined && userID !== false) {
                hasUserID = "/" + userID + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/taxonomy/course_category" + hasUserID + "";

            return createURL;
        },
        createEBookAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/ebook/create";

            return createURL;
        },
        eBookListAPI: function (ebookID, userID, view, category, limit, offset) {

            const createURL = this.pickHost() + "/wp-json/yuno/v1/ebook/" + ebookID + "/" + userID + "/" + view + "/" + category + "/" + limit + "/" + offset + "";

            return createURL;
        },
        deleteResourceAttachmentAPI: function (resourceType, resourceID, extension) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/" + resourceType + "/" + resourceID + "/attachment/delete/" + extension + "";

            return createURL;
        },
        resourceEmailAPI: function (resourceType) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/" + resourceType + "/send/email";

            return createURL;
        },
        createDocAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/document/create";

            return createURL;
        },
        docListAPI: function (docID, userID, view, category, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/document/" + docID + "/" + userID + "/" + view + "/" + category + "/" + limit + "/" + offset + "";

            return createURL;
        },
        videoListAPI: function (videoID, query) {
            let hasVideoID = "",
                hasQuery = "";

            if (videoID !== undefined) {
                hasVideoID = videoID;
            }

            if (hasQuery !== undefined || hasQuery !== false) {
                hasQuery = query;
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videos/" + hasVideoID + "";

            return createURL;
        },
        videoSearchAPI: function (query) {
            let hasQuery = "";

            if (hasQuery !== undefined || hasQuery !== false) {
                hasQuery = query;
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videos/" + hasQuery + "";

            return createURL;
        },
        videoListByViewAPI: function (view, videoID, category, limit, offset) {
            let params = "";

            if (videoID !== false) {
                params = videoID;
            } else {
                let hasCategory = "";

                if (category !== undefined) {
                    hasCategory = category;
                }

                params = "" + view + "/" + hasCategory + "/" + limit + "/" + offset + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/video/get/" + params + "";

            return createURL;
        },
        createVideoAPI: function (isUpdate) {
            let params = "";

            if (isUpdate) {
                params = "update";
            } else {
                params = "create";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/video/" + params + "";

            return createURL;
        },
        userInfoAPI: function (userID, noCache) {
            let hasNoCache = "";

            if (noCache) {
                hasNoCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v3/user/info/" + userID + "" + hasNoCache + "";

            return createURL;
        },
        vcSettingsAPI: function (type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/admin/vc/settings/" + type + "";

            return createURL;
        },
        reviewAPI: function (type, userID, filter, limit, offset, sort, isCache) {
            let hasType = "",
                hasNoCache = "",
                hasFilter = "",
                hasLimit = "",
                hasOffset = "",
                hasSort = "";

            if (type !== undefined && type !== false) {
                hasType = "/" + type;
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/" + limit;
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/" + offset;
            }

            if (sort !== undefined && sort !== false) {
                hasSort = "/" + sort;
            }

            if (filter !== undefined && filter !== false) {
                hasFilter = "/" + filter;
            };

            if (isCache) {
                hasNoCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/review" + hasType + "/" + userID + "" + hasFilter + "" + hasLimit + "" + hasOffset + "" + hasSort + "" + hasNoCache + "";

            return createURL;
        },
        courseListAPI: function (type, courseID, userID, isCache) {
            let hasNoCache = "",
                hasCourseID = "",
                hasUserID = ""; // user id or course id

            if (isCache) {
                hasNoCache = "?ver=" + timestamp() + ""
            }

            if (courseID !== undefined && courseID !== false) {
                hasCourseID = "/" + courseID + ""
            }

            if (userID !== undefined && userID !== false) {
                hasUserID = "/" + userID + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/all/" + type + "/detail/list" + hasCourseID + ""+ hasUserID +"" + hasNoCache + "";

            return createURL;
        },
        countriesListAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/countries";

            return createURL;
        },
        stateListAPI: function (countryID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/state/country/" + countryID + "";

            return createURL;
        },
        cityListAPI: function (stateID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/city/state/" + stateID + "";

            return createURL;
        },
        languageListAPI: function (stateID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/languages";

            return createURL;
        },
        listOfMappedInstructorAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/course/batch";

            return createURL;
        },
        batchCreateUpdateAPI: function (type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/" + type + "/batch";

            return createURL;
        },
        batchDetailAPI: function (batchID, isCache) {
            let hasNoCache = "";

            if (isCache) {
                hasNoCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/batches/" + batchID + "" + hasNoCache + "";

            return createURL;
        },
        learnerListAPI: function (search, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "&ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/learner/list/" + search + "" + hasCache + "";

            return createURL;
        },
        instructorAvailabilityAPI: function (userID, clock, isCache) {
            let hasCache = "",
                hasClock = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            if (clock !== undefined && clock !== false) {
                hasClock = "/"+ clock +"";
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/" + userID + ""+ hasClock +"" + hasCache + "";

            return createURL;
        },
        createUpdateAvailabilityAPI: function (requestType, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/" + requestType + "" + hasCache + "";

            return createURL;
        },
        timeSlotsAPI: function (isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }


            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/slots" + hasCache + "";

            return createURL;
        },
        availabilityGridAPI: function (userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }


            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/days/" + userID + "" + hasCache + "";

            return createURL;
        },
        instructorsByCategoryAPI: function (categoryID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/category/" + categoryID + "" + hasCache + "";

            return createURL;
        },
        capabilitiesAPI: function (userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/forte/" + userID + "" + hasCache + "";

            return createURL;
        },
        paymentLinkUpdateAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/update/payment/link";

            return createURL;
        },
        getInviteURLAPI: function (userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/invitation/link/" + userID + "";

            return createURL;
        },
        invitedByUserAPI: function (userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/" + userID + "" + hasCache + "";

            return createURL;
        },
        signInURLWithState(data) {
            const scopes = [
                "email",
                "profile"
            ];

            const state = encodeURI(JSON.stringify(data));
            let loginURL = "";

            if (yunoCognitoLoginURL !== undefined) {
                const urlObj = new URL(yunoCognitoLoginURL);
                urlObj.searchParams.set("state", state); 
                const newUrl = urlObj.toString();

                loginURL = newUrl
            } else {
                loginURL = "https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri=" + gRU + "&client_id=" + gCID + "&state=" + state + "&scope=" + scopes.join("%20") + "&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";
            }

            let url = loginURL;

            return url;
        },
        appleSignInURLWithState(data) {
            const state = encodeURI(JSON.stringify(data));
            
            // Use dynamic Cognito configuration from header.php
            const cognito_domain = cognitoDomain;
            const client_id = cognitoClientId;
            const redirect_uri = cognitoRedirectURI;
            const identity_provider = 'SignInWithApple';
            const scope = 'openid email profile';
            const response_type = 'code';
            
            // Construct the OAuth2 authorization URL for initiating login
            const auth_url = cognito_domain + '/oauth2/authorize?' + new URLSearchParams({
                'response_type': response_type,
                'client_id': client_id,
                'redirect_uri': redirect_uri,
                'identity_provider': identity_provider,
                'scope': scope,
                'state': state
            }).toString();
            
            return auth_url;
        },
        updateUserCategoryAPI: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/insert/category";

            return createURL;
        },
        learnerHistoryAPI: function(date, userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }
            
            const createURL = this.pickHost() + "/wp-json/yuno/v1/event/history/"+ date +"/"+ userID +""+ hasCache +"";

            return createURL;
        },
        eventDetailAPI: function(eventType, learnerID, classID, uuid, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "&ver="+ timestamp() +""
            }
            
            const createURL = this.pickHost() + "/wp-json/yuno/v1/event/history/detail/"+ eventType +"/"+ learnerID +"/"+ classID +"?uuid="+ uuid +""+ hasCache +"";

            return createURL;
        },
        profileDetailAPI: function(role, userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }
            
            const createURL = this.pickHost() + "/wp-json/yuno/v1/profile/"+ role +"/"+ userID +""+ hasCache +"";
            
            return createURL;
        },
        apiTokenExpiry: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/user/expire/time";

            return createURL;
        },
        apiTokenRefresh: function () {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/google/refresh/token";

            return createURL;
        },
        staticPageAPI: function (page) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/page/" + page + "";

            return createURL; 
        },
        resourcesListingAPI: function (category, type, userID, viewType, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/resources/" + category + "/" + type + "/" + userID + "/" + viewType + "/" + limit + "/" + offset + "";

            return createURL;
        },
        resourcesDetailAPI: function (category, type, resourceID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/resources/" + category + "/" + type + "/" + resourceID + "/" + userID + "";

            return createURL;
        },
        videoTestimonialAPI: function(search, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "&ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videotestimonial/"+ search +""+ hasCache +"";

            return createURL;
        },
        createExamResultAPI: function(method, limit, offset, version) {
            let hasLimit = "",
                hasOffset = "";

            if (version === undefined && version === false ) {
                version = "v1"
            } else {
                version = version
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/examresult/"+ method +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        deleteExamResultAPI: function(id) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/examresult/delete/"+ id +"";
            
            return createURL;
        },
        manageVideotestimonialAPI: function(method, limit, offset) {
            let hasLimit = "", 
                hasOffset = "";

            if (limit !== undefined) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videotestimonial/"+ method +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        videotestimonialListAPI: function(criteria, view, limit, offset) {
            let hasLimit = "",
                hasOffset = "";

            if (limit !== undefined) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/videotestimonial/"+ criteria +"/"+ view +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        deleteVideotestimonialAPI: function(id) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/videotestimonial/delete/"+ id +"";
            
            return createURL;
        },
        manageArticleAPI: function(method, resourceID, userID, view, category, limit, offset) {
            let hasLimit = "",
                hasOffset = "",
                hasMethod = "",
                hasUserID = "",
                hasView = "",
                hasCategory = "",
                hasResourceID = "";

            if (method !== undefined && method !== false) {
                hasMethod = "/"+ method +"";
            }

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }

            if (userID !== undefined && userID !== false) { // user-id or resource id
                hasUserID = "/"+ userID +"";
            }

            if (view !== undefined && view !== false) {
                hasView = "/"+ view +"";
            }

            if (category !== undefined && category !== false) {
                hasCategory = "/"+ category +"";
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/article"+ hasMethod +""+ hasResourceID +""+ hasUserID +""+ hasView +""+ hasCategory +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        webinarSingleAPI: function(classID, userID, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/webinar/"+ classID +"/"+ userID +""+ hasCache +"";

            return createURL;
        },
        webinarListingAPI: function(view, userID, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/webinar/"+ view +"/"+ userID +"/"+ limit +"/"+ offset +""+ hasCache +"";

            return createURL;
        },
        deleteWebinarAPI: function(id, instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/delete/class/"+ id +"/"+ instructorID +"";
            
            return createURL;
        },
        webinarEnrollmentAPI: function(id, instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/webinar/enrollment";
            
            return createURL;
        },
        webinarInsightsAPI: function(view, adminID, instructorID, categoryID, state, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/webinar/"+ view +"/"+ adminID +"/"+ instructorID +"/"+ categoryID +"/"+ state +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        notificationListAPI: function(userID, channel) {
            if (channel !== undefined) {
                channel = channel;
            } else {
                channel = "channel";
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/notification/"+ channel +"/"+ userID +"";
            
            return createURL;
        },
        notificationUpdateAPI: function(channel) {
            if (channel !== undefined) {
                channel = channel;
            } else {
                channel = "channel";
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/notification/"+ channel +"/update";
            
            return createURL;
        },
        manageNotificationAPI: function(type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/notification/"+ type +"";
            
            return createURL;
        },
        searchResourceAPI: function(userID, categoryID, hasWebiner, search) {
            // const createURL = this.pickHost() + "/wp-json/yuno/v1/learning-content/"+ userID  +"/"+ categoryID +"/"+ hasWebiner +"?search="+ search  +"";
            const createURL = this.pickHost() + "/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+ userID +"?search="+ search  +"";
            
            return createURL;
        },
        managelearningContentAPI: function(type, resourceID) {
            let hasResourceID = "";

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/learning-content/"+ type +""+ hasResourceID +"";
            
            return createURL;
        },
        learningContentAPI: function(userID, resourceID, category, limit, offset) {
            let hasResourceID = "", // Resource ID or view
                hasCategory = "", // Category or update/landing
                hasLimit = "",
                hasOffset = ""; 

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }
            
            if (category !== undefined && category !== false) {
                hasCategory = "/"+ category +"";
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/learning-content/"+ userID +""+ hasResourceID +""+ hasCategory +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        learnerInsightsAPI: function(role, userID, viewType, instructorID, days, learnerID, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/learner/"+ role +"/"+ userID +"/"+ viewType +"/"+ instructorID +"/"+ days +"/"+ learnerID +"/"+ limit +"/"+ offset +""+ hasCache +"";
            
            return createURL;
        },
        learnerInsightsClassAPI: function(classID, learnerID, isV2, isCache) {
            let hasCache = "",
                hasVersion = "";

            if (isV2 !== undefined && isV2 !== false) {
                hasVersion = "v2";
            } else {
                hasVersion = "v1";
            };

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/"+ hasVersion +"/learner/class/"+ classID +"/"+ learnerID +""+ hasCache +"";
            
            return createURL;
        },
        signupFormAPI: function(type, filters, isCache) {
            let hasCache = "",
                hasFilters = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            };

            if (filters !== undefined && filters !== false) {
                hasFilters = "/?state="+ filters +"";
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v1/signup/form/"+ type +""+ hasFilters +""+ hasCache +"";

            return createURL;
        },
        resourceTitleAPI: function(search, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/event/resources/?search="+ search  +"&item="+ type +"";
            
            return createURL;
        },
        resourceDraftsAPI: function(userID, view, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/drafts/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +""+ hasCache +"";

            return createURL;
        },
        resourceDraftsDeleteAPI: function(resourceID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/drafts/delete/"+ resourceID +"";

            return createURL;
        },
        demoRequestAPI: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/demo/class/enroll/request";
            
            return createURL;
        },
        instructorProfileAPI: function(userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/users/"+ userID +"/instructor/profile";
            
            return createURL;
        },
        subjectsListAPI: function(userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/all/subjects/list";
            
            return createURL;
        },
        campaignAudienceAPI: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/campaign/audience";
            
            return createURL;
        },
        createCampaignAPI: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/create/campaign";
            
            return createURL;
        },
        coursesFiltersAPI: function(categoryID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/category/"+ categoryID +"/"+ type +"";
            
            return createURL;
        },
        coursesResultsAPI: function(categorySlug, limit, offset, filters) {
            let hasFilter = "";

            if (filters !== false) {
                hasFilter = "?filters=" + encodeURI(JSON.stringify(filters)) + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v2/courses/web/"+ categorySlug +"/detail/"+ limit +"/"+ offset +"/"+ hasFilter +"";
            
            return createURL;
        },
        resourcesResultsAPI: function(categorySlug, userID, view, limit, offset, filters, deviceType) {
            let hasFilter = "";
            if (filters !== false) {
                hasFilter = "?filters=" + encodeURI(JSON.stringify(filters)) + ""
            }

            if (deviceType === undefined || deviceType === false) {
                deviceType = "web";
            } else {
                deviceType = deviceType;
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v2/resources/"+ deviceType +"/"+ categorySlug +"/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +"/"+ hasFilter +"";
            
            return createURL;
        },
        instructorStatsAPI: function(instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/getinstructorstats/"+ instructorID +"/attendance";
            
            return createURL;
        },
        instructorCoursesV2API: function (instructorID, userID, isCache) {

            let hasCache = "";

            if (isCache) {
                hasCache = "?ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v2/instructor/courses/category/"+ instructorID +"/"+ userID +""+ hasCache +"";

            return createURL;
        },
        instructorInsightsAPI: function(userID, type, days, status, view, limit, offset, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "?ver="+ timestamp() +""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/"+ userID +"/"+ type +"/"+ days +"/"+ status +"/"+ view +"/"+ limit +"/"+ offset +""+ hasCache +"";
            
            return createURL;
        },
        enableDisableInstructorAPI: function(userID, status) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/"+ userID +"/"+ status +"";
            
            return createURL;
        },
        vcPermissionAPI: function(type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/vc/settings/"+ type +"";
            
            return createURL;
        },
        instructorProfileInsightsAPI: function(version, instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/users/"+ instructorID +"/profile";
            
            return createURL;
        },
        piiAPI: function(version, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/users/pii/"+ type +"";
            
            return createURL;
        },
        mappedCoursesAPI: function(instructorID, groupType) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/mapped/course/"+ instructorID +"/"+ groupType +"";

            return createURL;
        },
        makeFeaturedAPI: function(instructorID, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/featured/"+ instructorID +"/"+ type +"";
            
            return createURL;
        },
        dashboardLearnersAPI: function(search) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/user/enrollment?search="+ search +"";
            
            return createURL;
        },
        manageDashboardAPI: function(type, recordID) {
            let hasRecordID = "";

            if (recordID !== undefined && recordID !== false) {
                hasRecordID = "/"+ recordID +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/"+ type +"/report"+ hasRecordID +"";
            
            return createURL;
        },
        dashboardListAPI: function(record, view, role, limit, offset) {
            let hasLimit = "",
                hasOffset = "",
                hasView = "",
                hasRole = "";

            if (role !== undefined && role !== false) {
                hasRole = "/"+ role +"";
            }

            if (view !== undefined && view !== false) {
                hasView = "/"+ view +"";
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) {
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/report/"+ record +""+ hasView +""+ hasRole +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        enrollmentDashboardAPI: function(reportID, view, role, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/enrollment/report/"+ reportID +"/"+ view +"/"+ role +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        usersListAPI: function(role) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/users/list/"+ role +"";
            
            return createURL;
        },
        enrollmentClassDetailAPI: function(learnerID, batchID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+ learnerID +"/"+ batchID +"";
            
            return createURL;
        },
        vimeoVideoAPI: function(videoID) {
            const createURL = "https://api.vimeo.com/videos/"+ videoID +"";
            
            return createURL;
        },
        batchLearnersAPI: function(batchID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/batch/"+ batchID +"/learners";
            
            return createURL;
        },
        courseBatchLearners: function(batchID, courseID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/batch/"+ batchID +"/"+ courseID +"/learners";
            
            return createURL;
        },
        blogCategoriesAPI: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/taxonomy/blog_category";

            return createURL;
        },
        manageBlogAPI: function(type, resourceID) {
            let hasResourceID = "";

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/blog/"+ type +""+ hasResourceID +"";

            return createURL;
        },
        publishedBlogsAPI: function(resourceID, userID, view, category, limit, offset) {
            let hasLimit = "",
                hasOffset = "",
                hasMethod = "",
                hasUserID = "",
                hasView = "",
                hasCategory = "",
                hasResourceID = "";

            if (resourceID !== undefined && resourceID !== false) {
                hasResourceID = "/"+ resourceID +"";
            }

            if (userID !== undefined && userID !== false) { // user-id or resource id
                hasUserID = "/"+ userID +"";
            }

            if (view !== undefined && view !== false) {
                hasView = "/"+ view +"";
            }

            if (category !== undefined && category !== false) {
                hasCategory = "/"+ category +"";
            }

            if (limit !== undefined && limit !== false) {
                hasLimit = "/"+ limit +"";
            }

            if (offset !== undefined && offset !== false) { 
                hasOffset = "/"+ offset +"";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/blog"+ hasResourceID +""+ hasUserID +""+ hasView +""+ hasCategory +""+ hasLimit +""+ hasOffset +"";
            
            return createURL;
        },
        categoriesListAPI: function(criteria) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/category/"+ criteria +"";
            
            return createURL;
        },
        vimeoUploadVideoAPI: function() {
            const createURL = "https://api.vimeo.com/me/videos";
            
            return createURL;
        },
        vimeoVideoPrivacyAPI: function(videoID, domain) {
            const createURL = "https://api.vimeo.com/videos/"+ videoID +"/privacy/domains/"+ domain +"";
            
            return createURL;
        },
        manageVideoClippingAPI: function(criteria, instructorID,  view, limit, offset, isList) {
            if (isList !== undefined && isList !== false) {
                isList = "clippings"
            } else {
                isList = "clipping"
            }

            if (instructorID !== undefined && instructorID !== false) {
                instructorID = "/" + instructorID;
            } else {
                instructorID = "";
            }

            // @view: We can also other value

            if (view !== undefined && view !== false) {
                view = "/" + view;
            } else {
                view = "";
            }

            if (limit !== undefined && limit !== false) {
                limit = "/" + limit;
            } else {
                limit = "";
            }

            if (offset !== undefined && offset !== false) {
                offset = "/" + offset;
            } else {
                offset = "";
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/"+ isList +"/"+ criteria +""+ instructorID +""+ view +""+ limit +""+ offset +"";
            
            return createURL;
        },
        instructorMyCourses: function(instructorID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/mycourses/instructor/"+ instructorID +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        instructorCourseBatches: function(courseID, role, userID, criteria, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/mycourses/"+ courseID +"/"+ role +"/"+ userID  +"/batches/"+ criteria +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        manageBookmarkAPI: function(type, version, userID, limit, offset, filters) {
            if (version !== undefined && version !== false) {
                version = version
            } else {
                version = "v1"
            }

            if (userID !== undefined && userID !== false) {
                userID = "/" + userID
            } else {
                userID = ""
            };

            if (limit !== undefined && limit !== false) {
                limit = "/" + limit
            } else {
                limit = ""
            };

            if (offset !== undefined && offset !== false) {
                offset = "/" + offset
            } else {
                offset = ""
            };

            if (filters !== undefined && filters !== false) {
                filters = "?filters=" + encodeURI(JSON.stringify(filters)) + ""
            } else {
                filters = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/bookmark/"+ type +""+ userID +""+ limit +""+ offset +""+ filters +"";
            
            return createURL;
        },
        availableCourses: function(personalization, limit, offset, filters) {
            if (limit !== undefined && limit !== false) {
                limit = "/" + limit
            } else {
                limit = ""
            };

            if (offset !== undefined && offset !== false) {
                offset = "/" + offset
            } else {
                offset = ""
            };

            if (filters !== undefined && filters !== false) {
                filters = "?params=" + encodeURI(JSON.stringify(filters)) + ""
            } else {
                filters = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v2/available-courses/"+ personalization +""+ limit +""+ offset +""+ filters +"";
            
            return createURL;
        },
        availableBatches: function(courseID, limit, offset, filters) {
            if (courseID !== undefined && courseID !== false) {
                courseID = "/" + courseID
            } else {
                courseID = ""
            };

            if (limit !== undefined && limit !== false) {
                limit = "/" + limit
            } else {
                limit = ""
            };

            if (offset !== undefined && offset !== false) {
                offset = "/" + offset
            } else {
                offset = ""
            };

            if (filters !== undefined && filters !== false) {
                filters = "?params=" + encodeURI(JSON.stringify(filters)) + ""
            } else {
                filters = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v2/batches/upcomingOngoing"+ courseID  +""+ limit +""+ offset +"";
            
            return createURL;
        },
        courseEnrollmentStatus: function(courseID, userID) {
            if (courseID !== undefined && courseID !== false) {
                courseID = "/" + courseID
            } else {
                courseID = ""
            };

            if (userID !== undefined && userID !== false) {
                userID = "/" + userID
            } else {
                userID = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v2/enrollment-status"+ courseID +""+ userID +"";
            
            return createURL;
        },
        courseOneToOne: function(courseID, personalization, day, time, duration, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructor/availability/"+ courseID +"/"+ personalization +"/"+ day +"/"+ time +"/"+ duration +"/"+ limit +"/"+ offset +"";
            
            return createURL; 
        },
        cloudinaryImageUpload: function(unsigned) {
            if (unsigned) {
                return {
                    upload_URL: "https://api.cloudinary.com/v1_1/harman-singh/upload",
                    upload_preset: "jg32bezo"
                }
            } else {
                return false
            }
        },
        imageUpload: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/image/upload/";
            
            return createURL;
        },
        categorySearch: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/category/search";
            
            return createURL;
        },
        categoryResources: function(filters) {
            if (filters !== undefined && filters !== false) {
                filters = "?ids=" + encodeURI(JSON.stringify(filters)) + ""
            } else {
                filters = ""
            };

            const createURL = this.pickHost() + "/wp-json/yuno/v2/resources/"+ filters +"";
            
            return createURL;
        },
        coursesList: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/courses/all";
            
            return createURL;
        },
        upcomingOngoingBatchesList: function(courseID, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+ courseID +"/"+ view +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        pastBatchesList: function(courseID, personalisation, userID, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/batches/past/"+ courseID +"/"+ personalisation +"/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        checkout: function(courseID, batchID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/checkout/"+ courseID + "/"+ batchID +"";
            
            return createURL;
        },
        instructorInsights: function(type, userID, days, status, view, vcStatus, courseID, categoryID, featured, nativeLanguage, avgRating, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/instructor/"+ type +"/"+ userID +"/"+ days +"/"+ status +"/"+ view +"/"+ vcStatus +"/"+ courseID +"/"+ categoryID +"/"+ featured +"/"+ nativeLanguage +"/"+ avgRating +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        updateNativelanguage: function(userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/instructor/nativelanguage/"+ userID +"";
            
            return createURL;
        },
        endBatch: function(batchID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/endbatch/"+ batchID +"";
            
            return createURL;
        },
        collections: function(userID, view, categoryID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/learning-content/"+ userID +"/"+ view +"/"+ categoryID +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        instructorVideotestimonial: function(userID, view, categoryID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor";
            
            return createURL;
        },
        courses: function(userID, courseID, categoryID, format, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/courses/"+ userID +"/"+ courseID +"/"+ categoryID +"/"+ format +"/"+ view +"/"+ limit +"/"+ offset +"";
            
            return createURL;
        },
        activityList: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/activity";
            
            return createURL;
        },
        subCategoriyList: function(category) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/"+ category +"/subcategories";
            
            return createURL;
        },
        courseSchedule: function(type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/schedule/"+ type +"";
            
            return createURL;
        },
        courseScheduleForm: function(courseID, scheduleID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/schedule/"+ courseID +"/"+ scheduleID +"";
            
            return createURL;
        },
        createCSV: function() {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/export/csv";
            
            return createURL;
        },
        downloadCSV: function(userID, role) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/csv/"+ userID +"/"+ role +"";
            
            return createURL;
        },
        courseDetail: function(courseID, userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/detail/"+ courseID +"/"+ userID +"";
            
            return createURL;
        },
        reviewIssues: function(id, role, taxonomyID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/items/"+ id +"/issue/"+ role +"/"+ taxonomyID +"";
            
            return createURL;
        },
        reviewPost: function(id) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/post";

            return createURL;
        },
        formReview: function(userID, classID, feedbackTypeID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/get/"+ userID +"/"+ classID +"/"+ feedbackTypeID +"";

            return createURL;
        },
        classReviews: function(type, classID, feedbackTypeID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/average/"+ type +"/"+ classID +"/"+ feedbackTypeID +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        classReviewsByInstructor: function(type, instructorID, feedbackTypeID, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/review/classreviews/instructor/"+ type +"/"+ instructorID +"/"+ feedbackTypeID +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        listOfUser: function (search, type, isCache) {
            let hasCache = "";

            if (isCache) {
                hasCache = "&ver=" + timestamp() + ""
            }

            const createURL = this.pickHost() + "/wp-json/yuno/v1/"+ type +"/list/" + search + "" + hasCache + "";

            return createURL;
        },
        learnerActivity: function(userID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v2/get-feedback/"+ userID +"";

            return createURL;
        },
        recentLearnerClass: function(userID, itemID) {

            const createURL = this.pickHost() + "/wp-json/yuno/v2/get-feedback-information/"+ userID +"/"+ itemID +"";

            return createURL;
        },
        enrollmentListByType: function(type, userID, view, orgAdminID, referrerID, enrollmentStarts, enrollmentEnds, instructorID, attendance, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/"+ type +"/enrollments/"+ userID +"/"+ view +"/"+ orgAdminID +"/"+ referrerID +"/"+ enrollmentStarts +"/"+ enrollmentEnds +"/"+ instructorID +"/"+ attendance +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        courseEconomics: function(type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/economics/"+ type +"";

            return createURL;
        },
        courseEconomicsForm: function(courseID, formID, personalisation) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/economics/"+ courseID +"/"+ formID +"/"+ personalisation +"";
            return createURL;
        },
        courseEconomicsSummary: function(courseID, formID, batchSize, personalisation) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/course/economics/"+ courseID +"/"+ formID +"/"+ batchSize +"/"+ personalisation +"";

            return createURL;
        },
        csvList: function(userID, role, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/csv/"+ userID +"/"+ role +"/"+ view +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        orgList: function (search, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/org/" + search + "";

            return createURL;
        },
        referrerDetails: function (search, type) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/details";

            return createURL;
        },
        referrerID: function (userID, buyerID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/"+ userID +"/"+ buyerID +"";

            return createURL;
        },
        mappedInstructors: function (instructorID) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/course/"+ instructorID +"";

            return createURL;
        },
        generateRefferralURL: function (params) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/get-referral-url?params="+ encodeURI(JSON.stringify(params)) +"";

            return createURL;
        },
        generateRefferralCode: function (params) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/code/generate";

            return createURL;
        },
        referrerURL: function (params) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/url";

            return createURL;
        },
        referralDetail: function (batchID, courseID, userID, version) {
            if (version === undefined) {
                version = "v1"
            }

            const createURL = this.pickHost() + "/wp-json/yuno/"+ version +"/referrer/user/"+ batchID +"/"+ courseID +"/"+ userID +"";

            return createURL;
        },
        referralReports: function (userID, view, limit, offset) {
            const createURL = this.pickHost() + "/wp-json/yuno/v1/referrer/report/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        orgToken: function (userID, type) {
            let createURL = "";

            if (type === "POST") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/create/token";
            } else if (type === "PUT") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/update/token";
            } else {
                createURL = this.pickHost() + "/wp-json/yuno/v1/token/"+ userID +"";
            }

            return createURL;
        },
        webhooks: function (type, webHookID, orgID, view, limit, offset) {
            let createURL = "";

            if (type === "grid") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/org/webhook/"+ webHookID +"/"+ orgID +"/"+ view +"/"+ limit +"/"+ offset +"";
            } else if (type === "create") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/org/webhook/create";
            } else if (type === "events") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/org/webhook/events";
            } else if (type === "update") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/org/webhook/update";
            }

            return createURL;
        },
        seo: function (type, pageID, searchCriteria, searchQuery) {
            let createURL = "";

            if (type === "status") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/seo/status/"+ pageID +"";
            } else if (type === "markNoIndex") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/seo/mark-no-index";
            } else if (type === "pageSearch") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/seo/search-get/"+ searchCriteria +"/?search="+ searchQuery +"";
            };

            return createURL;
        },
        quiz: function (type, quizID, userID, query) {
            let createURL = "";

            if (query!== undefined && query !== false) {
                query = query
            } else {
                query = ""
            }

            if (type === "create") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz/";
            } else if (type === "update") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz";
            } else if (type === "edit") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz/"+ quizID +"/"+ query +"";
            } else if (type === "quizgrid") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quizzes";
            } else if (type === "quizgridV2") {
                createURL = this.pickHost() + "/wp-json/yuno/v3/category/practice";
            } else if (type === "attempt") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/attempt/";
            } else if (type === "review") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/attempt/answers/"+ quizID +"/"+ userID +"";
            } else if (type === "delete") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz/"+ quizID +"";
            } else if (type === "quizReorder") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/quiz/reorder";
            };

            return createURL;
        },
        question: function (type, category, questionType, questionID, questionSetID) {
            let createURL = "";

            if (type === "questions") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionbank/"+ category +"/"+ questionType +"";
            } else if (type === "create") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/question";
            } else if (type === "single") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/question/"+ questionID +"";
            } else if (type === "delete") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/question/"+ questionID +"";
            } else if (type === "deleteQuestionSet") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset/"+ questionSetID +"";
            } else if (type === "attempt") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/question-attempt/"+ questionID +"";
            } else if (type === "attemptQuestionSet") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-attempt/"+ questionSetID +"";
            } else if (type === "questionset") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset";
            } else if (type === "questionsetGET") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset/"+ questionSetID +"";
            } else if (type === "questionsetQuestions") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-questions";
            } else if (type === "questionsetQuestionsGET") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-questions/"+ questionSetID +"";
            } else if (type === "questionsetQuestionsList") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-questions/reorder/"+ questionSetID +"";
            } else if (type === "questionsetQuestionsReorder") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/questionset-questions/reorder";
            };

            return createURL;
        },
        enrollments: function (type, batchID, courseID, query) {
            let createURL = "";

            if (type === "active") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/batch/"+ batchID +"/"+ courseID +"/learners";
            } else if (type === "extendDate") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/enrollment/update";
            }

            return createURL; 
        },
        blog: function (type, categoryID, limit, offset) {
            let createURL = "";

            if (type === "recentSingle") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+ categoryID +"";
            } else if (type === "recentList") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/blog/recent/"+ limit +"/"+ offset +"?is_list=true&category_id="+ categoryID +"";
            } else if (type === "categoriesList") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/blog/categories";
            } else if (type === "detail") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/blog/"+ categoryID +"";
            };
            
            return createURL;
        },
        writingTask: function (type, examType, taskType, dataID, userID, view, limit, offset) {
            let createURL = "";

            if (type === "type") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/writingtask/type/"+ examType +"/"+ taskType +"";
            } else if (type === "create") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/writingtask/create";
            } else if (type === "update") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/writingtask/update";
            } else if (type === "singleRecord") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/writingtask/"+ dataID +"/"+ userID +"/"+ view +"/"+ limit +"/"+ offset +"";
            } else if (type === "payload") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/writingtask/"+ dataID +"";
            } else if (type === "delete") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/writingtask/delete/"+ dataID +"";
            };

            

            return createURL;
        },
        categoryLandingPage: function (type, categoryID) {
            let createURL = "";

            if (type === "category") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/category/"+ categoryID +"";
            } else if (type === "practiceTests") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/category/practice/";
            };

            return createURL;
        },
        examResults: function (category, view, limit, offset) {
            let createURL = this.pickHost() + "/wp-json/yuno/v2/examresult/"+ category +"/"+ view +"/"+ limit +"/"+ offset +"";

            return createURL;
        },
        mainNav: function (userID, role) {
            let createURL = this.pickHost() + "/wp-json/yuno/v3/menu/"+ userID +"/"+ role +"";

            return createURL;
        },
        org: function (type, id, userID, courseEconomicsType, courseID, courseEconomicsID, personalization, courseScheduleType, createBatchType, categoryID, instructorFormat, view, limit, offset) {
            const baseHost = this.pickHost();
            const paths = {
                info: `/wp-json/yuno/v2/org/${id}`,
                singleLearner: `/wp-json/yuno/v1/org/user/${id}/${userID}`,
                industries: "/wp-json/yuno/v1/org/industries/details",
                detailsUpdate: "/wp-json/yuno/v1/org/update",
                create: "/wp-json/yuno/v1/org/create",
                settings: `/wp-json/yuno/v2/org/settings/${id}`,
                createCourseEconomics: `/wp-json/yuno/v3/org/course/economics/${courseEconomicsType}`,
                courseEconomicsPersonalization: `/wp-json/yuno/v3/org/course/economics/${courseID}/${courseEconomicsID}/${personalization}`,
                courseDetailForm: `/wp-json/yuno/v3/org/course/${courseID}`,
                courseDetailUpdate: `/wp-json/yuno/v3/org/course`,
                orgAcademies: `/wp-json/yuno/v3/org/academies/${id}`,
                courseSchedule: `/wp-json/yuno/v3/org/course/schedule/${courseScheduleType}`,
                batchesUpcomingOngoing: `/wp-json/yuno/v3/org/batches/upcomingOngoing`,
                createBatch: `/wp-json/yuno/v3/org/${createBatchType}/batch`,
                courses: `/wp-json/yuno/v3/org/course/${userID}/${id}/${courseEconomicsType}/${courseID}/${categoryID}/${instructorFormat}/${view}/${limit}/${offset}`,
                batchesPast: `/wp-json/yuno/v3/org/batches/past/${id}/${userID}/${courseEconomicsType}/${courseID}/${view}/${limit}/${offset}`,
                enrollments: `/wp-json/yuno/v3/org/academy/enrollments`,
                academyDetails: `/wp-json/yuno/v1/academy/${id}`,
                academyInstructors: `/wp-json/yuno/v1/org/user/instructors/${id}`,
                createUpdateAcademy: `/wp-json/yuno/v1/academy`,
                getAcademy: `/wp-json/yuno/v1/academy/${id}`,
            };

            return baseHost + (paths[type] || "");
        },
        leadForm: function (type, userID, categorySlug) {
            let createURL = "";

            if (type === "steps") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/signup/form/"+ userID +"/"+ categorySlug +"";
            } else if (type === "postStep") {
                createURL = this.pickHost() + "/wp-json/yuno/v2/signup/form/update/"+ userID +"/"+ categorySlug +"";
            } else if (type === "updateMobile") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/admin/user/phone/update ";
            };

            return createURL;
        },
        availableCoursesV2: function (type) {
            let createURL = "";

            if (type === "listing") {
                createURL = this.pickHost() + "/wp-json/yuno/v3/available-courses/"; // this is url
            }

            return createURL;
        },
        activeCategory: function (type) {
            let createURL = "";

            if (type === "set") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/user/add/category/";
            }

            return createURL;
        },
        learners: function (type, role, view, limit, offset, learnerID) {
            let endpoint = "";
        
            switch (type) {
                case "insights":
                    endpoint = `/wp-json/yuno/v2/users/${role}/learner/${view}/${limit}/${offset}`;
                    break;
                case "demoRequests":
                    endpoint = `/wp-json/yuno/v1/demo-requests/${role}/list/${limit}/${offset}`;
                    break;
                case "demoRequestsOrg":
                    endpoint = `/wp-json/yuno/v1/demo-requests/org-admin/${role}/${view}/${limit}/${offset}`;
                    break;
                case "learnerDetailOrg":
                    endpoint = `/wp-json/yuno/v1/demo-requests/${role}/${learnerID}`;
                    break;
                case "learnerDetail":
                    endpoint = `/wp-json/yuno/v1/demo-requests/${learnerID}`;
                    break;
                case "instructorLearnerDetail":
                    endpoint = `/wp-json/yuno/v2/instructor/mylearner/${learnerID}`;
                    break;
                case "orgAdminLearners":
                    endpoint = `/wp-json/yuno/v2/orgadmin/learner/${view}/${limit}/${offset}`;
                    break;
            }
        
            return this.pickHost() + endpoint;
        },
        deleteUser: function (type) {
            let createURL = "";

            if (type === "requested") {
                createURL = this.pickHost() + "/wp-json/yuno/v1/user/add/delete/requests";
            }
            
            return createURL;
        },
        generic: function (type, params, role, orgID, academyID) {
            let createURL = "";

            switch (type) {
                case "googleFonts":
                    createURL = `https://www.googleapis.com/webfonts/v1/webfonts/?${params}`;
                    break;

                case "courseSuggestions":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${params}`;
                    break;

                case "contentSearch":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${params}`;
                    break;
                case "userSearch":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${params}/${role}/${orgID}`;
                    break;

                case "orgBatches":
                    createURL = `${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${academyID}`;
                    break;

                case "org":
                    createURL = `${this.pickHost()}/wp-json/yuno/v2/org/${orgID}`;
                    break;

                case "categories":
                    createURL = `${this.pickHost()}/wp-json/yuno/v3/all/category/signup`;
                    break;

                default:
                    break;
            }

            return createURL;
        },
        course: function (type, courseID, userID, view, limit, offset) {
            let createURL = "";

            switch (type) {
                case "payload":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/${courseID}`;
                    break;

                case "updateCourse":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course`;
                    break;

                case "mapInstructor":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;
                    break;

                case "invitedInstructors":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${courseID}/${userID}/${view}/${limit}/${offset}`;
                    break;

                case "createBatchOrg":
                    createURL = `${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;
                    break;

                case "updateBatchOrg":
                    createURL = `${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;
                    break;

                case "mapCourses":
                    createURL = `${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`;
                    break;
                    
                default:
                    break;
            }

            return createURL;
        },
        learner: function (type, learnerID, courseType , role , prop) {
            let endpoint = "";
            switch (type) {
                case "enrolledCourses":
                    endpoint = `/wp-json/yuno/v3/learner/${learnerID}/enrollments/${courseType}`;
                    break;
                case "classes":
                    endpoint = `/wp-json/yuno/v4/classes/${courseType}/${role}/${learnerID}?limit=${prop.limit}&offset=${prop.offset}&course=${prop.course}&batch=${prop.batch}&academy=${prop.academy}`;
                    break;
                case "filters":
                    endpoint = `/wp-json/yuno/v4/classes/filter/${role}/${learnerID}`;
                    break;
                case "getClassDetail":
                    endpoint = `/wp-json/yuno/v4/classes/${prop.classID}`;
                    break;
                default:
                    break;
            }
        
            return this.pickHost() + endpoint;
        },
        classes: function (type, view, currentLoggedInUserID, instructorID, demoOrPrivate, courseID, attendancePercentage, classType, limit, offset, grandTotal) {
            let endpoint = "";
        
            switch (type) {
                case "allClasses":
                    endpoint = `/wp-json/yuno/v3/classes/${view}/${currentLoggedInUserID}/${instructorID}/${demoOrPrivate}/${courseID}/${attendancePercentage}/${classType}/${limit}/${offset}/${grandTotal}`;
                    break;
                default:
                    break;
            }
        
            return this.pickHost() + endpoint;
        },
        instructor: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                learners: `/wp-json/yuno/v1/instructor/mylearners/${props.instructorID}/${props.limit}/${props.offset}`,
                learnerDetail: `/wp-json/yuno/v2/instructor/mylearner/${props.learnerID}`,
                fetchMyLearners: `/wp-json/yuno/v2/${props.role}/mylearners/${props.instructorID}/${props.view}/${props.limit}/${props.offset}`,
                schedulePrivateClass: `/wp-json/yuno/v4/classes/private`,
                updatePrivateClass: `/wp-json/yuno/v4/classes/private/${props.classID}`,
                getClassDetail: `/wp-json/yuno/v4/classes/${props.classID}`,
                createAvailabilityV2: `/wp-json/yuno/v2/instructor/${props.id}/workinghours/create`,
                updateAvailabilityV2: `/wp-json/yuno/v2/instructor/${props.id}/workinghours/update`,
                getInstructorAvailability: `/wp-json/yuno/v4/working_hours/instructor/${props.id}`,
                learnersV2: `/wp-json/yuno/v2/${props.role}/mylearners/${props.instructorID}/${props.view}/${props.limit}/${props.offset}`,
                schedulePrivate: `/wp-json/yuno/v4/class/schedule-private`,
                freebusy: `/wp-json/yuno/v2/instructor/freebusy/batch`,
                learnersV2: `/wp-json/yuno/v2/${props.role}/mylearners/${props.instructorID}/${props.view}/${props.limit}/${props.offset}`,
            };

            return baseHost + (paths[type] || "");
        },
        attendance: function (type, role, view, limit, offset,) {
            const baseHost = this.pickHost();
            const paths = {
                learners: `/wp-json/yuno/v1/attendance/${role}/${view}/${limit}/${offset}`
            };

            return baseHost + (paths[type] || "");
        },
        user: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                region: `/wp-json/yuno/v3/user/region/${props.loggedinUserID}`,
                languages: `/wp-json/yuno/v3/user/languages`,
                countries: `/wp-json/yuno/v1/countries`,
                timezones: `/wp-json/yuno/v3/user/timezones`,
                currencies: `/wp-json/yuno/v3/user/currencies`,
                virtualClassRoom: `/wp-json/yuno/v3/virtual-classroom/${props.loggedinUserID}`,
                virtualClassRoomV4: `/wp-json/yuno/v4/settings/virtual-classrooms/${props.loggedinUserID}`,
                vcDisconnect: `/wp-json/yuno/v3/virtual-classroom`,
                classLaunchStatus: `/wp-json/yuno/v2/class/updateLaunchStatus/${props.classID}`,
                classSchedule: `/wp-json/yuno/v4/classes/demo`,
                slots: `/wp-json/yuno/v2/instructor/freebusy/slots`,
                slotsV4: `/wp-json/yuno/v4/availability/free_slots/${props.params}`,
            };

            return baseHost + (paths[type] || "");
        },
        classInsights: function (type, currentLoggedInUserID, view, limit, offset) {
            const baseHost = this.pickHost();
            const paths = {
                yunoAdminPast: `/wp-json/yuno/v3/classes/past/${currentLoggedInUserID}/${view}/${limit}/${offset}`,
                yunoAdminOngoingUpcoming: `/wp-json/yuno/v3/classes/ongoingUpcoming/${currentLoggedInUserID}/${view}/${limit}/${offset}`,
                yunoOrgPast: `/wp-json/yuno/v3/org/classes/past/${currentLoggedInUserID}/${view}/${limit}/${offset}`,
                yunoOrgOngoingUpcoming: `/wp-json/yuno/v3/org/classes/ongoingUpcoming/${currentLoggedInUserID}/${view}/${limit}/${offset}`,
            };

            return baseHost + (paths[type] || "");
        },
        resource: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                batches: `/wp-json/yuno/v2/batches/upcomingOngoing/${props.role}/${props.userID}/${props.limit}/${props.offset}`,
                batchLearners: `/wp-json/yuno/v1/batch/${props.batchID}/${props.courseID}/learners`,
                sendResource: `/wp-json/yuno/v1/resources/send/resource`
            };

            return baseHost + (paths[type] || "");
        },
        academy: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                academies: `/wp-json/yuno/v3/org/academies`,
                fetchAcademiesFilters: `/wp-json/yuno/v4/academies/filters${props.params}`,
                academiesV2: `/wp-json/yuno/v4/academies/${props.view_type}${props.params}`,
                activeOrg: `/wp-json/yuno/v3/user/state`,
                getOrgInstructors:`/wp-json/yuno/v3/org/instructor/completed/${props.id}/${props.org_id}/${props.academy_id}/${props.days}/${props.status}/${props.vc_status}/${props.course_id}/${props.category_id}/${props.is_featured}/${props.native_language}/${props.avg_rating}/${props.view_type}/${props.limit}/${props.offset}`,
                addDemoInstructors:`/wp-json/yuno/v3/academy/demo-instructors/add`,
                getDemoInstructors:`/wp-json/yuno/v3/academy/demo-instructors/${props.id}/${props.org_id}/${props.academy_id}`,
                updateDemoInstructors:`/wp-json/yuno/v3/academy/demo-instructors/edit`,
                fetchAcademyDetails: `/wp-json/yuno/v4/academies/${props.id}`,
                organizations: `/wp-json/yuno/v4/organizations/${props.org_id}`,
                fetchAllPlaces: `/wp-json/yuno/v4/places/${props.view_type}?org_id=${props.org_id}&limit=${props.limit}&offset=${props.offset}`,
                fetchPlace: `/wp-json/yuno/v4/places/${props.placeID}`,
                
            };
            return baseHost + (paths[type] || "");
        },
        googleMapLocation: function (type, props) {
            const baseHost = "https://maps.googleapis.com/maps/api";
            const paths = {
                geoLocation: `/geocode/json?latlng=${props.latitude},${props.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${props.key}`,
                detail: `/place/details/json?place_id=${props.placeID}&key=${props.key}`,

            };
            return baseHost + (paths[type] || "");
        },
        google: function (type, props) {
            const baseHost = "https://www.google.com";
            const paths = {
                openLocation: `/maps/search/?api=1&query=Google&query_place_id=${props.placeID}`,
            };
            return baseHost + (paths[type] || "");
        },
        createPlace: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                create: `/wp-json/yuno/v4/places`,
                createClassroom: `/wp-json/yuno/v4/classrooms`,
                fetchAllPlaces: `/wp-json/yuno/v4/places/${props.view_type}`,
                fetchPlace: `/wp-json/yuno/v4/places/${props.placeID}`,
            };
            return baseHost + (paths[type] || "");
        },
        createCourse: function (type, props) {
            const baseHost = this.laravelHost();
            const paths = {
                courseGPT: `/api/submit-chatgpt-request`,
                schedules: `/api/generate-course-schedules`,
                status: `/api/status/${props.loggedinUserID}/all/${props.jobID}`
            };
            return baseHost + (paths[type] || "");
        },
        enrollmentsV4: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                list: `/wp-json/yuno/v4/enrollments/${props.view}/${props.params}`,
                filters: `/wp-json/yuno/v4/enrollments/filters/${props.params}`,
                createLink: `/wp-json/yuno/v4/enrollments`,
                changeBatch: `/wp-json/yuno/v4/enrollments/${props.enrollmentID}`,
                enrollToggle: `/wp-json/yuno/v4/enrollments/${props.enrollmentID}/unenroll`
            };
            return baseHost + (paths[type] || "");
        },
        header: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                menu: `/wp-json/yuno/v4/menus/${props.userID}/${props.orgID}`,
            };
            return baseHost + (paths[type] || "");
        },
        payment: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                list: `/wp-json/yuno/v4/payments/${props.view}/${props.params}`,
                filters: `/wp-json/yuno/v4/payments/filters/${props.params}`
            };
            return baseHost + (paths[type] || "");
        },
        batch: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                learners: `/wp-json/yuno/v4/enrollments/active/batch/${props.batchID}`
            };
            return baseHost + (paths[type] || "");
        },
        courseV4: function (type, props) {
            const baseHost = this.pickHost();
            const paths = {
                list: `/wp-json/yuno/v4/courses/search`
            };
            return baseHost + (paths[type] || "");
        },        
    };

    const findObjectByKey = (array, key, value) => {
        return array.find(obj => obj[key] === value) || null;
    };

    const assignVValidationObj = function (customMsg) {
        const VeeValidate = window.VeeValidate,
            VeeValidateRules = window.VeeValidateRules,
            ValidationProvider = VeeValidate.ValidationProvider,
            ValidationObserver = VeeValidate.ValidationObserver;

        VeeValidate.extend('minLength', {
            validate(value, { length }) {
                return value.length >= length;
            },
            params: ['length'],
            message: 'At least {length} items must be selected'
        });

        VeeValidate.extend('maxLength', {
            validate(value, { length }) {
                return value.length <= length;
            },
            params: ['length'],
            message: 'No more than {length} items must be selected'
        });

        VeeValidate.extend('isSelected', {
            validate(value, { length }) {
                return length !== 0;
            },
            params: ['length'],
            message: 'Custom title is not allowed'
        });

        VeeValidate.extend('isSelectedFromList', {
            validate(value, { length }) {
                return length !== 0;
            },
            params: ['length'],
            message: 'Please select the user from list'
        });

        VeeValidate.extend('isBatchSelected', {
            validate(value, { length }) {
                return length !== 0;
            },
            params: ['length'],
            message: 'Please select the batch from list'
        });

        VeeValidate.extend('notAllowed', {
            validate(value, { number }) {
                const regExp = /^0[0-9].*$/;

                return regExp.test(value) === false;
            },
            params: ['number'],
            message: `Phone number can't start with {number}`
        });

        VeeValidate.extend('greaterThen', {
            validate(value, { number }) {
                if (value > number) {
                    return true;
                } else {
                    return false;
                }
            },
            params: ['number'],
            message: `Value should be greater then {number}`
        });

        VeeValidate.extend('isOverlapping', {
            validate(value) {
                if (value) {
                    return false;
                } else {
                    return true;
                }
            },
            
            message: `Time overlap with another set of time`
        });

        VeeValidate.extend('isEndTime', {
            validate(value) {
                if (value) {
                    return false;
                } else {
                    return true;
                }
            },
            
            message: `Choose an end time later than the start time.`
        });

        VeeValidate.extend('selectLearner', {
            validate(value, { number }) {
                return number !== 0;
            },
            params: ['number'],
            message: `Please add at least 1 learner from list`
        });

        VeeValidate.extend('isEmpty', {
            validate(value, { getValue }) {
                if (getValue === "") {
                    return false
                } else {
                    return true
                }
            },
            params: ['getValue'],
            message: "Field should not be blank"
        });

        VeeValidate.extend('isNotBlank', {
            validate(value, { getValue }) {
                if (getValue === null) {
                    return false
                } else {
                    return true
                }
            },
            params: ['getValue'],
            message: `Please select the learner from list`
        });

        VeeValidate.extend('url', {
            validate (value, {getValue}) {
                const regExp = /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;

                if (regExp.test(value)) {
                    return true
                } else {
                    return false
                }
            },
            params: ['getValue'],
            message: `Please enter valid URL`
        });

        VeeValidate.extend('httpsURL', {
            validate (value, {getValue}) {
                const regExp = /^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/;

                if (regExp.test(value)) {
                    return true
                } else {
                    return false
                }
            },
            params: ['getValue'],
            message: `Please make sure URL should start with "https" and should be valid`
        });

        VeeValidate.extend('email', {
            validate (value) {
            const regExp = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (regExp.test(value)) {
                return true;
            } else {
                return false;
            }
            },
            message: 'Please enter a valid email address'
        });

        VeeValidate.extend('hasCurlyBrackets', {
            validate(value) {
                const regExp = /\{.+?\}/; 
                return regExp.test(value);
            },
            message: 'String must have curly brackets with content inside'
        });

        for (let key in customMsg.messages) {
            VeeValidate.extend(key, VeeValidateRules[key]);
        };

        VeeValidate.localize('validationMsg', customMsg);
        Vue.component('ValidationProvider', ValidationProvider);
        Vue.component('ValidationObserver', ValidationObserver);
    };

    const heightOfEle = function (el, includeMargin) {
        let height = el.offsetHeight;

        if (includeMargin) {
            let style = getComputedStyle(el);

            height += parseInt(style.marginTop) + parseInt(style.marginBottom);
            return height
        } else {
            return height;
        };
    };

    const removeValInArr = function (arr) {
        let what, a = arguments, L = a.length, ax;
        while (L > 1 && arr.length) {
            what = a[--L];
            while ((ax = arr.indexOf(what)) !== -1) {
                arr.splice(ax, 1);
            }
        }
        return arr;
    };

    const removeObjInArr = function (arr, attr, value) {
        let i = arr.length;

        while (i--) {
            if (arr[i] &&
                arr[i].hasOwnProperty(attr) &&
                (arguments.length > 2 && arr[i][attr] === value)) {

                arr.splice(i, 1);

            }
        }
        return arr;
    };

    const formatDate = function (d) {
        var date = new Date(d);

        if (isNaN(date.getTime())) {
            return d;
        } else {
            var month = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

            day = date.getDate();

            if (day < 10) {
                day = "0" + day;
            }

            return month[date.getMonth()] + " " + day + " " + date.getFullYear();
        };
    };

    const timeConvert = function (time) {
        time = time.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];

        if (time.length > 1) {
            time = time.slice(1);
            time[5] = +time[0] < 12 ? 'AM' : 'PM';
            time[0] = +time[0] % 12 || 12;
        }
        return time.join('');
    };

    const dateTimeToArray = function (str) {
        const splitarray = new Array(),
            toArray = str.split(" ");

        return toArray;
    };

    const getQueryParameter = function (queryKey) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == queryKey) { return pair[1]; }
        }
        return (false);
    };

    const queryParameterNonWindow = function (url, queryKey) {
        var query = url.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == queryKey) { return pair[1]; }
        }
        return (false);
    };

    const countriesData = function () {
        return [{ name: 'Afghanistan', code: 'AF' }, { name: 'Åland Islands', code: 'AX' }, { name: 'Albania', code: 'AL' }, { name: 'Algeria', code: 'DZ' }, { name: 'American Samoa', code: 'AS' }, { name: 'AndorrA', code: 'AD' }, { name: 'Angola', code: 'AO' }, { name: 'Anguilla', code: 'AI' }, { name: 'Antarctica', code: 'AQ' }, { name: 'Antigua and Barbuda', code: 'AG' }, { name: 'Argentina', code: 'AR' }, { name: 'Armenia', code: 'AM' }, { name: 'Aruba', code: 'AW' }, { name: 'Australia', code: 'AU' }, { name: 'Austria', code: 'AT' }, { name: 'Azerbaijan', code: 'AZ' }, { name: 'Bahamas', code: 'BS' }, { name: 'Bahrain', code: 'BH' }, { name: 'Bangladesh', code: 'BD' }, { name: 'Barbados', code: 'BB' }, { name: 'Belarus', code: 'BY' }, { name: 'Belgium', code: 'BE' }, { name: 'Belize', code: 'BZ' }, { name: 'Benin', code: 'BJ' }, { name: 'Bermuda', code: 'BM' }, { name: 'Bhutan', code: 'BT' }, { name: 'Bolivia', code: 'BO' }, { name: 'Bosnia and Herzegovina', code: 'BA' }, { name: 'Botswana', code: 'BW' }, { name: 'Bouvet Island', code: 'BV' }, { name: 'Brazil', code: 'BR' }, { name: 'British Indian Ocean Territory', code: 'IO' }, { name: 'Brunei Darussalam', code: 'BN' }, { name: 'Bulgaria', code: 'BG' }, { name: 'Burkina Faso', code: 'BF' }, { name: 'Burundi', code: 'BI' }, { name: 'Cambodia', code: 'KH' }, { name: 'Cameroon', code: 'CM' }, { name: 'Canada', code: 'CA' }, { name: 'Cape Verde', code: 'CV' }, { name: 'Cayman Islands', code: 'KY' }, { name: 'Central African Republic', code: 'CF' }, { name: 'Chad', code: 'TD' }, { name: 'Chile', code: 'CL' }, { name: 'China', code: 'CN' }, { name: 'Christmas Island', code: 'CX' }, { name: 'Cocos (Keeling) Islands', code: 'CC' }, { name: 'Colombia', code: 'CO' }, { name: 'Comoros', code: 'KM' }, { name: 'Congo', code: 'CG' }, { name: 'Congo, The Democratic Republic of the', code: 'CD' }, { name: 'Cook Islands', code: 'CK' }, { name: 'Costa Rica', code: 'CR' }, { name: 'Cote D\'Ivoire', code: 'CI' }, { name: 'Croatia', code: 'HR' }, { name: 'Cuba', code: 'CU' }, { name: 'Cyprus', code: 'CY' }, { name: 'Czech Republic', code: 'CZ' }, { name: 'Denmark', code: 'DK' }, { name: 'Djibouti', code: 'DJ' }, { name: 'Dominica', code: 'DM' }, { name: 'Dominican Republic', code: 'DO' }, { name: 'Ecuador', code: 'EC' }, { name: 'Egypt', code: 'EG' }, { name: 'El Salvador', code: 'SV' }, { name: 'Equatorial Guinea', code: 'GQ' }, { name: 'Eritrea', code: 'ER' }, { name: 'Estonia', code: 'EE' }, { name: 'Ethiopia', code: 'ET' }, { name: 'Falkland Islands (Malvinas)', code: 'FK' }, { name: 'Faroe Islands', code: 'FO' }, { name: 'Fiji', code: 'FJ' }, { name: 'Finland', code: 'FI' }, { name: 'France', code: 'FR' }, { name: 'French Guiana', code: 'GF' }, { name: 'French Polynesia', code: 'PF' }, { name: 'French Southern Territories', code: 'TF' }, { name: 'Gabon', code: 'GA' }, { name: 'Gambia', code: 'GM' }, { name: 'Georgia', code: 'GE' }, { name: 'Germany', code: 'DE' }, { name: 'Ghana', code: 'GH' }, { name: 'Gibraltar', code: 'GI' }, { name: 'Greece', code: 'GR' }, { name: 'Greenland', code: 'GL' }, { name: 'Grenada', code: 'GD' }, { name: 'Guadeloupe', code: 'GP' }, { name: 'Guam', code: 'GU' }, { name: 'Guatemala', code: 'GT' }, { name: 'Guernsey', code: 'GG' }, { name: 'Guinea', code: 'GN' }, { name: 'Guinea-Bissau', code: 'GW' }, { name: 'Guyana', code: 'GY' }, { name: 'Haiti', code: 'HT' }, { name: 'Heard Island and Mcdonald Islands', code: 'HM' }, { name: 'Holy See (Vatican City State)', code: 'VA' }, { name: 'Honduras', code: 'HN' }, { name: 'Hong Kong', code: 'HK' }, { name: 'Hungary', code: 'HU' }, { name: 'Iceland', code: 'IS' }, { name: 'India', code: 'IN' }, { name: 'Indonesia', code: 'ID' }, { name: 'Iran, Islamic Republic Of', code: 'IR' }, { name: 'Iraq', code: 'IQ' }, { name: 'Ireland', code: 'IE' }, { name: 'Isle of Man', code: 'IM' }, { name: 'Israel', code: 'IL' }, { name: 'Italy', code: 'IT' }, { name: 'Jamaica', code: 'JM' }, { name: 'Japan', code: 'JP' }, { name: 'Jersey', code: 'JE' }, { name: 'Jordan', code: 'JO' }, { name: 'Kazakhstan', code: 'KZ' }, { name: 'Kenya', code: 'KE' }, { name: 'Kiribati', code: 'KI' }, { name: 'Korea, Democratic People\'S Republic of', code: 'KP' }, { name: 'Korea, Republic of', code: 'KR' }, { name: 'Kuwait', code: 'KW' }, { name: 'Kyrgyzstan', code: 'KG' }, { name: 'Lao People\'S Democratic Republic', code: 'LA' }, { name: 'Latvia', code: 'LV' }, { name: 'Lebanon', code: 'LB' }, { name: 'Lesotho', code: 'LS' }, { name: 'Liberia', code: 'LR' }, { name: 'Libyan Arab Jamahiriya', code: 'LY' }, { name: 'Liechtenstein', code: 'LI' }, { name: 'Lithuania', code: 'LT' }, { name: 'Luxembourg', code: 'LU' }, { name: 'Macao', code: 'MO' }, { name: 'Macedonia, The Former Yugoslav Republic of', code: 'MK' }, { name: 'Madagascar', code: 'MG' }, { name: 'Malawi', code: 'MW' }, { name: 'Malaysia', code: 'MY' }, { name: 'Maldives', code: 'MV' }, { name: 'Mali', code: 'ML' }, { name: 'Malta', code: 'MT' }, { name: 'Marshall Islands', code: 'MH' }, { name: 'Martinique', code: 'MQ' }, { name: 'Mauritania', code: 'MR' }, { name: 'Mauritius', code: 'MU' }, { name: 'Mayotte', code: 'YT' }, { name: 'Mexico', code: 'MX' }, { name: 'Micronesia, Federated States of', code: 'FM' }, { name: 'Moldova, Republic of', code: 'MD' }, { name: 'Monaco', code: 'MC' }, { name: 'Mongolia', code: 'MN' }, { name: 'Montserrat', code: 'MS' }, { name: 'Morocco', code: 'MA' }, { name: 'Mozambique', code: 'MZ' }, { name: 'Myanmar', code: 'MM' }, { name: 'Namibia', code: 'NA' }, { name: 'Nauru', code: 'NR' }, { name: 'Nepal', code: 'NP' }, { name: 'Netherlands', code: 'NL' }, { name: 'Netherlands Antilles', code: 'AN' }, { name: 'New Caledonia', code: 'NC' }, { name: 'New Zealand', code: 'NZ' }, { name: 'Nicaragua', code: 'NI' }, { name: 'Niger', code: 'NE' }, { name: 'Nigeria', code: 'NG' }, { name: 'Niue', code: 'NU' }, { name: 'Norfolk Island', code: 'NF' }, { name: 'Northern Mariana Islands', code: 'MP' }, { name: 'Norway', code: 'NO' }, { name: 'Oman', code: 'OM' }, { name: 'Pakistan', code: 'PK' }, { name: 'Palau', code: 'PW' }, { name: 'Palestinian Territory, Occupied', code: 'PS' }, { name: 'Panama', code: 'PA' }, { name: 'Papua New Guinea', code: 'PG' }, { name: 'Paraguay', code: 'PY' }, { name: 'Peru', code: 'PE' }, { name: 'Philippines', code: 'PH' }, { name: 'Pitcairn', code: 'PN' }, { name: 'Poland', code: 'PL' }, { name: 'Portugal', code: 'PT' }, { name: 'Puerto Rico', code: 'PR' }, { name: 'Qatar', code: 'QA' }, { name: 'Reunion', code: 'RE' }, { name: 'Romania', code: 'RO' }, { name: 'Russian Federation', code: 'RU' }, { name: 'RWANDA', code: 'RW' }, { name: 'Saint Helena', code: 'SH' }, { name: 'Saint Kitts and Nevis', code: 'KN' }, { name: 'Saint Lucia', code: 'LC' }, { name: 'Saint Pierre and Miquelon', code: 'PM' }, { name: 'Saint Vincent and the Grenadines', code: 'VC' }, { name: 'Samoa', code: 'WS' }, { name: 'San Marino', code: 'SM' }, { name: 'Sao Tome and Principe', code: 'ST' }, { name: 'Saudi Arabia', code: 'SA' }, { name: 'Senegal', code: 'SN' }, { name: 'Serbia and Montenegro', code: 'CS' }, { name: 'Seychelles', code: 'SC' }, { name: 'Sierra Leone', code: 'SL' }, { name: 'Singapore', code: 'SG' }, { name: 'Slovakia', code: 'SK' }, { name: 'Slovenia', code: 'SI' }, { name: 'Solomon Islands', code: 'SB' }, { name: 'Somalia', code: 'SO' }, { name: 'South Africa', code: 'ZA' }, { name: 'South Georgia and the South Sandwich Islands', code: 'GS' }, { name: 'Spain', code: 'ES' }, { name: 'Sri Lanka', code: 'LK' }, { name: 'Sudan', code: 'SD' }, { name: 'Suriname', code: 'SR' }, { name: 'Svalbard and Jan Mayen', code: 'SJ' }, { name: 'Swaziland', code: 'SZ' }, { name: 'Sweden', code: 'SE' }, { name: 'Switzerland', code: 'CH' }, { name: 'Syrian Arab Republic', code: 'SY' }, { name: 'Taiwan, Province of China', code: 'TW' }, { name: 'Tajikistan', code: 'TJ' }, { name: 'Tanzania, United Republic of', code: 'TZ' }, { name: 'Thailand', code: 'TH' }, { name: 'Timor-Leste', code: 'TL' }, { name: 'Togo', code: 'TG' }, { name: 'Tokelau', code: 'TK' }, { name: 'Tonga', code: 'TO' }, { name: 'Trinidad and Tobago', code: 'TT' }, { name: 'Tunisia', code: 'TN' }, { name: 'Turkey', code: 'TR' }, { name: 'Turkmenistan', code: 'TM' }, { name: 'Turks and Caicos Islands', code: 'TC' }, { name: 'Tuvalu', code: 'TV' }, { name: 'Uganda', code: 'UG' }, { name: 'Ukraine', code: 'UA' }, { name: 'United Arab Emirates', code: 'AE' }, { name: 'United Kingdom', code: 'GB' }, { name: 'United States', code: 'US' }, { name: 'United States Minor Outlying Islands', code: 'UM' }, { name: 'Uruguay', code: 'UY' }, { name: 'Uzbekistan', code: 'UZ' }, { name: 'Vanuatu', code: 'VU' }, { name: 'Venezuela', code: 'VE' }, { name: 'Viet Nam', code: 'VN' }, { name: 'Virgin Islands, British', code: 'VG' }, { name: 'Virgin Islands, U.S.', code: 'VI' }, { name: 'Wallis and Futuna', code: 'WF' }, { name: 'Western Sahara', code: 'EH' }, { name: 'Yemen', code: 'YE' }, { name: 'Zambia', code: 'ZM' }, { name: 'Zimbabwe', code: 'ZW' }]
    };

    const PrivateWindow = new Promise(function (resolve, reject) {
        try {
            if (navigator.userAgent.includes("Firefox")) {
                //Firefox
                var db = indexedDB.open("test");
                db.onerror = function () { resolve(true); };
                db.onsuccess = function () { resolve(false); };
            } else {
                resolve(null);
            }
        }
        catch (err) {
            console.log(err);
            resolve(null);
        }
    });

    const isPrivateWindow = function (callback) {
        PrivateWindow.then(function (is_private) {
            callback(is_private);
        });
    };

    const setCookie = function (cname, cvalue, exdays) {
        let d = new Date();

        exdays === undefined ? exdays = 30 : exdays;
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));

        let expires = "expires=" + d.toGMTString();

        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    };

    const deleteCookie = function (name) {
        document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    };

    const getCookie = function (name) {
        let value = "; " + document.cookie,
            parts = value.split("; " + name + "=");

        if (parts.length == 2) return parts.pop().split(";").shift();
    };

    const timestamp = function () {
        const currentTime = performance.now()

        return currentTime;
    };

    const hasInArray = function (arr, obj) {
        return (arr.indexOf(obj) != -1);
    };

    const getFromString = function (str, regexp, isLastChar) {
        let arr = str.match(regexp);

        if (arr != null) { // Did it match?
            return isLastChar === true ? arr[1].replace(/\/$/, "") : arr[1];
        } else {
            return false;
        }
    };

    const encodeObj = function (data) {
        const state = encodeURI(JSON.stringify(data));

        return state;
    };

    const detectQueryString = function() {
        const currentQueryString = window.location.search;

        if (currentQueryString) {
            return currentQueryString;
        } else {
          return false;
        }
    };

    const scrollToElement = function (element, duration, headerHeight) {
        const getElementY = function (query) {
            return window.pageYOffset + document.querySelector(query).getBoundingClientRect().top
        };

        let startingY = window.pageYOffset,
            elementY = getElementY(element);
            targetY = document.body.scrollHeight - elementY < window.innerHeight ? document.body.scrollHeight - window.innerHeight : elementY,
            customHeight = headerHeight !== undefined ? headerHeight : 74,
            diff = targetY - startingY - customHeight;

        // Easing function: easeInOutCubic
        let easing = function (t) { return t < .5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1 },
            start = "";

        if (!diff) return

        // Bootstrap our animation - it will get called right before next frame shall be rendered.
        window.requestAnimationFrame(function step(timestamp) {
            if (!start) start = timestamp
            // Elapsed miliseconds since start of scrolling.
            let time = timestamp - start
            // Get percent of completion in range [0, 1].
            let percent = Math.min(time / duration, 1)
            // Apply the easing.
            percent = easing(percent)

            window.scrollTo(0, startingY + diff * percent)

            // Proceed with animation as long as we wanted it to.
            if (time < duration) {
                window.requestAnimationFrame(step)
            }
        })
    };

    const removeTagsFromString = function(str) {
        return str.replace(/(<([^>]+)>)/ig, "") 
    };

    const cleanTextAndTruncate = function(htmlString, maxLength) {
        // Remove tags by replacing them with a space and then trim the result
        let textWithoutTags = htmlString.replace(/<\/[^>]+>/gi, ' ').replace(/<[^>]+>/gi, '').trim();
        
        // Replace multiple spaces with a single space
        textWithoutTags = textWithoutTags.replace(/\s\s+/g, ' ');
      
        // Truncate to the maxLength if necessary and add ellipsis
        if (textWithoutTags.length > maxLength) {
          // Ensure we don't break in the middle of a word
          // Find the last space before the maxLength
          const lastSpaceIndex = textWithoutTags.lastIndexOf(' ', maxLength - 1);
          // Use the lastSpaceIndex to truncate the string, and add an ellipsis
          return textWithoutTags.substring(0, lastSpaceIndex) + '...';
        }
      
        // Return the text if it's less than the maxLength
        return textWithoutTags;
    } 

    const findInArray = function(array, value) {
        const found = array.find(element => element === value);

        if (found !== undefined) {
            return true
        } else {
            return false
        }
    };

    return {
        config: config,
        findObjectByKey: findObjectByKey,
        heightOfEle: heightOfEle,
        assignVValidationObj: assignVValidationObj,
        removeObjInArr: removeObjInArr,
        formatDate: formatDate,
        dateTimeToArray: dateTimeToArray,
        timeConvert: timeConvert,
        getQueryParameter: getQueryParameter,
        countriesData: countriesData,
        isPrivateWindow: isPrivateWindow,
        setCookie: setCookie,
        deleteCookie: deleteCookie,
        getCookie: getCookie,
        timestamp: timestamp,
        removeValInArr: removeValInArr,
        hasInArray: hasInArray,
        getFromString: getFromString,
        encodeObj: encodeObj,
        detectQueryString: detectQueryString,
        scrollToElement: scrollToElement,
        removeTagsFromString: removeTagsFromString,
        findInArray: findInArray,
        queryParameterNonWindow: queryParameterNonWindow,
        cleanTextAndTruncate: cleanTextAndTruncate
    };
})(jQuery);
