Vue.component('yuno-table-grid', {
    props: {
        apiURL: {
            type: Object,
            required: true,
        },
        filterAPIURL: {
            type: Object,
            required: true,
        },
        apiMethodType: {
            type: String,
            required: true
        },
        payload: {
            type: Object,
            required: false
        },
        sessionStorage: {
            type: Object,
            required: true
        },
        emptyStateCTA: {
            type: Array,
            default: () => []
        },
        emptyStateImg: {
            type: String,
        },
        recordFoundCaption: {
            type: String,
            default: "found"
        },
    },
    template: `
        <div :class="[isFilterMobile ? 'filterActive' : '']">
            <div class="mainHeader">
                <yuno-table-grid-filters
                    @onDropdownChange="onDropdownChange"
                    @manageFilters="onManageFilters"
                    @clearFilter="onClearFilter"
                    :data="filterResult.payload"
                    :defaultFilters="defaultFilters"
                    :options="{'isFilterMobile': isFilterMobile}"
                    @onQuerySearch="onQuerySearch"
                    @onSearchInput="onSearchInput"
                >
                </yuno-table-grid-filters>
                <slot name="stats"></slot>
                <div class="gridInfo" v-if="filterResult.success && !filterResult.error">
                    <p class="note">
                        <span>{{ filterResult.count }}</span> {{ recordFoundCaption }} 
                    </p>
                    <ul class="actions">
                        <template v-for="(action, i) in gridActions">
                            <li v-if="action.type === 'dropdown'" :key="i">
                                <div class="yunoDropdown iconOnly" :class="[action.filter]">
                                    <b-dropdown 
                                        v-model="action.selected" 
                                        aria-role="list"
                                        :mobile-modal="false"
                                        :multiple="true"
                                        @change="onHideColChange($event, action)"
                                    >
                                        <template #trigger>
                                            <div class="labelWrapper">
                                                <span class="selectedItem"><span class="material-icons-outlined">{{ action.icon }}</span></span>
                                                <span class="material-icons icon">expand_more</span>
                                            </div>
                                        </template>
                                        <p class="listCaption">Hide Columns</p>
                                        <template v-for="(option, j) in filterResult.data.columns">
                                            <b-dropdown-item 
                                                :value="option.field.parent" 
                                                :key="j"
                                                aria-role="listitem"
                                                @click="onHideColItemSelect(option, action)"
                                            >
                                                {{ option.label }}
                                            </b-dropdown-item>
                                        </template>
                                    </b-dropdown>
                                </div>
                            </li>
                        </template>
                    </ul>
                </div>
            </div>
            <yuno-csv-download-modal :data="filterResult"></yuno-csv-download-modal>
            <section class="yunoTable">
                <template v-if="filterResult.loading">
                    <b-skeleton height="500px"></b-skeleton>
                </template>
                <template v-if="filterResult.success">
                    <template v-if="filterResult.error">
                        <yuno-empty-state-v2 
                            :options="{'type': 'noDataFound', 'message': filterResult.errorData}"
                            :cta="emptyStateCTA"
                            :image="emptyStateImg"
                        >
                        </yuno-empty-state-v2>
                    </template>    
                    <template v-else>
                        <b-table
                            :data="filterResult.data.rows"
                            :loading="filterResult.isLoadMore"
                            :paginated="true"
                            :backend-pagination="true"
                            :total="filterResult.count"
                            :per-page="filterResult.limit"
                            :current-page="filterResult.currentPage"
                            :striped="true"
                            backend-sorting
                            @sort="sortPressed"
                            :default-sort-direction="defaultSortOrder"
                            :default-sort="defaultSort"
                            @page-change="pageChange($event)"
                            ref="table">
                            <b-table-column
                                v-for="(column, i) in filterResult.data.columns" 
                                :key="i" 
                                :field="column.field.parent" 
                                :visible="column.is_active"
                                :label="column.label" 
                                v-slot="props"
                                :sortable="column.sortable">
                                <template v-if="column.field.child.length > 0">
                                    <template v-if="column.field.parent ===  'start_date' || column.field.parent === 'end_date'">
                                        <div :class="[column.field.parent]">
                                            <template v-for="(child, j) in column.field.child">
                                                {{ formatDate(props.row[column.field.parent][child]) }}
                                            </template>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'learner' || column.field.parent === 'instructor' || column.field.parent === 'counselor'">
                                        <figure 
                                            :class="[column.field.parent]" class="userWithPhoto"
                                        >
                                            <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">
                                            <figcaption>
                                                {{ props.row[column.field.parent].full_name }}
                                            </figcaption>
                                        </figure>    
                                    </template>
                                    <template v-else-if="column.field.parent === 'course'">
                                        <div :class="[column.field.parent]" class="grid">
                                            <template v-for="(child, j) in column.field.child">
                                                <span class="gridItem">
                                                    <template v-if="child === 'title'">
                                                        {{ props.row[column.field.parent][child] }}
                                                    </template>
                                                    <template v-if="child === 'id'">
                                                        {{ "(" +  props.row[column.field.parent][child] + ")" }}
                                                    </template>
                                                </span>
                                                <template v-if="child === 'url'">
                                                    <a class="gridItem" :href="props.row[column.field.parent][child]" target="_blank"><span class="material-icons">open_in_new</span></a>
                                                </template>
                                            </template>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'academy' || column.field.parent === 'of_org'">
                                        <div :class="[column.field.parent]" class="grid">
                                            <span class="gridItem">
                                                {{ props.row[column.field.parent].name }}
                                            </span>
                                            <span class="gridItem">
                                                ({{ props.row[column.field.parent].id }})
                                            </span>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'classes'">
                                        <div :class="[column.field.parent]" class="grid">
                                            <span class="gridItem">
                                                {{ props.row[column.field.parent].attended }}
                                            </span>
                                            <span class="gridItem">
                                                ({{ props.row[column.field.parent].total }})
                                            </span>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'attendance'">
                                        <template v-if="props.row[column.field.parent].percentage === 0 || props.row[column.field.parent].percentage === ''">
                                            NA
                                        </template>
                                        <div class="percentageBlock" v-else>
                                            <b-progress 
                                                :type="{
                                                    'is-red': props.row[column.field.parent].percentage <= 30,
                                                    'is-orange': props.row[column.field.parent].percentage > 30,
                                                    'is-yellow': props.row[column.field.parent].percentage > 50,
                                                    'is-lightGreen': props.row[column.field.parent].percentage > 70,
                                                    'is-darkGreen': props.row[column.field.parent].percentage > 80
                                                }"  
                                                format="percent"    
                                                :value="Number(props.row[column.field.parent].percentage)">
                                                {{props.row[column.field.parent].percentage}}
                                            </b-progress>
                                            <div class="percentage">{{ props.row[column.field.parent].percentage }}% <a href="#" @click.prevent="manageDrawer(props.row, column.field.parent)"><span class="material-icons">pin_invoke</span></a></div>
                                        </div>
                                    </template>
                                    <template v-else-if="column.field.parent === 'enrolled_by'">
                                        <figure 
                                            :class="[column.field.parent]" class="userWithPhoto"
                                        >
                                            <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">
                                            <figcaption>
                                                {{ props.row[column.field.parent].full_name + " " + "(" + props.row[column.field.parent].id + " " + props.row[column.field.parent].role + ")" }} 
                                            </figcaption>
                                        </figure>    
                                    </template>
                                    <template v-else>
                                        <template v-if="column.field.parent === 'referral'">
                                            <figure 
                                                :class="[column.field.parent]" class="userWithPhoto"
                                            >
                                                <img :src="props.row[column.field.parent].image_url" :alt="props.row[column.field.parent].full_name" width="20" height="20">
                                                <figcaption>
                                                    {{ props.row[column.field.parent].full_name }}
                                                </figcaption>
                                            </figure>    
                                        </template>
                                        <template v-else>
                                            <div :class="[column.field.parent]">
                                                <template v-for="(child, j) in column.field.child">
                                                    <template v-if="child.sub_child && child.sub_child.length > 0">
                                                        <template v-for="(subChild, k) in child.sub_child">
                                                            <span class="subChild">
                                                                {{ formatDate(props.row[column.field.parent][child.field][subChild], column.field.parent) }}
                                                            </span>
                                                        </template>
                                                    </template>
                                                    <template v-else>
                                                        {{ props.row[column.field.parent][child] }}
                                                    </template>
                                                </template>
                                            </div>
                                        </template>
                                    </template>
                                </template>
                                <template v-else>
                                    <template v-if="column.field.parent === 'status'">
                                        <div :class="[column.field.parent, 'hasActiveInactive', props.row[column.field.parent] === 'ACTIVE' ? 'active' : 'inactive']">
                                            <span class="value">{{ props.row[column.field.parent] }}</span>
                                        </div>    
                                    </template>
                                    <template v-else-if="column.field.parent === 'is_active'">
                                        <div :class="[column.field.parent, 'hasActiveInactive', props.row[column.field.parent] ? 'active' : 'inactive']">
                                            <template v-if="props.row[column.field.parent]">
                                                <span class="material-icons">how_to_reg</span>
                                            </template>
                                            <template v-else>
                                                <span class="material-icons">person_off</span>
                                            </template>
                                        </div>    
                                    </template>
                                    <template v-else-if="column.field.parent === 'actions'">
                                        <ul :class="[column.field.parent]">
                                            <li 
                                                v-for="(item, k) in props.row[column.field.parent]"
                                                v-if="item.is_active"
                                                :key="'action-' + k">
                                                <b-tooltip :label="item.label"
                                                    type="is-dark"
                                                    position="is-left">
                                                    <a :href="item.url !== false ? item.url : '#'" :target="item.link_target" @click="initAction(props.row, item, $event)">
                                                        <span class="itemLabel">{{ item.label }}</span>
                                                        <span class="itemIcon" :class="[item.active_class]" @mouseover="manageMouse(item, 'over')" @mouseout="manageMouse(item, 'out')">
                                                            {{ item.icon.value === undefined ?  item.icon.font : item.icon.value }}
                                                        </span>
                                                    </a>
                                                </b-tooltip>
                                            </li>
                                        </ul>    
                                    </template>
                                    <template v-else>
                                        <div :class="[column.field.parent]">{{ props.row[column.field.parent] }}</div>    
                                    </template>
                                </template>
                            </b-table-column>
                        </b-table>    
                    </template>    
                </template>
            </section>
        </div>
    `,
    data() {
        return {
            hasDrawerCols: [
                "enrollment_count",
                "attendance_count"
            ],
            isFilterMobile: false,
            defaultFilters: [],
            selectedOption: [],
            defaultSortOrder: "asc",
            defaultSort: [],
            gridActions: [
                {
                    message: "Request CSV download",
                    icon: "summarize",
                    filter: "request_CSV_download",
                    is_active: true,
                    selected: "",
                    type: "custom",
                    placeholder: "Role",
                    items: []
                },
                {
                    message: "Hide columns",
                    icon: "settings",
                    filter: "hideColumn",
                    is_active: true,
                    selected: [],
                    type: "dropdown",
                    placeholder: "Role",
                    items: []
                }
            ],
            // Constants for date formatting
            WEEKDAYS: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
            MONTHS: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            // API timeout in milliseconds
            API_TIMEOUT: 30000,
            // Storage debounce time in milliseconds
            STORAGE_DEBOUNCE: 100
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'userRole',
            'filterResult',
            'learnerInsightsClass',
            'filters'
        ]),
        attendance() {
            return this.learnerInsightsClass?.data || [];
        },
        activeOrganization() {
            return this.userInfo?.data?.current_state?.org_id || null;
        },
        visibleColumns() {
            return this.filterResult?.data?.columns?.filter(col => col.is_active) || [];
        },
        tableData() {
            return this.filterResult?.success && !this.filterResult?.error ? this.filterResult.data.rows : [];
        }
    },
    watch: {
        'filterResult.refreshTable': {
            handler(newValue, oldValue) {
                if (newValue) {
                    this.refreshTable();
                }
            },
            deep: true
        }
    },
    created() {
        this.manageDefaultFilters();
    },
    mounted() {
        this.getStorage();
    },
    methods: {
        /**
         * Formats a date string into a readable format
         * @param {string} dateStr - The date string to format
         * @param {string} parent - Optional parent context for date formatting
         * @returns {string} Formatted date string or original input if invalid
         */
        formatDate(dateStr, parent) {
            if (!dateStr) return '';
            
            const date = new Date(dateStr);
            if (dateStr === "0" || /^\d+$/.test(dateStr) || isNaN(date.getTime())) {
                return dateStr;
            }

            return `${this.WEEKDAYS[date.getDay()]}, ${this.MONTHS[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
        },
        /**
         * Copies text to clipboard and shows a toast notification
         * @param {Object} row - The row data containing the text to copy
         * @param {Object} action - The action object containing the URL to copy
         */
        async copyToClipboard(row, action) {
            if (!navigator.clipboard) {
                this.$buefy.toast.open({
                    duration: 1000,
                    message: 'Clipboard API not available',
                    position: 'is-bottom'
                });
                return;
            }

            try {
                await navigator.clipboard.writeText(action.url);
                this.$buefy.toast.open({
                    duration: 1000,
                    message: 'Copied to clipboard',
                    position: 'is-bottom'
                });
            } catch (error) {
                console.error('Failed to copy:', error);
                this.$buefy.toast.open({
                    duration: 1000,
                    message: 'Failed to copy!',
                    position: 'is-bottom'
                });
            }
        },
        /**
         * Initializes an action based on its slug type
         * @param {Object} row - The row data
         * @param {Object} action - The action object containing the action details
         * @param {Event} e - The event object
         */
        initAction(row, action, e) {
            if (!row || !action) return;

            if (action.slug === "clickToCopy") {
                e.preventDefault();
                this.copyToClipboard(row, action);
            }

            this.$emit("initAction", row, action, e);
        },
        /**
         * Manages mouse hover states for action icons
         * @param {Object} data - The action data
         * @param {string} type - The mouse event type ('over' or 'out')
         */
        manageMouse(data, type) {
            if (!data || !data.icon) return;
            data.active_class = type === "over" ? data.icon.hover : data.icon.class;
        },
        /**
         * Retrieves and applies stored table state from sessionStorage
         */
        getStorage() {
            const storage = this.$props.sessionStorage;
            if (!storage?.name || !storage?.version) return;

            const version = Number(JSON.parse(JSON.stringify(storage.version)));
            const lastStorage = storage.name + "V" + (version - 1);
            
            try {
                sessionStorage.removeItem(lastStorage);
                const store = sessionStorage.getItem(storage.name + "V" + storage.version);

                if (store) {
                    const data = JSON.parse(store);
                    this.filterResult.payload = data.payload;
                    this.filters.data = this.refineFilters(data.filters);
                    this.filters.success = true;

                    if (this.userRole.data === "org-admin") {
                        ['org_id', 'org'].forEach(key => {
                            if (key in this.filterResult.payload) {
                                this.filterResult.payload[key] = this.activeOrganization;
                            }
                        });
                    }

                    this.gridActions = data.gridActions;
                    this.filterResult.currentPage = data.currentPage;
                    this.filterResult.offset = data.offset;
                    this.$emit("gotFilters", this.filters.data);
                    this.fetchGrid(true);
                } else {
                    this.fetchFilters(true);
                    this.fetchGrid(true);
                }
            } catch (error) {
                console.error('Error accessing storage:', error);
                this.fetchFilters(true);
                this.fetchGrid(true);
            }
        },
        /**
         * Stores the current table state in sessionStorage
         * @param {boolean} hasColumns - Whether to include column visibility state
         */
        setStorage(hasColumns) {
            const storage = this.$props.sessionStorage;
            if (!storage?.name || !storage?.version) return;

            const store = {
                payload: this.filterResult.payload,
                filters: this.filters.data,
                gridActions: this.gridActions,
                currentPage: this.filterResult.currentPage,
                offset: this.filterResult.offset,
                currentTab: this.filterResult.tabs?.activeTab
            };

            try {
                setTimeout(() => {
                    sessionStorage.setItem(storage.name + "V" + storage.version, JSON.stringify(store));
                }, this.STORAGE_DEBOUNCE);
            } catch (error) {
                console.error('Error storing data:', error);
            }
        },
        /**
         * Handles grid data response
         * @param {Object} options - The response options
         */
        gotGrid(options) {
            if (!options?.response?.data) {
                this.filterResult.error = true;
                this.filterResult.errorData = 'Invalid response format';
                return;
            }

            this.filterResult.isLoadMore = false;
            this.filterResult.loading = false;
            this.filterResult.refreshTable = false;
            
            const { code, data, count } = options.response.data;
            
            if (code === 201 || code === 200) {
                this.manageColumns(data.columns);
                this.manageColumnsVisibility(data.columns);
                this.$emit("gotGrid", data);
                this.filterResult.count = count;
                this.filterResult.data = data;
            } else {
                this.filterResult.error = true;
                this.filterResult.errorData = options.response.data.message || 'Failed to fetch data';
            }
        },
        /**
         * Fetches grid data from the API
         * @param {boolean} moduleLoading - Whether to show loading state
         */
        fetchGrid(moduleLoading) {
            this.setStorage();
            this.isFilterMobile = false;
            this.filterResult.loading = moduleLoading;

            const params = {
                view: this.$props.apiURL.options.view,
                params: `?${this.objectToQueryString(this.filterResult.payload)}`
            };

            const options = { 
                apiURL: YUNOCommon.config[this.$props.apiURL.configMethod](this.$props.apiURL.type, params),
                module: "gotData",
                store: "filterResult",
                moduleLoading,
                callback: true,
                addToModule: false,
                callbackFunc: (options) => this.gotGrid(options),
                timeout: this.API_TIMEOUT
            };

            if (this.$props.apiMethodType === "POST") {
                options.payload = this.filterResult.payload;
                options.headers = {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                }
            }

            this.$store.dispatch(this.$props.apiMethodType === "POST" ? "postData" : "fetchData", options);
        },
        /**
         * Handles filter data response
         * @param {Object} options - The response options
         */
        gotFilters(options) {
            if (!options?.response?.data) {
                this.filters.error = true;
                return;
            }

            const { code, data } = options.response.data;
            
            if (code === 201 || code === 200) {
                this.$emit("gotFilters", data);
                this.filters.data = data;
                this.setStorage();
            } else {
                this.filters.error = true;
            }
        },
        /**
         * Fetches filter data from the API
         * @param {boolean} moduleLoading - Whether to show loading state
         */
        fetchFilters(moduleLoading) {
            const options = { 
                apiURL: YUNOCommon.config[this.$props.filterAPIURL.configMethod](this.$props.filterAPIURL.type, this.$props.filterAPIURL.options),
                module: "gotData",
                store: "filters",
                moduleLoading,
                callback: true,
                addToModule: false,
                callbackFunc: (options) => this.gotFilters(options),
                timeout: this.API_TIMEOUT
            };

            this.$store.dispatch("fetchData", options);
        },
        /**
         * Parses a column field string into a structured object
         * @param {string} fieldStr - The field string to parse
         * @returns {Object} Parsed field object with parent and child structure
         */
        parseColumnField(fieldStr) {
            if (!fieldStr || typeof fieldStr !== 'string') {
                return { parent: '', child: [] };
            }

            if (fieldStr.indexOf(',') !== -1) {
                const tokens = fieldStr.split(',').map(s => s.trim());
                const parent = tokens[0].split('.')[0];
                const childrenGrouped = {};

                tokens.forEach(token => {
                    const parts = token.split('.');
                    if (parts.length === 1) {
                        childrenGrouped[parts[0]] = [];
                    } else if (parts.length === 2) {
                        const child = parts[1];
                        childrenGrouped[child] = childrenGrouped[child] || [];
                    } else if (parts.length >= 3) {
                        const child = parts[1];
                        const subChild = parts.slice(2).join('.');
                        childrenGrouped[child] = childrenGrouped[child] || [];
                        if (!childrenGrouped[child].includes(subChild)) {
                            childrenGrouped[child].push(subChild);
                        }
                    }
                });

                const childrenArray = Object.entries(childrenGrouped).map(([child, subChildren]) => {
                    return subChildren.length > 0 ? { field: child, sub_child: subChildren } : child;
                });

                return { parent, child: childrenArray };
            }

            const parts = fieldStr.split('.');
            if (parts.length >= 3) {
                return { 
                    parent: parts[0], 
                    child: [{ field: parts[1], sub_child: parts.slice(2) }] 
                };
            }
            
            return { 
                parent: parts[0], 
                child: parts.slice(1) 
            };
        },
        /**
         * Processes and structures column definitions
         * @param {Array} columns - The columns to process
         */
        manageColumns(columns) {
            if (!Array.isArray(columns)) return;

            columns.forEach(column => {
                if (column && typeof column.field === 'string') {
                    column.field = this.parseColumnField(column.field);
                    column.is_active = true;
                }
            });
        },
        /**
         * Converts an object to a query string
         * @param {Object} obj - The object to convert
         * @returns {string} The query string
         */
        objectToQueryString(obj) {
            if (!obj || typeof obj !== 'object') return '';
            return new URLSearchParams(obj).toString();
        },
        /**
         * Manages column visibility based on user preferences
         * @param {Array} columns - The columns to manage
         */
        manageColumnsVisibility(columns) {
            if (!Array.isArray(columns)) return;

            const filter = this.gridActions.find(action => action.filter === "hideColumn");
            if (!filter || !Array.isArray(filter.selected)) return;

            filter.selected.forEach(selectedColumn => {
                const col = columns.find(colItem => colItem.field.parent === selectedColumn);
                if (col) {
                    col.is_active = false;
                }
            });
        },
        /**
         * Handles search query changes
         * @param {string} name - The search field name
         * @param {Object} filter - The filter object
         * @param {boolean} isSelect - Whether this is a select event
         */
        onQuerySearch(name, filter, isSelect) {
            if (!filter) return;

            this.$emit("onQuerySearch", name, filter, isSelect);

            if (isSelect) {
                this.resetTable();
                this.fetchGrid(true);
            }
        },
        /**
         * Handles search input changes
         * @param {string} name - The search field name
         * @param {Object} filter - The filter object
         */
        onSearchInput(name, filter) {
            if (!name || !filter) return;

            this.resetTable();
            this.fetchGrid(true);
        },
        /**
         * Resets table state to initial values
         */
        resetTable() {
            this.filterResult.currentPage = 1;
            this.filterResult.error = null;
            this.filterResult.data = { rows: [], columns: [] };
            this.filterResult.success = false;
            this.filterResult.offset = 0;
            this.filterResult.payload.offset = this.filterResult.offset;
        },
        /**
         * Handles table sorting
         * @param {string} field - The field to sort by
         * @param {string} order - The sort order
         * @param {Event} event - The sort event
         */
        sortPressed(field, order, event) {
            if (!field || !order) return;

            const payload = this.$props.payload;
            payload.sorting = payload.sorting || {};
            payload.sorting.column = field;
            payload.sorting.type = order;

            this.filterResult.isLoadMore = true;
            this.fetchGrid(false);
        },
        /**
         * Refreshes the table data
         */
        refreshTable() {
            this.filterResult.isLoadMore = true;
            this.fetchGrid(false);
        },
        /**
         * Handles pagination page changes
         * @param {number} e - The new page number
         */
        pageChange(e) {
            console.log(e);
            if (typeof e !== 'number' || e < 1) return;

            this.filterResult.currentPage = e;
            this.filterResult.isLoadMore = true;
            this.filterResult.offset = Math.floor(this.filterResult.limit * e - this.filterResult.limit);
            console.log(this.filterResult.offset);
            this.$emit("onPageChange");
            this.fetchGrid(false);
        },
        /**
         * Initializes default filters from props
         */
        manageDefaultFilters() {
            // Create deep copy of payload for default filters
            this.defaultFilters = JSON.parse(JSON.stringify(this.$props.payload));
        },
        /**
         * Clears a specific filter and resets to default
         * @param {Object} selected - The selected filter value
         * @param {Object} filter - The filter object to clear
         */
        onClearFilter(selected, filter) {
            // Reset filter to default value
            const payload = this.$props.payload; 
            payload[filter.filter] = this.defaultFilters[filter.filter];
            filter.selected = this.defaultFilters[filter.filter];
            // Reset table and fetch new data
            this.resetTable();
            this.fetchGrid(true);
        },
        /**
         * Handles dropdown filter changes
         * @param {Object} selectOption - The selected option
         * @param {Object} filter - The filter object
         */
        onDropdownChange(selectOption, filter) {
            // Reset table and fetch new data with updated filter
            this.resetTable();
            this.fetchGrid(true);
        },
        /**
         * Toggles mobile filter visibility
         */
        onManageFilters() {
            // Toggle mobile filter state
            this.isFilterMobile = !this.isFilterMobile;
        },
        /**
         * Handles column visibility toggle in the hide columns dropdown
         * @param {Event} e - The change event
         * @param {Object} filter - The filter object
         */
        onHideColChange(e, filter) {
            // Handle column visibility change
        },
        /**
         * Handles column visibility change event
         * @param {Event} e - The change event
         * @param {Object} filter - The filter object
         */
        onHideColItemSelect(option, action) {
            // Toggle column visibility
            if (option.is_active) {
                option.is_active = false
            } else {
                option.is_active = true
            }
            // Update storage with new visibility state
            this.setStorage(true);
        },
        refineFilters(filters) {
            return filters.map(filter => {
                filter.success = false;
                return filter;
            });
        },
        /**
         * Emits an event to manage the drawer component
         * @param {Object} row - The row data
         * @param {string} col - The column identifier
         */
        manageDrawer(row, col) {
            // Emit event to parent component with row and column data
            this.$emit("manageDrawer", row, col)
        },
    }
});
