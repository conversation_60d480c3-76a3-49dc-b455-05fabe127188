<?php
namespace V3;

use WP_Error;
use WP_REST_Controller;
use WP_REST_Request;
use WP_REST_Response;
use WP_REST_Server;
use Utility; // Added for Utility::curl_request

// It's good practice to define a module title if other controllers do.
// define('V3\\CURRENT_USER_MODULE_TITLE', 'Current User Info'); // Optional: if you want a specific title

class CurrentUserController extends WP_REST_Controller
{
    public $namespace;
    public $resource_name_current_user_info;
    // private $logger; // Not used by current_user_info directly, it uses error_log

    public function __construct()
    {
        $this->namespace = 'yuno/v3';
        $this->resource_name_current_user_info = '/userinfo';
        // $this->logger = WP_Structured_Logger::get_instance(); // If needed in future
    }

    public function register_routes()
    {
        register_rest_route($this->namespace, $this->resource_name_current_user_info, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'current_user_info'),
                // 'permission_callback' => '__return_true', // Or a specific one if re-introduced
                'args' => array(),
            ),
        ));
    }

    /**
     * Getting current user info
     * Uses JWT from Authorization header to determine user ID.
     */
    public function current_user_info(WP_REST_Request $request)
    {
        date_default_timezone_set('Asia/Kolkata');
        $codes = error_code_setting();
        global $wpdb;

        // --- Start: JWT Processing directly in the function ---
        $userId = 0; // Initialize userId
        $authToken = $request->get_header('authorization');

        // Explode JWT token into parts
        $jwt_parts = explode('.', $authToken);
        $exploded_token = [
            'header'    => isset($jwt_parts[0]) ? json_decode(base64_decode($jwt_parts[0]), true) : null,
            'payload'   => isset($jwt_parts[1]) ? json_decode(base64_decode($jwt_parts[1]), true) : null,
            'signature' => $jwt_parts[2] ?? null,
        ];

        $userId = $exploded_token['payload']['userId'] ?? $exploded_token['payload']['sub'] ?? 0;

        if (empty($authToken)) {
            error_log("current_user_info: Authorization token not found. Request Headers: " . json_encode($request->get_headers()), 3, ABSPATH . "error-logs/CurrentUserController.log");
            return new WP_Error('AUTH_TOKEN_MISSING', 'Authorization token not found.', array('status' => 401));
        }

        if (stripos($authToken, 'bearer ') !== 0) {
            error_log("current_user_info: Invalid token format. Missing Bearer prefix. Token: " . $authToken, 3, ABSPATH . "error-logs/CurrentUserController.log");
            return new WP_Error('INVALID_TOKEN_FORMAT', 'Invalid token format. Missing Bearer prefix.', array('status' => 401));
        }
        list($bearer, $token) = explode(" ", $authToken, 2);

        if (empty($token)) {
            error_log("current_user_info: Token is empty after Bearer. Original AuthToken: " . $authToken, 3, ABSPATH . "error-logs/CurrentUserController.log");
            return new WP_Error('EMPTY_TOKEN', 'Token is empty after Bearer.', array('status' => 401));
        }

        // Attempt 1: Use jwt_token_validation_check
        $validation_result = jwt_token_validation_check($token);
        error_log("current_user_info: jwt_token_validation_check result: " . json_encode($validation_result), 3, ABSPATH . "error-logs/CurrentUserController.log");

        if (is_numeric($validation_result) && $validation_result > 0) {
            $userId = (int) $validation_result;
        } elseif (is_wp_error($validation_result)) {
            $error_message_text = $validation_result->get_error_message();
            error_log("current_user_info: WP_Error from jwt_token_validation_check: " . $error_message_text, 3, ABSPATH . "error-logs/CurrentUserController.log");
            // Optionally return error or try next method, or just log and proceed to token_validation_check
        } elseif ($validation_result === true) { 
            // Attempt 2: If jwt_token_validation_check returned true, try token_validation_check
            $user_id_from_tv_check = token_validation_check($token);
            error_log("current_user_info: token_validation_check result: " . json_encode($user_id_from_tv_check), 3, ABSPATH . "error-logs/CurrentUserController.log");
            if (is_numeric($user_id_from_tv_check) && $user_id_from_tv_check > 0) {
                $userId = (int) $user_id_from_tv_check;
            } elseif ($user_id_from_tv_check === true) {
                // Attempt 3: Basic JWT decode if other checks only return true
                error_log("current_user_info: Both validation checks returned true. Attempting direct payload decode.", 3, ABSPATH . "error-logs/CurrentUserController.log");
                try {
                    $token_parts = explode('.', $token);
                    if (count($token_parts) === 3) {
                        $payload = json_decode(base64_decode(strtr($token_parts[1], '-_', '+/')), true);
                        if ($payload && isset($payload['sub'])) {
                            $userId = (int) $payload['sub'];
                        } elseif ($payload && isset($payload['userId'])) {
                            $userId = (int) $payload['userId'];
                        }
                        error_log("current_user_info: Decoded payload: " . json_encode($payload) . " | UserID from payload: " . $userId, 3, ABSPATH . "error-logs/CurrentUserController.log");
                    }
                } catch (\Exception $e) {
                    error_log('current_user_info: JWT direct decoding error: ' . $e->getMessage(), 3, ABSPATH . "error-logs/CurrentUserController.log");
                }
            }
        }
        // --- End: JWT Processing directly in the function ---

        error_log("Request object for current_user_info " . date("Y-m-d H:i:s") . " === UserID determined: " . $userId . " FullRequest: " . json_encode($request->get_params()) . "\\n\\n", 3, ABSPATH . "error-logs/CurrentUserController.log");
        
        if (!($userId > 0)) {
            return new WP_Error(
                $codes["USER_ID_FAIL"]["code"] ?? 'USER_ID_PROCESSING_FAIL', 
                $codes["USER_ID_FAIL"]["message"] ?? 'Could not determine User ID from token.', 
                array('status' => $codes["USER_ID_FAIL"]["status"] ?? 401) // 401 as it is an auth issue now
            );
        }

        $userdata = get_userdata($userId);
        if (empty($userdata)) {
            return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
        }
        $user_email = $userdata->data->user_email;
        $profileUserIdReference = get_user_meta($userId, 'profile_user_id_reference', true);
        $profileURL = get_permalink($profileUserIdReference);
        $profileURLArray = explode("/", $profileURL);
        $userProfileArray = array_filter($profileURLArray);
        $userProfileName = end($userProfileArray);
        $user_nicename = $userProfileName ?? "";
        $isHomePageSignup = get_user_meta($userId, 'Home_Page_Signup_Form', true);

        $roles = $userdata->roles ? $userdata->roles : '';
        if ($userdata->roles) {
            if (in_array('um_instructor', $userdata->roles)) {
                $current_role = 'Instructor';
            } else if (in_array('um_counselor', $userdata->roles)) {
                $current_role = 'Counselor';
            } else if (in_array('um_yuno-admin', $userdata->roles)) {
                $current_role = 'yuno-admin';
            } else if (in_array('um_content-admin', $userdata->roles)) {
                $current_role = 'content-admin';
            } else if (in_array('um_yuno-category-admin', $userdata->roles)) {
                $current_role = 'yuno-category-admin';
            } else if (in_array('administrator', $userdata->roles)) {
                $current_role = 'administrator';
            } else if (in_array('um_dashboard-viewer', $userdata->roles)) {
                $current_role = 'dashboard-viewer';
            } else if (in_array('um_org-admin', $userdata->roles)) {
                $current_role = 'org-admin';
            } else {
                $current_role = 'Learner';
            }
        } else {
            return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
        }
        $signupDetail = get_user_meta($userId, 'is_signup_complete', true);
        if ($signupDetail == true || $signupDetail == "true" || $signupDetail == 1) {
            $isSignup = "completed";
            $isInvited = false;
            $withoutCategory = false;
        } else {
            if (!empty($isHomePageSignup)) {
                $withoutCategory = true;
            } else {
                $withoutCategory = false;
            }
            $isInvited = get_user_meta($userId, 'Yuno_Is_Invited', true);
            if ($isInvited == 1) {
                $isInvited = true;
            } else {
                $isInvited = false;
            }
            $isSignup = "pending";
            $classEnrollmentStatus = get_user_meta($userId, 'UNBOUNCE_CLASS_ENROLLMENT_STATUS', true);
            if ($classEnrollmentStatus == true) {
                $hasWebinarEnrolled = true;
            }
        }
        $l_category = get_user_meta($userId, 'Category_URL_For_Signup', true);
        if (is_array($l_category) && count($l_category) > 0) {
            $learnerCategory = $l_category;
        } else {
            if (empty($l_category)) {
                $learnerCategory = [];
            } else {
                if (strpos($l_category, '/') !== false) {
                    $cat = strtolower(ltrim($l_category, '/'));
                } else {
                    $cat = strtolower($l_category);
                }

                if ($cat == trim($cat) && strpos($cat, ' ') !== false) {
                    $learner_cat = str_replace(' ', '-', $cat);
                    $learnerCategory = explode(" ", $learner_cat);
                } else {
                    $learnerCategory = explode(" ", $cat);
                }
            }
        }

        $orgs_id = [
            'id' => 0,
            'name' => '',
            'image' => '',
        ];
        $org_id[] = $orgs_id; // This initializes $org_id as an array with one default element.
                               // It will be overwritten if 'um_org-admin' role is found.

        // get active category
        $active_category = get_user_meta($userId, 'active_category', true);

        // Initialize $active_category_obj with default values
        $active_category_obj = [
            'id' => 0,
            'slug' => '',
            'label' => '',
        ];
        $term = null; // Initialize $term

        if (!empty($active_category)) {
            // Assume $taxonomy is your custom taxonomy name, and $slug is the slug of the category you\'re interested in
            $taxonomy = 'course_category';

            // Get the term object by slug
            $term = get_term_by('slug', $active_category, $taxonomy);

            // Check if the term exists and there are no errors
            if ($term !== false && !is_wp_error($term)) {
                // Update $active_category_obj with the term\'s details
                $active_category_obj = [
                    'id' => (int) $term->term_id,
                    'slug' => $term->slug,
                    'label' => $term->name,
                ];
            }
        }
        $mobile = get_user_meta($userId, 'yuno_gplus_mobile', true);
        $has_phone_number = false;
        if (!empty($mobile)) {
            $has_phone_number = true;
        }
        $category_slug =  $term->slug ?? ""; // Use $term from active_category logic
        $defined_org = 0;
        $organisations = get_user_meta($userId, 'organisation', true);
        if (!empty($organisations) && is_array($organisations) && count($organisations) == 1) {
            $defined_org = $organisations[0]; 
        }
        // get active org
        $active_org = get_user_meta($userId, 'active_org', true);
        $active_org = !empty($active_org) ? (int)$active_org : $defined_org;
        if (get_user_meta($userId, 'active_org', true) <= 0 && $defined_org > 0) { // Ensure defined_org is also positive
            update_user_meta($userId, 'active_org', $defined_org);
        }
        $redirect_url = get_user_meta($userId, 'redirect_url', true) ?? "";
        $current_state = ["has_phone_number" => $has_phone_number, "category_slug" => $category_slug, "org_id" => (int)$active_org, "redirect_url" => $redirect_url];
        
        $academies = []; // Initialize academies
        $courseToMap = []; // Initialize courseToMap

        if ($current_role == "Learner") {
            $classEnrolledUser = $wpdb->get_results("SELECT * FROM {$wpdb->postmeta} WHERE meta_key = 'YunoClassPrivateLearners' and FIND_IN_SET('" . $userId . "', meta_value) > 0", ARRAY_A);
            if (count($classEnrolledUser) > 0) {
                $hasEnrolledInClassBefore = true;
            } else {
                $hasEnrolledInClassBefore = false;
            }
            // date_default_timezone_set('Asia/Kolkata'); // Already set at function start
            $currentDate = date("Y-m-d H:i:s");
            $enrollmentData = [];
            $enrollmentFalseStatusData = [];
            $data = $wpdb->get_results("SELECT id,product_db_id, enrollment_end_date, enrollment_status FROM {$wpdb->prefix}enrollment WHERE user_id='" . $userId . "'", ARRAY_A);
            foreach ($data as $key => $value) {
                if ($currentDate < $value['enrollment_end_date']) {
                    $enrollmentData[] = [
                        "course_id" => $value['product_db_id'],
                        "id" => $value['id'],
                    ];
                }
            }

            // get category belongs to logged in learner
            if (count($enrollmentData) > 0) {
                // $category = []; // This variable is assigned but not used in the original userData for learner
                // foreach ($enrollmentData as $key => $value) {
                //     $categories = wp_get_post_terms((int) $value['course_id'], 'course_category');
                //     foreach ($categories as $k => $v) {
                //         if ($v->parent == 0) {
                //             $category[] = strtolower($v->slug);
                //         }
                //     }
                // }

                $userData = [
                    "user_id" => (int) $userId,
                    "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                    "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                    'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                    'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                    'email' => $user_email,
                    'role' => $current_role,
                    'vc_permission' => empty(get_user_meta($userId, 'vc_permission', true)) ? false : true,
                    'profile_name' => $user_nicename,
                    "mobile" => $mobile,
                    "signup_category" => array_values($learnerCategory),
                    "is_signup_completed" => $isSignup,
                    "is_lead_created" => true,
                    'is_enrollment_active' => true,
                    'isInvited' => $isInvited,
                    'current_state' => $current_state,
                    'withoutCategory' => $withoutCategory,
                    'hasEnrolledInClassBefore' => $hasEnrolledInClassBefore,
                    'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
                    'active_category' => $active_category_obj,
                ];
            } else {
                $enrollmentStatus = "INACTIVE";
                $update_status = [
                    'enrollment_status' => $enrollmentStatus,
                ];
                foreach ($data as $key => $value) {
                    if ($currentDate > $value['enrollment_end_date']) {
                        $enrollmentFalseStatusData[] = [
                            "id" => $value['id'],
                        ];
                    }
                }
                if (count($enrollmentFalseStatusData) > 0) {
                    foreach ($enrollmentFalseStatusData as $key => $value) {
                        $wpdb->update($wpdb->prefix.'enrollment', $update_status, ['id' => $value['id']]);
                    }
                }

                $userData = [
                    "user_id" => (int) $userId,
                    "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                    "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                    'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                    'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                    'email' => $user_email,
                    'role' => $current_role,
                    'vc_permission' => empty(get_user_meta($userId, 'vc_permission', true)) ? false : true,
                    'profile_name' => $user_nicename,
                    "mobile" => $mobile,
                    "signup_category" => array_values($learnerCategory),
                    "is_signup_completed" => $isSignup,
                    "is_lead_created" => true,
                    'is_enrollment_active' => false,
                    'isInvited' => $isInvited,
                    'current_state' => $current_state,
                    'withoutCategory' => $withoutCategory,
                    'hasEnrolledInClassBefore' => $hasEnrolledInClassBefore,
                    'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
                    'active_category' => $active_category_obj,
                ];
            }
        } else { // Non-learner roles
            if (in_array('um_instructor', $userdata->roles)) {
                $is_completed_step_3 = get_user_meta($userId, 'is_completed_step_3', true);
                if ($is_completed_step_3) {
                    if ($is_completed_step_3 == "yes") {
                        $isSignup = "completed";
                    } else {
                        $isSignup = "pending";
                    }
                } else {
                    $isSignup = "pending";
                }
                $_courseToMap = get_user_meta($userId, 'course_to_be_map', true); // Use temp var
                if (empty($_courseToMap)) {
                    $courseToMap = [];
                } else {
                    $courseToMap = $_courseToMap;
                }
                 // Elasticsearch query to fetch unique academies
                 $curlPost = [
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "term" => [
                                    "data.details.mapped_instructor_ids" => [
                                        "value" => $userId
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "aggs" => [
                        "unique_academies" => [
                            "nested" => [
                                "path" => "data.details"
                            ],
                            "aggs" => [
                                "unique_academy_values" => [
                                    "terms" => [
                                        "field" => "data.details.academies",
                                        "size" => 10000
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "_source" => false
                ];
                $elasticsearch_url = ELASTIC_SEARCH_END_URL . '/course/_search';
                $headers = [
                    "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                    "cache-control: no-cache"
                  ];
                
                // Ensure Utility class is available or handle potential error
                if (class_exists('Utility')) {
                    $responseElastic = Utility::curl_request($elasticsearch_url, 'GET', $curlPost, $headers, ELASTIC_SEARCH_PORT);
                    $esResponse = json_decode($responseElastic['response'], true);
                    
                    $academyIds = [];
                    if (isset($esResponse['aggregations']['unique_academies']['unique_academy_values']['buckets'])) {
                        foreach ($esResponse['aggregations']['unique_academies']['unique_academy_values']['buckets'] as $bucket) {
                            $academyIds[] = $bucket['key'];
                        }
                    }

                    if(!empty($academyIds)){
                        $academyQuery = [
                            "query" => [ "terms" => [ "data.details.record_id" => $academyIds ] ]
                        ];
                        $elasticSearchUrl = ELASTIC_SEARCH_END_URL . '/academies/_search';
                        $academyResponse = Utility::curl_request($elasticSearchUrl, 'GET', $academyQuery, $headers, ELASTIC_SEARCH_PORT);
                        $academyResponseData = json_decode($academyResponse['response'], true);

                        if (isset($academyResponseData['hits']['hits'])) {
                            foreach ($academyResponseData['hits']['hits'] as $hit) {
                                $academies[] = [
                                    'id' => $hit['_source']['data']['details']['record_id'],
                                    'name' => $hit['_source']['data']['details']['academy_name'] ?? ""
                                ];
                            }
                        }
                    }
                } else {
                     error_log("current_user_info: Utility class not found for Elasticsearch request.", 3, ABSPATH . "error-logs/CurrentUserController.log");
                }

                $course_ids = $wpdb->get_results("SELECT course_id from {$wpdb->prefix}course_instructor_relationships where instructor_id=$userId", ARRAY_A);
                $vc_flag = false;
                foreach ($course_ids as $key => $value) {
                    $course_id_val = (int)$value['course_id']; // Use a different var name
                    $organization_id = get_post_meta($course_id_val, 'organization', true);
                    if ($organization_id > 0) {
                        $org_vc_app = get_post_meta($organization_id, 'org_vc_app', true);
                        if ($org_vc_app == "gmeet" || $org_vc_app == "zoom") {
                            $vc_flag = true;
                            break;
                        }
                    }
                }
                update_user_meta($userId, 'vc_permission', $vc_flag);
            } // End instructor specific block
            
            if (in_array('um_org-admin', $userdata->roles)) {
                $org_ids_meta = get_user_meta($userId, 'organisation', true);
                if (!empty($org_ids_meta) && is_array($org_ids_meta)) { // Ensure it's an array
                    $org_id = []; // Reset $org_id for org-admin
                    foreach ($org_ids_meta as $id_val) { // Use different var name
                        $logo_image = get_field('logo_image', $id_val);
                        $org_id[] = [
                            'id' => (int) $id_val,
                            'name' => get_post_meta($id_val, 'organisation_name', true),
                            'image' => $logo_image['url'] ?? "",
                        ];
                    }
                }
            }

            $has_org = false;
            // Check if $org_id was populated (either by default or by org-admin logic)
            if (is_array($org_id)) { 
                foreach ($org_id as $id_item) { // Use different var name
                    if ($id_item['id'] != 0) {
                        $has_org = true;
                        break; 
                    }
                }
            }


            $userData = [
                "user_id" => (int) $userId,
                "first_name" => get_user_meta($userId, 'yuno_first_name', true),
                "last_name" => get_user_meta($userId, 'yuno_last_name', true),
                'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
                'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
                'email' => $user_email,
                'role' => $current_role,
                'vc_permission' => empty(get_user_meta($userId, 'vc_permission', true)) ? false : true,
                'profile_name' => $user_nicename,
                "mobile" => get_user_meta($userId, 'yuno_gplus_mobile', true),
                "signup_category" => array_values($learnerCategory),
                "is_signup_completed" => $isSignup,
                "is_lead_created" => true,
                'isInvited' => $isInvited, // This was defined earlier, might be relevant
                'org_id' => $org_id, // This is now correctly populated or defaulted
                'has_org' => $has_org,
                'account_status' => get_user_meta($userId, 'account_login_status', true),
                'current_state' => $current_state,
                'withoutCategory' => $withoutCategory, // This was defined earlier
                'step_one' => empty(get_user_meta($userId, 'is_step_one_complete', true)) ? false : true,
                'courses_to_be_map' => $courseToMap, // Populated for instructors
            ];

            if (in_array('um_instructor', $userdata->roles)) {
                // Ensure 'academies' key exists, even if empty
                $userData['academies'] = !empty($academies) ? $academies : [];
            } 
        }

        if (!empty($hasWebinarEnrolled)) { // Defined earlier, relevant for learners
            $userData = array_merge($userData, ["hasWebinarEnrolled" => $hasWebinarEnrolled]);
        }

        $response = array(
            'code' => $codes["GET_SUCCESS"]["code"],
            'message' => 'Current user info found', // Changed message slightly
            "data" => $userData,
        );
        track_logs(time(), "current_user_info_controller", '', time(), "end", 'user'); // Modified log source
        return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
    }
} 