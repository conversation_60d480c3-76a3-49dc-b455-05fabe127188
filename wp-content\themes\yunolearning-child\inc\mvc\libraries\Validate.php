<?php
namespace V4;

/**
 * Validate class to handle various validation functions
 */
class Validate extends Library {
    
    /**
     * Constructor for the Validate class.
     * Initializes the response property with a new Response object.
     */
    public function __construct() {
        parent::__construct();
        
        $this->loadLibary('response');
    }

    /**
     * Flattens a multi-dimensional array into a single-dimensional array with keys indicating the original structure.
     *
     * @param array $array The array to flatten.
     * @param string $prefix Optional. The prefix to use for the keys.
     * @return array The flattened array.
     */
    public function flattenArray($array, $prefix = '') {
        $result = [];
        foreach ($array as $key => $value) {
            $newKey = $prefix . (empty($prefix) ? '' : '->') . $key; // Create flattened key
            if (is_array($value)) {
                $result = array_merge($result, $this->flattenArray($value, $newKey));
            } else {
                $result[$newKey] = $value;
            }
        }
        return $result;
    }

    /**
     * Validates the date format.
     *
     * @param string $date The date to validate.
     * @param string $format The format of the date (default: 'Y-m-d\TH:i:s.u\Z').
     * @return bool Returns true if the date is in the specified format, false otherwise.
     */
    public function validateDate($date, $format = 'Y-m-d\TH:i:s.u\Z') {
        // Create a DateTime object from the given date string
        $d = \DateTime::createFromFormat($format, $date);
        // Return true if the date matches the given format, false otherwise
        return $d && $d->format($format) === $date;
    }

    /**
     * Validates the post IDs.
     *
     * @param array $post_ids The post IDs to validate.
     * @return bool Returns true if all post IDs are valid, false otherwise.
     */
    public function validatePostId($post_ids) {
        // Array to store invalid post IDs
        $invalid_ids = [];
        
        // Loop through each post ID
        foreach ($post_ids as $post_id) {
            // Check if the post exists
            if (get_post($post_id) === null) {
                $invalid_ids[] = $post_id; // Add to invalid list if post does not exist
            }
        }

        // Return true if no invalid post IDs, false otherwise
        return empty($invalid_ids);
    }

    /**
     * Validates the required data.
     *
     * @param array $data The data to validate.
     * @param string $key The key to validate.
     * @param string $type The type of the data.
     * @param bool $required Optional. Whether the key is required (default: true).
     * @return WP_Error|bool Returns a WP_Error object if the key is not set or empty, otherwise returns the result of validateType.
     */
    public function validateRequired($data, $key, $type, $required = true) {

        // Check if the key is set and not empty
        if($type=="boolean"){
            if (!isset($data[$key])) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => "$key is required."]);
            }
        }else{
            if (!isset($data[$key]) || (empty($data[$key]) && $required === true)) {
                // Return a WP_Error object if the key is not set or empty
                return $this->response->error('PAYLOAD_FAIL', ['message' => "$key is required."]);
            }
        }
        // Validate the type of the value
        return $this->validateType($data[$key], $key, $type);
    }

    /**
     * Validates the type of a value.
     *
     * @param mixed $value The value to validate.
     * @param string $key The key of the value.
     * @param string $type The type to validate against.
     * @return WP_Error|bool Returns a WP_Error object if the value is not of the specified type, otherwise returns true.
     */
    public function validateType($value, $key, $type) {
        switch ($type) {
            case 'numeric':
                if (!is_numeric($value)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be numeric."]);
                }
                break;
            case 'integer':
                // Strictly check if the value is an integer
                if (!is_int($value) && $value != (int)$value) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be integer."]);
                }
                break;
            case 'array':
                if (!is_array($value)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be an array."]);
                }
                break;
            case 'boolean':
                if (!is_bool($value)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be a boolean."]);
                }
                break;
            case 'object':
                if (!is_object($value)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be an object."]);
                }
                break;
            case 'string':
                if (!is_string($value)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be a string."]);
                }
                break;
            case 'date':
                if (!$this->validateDate($value)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be a valid date."]);
                }
                break;
            case 'datetime':
                $dateRegex = '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:Z|[\+\-]\d{2}:\d{2})$/';
                if (preg_match($dateRegex, $value)) {
                    return true;
                } else {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be a valid datetime."]);
                }
            case 'post_id':
                if (!$this->validatePostId($value)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => "$key must be a valid post ID."]);
                }
                break;
            default:
                if (!empty($type)) {
                    // Check if the pattern(type) is a valid regex
                    if (@preg_match($type, '') === false) {
                        return $this->response->error('PAYLOAD_FAIL', ['message' => "$key contains invalid pattern to match."]);
                    }
                    if (preg_match($type, $value)) {
                        return true;
                    } else {
                        return $this->response->error('PAYLOAD_FAIL', ['message' => "$key contains invalid data type."]);
                    }
                }
                // No additional validation for strings or other types
                break;
        }
        return true;
    }

    /**
     * Validates data against a set of rules.
     * 
     * The validation rules can include optional fields and custom labels using the following format:
     * - For optional fields: Add *REQ:NOT to the rule
     * - For custom labels: Add |LABEL:Custom Name to the rule
     * 
     * Example validation rules:
     * ```php
     * $validation_checks = [
     *     // Required field with custom label
     *     "type" => '/^(SCHOOL|COLLEGE|COMMERCIAL_BUILDING)$/[LABEL:Building Type]',
     *     
     *     // Optional field with custom label
     *     "short_description" => '/^.{1,150}$/u[REQ:NOT][LABEL:Short Description]',
     *     
     *     // Required field without custom label
     *     "name" => '/^[a-zA-Z0-9_\s-]{1,255}$/',
     *     
     *     // Nested validation rules
     *     "address" => [
     *         "type" => '/^(HOME|OFFICE|OTHER)$/[LABEL:Address Type]',
     *         "address_1" => '/^.{1,255}$/[LABEL:Street Address]',
     *         "address_2" => '/^.{0,255}$/[REQ:NOT][LABEL:Additional Address]'
     *     ],
     *     
     *     // Special type validation
     *     "facilities" => [
     *         "car_parking" => [
     *             "self_parking" => 'boolean[REQ:NOT][LABEL:Self Parking]',
     *             "valet_service" => 'boolean[REQ:NOT][LABEL:Valet Service]'
     *         ]
     *     ]
     * ];
     * ```
     * 
     * Supported validation types:
     * - regex patterns (e.g., '/^[0-9]+$/')
     * - 'numeric'
     * - 'integer'
     * - 'array'
     * - 'boolean'
     * - 'object'
     * - 'string'
     * - 'date'
     * - 'datetime'
     * - 'post_id'
     * 
     * @param array $data The data to validate
     * @param array $rules The validation rules
     * @param string $path The current path in the data structure (used internally for nested validation)
     * @return array Array of validation errors, empty if validation passes
     */
    public function validateData(array $data, array $rules, $path = '') {
        $errors = [];

        if(is_array($data)){
            $assoc = array_keys($data) !== range(0, count($data) - 1);
            
            if (!$assoc) {
                $count = count($data) - 1;
                while ($count > 0) {
                    $rules[] = $rules[0];
                    $count--;
                }
            }
        }

        

        foreach ($rules as $key => $rule) {
            $currentPath = $path ? $path . "[$key]" : $key;

            if ($key === '*') {
                // This indicates we should apply these rules to all elements of a numeric-indexed array in $data
                if (!is_array($data)) {
                    $errors[$currentPath] = "'$currentPath' expected an array.";
                    continue;
                }
                foreach ($data as $idx => $subData) {
                    $res = $this->validateData($subData, $rule, $currentPath . "[$idx]");
                    $errors = array_merge($errors, $res);
                }
                continue;
            }

            // Parse the rule for optional flag and custom label
            $isOptional = false;
            $customLabel = $key;
            $actualRule = $rule;
            
            if (is_string($rule)) {
                // Check for [REQ:NOT] pattern
                if (preg_match('/\[REQ:NOT\]/', $rule)) {
                    $isOptional = true;
                    $actualRule = preg_replace('/\[REQ:NOT\]/', '', $rule);
                }
                
                // Check for [LABEL:...] pattern
                if (preg_match('/\[LABEL:([^\]]+)\]/', $rule, $matches)) {
                    $customLabel = trim($matches[1]);
                    $actualRule = preg_replace('/\[LABEL:[^\]]+\]/', '', $actualRule);
                }
                
                // Clean up any remaining separators and whitespace
                $actualRule = trim($actualRule, '[] ');
            }

            // Skip validation for optional fields that don't exist or have empty values
            if ($isOptional && (!array_key_exists($key, $data) || empty($data[$key]))) {
                continue;
            }

            if (!array_key_exists($key, $data)) {
                $errors[$currentPath] = "'$customLabel' is required.";
                continue;
            }

            if (is_array($actualRule)) {
                if (!is_array($data[$key])) {
                    $errors[$currentPath] = "'$customLabel' expected an array.";
                    continue;
                }
                $res = $this->validateData($data[$key], $actualRule, $currentPath);
                $errors = array_merge($errors, $res);
            } else {
                // It's either a regex string or a special type
                $value = $data[$key];
                
                // Single rule: could be a regex or a known type
                if (is_string($actualRule) && @preg_match($actualRule, '') !== false) {
                    // It's a valid regex rule
                    if (!preg_match($actualRule, (string)$value)) {
                        $errors[$currentPath] = "'$customLabel' contains invalid pattern to match.";
                    }
                } else {
                    // Check known types or special validations
                    switch ($actualRule) {
                        case 'numeric':
                            if (!is_numeric($value)) {
                                $errors[$currentPath] = "'$customLabel' must be numeric.";
                            }
                            break;
                        case 'integer':
                            // Check if strictly integer or integer string
                            if (!is_int($value) && (string)(int)$value !== (string)$value) {
                                $errors[$currentPath] = "'$customLabel' must be an integer.";
                            }
                            break;
                        case 'array':
                            if (!is_array($value)) {
                                $errors[$currentPath] = "'$customLabel' must be an array.";
                            }
                            break;
                        case 'boolean':
                            // Strict boolean check
                            if (!is_bool($value)) {
                                $errors[$currentPath] = "'$customLabel' must be a boolean.";
                            }
                            break;
                        case 'object':
                            if (!is_object($value)) {
                                $errors[$currentPath] = "'$customLabel' must be an object.";
                            }
                            break;
                        case 'string':
                            if (!is_string($value)) {
                                $errors[$currentPath] = "'$customLabel' must be a string.";
                            }
                            break;
                        case 'date':
                            // Validate date in Y-m-d format (or adjust as needed)
                            if (!$this->validateDate($value)) {
                                $errors[$currentPath] = "'$customLabel' must be a valid date in format YYYY-MM-DD.";
                            }
                            break;
                        case 'datetime':
                            $dateRegex = '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:Z|[\+\-]\d{2}:\d{2})$/';
                            if (!is_string($value) || !preg_match($dateRegex, $value)) {
                                $errors[$currentPath] = "'$customLabel' must be a valid datetime in ISO 8601 format.";
                            }
                            break;
                        case 'post_id':
                            if (!$this->validatePostId($value)) {
                                $errors[$currentPath] = "'$customLabel' must be a valid post ID.";
                            }
                            break;
                        default:
                            $errors[$currentPath] = "Unknown validation rule '$actualRule' for '$customLabel'.";
                    }
                }
            }
        }

        return $errors;
    }
    

    /**
     * Validate if a given string is a valid Newline Delimited JSON (NDJSON).
     * NDJSON is a format for serializing data that is similar to JSON, but with
     * each JSON object on a new line. This is useful for streaming data.
     *
     * @param string $ndjsonString The string to validate.
     * @return bool True if the string is a valid NDJSON, false otherwise.
     */
    public function isValidNdjson($ndjsonString) {
        // Split the string by new lines
        $lines = explode("\n", $ndjsonString);
    
        foreach ($lines as $line) {
            // Skip empty lines (sometimes there can be an empty line at the end)
            $line = trim($line);
            if (!empty($line)) {
                // Try to decode each line as JSON
                $decoded = json_decode($line, true);
    
                // If json_decode returns null and the line is not 'null', it's invalid JSON
                if ($decoded === null && strtolower($line) !== 'null') {
                    return false; // Invalid NDJSON
                }
            }
        }
    
        return true; // All lines are valid JSON
    }
}
