<?php

namespace V4;

/**
 * Class OrgModel
 * Handles Org-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */

class OrgModel extends Model
{
    /**
     * Constructor for OrgModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema', 'schema');
        $this->loadLibary('locale');
        $this->loadLibary('common');
        $this->loadLibary('dateTime', 'dt');
    }

    /**
     * Retrieves the total number of enrollments for a given organization.
     *
     * This function queries Elasticsearch to fetch the count of enrollments based on the organization ID. 
     * It ensures that the enrollments have a valid enrollment status and aggregates the result.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $orgId The organization ID or an array of organization IDs.
     * @return array An associative array containing the total number of enrollments.
     * @throws Exception If an error occurs while querying Elasticsearch or if the response is invalid.
     * <AUTHOR> Name]
     */
    public function getOrgEnrollmentsCount($orgId)
    {
        // Validate and format orgId array
        if (!is_array($orgId)) {
            $orgId = [$orgId];
        }

        // Build the optimized query with aggregations
        $query = [
            //"size" => 0, // No need to return full documents
            "query" => [
                "bool" => [
                    "must" => [
                        [
                            "terms" => [
                                "data.details.org_admin.id" => array_map('intval', (array) $orgId) // Ensure it's an array of integers
                            ]
                        ]
                    ],
                    "filter" => [
                        ["exists" => ["field" => "data.details.enrollment_status.keyword"]] // Ensure status exists
                    ]
                ]
            ]
        ];


        // Send the query to Elasticsearch
        $response = $this->es->count('batchenrollmentevent', $query);

        if($response['status_code'] == 200){
            // Extract total count from response
            return  $response['body']['count'] ?? 0;
        }

        return 0;

    }

    /**
     * Retrieves the total number of academies for a given organization.
     *
     * This function queries Elasticsearch to fetch the count of academies based on the organization ID.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $orgId The organization ID or an array of organization IDs.
     * @return array An associative array containing the total number of academies.
     * @throws Exception If an error occurs while querying Elasticsearch or if the response is invalid.
     * <AUTHOR>
     */
    public function getOrgAcademiesCount($orgId)
    {
        // Validate and format orgId array
        if (!is_array($orgId)) {
            $orgId = [$orgId];
        }

        // Build the query
        $query = [
            "query" => [
                "bool" => [
                    "must" => [
                        [
                            "terms" => [
                                "data.details.org_id" => array_map('intval', (array) $orgId) // Ensure it's an array of integers
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Send the query to Elasticsearch
        $response = $this->es->count('academies', $query);
        
        if($response['status_code'] == 200){
            // Extract total count from response
            return $response['body']['count'] ?? 0;
        }

        return 0;
    }
    
    /**
     * Retrieves detailed information about an organization, including its enrollment data, features, subscription, and more.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query The organization identifier (ID) or an array containing query parameters.
     * @param array $filter Optional filters for debugging and validation.
     * @return array JSON response containing organization details.
     * @throws Exception If an error occurs while fetching or processing data from Elasticsearch or WordPress.
     * <AUTHOR>
     */
    public function getOrganization($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {

            if(ynSchemaType($filter, 'Organization_Minimal')){
                $qryStr['_source'] = implode(',', [
                    'data.details.record_id',
                    'data.details.organisation_name'
                ]);
            }else{
                $qryStr['_source'] = implode(',', [
                    'data.details.record_id',
                    'data.details.organisation_name',
                    'data.details.created_at',
                    'data.details.excerpt',
                    'data.details.description',
                    'data.details.org_url',
                    'data.details.logo_image',
                    'data.details.fav_icon',
                    'data.details.color_theme',
                    'data.details.number_of_employees',
                    'data.details.industry',
                    'data.details.sub_industry',
                    'data.details.category',
                    'data.details.user_id',
                    'data.details.yuno_academy_subscription',
                    'data.details.prasar',
                    'data.details.org_vc_app'
                ]);
            }

            if(isset($query['_source']) && is_array($query['_source']) && !empty($query['_source'])){
                $qryStr['_source'] = implode(',', $query['_source']);
            }
            // Fetch organization data from your data source (like a database or Elasticsearch)
            $organizationDataResponse = $this->es->read('org', 'org-' . $query['id'], $qryStr);
        }

        if ($organizationDataResponse['status_code'] == 200) {
            $organization = $organizationDataResponse['body']['_source']['data']['details'];

            // Build the structured response
            $orgResponse = array(
                'id' =>  $organization['record_id'] ?? '',  // Organization ID
                'name' =>  $organization['organisation_name'] ?? '',  // Organization name
                'created_at' =>  [
                    'time' => isset($organization['created_at'])
                                ? $this->dt->convertToActiveDT($organization['created_at'])
                                : '0000-00-00T00:00:00Z',
                    'timezone' => $this->locale->activeTimezone() ?? ''
                ],  // Creation timestamp
                'short_description' =>  $organization['excerpt'] ?? '',  // Short description
                'long_description' =>  $organization['description'] ?? '',  // Long description
                'url' =>  $organization['org_url'] ?? '',  // URL of the organization
                // Logo images
                'logo' =>  [
                    'url' =>  $organization['logo_image'] ?? '',
                    "alt_text" => !empty($organization['logo_image']) ? $this->common->imgAltTextFromUrl($organization['logo_image']) : ''
                ],
                'fav_icon' => [
                    'url' =>  $organization['fav_icon'] ?? '',
                    "alt_text" => !empty($organization['fav_icon']) ? $this->common->imgAltTextFromUrl($organization['fav_icon']) : ''
                ],
                'theme' => [
                    'font_family' => $organization['color_theme']['font'] ?? '',
                    'background_color' => $organization['color_theme']['background_color'] ?? '',
                    'primary_color' => $organization['color_theme']['primary_color'] ?? $organization['color_theme']['theme_color']['primary'] ?? '',
                ],
                'number_of_employees' =>  $organization['number_of_employees'] ?? 0,  // Number of employees
                // Industry data
                'industry' =>  [
                    'type' => $organization['industry'] ?? '', // ['MANUFACTURING', 'TECHNOLOGY', 'CREATIVESERVICES', 'PROFESSIONALSERVICES']
                    'slug' => $organization['industry'] ?? '',
                    'name' => ucfirst($organization['industry'] ?? ''),
                    'sub_industry' => !empty($organization['sub_industry']) && is_array($organization['sub_industry']) ? array_map(function ($sub_industry) {
                        return [
                            'type' => $sub_industry,
                            'slug' => $sub_industry,
                            'name' => ucfirst($sub_industry), // Capitalize the first letter
                        ];
                    }, $organization['sub_industry']) : false,
                ],

                // Category data (array of categories the organization belongs to)
                'subject_categories' => !empty($organization['category']) && is_array($organization['category']) ? array_map(function ($category) {
                    return $this->load->subData("category", "getCategory", $category['id'], ['schema' => 'Category_Minimal', 'noResponse' => ['id' => 0, 'name' => '', 'slug' => '']]);
                }, $organization['category']) : false,
                // Admins
                'admins' => !empty($organization['user_id']) && is_array($organization['user_id']) ? array_map(function ($admin) {
                    return $this->load->subData("user", "getUser", $admin, ['schema' => 'User_Minimal', 'noResponse' => ['id' => 0, 'role' => [''], 'full_name' => '', 'image_url' => '']]);
                }, $organization['user_id']) : false,
                'enrollments' => $this->load->subData("org", "getOrgEnrollmentsCount", $organization['record_id'] ?? 0),
                'academies' => $this->load->subData("org", "getOrgAcademiesCount", $organization['record_id'] ?? 0),
                // Yuno academy subscription
                'yuno_academy_subscription' =>  [
                    'id' => $organization['yuno_academy_subscription']['plan_id'] ?? '', // Unique ID of plan; example: 1
                    'plan_name' => $organization['yuno_academy_subscription']['plan_label'] ?? '', // label of plan; enum: "BASIC, CORE, PRO"; example: Basic; default: BASIC
                    'is_active' => ($organization['yuno_academy_subscription']['plan_status'] ?? '') == "active" ? true : false, // status of plan; example: active; default: true
                    'billing' => [ // time interval i.e. frequency; example: month
                        'frequency' => $organization['yuno_academy_subscription']['billing_format']['frequency'] ?? '', // frequency of billing: monthly, quarterly, or annually; enum: "MONTHLY, QUARTERLY, ANNUALLY"; example: MONTHLY
                        'country' => [
                            'id' => 0,
                            'name' => $organization['yuno_academy_subscription']['billing_format']['country_name'] ?? '',
                            'code' => $organization['yuno_academy_subscription']['billing_format']['country_code'] ?? ''
                        ], // The customer's country
                        'currency' => [
                            'code' => $organization['yuno_academy_subscription']['billing_format']['currency'] ?? '',
                            'name' => $organization['yuno_academy_subscription']['billing_format']['currency_name'] ?? '',
                            'symbol' => $organization['yuno_academy_subscription']['billing_format']['currency_symbol'] ?? '',
                            'symbol_html' => $organization['yuno_academy_subscription']['billing_format']['currency_symbol_html'] ?? ''
                        ], // The currency in which the customer is billed
                        'inclusive_tax' => 0.0,
                        'exclusive_tax' => 0.0,
                        'tax' => [
                            'type' => '', //type of tax like GST
                            'label' => '', //label of tax
                            'percentage' => '',  //percentage of tax
                            'amount' => ''   //amount of tax
                        ], // Tax associated with the billed amount
                        'payment_gateway' => [ // gateway info for payments
                            'gateway' => '', // Name of payment gateway, e.g., Stripe, Razorpay; enum: "Razorpay, Stripe"; example: Razorpay
                            'subscription_plan_id' => '', // Subscription plan ID in the payment gateway database
                        ],
                    ],
                    'has_access' => [ // The list of platform capabilities that the Academy is entitled to
                        'number_of_instructors' =>  $organization['yuno_academy_subscription']['max_instructors'] ?? '', // max number of instructors allowed to be mapped with courses of academy; example: 1
                        'api' =>  $organization['yuno_academy_subscription']['api_access'] ?? '', // API access to academy; default: true
                        'prasar' =>  $organization['yuno_academy_subscription']['prasar'] ?? '', // stands for white label app; default: true
                    ]
                ],
                'prasar' => [
                    'prasar_url' => $organization['prasar']['prasar_url'] ?? '',
                    'privacy_policy_url' => $organization['prasar']['mobile-app']['privacy_policy_url'] ?? '',
                    'terms_and_conditions_url' => get_post_meta($organization['record_id'] ?? 0, 'terms_and_conditions_url', true) ?? '',
                    'features' => [
                        'has_courses' => $prasar['prasar']['live_classes_included'] ?? false,
                        'has_study_material' => $organization['prasar']['resources']['study_material'] ?? false,
                        'has_videos' => $organization['prasar']['resources']['videos'] ?? false,
                        'has_practice_tests' => $organization['prasar']['resources']['practice_tests'] ?? false
                    ],
                    'only_my_org_courses' => get_post_meta($organization['record_id'], 'only_my_org_courses', true) ?? false,
                    'subject_categories' => !empty($organization['prasar_subject_categories']) && is_array($organization['prasar_subject_categories']) ? array_map(function ($category) {
                        return $this->load->subData("category", "getCategory", $category['id'], ['schema' => 'Category_Minimal']);
                    }, $organization['prasar_subject_categories']) : false,
                    'mobile_app' => [
                        'app_name' => $organization['prasar']['mobile-app']['app_name'] ?? '',
                        'app_short_description' => $organization['prasar']['mobile-app']['app_short_description'] ?? '',
                        'app_long_description' => $organization['prasar']['mobile-app']['app_long_description'] ?? '',
                        'video_url' => $organization['prasar']['mobile-app']['video_url'] ?? '',
                        'app_developer' => [
                            'email' => $organization['prasar']['mobile-app']['app_developer']['email'] ?? '',
                            'phone' => $organization['prasar']['mobile-app']['app_developer']['phone'] ?? '',
                            'website' => $organization['prasar']['mobile-app']['app_developer']['website'] ?? ''
                        ]
                    ]
                ],
                'virtual_classroom' => (function($plateform) {
                    $favIcon = [
                        'url'      => '',
                        'alt_text' => ''
                    ];
                
                    // Define the mapping for platform-specific icons
                    $platformIcons = [
                        'gmeet' => "images/googleMeetIcon.png",
                        'zoom'  => "images/zoomIcon.png"
                    ];
                
                    // Check if the platform is valid and get the icon URL if it exists
                    if (isset($platformIcons[$plateform])) {
                        $vcFavIcon = $platformIcons[$plateform];
                        $url = ynAssetURL($vcFavIcon);
                        $favIcon = [
                            'url'      => $url,
                            'alt_text' => !empty($url) ? $this->common->imgAltTextFromUrl($url) : ''
                        ];
                    }
                
                    return [
                        'platform'     => $plateform,
                        'name'         => $plateform == "zoom" ? ucfirst($plateform) : ($plateform == "gmeet" ? "Google Meet" : ""),
                        'fav_icon_url' => $favIcon
                    ];
                })($organization['org_vc_app'] ?? '')
            );
            
            // Validate the response against the Organization schema
            $this->schema->validate($orgResponse, 'Organization', $filter);
            return $orgResponse;
        }
        return false;
    }

    public function getOrganizationMinimal($query, $filter = []){
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch organization data from your data source (like a database or Elasticsearch)
            $organizationDataResponse = $this->es->read('org', 'org-' . $query['id']);
        }

        if ($organizationDataResponse['status_code'] == 200) {
            $organization = $organizationDataResponse['body']['_source']['data']['details'];

            // Build the structured response
            $orgResponse = array(
                'id' =>  $organization['record_id'] ?? '',  // Organization ID
                'name' =>  $organization['organisation_name'] ?? '',  // Organization name
                'logo' =>  [
                    'url' =>  $organization['logo_image'] ?? '',
                    "alt_text" => $this->common->imgAltTextFromUrl($organization['logo_image'])
                ],
                'fav_icon' => [
                    'url' =>  $organization['fav_icon'] ?? '',
                    "alt_text" => $this->common->imgAltTextFromUrl($organization['fav_icon'])
                ],
            );
            
            // Validate the response against the Organization schema
            $this->schema->validate($orgResponse, 'Organization_Minimal', $filter);
            return $orgResponse;
        }
        return false;
    }

    /**
     * Updates the virtual classroom settings (app) for a given organization.
     *
     * @since 1.0.0
     * @access public
     * @param int $orgId The ID of the organization whose virtual classroom settings are to be updated.
     * @param array $data Array containing the new virtual classroom app data.
     * @return bool Returns true if the settings were updated successfully, false otherwise.
     * @throws Exception If the post ID is invalid or if an error occurs during the update.
     * <AUTHOR> Name]
     */
    function updOrgVCSettings($orgId, $data)
    {
        $post = get_post($orgId);
        if (! $post || 'org' !== get_post_type($orgId)) {
            return false; // Invalid post ID or not a place
        }
        update_post_meta($orgId, 'org_vc_app', $data['app']);

        $query = [
            "data" => [
                "details" => [
                    "org_vc_app" => $data['app']
                ]
            ]
        ];

        $orgDataResponse = $this->es->update('org', 'org-' . $orgId, $query);

        if ($orgDataResponse['status_code'] == 200) {
            return true;
        }

        return false;
    }

    /**
     * Generates organization filters based on the user's role.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The selected organization ID (if any).
     * @return array|false Returns an array containing organization filter data or false if no data found.
     * <AUTHOR>
     */
    public function generateEnrollmentOrgFilters($userId, $orgId)
    {
        return [
            'filter' => 'org_id',
            'title' => 'Organization',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Organization',
            'ui_control_type' => 'dynamic_dropdown',
            'selected' => $orgId,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []
        ];
    }
    public function generateEnrollmentOrgFiltersOld($userId, $orgId)
    {
        $this->loadModel('user');
        $row = [
            'filter' => 'organization',
            'title' => 'Organization',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Organization',
            'ui_control_type' => 'dropdown',
            'selected' => $orgId,
            'items' => []
        ];

        $role = $this->userModel->getUserRole($userId);

        // Query conditions based on role
        $queryConditions = [];
        if ($role === 'yuno-admin') {
            // Yuno Admin: Fetch all organizations
            $queryConditions = [];
        } elseif ($role === 'org-admin' && $orgId > 0) {
            // Org Admin: Fetch only their organization
            $queryConditions = [['term' => ['data.details.org_id' => $orgId]]];
        }

        // Build Elasticsearch query
        $customQuery = [
            'query' => ['bool' => ['must' => $queryConditions]]
        ];

        // Fetch organizations from Elasticsearch
        $orgRecords = $this->es->customQuery($customQuery, 'org', []);

        if ($orgRecords['status_code'] == 200) {
            $organizations = $orgRecords['body']['hits']['hits'];
        } else {
            return false;
        }

        // Populate organization filter list
        foreach ($organizations as $organization) {
            $details = $organization['_source']['data']['details'] ?? [];
            $organizationId = $details['record_id'] ?? 0;
            $organizationName = $details['organisation_name'] ?? '';

            if ($organizationId) {
                $row['items'][] = [
                    'id' => $organizationId,
                    'label' => $organizationName,
                    'filter' => 'organization',
                ];
            }
        }

        return $row;
    }
}
