<?php
namespace V4;

class LearnerFilter extends Filter
{
    protected $es;

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
    }

    private function formatFilterItem($id, $label, $filterName) {
        return [
            'id' => is_numeric($id) ? (int)$id : $id,
            'label' => (string)$label,
            'slug' => sanitize_title((string)$label),
            'filter' => $filterName,
            'sub_title' => '',
            'interval' => '',
            'parent_id' => 0,
            'icon' => [
                'type' => '',
                'code' => ''
            ]
        ];
    }

    public function generateAcademyFilterData($request, $limit, $offset)
    {
        $filterName = 'academies';
        $params = array_change_key_case($request->get_query_params(), CASE_LOWER);
        $selected = $params[$filterName] ?? [];
        $result = $this->getAcademyFilterData($limit, $offset, $filterName);

        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Academy',
            'placeholder' => 'Select Academy',
            'tooltip' => 'Filter learners by the academy they belong to',
            'filter' => $filterName,
            'multiple' => true,
            'selected' => $this->parseIdList($selected),
            'items' => $result['items'],
            'count' => $result['count'],
            'current' => '',
            'loading' => false,
            'success' => $result['count'] > 0,
        ];
    }

    public function generateCourseFilterData($request, $limit, $offset)
    {
        $filterName = 'courses';
        $params = array_change_key_case($request->get_query_params(), CASE_LOWER);
        $selected = $params[$filterName] ?? [];
        $result = $this->getCourseFilterData($limit, $offset, $filterName);

        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Course',
            'placeholder' => 'Select Course',
            'tooltip' => 'Filter learners by the course they are enrolled in',
            'filter' => $filterName,
            'multiple' => true,
            'selected' => $this->parseIdList($selected),
            'items' => $result['items'],
            'count' => $result['count'],
            'current' => '',
            'loading' => false,
            'success' => $result['count'] > 0,
        ];
    }

    public function generateBatchFilterData($request, $limit, $offset)
    {
        $filterName = 'batches';
        $params = array_change_key_case($request->get_query_params(), CASE_LOWER);
        $selected = $params[$filterName] ?? [];
        $result = $this->getBatchFilterData($limit, $offset, $filterName);

        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Batch',
            'placeholder' => 'Select Batch',
            'tooltip' => 'Filter learners by the batch they belong to',
            'filter' => $filterName,
            'multiple' => true,
            'selected' => $this->parseIdList($selected),
            'items' => $result['items'],
            'count' => $result['count'],
            'current' => '',
            'loading' => false,
            'success' => $result['count'] > 0,
        ];
    }

    public function generateClassFilterData($request, $limit, $offset)
    {
        $filterName = 'classes';
        $params = array_change_key_case($request->get_query_params(), CASE_LOWER);
        $selected = $params[$filterName] ?? [];
        $result = $this->getClassFilterData($limit, $offset, $filterName);
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Class',
            'placeholder' => 'Select Class',
            'tooltip' => 'Filter learners by the class they are enrolled in',
            'filter' => $filterName,
            'multiple' => true,
            'selected' => $this->parseIdList($selected),
            'items' => $result['items'],
            'count' => $result['count'],
            'current' => '',
            'loading' => false,
            'success' => $result['count'] > 0,
        ];
    }

    public function generateOrganizationFilterData($request, $limit, $offset)
    {
        $filterName = 'org';
        $params = array_change_key_case($request->get_query_params(), CASE_LOWER);
        $selected = $params[$filterName] ?? [];
        $result = $this->getOrganizationFilterData($limit, $offset, $filterName);
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Organization',
            'placeholder' => 'Select Organization',
            'tooltip' => 'Filter learners by the organization they belong to',
            'filter' => $filterName,
            'multiple' => false,
            'selected' => $this->parseIdList($selected, false),
            'items' => $result['items'],
            'count' => $result['count'],
            'current' => '',
            'loading' => false,
            'success' => $result['count'] > 0,
        ];
    }

    public function generateInstructorFilterData($request, $limit, $offset)
    {
        $filterName = 'instructors';
        $params = array_change_key_case($request->get_query_params(), CASE_LOWER);
        $selected = $params[$filterName] ?? [];
        $result = $this->getInstructorFilterData($limit, $offset, $filterName);
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Instructors',
            'placeholder' => 'Select Instructors',
            'tooltip' => 'Filter learners by their instructors',
            'filter' => $filterName,
            'multiple' => true,
            'selected' => $this->parseIdList($selected, false),
            'items' => $result['items'],
            'count' => $result['count'],
            'current' => '',
            'loading' => false,
            'success' => $result['count'] > 0,
        ];
    }

    private function fetchAndParseResults($query, $index, $id_field, $label_field, $limit, $offset, $validate_as_wp_user = false, $filterName = '') {
        // Fetch a large number of results to perform manual, robust pagination
        unset($query['from']);
        $query['size'] = 10000; 
    
        $results = $this->es->customQuery($query, $index);
        $valid_items = [];
    
        if (!empty($results['body']['hits']['hits'])) {
            // First, iterate over all hits to build a clean list of valid items
            foreach ($results['body']['hits']['hits'] as $hit) {
                $details = $hit['_source']['data']['details'];
                
                $label_data = '';
                if (is_array($label_field)) {
                    $label_parts = [];
                    foreach ($label_field as $field) {
                        $label_parts[] = $details[$field] ?? '';
                    }
                    $label_data = trim(implode(' ', $label_parts));
                } else if (strpos($label_field, '.') !== false) {
                    $current_level = $details;
                    $keys = explode('.', $label_field);
                    foreach($keys as $key) {
                        if (is_array($current_level) && isset($current_level[$key])) {
                            $current_level = $current_level[$key];
                        } else {
                            $current_level = '';
                            break;
                        }
                    }
                    $label_data = $current_level;
                } else {
                    $label_data = $details[$label_field] ?? '';
                }
    
                // Only add items that have both an ID and a label
                if (!empty($details[$id_field]) && !empty($label_data)) {
                    $valid_items[] = $this->formatFilterItem($details[$id_field], $label_data, $filterName);
                }
            }
        }
        
        if ($validate_as_wp_user && !empty($valid_items)) {
            $userIds = array_column($valid_items, 'id');
            $users = get_users(['include' => $userIds, 'fields' => 'ID']);
            $validUserIds = array_flip($users);
            $valid_items = array_filter($valid_items, fn ($item) => isset($validUserIds[$item['id']]));
            $valid_items = array_values($valid_items); // Re-index the array after filtering
        }

        // The total count is the count of the clean, valid items.
        $total_valid_count = count($valid_items);

        // Now, paginate the clean list of valid items
        $paginated_items = array_slice($valid_items, $offset, $limit);
    
        // Return the paginated items and the total count of VALID items
        return ['items' => $paginated_items, 'count' => $total_valid_count];
    }

    private function getAcademyFilterData($limit, $offset, $filterName) {
        $query = [
            '_source' => ['data.details.id', 'data.details.academy_name'],
            'query' => ['match_all' => new \stdClass()],
            'track_total_hits' => true
        ];
        return $this->fetchAndParseResults($query, 'academies', 'id', 'academy_name', $limit, $offset, false, $filterName);
    }

    private function getCourseFilterData($limit, $offset, $filterName) {
        $query = [
            '_source' => ['data.details.record_id', 'data.details.title'],
            'query' => ['match_all' => new \stdClass()],
            'track_total_hits' => true
        ];
        return $this->fetchAndParseResults($query, 'course', 'record_id', 'title', $limit, $offset, false, $filterName);
    }

    private function getBatchFilterData($limit, $offset, $filterName) {
        $query = [
            'size' => 0,
            'aggs' => [
                'batches' => [
                    'nested' => ['path' => 'data.details.batch_details'],
                    'aggs' => [
                        'unique_batches' => [
                            'terms' => [
                                'field' => 'data.details.batch_details.batch_id',
                                'size' => 10000
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $results = $this->es->customQuery($query, 'course');
        $items = [];
        $count = 0;
        if (!empty($results['body']['aggregations']['batches']['unique_batches']['buckets'])) {
            $all_batches = $results['body']['aggregations']['batches']['unique_batches']['buckets'];
            
            $count = count($all_batches);
            $paginated_batches = array_slice($all_batches, $offset, $limit);

            foreach($paginated_batches as $bucket) {
                $batch_id = $bucket['key'];
                $batch_name = 'Batch ' . $batch_id;

                $items[] = $this->formatFilterItem($batch_id, $batch_name, $filterName);
            }
        }
        return ['items' => $items, 'count' => $count];
    }

    private function getClassFilterData($limit, $offset, $filterName) {
         $query = [
            '_source' => ['data.details.record_id', 'data.details.title'],
            'query' => ['match_all' => new \stdClass()],
            'track_total_hits' => true
        ];
        return $this->fetchAndParseResults($query, 'privateclass', 'record_id', 'title', $limit, $offset, false, $filterName);
    }

    private function getOrganizationFilterData($limit, $offset, $filterName) {
        $query = [
            '_source' => ['data.details.record_id', 'data.details.organisation_name'],
            'query' => ['match_all' => new \stdClass()],
            'track_total_hits' => true,
            'size' => 10000 
        ];
    
        $results = $this->es->customQuery($query, 'org');
        $all_items = [];
    
        if (!empty($results['body']['hits']['hits'])) {
            foreach ($results['body']['hits']['hits'] as $hit) {
                $details = $hit['_source']['data']['details'];
                if (!empty($details['record_id']) && !empty($details['organisation_name'])) {
                    $all_items[] = $this->formatFilterItem($details['record_id'], $details['organisation_name'], $filterName);
                }
            }
        }
    
        $total_count = count($all_items);
        $paginated_items = array_slice($all_items, $offset, $limit);
    
        return ['items' => $paginated_items, 'count' => $total_count];
    }

    private function getInstructorFilterData($limit, $offset, $filterName) {
        $query = [
            '_source' => ['data.details.user_id', 'data.details.first_name', 'data.details.last_name'],
            'query' => ['match_all' => new \stdClass()],
            'track_total_hits' => true
        ];
        return $this->fetchAndParseResults($query, 'instructorsignedup', 'user_id', ['first_name', 'last_name'], $limit, $offset, true, $filterName);
    }

    private function parseIdList($value, $asInt = true): array
    {
        if (is_array($value)) {
            $value = array_filter(array_map('trim', $value));
            return $asInt ? array_map('intval', $value) : $value;
        }
        if (is_string($value) && !empty($value)) {
            $ids = array_filter(array_map('trim', explode(',', trim($value, '[] '))));
            return $asInt ? array_map('intval', $ids) : $ids;
        }
        return [];
    }

    public function getLearnerListColumns()
    {
        return [
            ['field' => 'name', 'label' => 'Name', 'sortable' => true],
            ['field' => 'signedup', 'label' => 'Signed up', 'sortable' => true],
            ['field' => 'can_schedule_class', 'label' => 'VC Status', 'sortable' => true],
            ['field' => 'email', 'label' => 'Email', 'sortable' => true],
            ['field' => 'phone', 'label' => 'Phone', 'sortable' => true],
            ['field' => 'native_lang', 'label' => 'Native Lang', 'sortable' => true],
            ['field' => 'active_batches', 'label' => 'Active Batches', 'sortable' => true],
            ['field' => 'mapped_courses_count', 'label' => 'Mapped Courses', 'sortable' => true],
            ['field' => 'learner_avg_class_rating', 'label' => 'Learner avg. class rating', 'sortable' => true],
            ['field' => 'reviews_count', 'label' => 'Review', 'sortable' => true],
            ['field' => 'last_login', 'label' => 'Last Login', 'sortable' => true],
            ['field' => 'location', 'label' => 'Location', 'sortable' => true],
        ];
    }
} 