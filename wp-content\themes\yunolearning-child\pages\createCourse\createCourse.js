window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 15 numbers required ",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-create-course', {
    template: `
        <yuno-page-grid
            :authorizedRoles="authorizedRoles"
            @onUserInfo="onUserInfo"
            :hasSearchBar="false"
            :hasLHSMenu="false"
        >
            <template v-slot:main>
                <div class="container">
                    <is-in-progress
                        v-if="isInProgress"
                    >
                    </is-in-progress>
                    <template v-else>
                        <figure class="infiniteSpinner" v-if="subform2.loading">
                            <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                        </figure>
                        <template v-else-if="!subform2.loading && subform2.error === null">
                            <div class="mainHeader">
                                <div class="block">
                                    <h1 class="pageTitle headline5">{{ pageHeader.title }}</h1>
                                </div>
                            </div>
                            <b-steps
                                v-model="activeStep"
                                position="is-right"
                                label-position="right"
                                size="is-small"
                                vertical
                                destroy-on-hide
                                :has-navigation="true"
                                class="createCourseSteps"
                            >
                                <template v-for="step in filterResult.tabs">
                                    <b-step-item :step="step.step" :label="step.label">
                                        <component
                                            :is="getStepComponent(step.step)"
                                            @onStepComplete="handleStepComplete"
                                            :data="step"
                                        />
                                    </b-step-item>
                                </template>
                                <template #navigation="{previous, next}">
                                    <div class="ctaWrapper">
                                        <b-button
                                            outlined
                                            :disabled="previous.disabled"
                                            @click.prevent="previous.action"
                                            class="yunoSecondaryCTA"
                                        >
                                            Previous
                                        </b-button>
                                        <b-button
                                            outlined
                                            :disabled="isNextDisabled"
                                            @click.prevent="handleNext(next)"
                                            :loading="form.isLoading"
                                            class="yunoPrimaryCTA"
                                        >
                                            {{ isLastStep ? 'Submit' : 'Next' }}
                                        </b-button>
                                    </div>
                                </template>
                            </b-steps>
                        </template>
                        <template v-else>
                            <div class="isInProgress container">
                                <div>
                                    <yuno-empty-state-v6
                                        :errorMessage="subform2.errorData"
                                    >
                                    </yuno-empty-state-v6>
                                </div>
                            </div>
                        </template>
                    </template>
                </div>
            </template>
        </yuno-page-grid>
    `,
    data() {
        return {
            authorizedRoles: [
                "yuno-admin",
                "org-admin"
            ],
            pageHeader: {
                title: "Create Course",
            },
            activeStep: 0,
            isInProgress: false,
            statusFetchInterval: 5000,
            waitingTime: 240000,
            statusCheckTimer: null,
            startTime: null
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'header',
            'userProfile',
            'userRole',
            'footer',
            'loader',
            'filterResult',
            'form',
            'subform',
            'subform2'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
        isLastStep() {
            return this.activeStep + 1 === this.filterResult.tabs.length;
        },
        isNextDisabled() {
            return !this.filterResult.tabs[this.activeStep]?.isCompleted;
        },
        currentStep() {
            return this.filterResult.tabs[this.activeStep];
        }
    },
    created() {
        // this.setupSteps();
    },
    beforeDestroy() {
        this.cleanupTimers();
        window.removeEventListener('beforeunload', this.handleBeforeUnload);
    },
    mounted() {
        window.addEventListener('beforeunload', this.handleBeforeUnload);
    },
    methods: {
        /**
         * Maps step IDs to their corresponding Vue components
         * @param {number} step - The step number (1-based index)
         * @returns {string|null} The component name to render for the given step
         */
        getStepComponent(step) {
            // Map step IDs to their corresponding components
            const componentMap = {
                'academy': 'choose-academy',
                'enrollment': 'choose-enrollment-type',
                'subject': 'subject-category',
                'objectives': 'learning-objectives',
                'duration': 'course-duration',
                'personalization': 'personalization',
                'why-enroll': 'why-enroll'
            };

            // Get the current step from filterResult.tabs
            const currentStep = this.filterResult.tabs[step - 1];
            if (!currentStep) return null;

            // Return the component based on the step's ID
            return componentMap[currentStep.id];
        },
        /**
         * Handles the completion of a step in the course creation process
         * @param {number} stepIndex - The index of the completed step
         */
        handleStepComplete(stepIndex) {
            this.updateStepCompletion(stepIndex);
        },
        /**
         * Handles the next button click in the step navigation
         * If on the last step, submits the form, otherwise moves to next step
         * @param {Object} next - The next step navigation object
         */
        handleNext(next) {
            if (this.isLastStep) {
                this.submitForm();
            } else {
                next.action();
            }
        },
        /**
         * Cleans up any active timers to prevent memory leaks
         * Called during component destruction and status check completion
         */
        cleanupTimers() {
            if (this.statusCheckTimer) {
                clearInterval(this.statusCheckTimer);
                this.statusCheckTimer = null;
            }
        },
        /**
         * Dispatches a Vuex action with the given options
         * @param {string} action - The Vuex action to dispatch
         * @param {Object} options - The options to pass to the action
         */
        dispatchData(action, options) {
            this.$store.dispatch(action, options);
        },
        /**
         * Shows a toast message using Buefy toast component
         * @param {string} message - The message to display in the toast
         */
        showToastMessage(message) {
            this.$buefy.toast.open({
                duration: 5000,
                message,
                position: 'is-bottom'
            });
        },
        /**
         * Updates the completion status of a specific step
         * @param {number} stepIndex - The index of the step to mark as completed
         */
        updateStepCompletion(stepIndex) {
            const step = this.filterResult.tabs[stepIndex];
            if (step) {
                step.isCompleted = true;
            }
        },
        /**
         * Sets up the steps for the course creation process based on user role and academy count
         * @param {string} role - The user's role ('org-admin' or other)
         * @param {boolean} hasMultipleAcademies - Whether the user has access to multiple academies
         */
        setupSteps(role, hasMultipleAcademies, academyId) {
            const baseSteps = [
                { id: 'enrollment', label: "Enrollment type", isCompleted: false },
                { id: 'subject', label: "Subject category", isCompleted: false },
                { id: 'objectives', label: "Learning objectives", isCompleted: false },
                { id: 'duration', label: "Course Duration", isCompleted: false },
                { id: 'personalization', label: "Personalization", isCompleted: false },
                { id: 'why-enroll', label: "Why Enroll", isCompleted: false }
            ];

            const academyStep = { id: 'academy', label: "Choose Academy", isCompleted: false };
            
            // Add academy step at the beginning only for org-admin with multiple academies
            const steps = (role === 'org-admin' && hasMultipleAcademies)
                ? [academyStep, ...baseSteps]
                : baseSteps;

            // Add step numbers dynamically
            this.filterResult.tabs = steps.map((step, index) => ({
                ...step,
                step: index + 1
            }));

            this.filterResult.payload = {
                category: "",
                learning_objectives: "",
                why_should_learner_enroll: "",
                duration_of_course: null,
                class_time: "",
                personalization: [],
                enrollment_type: "",
                group_price: "",
                one_to_one_price: "",
                uniqueness: "",
                live_hours: "",
                days_classes_offered: null,
                class_duration: null,
                userId: isLoggedIn,
                academy: this.getAcademyValue(hasMultipleAcademies, academyId),
                org: YUNOCommon.getQueryParameter('org_id') || this.activeOrg()
            };
        },
        /**
         * Determines the academy value based on user's academy access
         * @param {boolean} hasMultipleAcademies - Whether user has access to multiple academies
         * @param {string|number|boolean} academyId - The academy ID from URL parameter
         * @returns {Array} Array containing the academy ID(s) or empty array if no academy ID
         */
        getAcademyValue(hasMultipleAcademies, academyId) {
            // If user has multiple academies, return empty array as academy will be selected in the UI
            if (hasMultipleAcademies) {
                return [];
            }
            
            // If user has single academy access, use that academy
            if (this.subform2.data.length === 1) {
                return [this.subform2.data[0].value];
            }
            
            // If no academyId is provided, show error
            if (!academyId) {
                this.subform2.error = true;
                this.subform2.errorData = "Without the academy, you can't create a course";
                return [];
            }
            
            // Use the academy ID from URL parameter
            return [academyId];
        },
        /**
         * Handles the beforeunload event to prevent accidental navigation away
         * @param {Event} event - The beforeunload event object
         */
        handleBeforeUnload(event) {
            if (this.isJsonNotBlank(this.filterResult.payload)) {
                event.preventDefault();
                event.returnValue = '';    
            }
        },
        /**
         * Checks if a JSON object has any non-blank values
         * @param {Object} json - The JSON object to check
         * @returns {boolean} True if the object has any non-blank values
         */
        isJsonNotBlank(json) {
            return Object.values(json).some(value => {
                if (Array.isArray(value)) {
                    return value.length > 0;
                }
                return value !== "" && value !== null;
            });
        },
        /**
         * Checks the status of a background job
         * @param {string} jobID - The ID of the job to check
         * @param {string} type - The type of job ('course' or 'schedule')
         */
        checkStatus(jobID, type = 'course') {
            const params = {
                jobID,
                loggedinUserID: isLoggedIn
            };

            this.cleanupTimers();
            this.startTime = Date.now();

            this.statusCheckTimer = setInterval(() => {
                const elapsedTime = Date.now() - this.startTime;
                
                if (elapsedTime >= this.waitingTime) {
                    this.handleTimeout();
                    return;
                }

                const options = {
                    apiURL: YUNOCommon.config.createCourse('status', params),
                    module: "gotData",
                    store: "updateLink",
                    callback: true,
                    callbackFunc: (options) => this.handleStatusResponse(options, type)
                };

                this.dispatchData('fetchData', options);
            }, this.statusFetchInterval);
        },
        /**
         * Handles timeout of status check operation
         * Shows error message and resets component state
         */
        handleTimeout() {
            this.cleanupTimers();
            this.isInProgress = false;
            this.showToastMessage("Operation timed out. Please try again.");
        },
        /**
         * Handles the response from status check API
         * @param {Object} options - The response options object
         * @param {string} type - The type of operation ('course' or 'schedule')
         */
        handleStatusResponse(options, type) {
            const response = options?.response?.data?.data;

            if (response?.status === 'completed') {
                this.cleanupTimers();
                if (type === 'course') {
                    this.filterResult.additional = response;
                    this.submitSubForm(response);
                } else {
                    const courseId = this.filterResult.additional?.course_id;
                    if (courseId) {
                        window.removeEventListener('beforeunload', this.handleBeforeUnload);
                        window.location.href = `/edit-course/?courseid=${courseId}`;
                    } else {
                        this.showToastMessage("Course ID not found. Please try again.");
                    }
                }
            } else if (response?.status === 'failed') {
                this.cleanupTimers();
                this.isInProgress = false;
                this.showToastMessage("Operation failed. Please try again.");

                if (type === 'schedule') {
                    const courseId = this.filterResult.additional?.course_id;
                    if (courseId) {
                        window.removeEventListener('beforeunload', this.handleBeforeUnload);
                        window.location.href = `/edit-course/?courseid=${courseId}`;
                    } else {
                        this.showToastMessage("Course ID not found. Please try again.");
                    }
                } 
            }
        },
        /**
         * Submits the main course creation form
         * Initiates the course creation process
         */
        submitForm() {
            this.form.isLoading = true;
            this.isInProgress = true;

            const options = {
                apiURL: YUNOCommon.config.createCourse("courseGPT", false),
                module: "gotData",
                store: "form",
                payload: this.refinePayload(),
                callback: true,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callbackFunc: (options) => this.handleFormResponse(options)
            };

            this.dispatchData('postData', options);
        },
        /**
         * Refines the payload data before submission
         * Handles academy data formatting
         * @returns {Object} The refined payload object
         */
        refinePayload() {
            const payload = JSON.parse(JSON.stringify(this.filterResult.payload));

            if (this.subform2.data.length > 1) {
                payload.academy = payload.academy.map(item => item.value);
                
            }

            return payload;
        },
        /**
         * Handles the response from the course creation API
         * @param {Object} options - The response options object
         */
        handleFormResponse(options) {
            const response = options?.response?.data;
            this.form.isLoading = false;

            if (response?.code === 200) {
                this.startTime = Date.now();
                this.checkStatus(response.job_uuid, 'course');
            } else if (response?.message) {
                const courseId = this.filterResult.additional?.course_id;
                if (courseId) {
                    window.removeEventListener('beforeunload', this.handleBeforeUnload);
                    window.location.href = `/edit-course/?courseid=${courseId}`;
                }
                this.isInProgress = false;
                this.showToastMessage(response.message);
            }
        },
        /**
         * Submits the schedule creation form
         * @param {Object} payload - The schedule data to submit
         */
        submitSubForm(payload) {
            const options = {
                apiURL: YUNOCommon.config.createCourse("schedules", false),
                module: "gotData",
                store: "subform",
                payload,
                callback: true,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callbackFunc: (options) => this.handleSubFormResponse(options)
            };

            this.dispatchData('postData', options);
        },
        /**
         * Handles the response from the schedule creation API
         * @param {Object} options - The response options object
         */
        handleSubFormResponse(options) {
            const response = options?.response?.data;
            this.form.isLoading = false;

            if (response?.code === 200) {
                this.startTime = Date.now();
                this.checkStatus(response.job_uuid, 'schedule');
            } else if (response?.message) {
                this.showToastMessage(response.message);
            }
        },
        /**
         * Handles user info data and initializes appropriate steps
         * @param {Object} data - The user data object
         */
        onUserInfo(data) {
            if (data.role === 'org-admin') {
                this.fetchAcademies(data.role);
            } else {
                const academyId = YUNOCommon.getQueryParameter('academy_id');
                this.setupSteps(false, false, academyId);
            }
        },
        /**
         * Gets the active organization ID from user info
         * @returns {number} The active organization ID or 0 if not found
         */
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            } else {
                return 0;
            }
        },
        /**
         * Handles the response from fetching academies
         * @param {Object} options - The response options object
         */
        gotAcademies(options) {
            const { code, data } = options.response?.data || {};

            if (code === 200) {
                this.setupSteps(data.length > 0 ? options.role : false, data.length > 1, false);
            }
        },
        /**
         * Fetches the list of academies for the organization
         * @param {string} role - The user's role
         */
        fetchAcademies(role) {
            const options = { 
                apiURL: YUNOCommon.config.org("orgAcademies", this.activeOrg()),
                module: "gotData",
                store: "subform2",
                callback: true,
                role: role,
                callbackFunc: this.gotAcademies
            };
            this.dispatchData('fetchData', options); 
        },
    }
});