/**
 * Minified by jsDelivr 
 * Original file: /npm/moment@2.30.1/moment.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.moment=t()}(this,function(){"use strict";var H;function _(){return H.apply(null,arguments)}function y(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function F(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function c(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function L(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;for(var t in e)if(c(e,t))return;return 1}function g(e){return void 0===e}function w(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function V(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function G(e,t){for(var n=[],s=e.length,i=0;i<s;++i)n.push(t(e[i],i));return n}function E(e,t){for(var n in t)c(t,n)&&(e[n]=t[n]);return c(t,"toString")&&(e.toString=t.toString),c(t,"valueOf")&&(e.valueOf=t.valueOf),e}function l(e,t,n,s){return Wt(e,t,n,s,!0).utc()}function p(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function A(e){var t,n,s=e._d&&!isNaN(e._d.getTime());return s&&(t=p(e),n=j.call(t.parsedDateParts,function(e){return null!=e}),s=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n),e._strict)&&(s=s&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e)?s:(e._isValid=s,e._isValid)}function I(e){var t=l(NaN);return null!=e?E(p(t),e):p(t).userInvalidated=!0,t}var j=Array.prototype.some||function(e){for(var t=Object(this),n=t.length>>>0,s=0;s<n;s++)if(s in t&&e.call(this,t[s],s,t))return!0;return!1},Z=_.momentProperties=[],z=!1;function q(e,t){var n,s,i,r=Z.length;if(g(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),g(t._i)||(e._i=t._i),g(t._f)||(e._f=t._f),g(t._l)||(e._l=t._l),g(t._strict)||(e._strict=t._strict),g(t._tzm)||(e._tzm=t._tzm),g(t._isUTC)||(e._isUTC=t._isUTC),g(t._offset)||(e._offset=t._offset),g(t._pf)||(e._pf=p(t)),g(t._locale)||(e._locale=t._locale),0<r)for(n=0;n<r;n++)g(i=t[s=Z[n]])||(e[s]=i);return e}function $(e){q(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===z&&(z=!0,_.updateOffset(this),z=!1)}function k(e){return e instanceof $||null!=e&&null!=e._isAMomentObject}function B(e){!1===_.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function e(r,a){var o=!0;return E(function(){if(null!=_.deprecationHandler&&_.deprecationHandler(null,r),o){for(var e,t,n=[],s=arguments.length,i=0;i<s;i++){if(e="","object"==typeof arguments[i]){for(t in e+="\n["+i+"] ",arguments[0])c(arguments[0],t)&&(e+=t+": "+arguments[0][t]+", ");e=e.slice(0,-2)}else e=arguments[i];n.push(e)}B(r+"\nArguments: "+Array.prototype.slice.call(n).join("")+"\n"+(new Error).stack),o=!1}return a.apply(this,arguments)},a)}var J={};function Q(e,t){null!=_.deprecationHandler&&_.deprecationHandler(e,t),J[e]||(B(t),J[e]=!0)}function a(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function X(e,t){var n,s=E({},e);for(n in t)c(t,n)&&(F(e[n])&&F(t[n])?(s[n]={},E(s[n],e[n]),E(s[n],t[n])):null!=t[n]?s[n]=t[n]:delete s[n]);for(n in e)c(e,n)&&!c(t,n)&&F(e[n])&&(s[n]=E({},s[n]));return s}function K(e){null!=e&&this.set(e)}_.suppressDeprecationWarnings=!1,_.deprecationHandler=null;var ee=Object.keys||function(e){var t,n=[];for(t in e)c(e,t)&&n.push(t);return n};function r(e,t,n){var s=""+Math.abs(e);return(0<=e?n?"+":"":"-")+Math.pow(10,Math.max(0,t-s.length)).toString().substr(1)+s}var te=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,ne=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,se={},ie={};function s(e,t,n,s){var i="string"==typeof s?function(){return this[s]()}:s;e&&(ie[e]=i),t&&(ie[t[0]]=function(){return r(i.apply(this,arguments),t[1],t[2])}),n&&(ie[n]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function re(e,t){return e.isValid()?(t=ae(t,e.localeData()),se[t]=se[t]||function(s){for(var e,i=s.match(te),t=0,r=i.length;t<r;t++)ie[i[t]]?i[t]=ie[i[t]]:i[t]=(e=i[t]).match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"");return function(e){for(var t="",n=0;n<r;n++)t+=a(i[n])?i[n].call(e,s):i[n];return t}}(t),se[t](e)):e.localeData().invalidDate()}function ae(e,t){var n=5;function s(e){return t.longDateFormat(e)||e}for(ne.lastIndex=0;0<=n&&ne.test(e);)e=e.replace(ne,s),ne.lastIndex=0,--n;return e}var oe={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function o(e){return"string"==typeof e?oe[e]||oe[e.toLowerCase()]:void 0}function ue(e){var t,n,s={};for(n in e)c(e,n)&&(t=o(n))&&(s[t]=e[n]);return s}var le={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};var de=/\d/,t=/\d\d/,he=/\d{3}/,ce=/\d{4}/,fe=/[+-]?\d{6}/,n=/\d\d?/,me=/\d\d\d\d?/,_e=/\d\d\d\d\d\d?/,ye=/\d{1,3}/,ge=/\d{1,4}/,we=/[+-]?\d{1,6}/,pe=/\d+/,ke=/[+-]?\d+/,Me=/Z|[+-]\d\d:?\d\d/gi,ve=/Z|[+-]\d\d(?::?\d\d)?/gi,i=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,u=/^[1-9]\d?/,d=/^([1-9]\d|\d)/;function h(e,n,s){Ye[e]=a(n)?n:function(e,t){return e&&s?s:n}}function De(e,t){return c(Ye,e)?Ye[e](t._strict,t._locale):new RegExp(f(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,s,i){return t||n||s||i})))}function f(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function m(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function M(e){var e=+e,t=0;return t=0!=e&&isFinite(e)?m(e):t}var Ye={},Se={};function v(e,n){var t,s,i=n;for("string"==typeof e&&(e=[e]),w(n)&&(i=function(e,t){t[n]=M(e)}),s=e.length,t=0;t<s;t++)Se[e[t]]=i}function Oe(e,i){v(e,function(e,t,n,s){n._w=n._w||{},i(e,n._w,n,s)})}function be(e){return e%4==0&&e%100!=0||e%400==0}var D=0,Y=1,S=2,O=3,b=4,T=5,Te=6,xe=7,Ne=8;function We(e){return be(e)?366:365}s("Y",0,0,function(){var e=this.year();return e<=9999?r(e,4):"+"+e}),s(0,["YY",2],0,function(){return this.year()%100}),s(0,["YYYY",4],0,"year"),s(0,["YYYYY",5],0,"year"),s(0,["YYYYYY",6,!0],0,"year"),h("Y",ke),h("YY",n,t),h("YYYY",ge,ce),h("YYYYY",we,fe),h("YYYYYY",we,fe),v(["YYYYY","YYYYYY"],D),v("YYYY",function(e,t){t[D]=2===e.length?_.parseTwoDigitYear(e):M(e)}),v("YY",function(e,t){t[D]=_.parseTwoDigitYear(e)}),v("Y",function(e,t){t[D]=parseInt(e,10)}),_.parseTwoDigitYear=function(e){return M(e)+(68<M(e)?1900:2e3)};var x,Pe=Re("FullYear",!0);function Re(t,n){return function(e){return null!=e?(Ue(this,t,e),_.updateOffset(this,n),this):Ce(this,t)}}function Ce(e,t){if(!e.isValid())return NaN;var n=e._d,s=e._isUTC;switch(t){case"Milliseconds":return s?n.getUTCMilliseconds():n.getMilliseconds();case"Seconds":return s?n.getUTCSeconds():n.getSeconds();case"Minutes":return s?n.getUTCMinutes():n.getMinutes();case"Hours":return s?n.getUTCHours():n.getHours();case"Date":return s?n.getUTCDate():n.getDate();case"Day":return s?n.getUTCDay():n.getDay();case"Month":return s?n.getUTCMonth():n.getMonth();case"FullYear":return s?n.getUTCFullYear():n.getFullYear();default:return NaN}}function Ue(e,t,n){var s,i,r;if(e.isValid()&&!isNaN(n)){switch(s=e._d,i=e._isUTC,t){case"Milliseconds":return i?s.setUTCMilliseconds(n):s.setMilliseconds(n);case"Seconds":return i?s.setUTCSeconds(n):s.setSeconds(n);case"Minutes":return i?s.setUTCMinutes(n):s.setMinutes(n);case"Hours":return i?s.setUTCHours(n):s.setHours(n);case"Date":return i?s.setUTCDate(n):s.setDate(n);case"FullYear":break;default:return}t=n,r=e.month(),e=29!==(e=e.date())||1!==r||be(t)?e:28,i?s.setUTCFullYear(t,r,e):s.setFullYear(t,r,e)}}function He(e,t){var n;return isNaN(e)||isNaN(t)?NaN:(n=(t%(n=12)+n)%n,e+=(t-n)/12,1==n?be(e)?29:28:31-n%7%2)}x=Array.prototype.indexOf||function(e){for(var t=0;t<this.length;++t)if(this[t]===e)return t;return-1},s("M",["MM",2],"Mo",function(){return this.month()+1}),s("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),s("MMMM",0,0,function(e){return this.localeData().months(this,e)}),h("M",n,u),h("MM",n,t),h("MMM",function(e,t){return t.monthsShortRegex(e)}),h("MMMM",function(e,t){return t.monthsRegex(e)}),v(["M","MM"],function(e,t){t[Y]=M(e)-1}),v(["MMM","MMMM"],function(e,t,n,s){s=n._locale.monthsParse(e,s,n._strict);null!=s?t[Y]=s:p(n).invalidMonth=e});var Fe="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Le="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ve=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ge=i,Ee=i;function Ae(e,t){if(e.isValid()){if("string"==typeof t)if(/^\d+$/.test(t))t=M(t);else if(!w(t=e.localeData().monthsParse(t)))return;var n=(n=e.date())<29?n:Math.min(n,He(e.year(),t));e._isUTC?e._d.setUTCMonth(t,n):e._d.setMonth(t,n)}}function Ie(e){return null!=e?(Ae(this,e),_.updateOffset(this,!0),this):Ce(this,"Month")}function je(){function e(e,t){return t.length-e.length}for(var t,n,s=[],i=[],r=[],a=0;a<12;a++)n=l([2e3,a]),t=f(this.monthsShort(n,"")),n=f(this.months(n,"")),s.push(t),i.push(n),r.push(n),r.push(t);s.sort(e),i.sort(e),r.sort(e),this._monthsRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+s.join("|")+")","i")}function Ze(e,t,n,s,i,r,a){var o;return e<100&&0<=e?(o=new Date(e+400,t,n,s,i,r,a),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,s,i,r,a),o}function ze(e){var t;return e<100&&0<=e?((t=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,t)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function qe(e,t,n){n=7+t-n;return n-(7+ze(e,0,n).getUTCDay()-t)%7-1}function $e(e,t,n,s,i){var r,t=1+7*(t-1)+(7+n-s)%7+qe(e,s,i),n=t<=0?We(r=e-1)+t:t>We(e)?(r=e+1,t-We(e)):(r=e,t);return{year:r,dayOfYear:n}}function Be(e,t,n){var s,i,r=qe(e.year(),t,n),r=Math.floor((e.dayOfYear()-r-1)/7)+1;return r<1?s=r+N(i=e.year()-1,t,n):r>N(e.year(),t,n)?(s=r-N(e.year(),t,n),i=e.year()+1):(i=e.year(),s=r),{week:s,year:i}}function N(e,t,n){var s=qe(e,t,n),t=qe(e+1,t,n);return(We(e)-s+t)/7}s("w",["ww",2],"wo","week"),s("W",["WW",2],"Wo","isoWeek"),h("w",n,u),h("ww",n,t),h("W",n,u),h("WW",n,t),Oe(["w","ww","W","WW"],function(e,t,n,s){t[s.substr(0,1)]=M(e)});function Je(e,t){return e.slice(t,7).concat(e.slice(0,t))}s("d",0,"do","day"),s("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),s("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),s("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),s("e",0,0,"weekday"),s("E",0,0,"isoWeekday"),h("d",n),h("e",n),h("E",n),h("dd",function(e,t){return t.weekdaysMinRegex(e)}),h("ddd",function(e,t){return t.weekdaysShortRegex(e)}),h("dddd",function(e,t){return t.weekdaysRegex(e)}),Oe(["dd","ddd","dddd"],function(e,t,n,s){s=n._locale.weekdaysParse(e,s,n._strict);null!=s?t.d=s:p(n).invalidWeekday=e}),Oe(["d","e","E"],function(e,t,n,s){t[s]=M(e)});var Qe="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Xe="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ke="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),et=i,tt=i,nt=i;function st(){function e(e,t){return t.length-e.length}for(var t,n,s,i=[],r=[],a=[],o=[],u=0;u<7;u++)s=l([2e3,1]).day(u),t=f(this.weekdaysMin(s,"")),n=f(this.weekdaysShort(s,"")),s=f(this.weekdays(s,"")),i.push(t),r.push(n),a.push(s),o.push(t),o.push(n),o.push(s);i.sort(e),r.sort(e),a.sort(e),o.sort(e),this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function it(){return this.hours()%12||12}function rt(e,t){s(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function at(e,t){return t._meridiemParse}s("H",["HH",2],0,"hour"),s("h",["hh",2],0,it),s("k",["kk",2],0,function(){return this.hours()||24}),s("hmm",0,0,function(){return""+it.apply(this)+r(this.minutes(),2)}),s("hmmss",0,0,function(){return""+it.apply(this)+r(this.minutes(),2)+r(this.seconds(),2)}),s("Hmm",0,0,function(){return""+this.hours()+r(this.minutes(),2)}),s("Hmmss",0,0,function(){return""+this.hours()+r(this.minutes(),2)+r(this.seconds(),2)}),rt("a",!0),rt("A",!1),h("a",at),h("A",at),h("H",n,d),h("h",n,u),h("k",n,u),h("HH",n,t),h("hh",n,t),h("kk",n,t),h("hmm",me),h("hmmss",_e),h("Hmm",me),h("Hmmss",_e),v(["H","HH"],O),v(["k","kk"],function(e,t,n){e=M(e);t[O]=24===e?0:e}),v(["a","A"],function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e}),v(["h","hh"],function(e,t,n){t[O]=M(e),p(n).bigHour=!0}),v("hmm",function(e,t,n){var s=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s)),p(n).bigHour=!0}),v("hmmss",function(e,t,n){var s=e.length-4,i=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s,2)),t[T]=M(e.substr(i)),p(n).bigHour=!0}),v("Hmm",function(e,t,n){var s=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s))}),v("Hmmss",function(e,t,n){var s=e.length-4,i=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s,2)),t[T]=M(e.substr(i))});i=Re("Hours",!0);var ot,ut={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Fe,monthsShort:Le,week:{dow:0,doy:6},weekdays:Qe,weekdaysMin:Ke,weekdaysShort:Xe,meridiemParse:/[ap]\.?m?\.?/i},W={},lt={};function dt(e){return e&&e.toLowerCase().replace("_","-")}function ht(e){for(var t,n,s,i,r=0;r<e.length;){for(t=(i=dt(e[r]).split("-")).length,n=(n=dt(e[r+1]))?n.split("-"):null;0<t;){if(s=ct(i.slice(0,t).join("-")))return s;if(n&&n.length>=t&&function(e,t){for(var n=Math.min(e.length,t.length),s=0;s<n;s+=1)if(e[s]!==t[s])return s;return n}(i,n)>=t-1)break;t--}r++}return ot}function ct(t){var e,n;if(void 0===W[t]&&"undefined"!=typeof module&&module&&module.exports&&(n=t)&&n.match("^[^/\\\\]*$"))try{e=ot._abbr,require("./locale/"+t),ft(e)}catch(e){W[t]=null}return W[t]}function ft(e,t){return e&&((t=g(t)?P(e):mt(e,t))?ot=t:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),ot._abbr}function mt(e,t){if(null===t)return delete W[e],null;var n,s=ut;if(t.abbr=e,null!=W[e])Q("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),s=W[e]._config;else if(null!=t.parentLocale)if(null!=W[t.parentLocale])s=W[t.parentLocale]._config;else{if(null==(n=ct(t.parentLocale)))return lt[t.parentLocale]||(lt[t.parentLocale]=[]),lt[t.parentLocale].push({name:e,config:t}),null;s=n._config}return W[e]=new K(X(s,t)),lt[e]&&lt[e].forEach(function(e){mt(e.name,e.config)}),ft(e),W[e]}function P(e){var t;if(!(e=e&&e._locale&&e._locale._abbr?e._locale._abbr:e))return ot;if(!y(e)){if(t=ct(e))return t;e=[e]}return ht(e)}function _t(e){var t=e._a;return t&&-2===p(e).overflow&&(t=t[Y]<0||11<t[Y]?Y:t[S]<1||t[S]>He(t[D],t[Y])?S:t[O]<0||24<t[O]||24===t[O]&&(0!==t[b]||0!==t[T]||0!==t[Te])?O:t[b]<0||59<t[b]?b:t[T]<0||59<t[T]?T:t[Te]<0||999<t[Te]?Te:-1,p(e)._overflowDayOfYear&&(t<D||S<t)&&(t=S),p(e)._overflowWeeks&&-1===t&&(t=xe),p(e)._overflowWeekday&&-1===t&&(t=Ne),p(e).overflow=t),e}var yt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,gt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,wt=/Z|[+-]\d\d(?::?\d\d)?/,pt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],kt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Mt=/^\/?Date\((-?\d+)/i,vt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Dt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Yt(e){var t,n,s,i,r,a,o=e._i,u=yt.exec(o)||gt.exec(o),o=pt.length,l=kt.length;if(u){for(p(e).iso=!0,t=0,n=o;t<n;t++)if(pt[t][1].exec(u[1])){i=pt[t][0],s=!1!==pt[t][2];break}if(null==i)e._isValid=!1;else{if(u[3]){for(t=0,n=l;t<n;t++)if(kt[t][1].exec(u[3])){r=(u[2]||" ")+kt[t][0];break}if(null==r)return void(e._isValid=!1)}if(s||null==r){if(u[4]){if(!wt.exec(u[4]))return void(e._isValid=!1);a="Z"}e._f=i+(r||"")+(a||""),xt(e)}else e._isValid=!1}}else e._isValid=!1}function St(e,t,n,s,i,r){e=[function(e){e=parseInt(e,10);{if(e<=49)return 2e3+e;if(e<=999)return 1900+e}return e}(e),Le.indexOf(t),parseInt(n,10),parseInt(s,10),parseInt(i,10)];return r&&e.push(parseInt(r,10)),e}function Ot(e){var t,n,s=vt.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));s?(t=St(s[4],s[3],s[2],s[5],s[6],s[7]),function(e,t,n){if(!e||Xe.indexOf(e)===new Date(t[0],t[1],t[2]).getDay())return 1;p(n).weekdayMismatch=!0,n._isValid=!1}(s[1],t,e)&&(e._a=t,e._tzm=(t=s[8],n=s[9],s=s[10],t?Dt[t]:n?0:60*(((t=parseInt(s,10))-(n=t%100))/100)+n),e._d=ze.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),p(e).rfc2822=!0)):e._isValid=!1}function bt(e,t,n){return null!=e?e:null!=t?t:n}function Tt(e){var t,n,s,i,r,a,o,u,l,d,h,c=[];if(!e._d){for(s=e,i=new Date(_.now()),n=s._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()],e._w&&null==e._a[S]&&null==e._a[Y]&&(null!=(i=(s=e)._w).GG||null!=i.W||null!=i.E?(u=1,l=4,r=bt(i.GG,s._a[D],Be(R(),1,4).year),a=bt(i.W,1),((o=bt(i.E,1))<1||7<o)&&(d=!0)):(u=s._locale._week.dow,l=s._locale._week.doy,h=Be(R(),u,l),r=bt(i.gg,s._a[D],h.year),a=bt(i.w,h.week),null!=i.d?((o=i.d)<0||6<o)&&(d=!0):null!=i.e?(o=i.e+u,(i.e<0||6<i.e)&&(d=!0)):o=u),a<1||a>N(r,u,l)?p(s)._overflowWeeks=!0:null!=d?p(s)._overflowWeekday=!0:(h=$e(r,a,o,u,l),s._a[D]=h.year,s._dayOfYear=h.dayOfYear)),null!=e._dayOfYear&&(i=bt(e._a[D],n[D]),(e._dayOfYear>We(i)||0===e._dayOfYear)&&(p(e)._overflowDayOfYear=!0),d=ze(i,0,e._dayOfYear),e._a[Y]=d.getUTCMonth(),e._a[S]=d.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=c[t]=n[t];for(;t<7;t++)e._a[t]=c[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[O]&&0===e._a[b]&&0===e._a[T]&&0===e._a[Te]&&(e._nextDay=!0,e._a[O]=0),e._d=(e._useUTC?ze:Ze).apply(null,c),r=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[O]=24),e._w&&void 0!==e._w.d&&e._w.d!==r&&(p(e).weekdayMismatch=!0)}}function xt(e){if(e._f===_.ISO_8601)Yt(e);else if(e._f===_.RFC_2822)Ot(e);else{e._a=[],p(e).empty=!0;for(var t,n,s,i,r,a=""+e._i,o=a.length,u=0,l=ae(e._f,e._locale).match(te)||[],d=l.length,h=0;h<d;h++)n=l[h],(t=(a.match(De(n,e))||[])[0])&&(0<(s=a.substr(0,a.indexOf(t))).length&&p(e).unusedInput.push(s),a=a.slice(a.indexOf(t)+t.length),u+=t.length),ie[n]?(t?p(e).empty=!1:p(e).unusedTokens.push(n),s=n,r=e,null!=(i=t)&&c(Se,s)&&Se[s](i,r._a,r,s)):e._strict&&!t&&p(e).unusedTokens.push(n);p(e).charsLeftOver=o-u,0<a.length&&p(e).unusedInput.push(a),e._a[O]<=12&&!0===p(e).bigHour&&0<e._a[O]&&(p(e).bigHour=void 0),p(e).parsedDateParts=e._a.slice(0),p(e).meridiem=e._meridiem,e._a[O]=function(e,t,n){if(null==n)return t;return null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((e=e.isPM(n))&&t<12&&(t+=12),t=e||12!==t?t:0):t}(e._locale,e._a[O],e._meridiem),null!==(o=p(e).era)&&(e._a[D]=e._locale.erasConvertYear(o,e._a[D])),Tt(e),_t(e)}}function Nt(e){var t,n,s,i=e._i,r=e._f;if(e._locale=e._locale||P(e._l),null===i||void 0===r&&""===i)return I({nullInput:!0});if("string"==typeof i&&(e._i=i=e._locale.preparse(i)),k(i))return new $(_t(i));if(V(i))e._d=i;else if(y(r)){var a,o,u,l,d,h,c=e,f=!1,m=c._f.length;if(0===m)p(c).invalidFormat=!0,c._d=new Date(NaN);else{for(l=0;l<m;l++)d=0,h=!1,a=q({},c),null!=c._useUTC&&(a._useUTC=c._useUTC),a._f=c._f[l],xt(a),A(a)&&(h=!0),d=(d+=p(a).charsLeftOver)+10*p(a).unusedTokens.length,p(a).score=d,f?d<u&&(u=d,o=a):(null==u||d<u||h)&&(u=d,o=a,h)&&(f=!0);E(c,o||a)}}else if(r)xt(e);else if(g(r=(i=e)._i))i._d=new Date(_.now());else V(r)?i._d=new Date(r.valueOf()):"string"==typeof r?(n=i,null!==(t=Mt.exec(n._i))?n._d=new Date(+t[1]):(Yt(n),!1===n._isValid&&(delete n._isValid,Ot(n),!1===n._isValid)&&(delete n._isValid,n._strict?n._isValid=!1:_.createFromInputFallback(n)))):y(r)?(i._a=G(r.slice(0),function(e){return parseInt(e,10)}),Tt(i)):F(r)?(t=i)._d||(s=void 0===(n=ue(t._i)).day?n.date:n.day,t._a=G([n.year,n.month,s,n.hour,n.minute,n.second,n.millisecond],function(e){return e&&parseInt(e,10)}),Tt(t)):w(r)?i._d=new Date(r):_.createFromInputFallback(i);return A(e)||(e._d=null),e}function Wt(e,t,n,s,i){var r={};return!0!==t&&!1!==t||(s=t,t=void 0),!0!==n&&!1!==n||(s=n,n=void 0),(F(e)&&L(e)||y(e)&&0===e.length)&&(e=void 0),r._isAMomentObject=!0,r._useUTC=r._isUTC=i,r._l=n,r._i=e,r._f=t,r._strict=s,(i=new $(_t(Nt(i=r))))._nextDay&&(i.add(1,"d"),i._nextDay=void 0),i}function R(e,t,n,s){return Wt(e,t,n,s,!1)}_.createFromInputFallback=e("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),_.ISO_8601=function(){},_.RFC_2822=function(){};me=e("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=R.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:I()}),_e=e("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=R.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:I()});function Pt(e,t){var n,s;if(!(t=1===t.length&&y(t[0])?t[0]:t).length)return R();for(n=t[0],s=1;s<t.length;++s)t[s].isValid()&&!t[s][e](n)||(n=t[s]);return n}var Rt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ct(e){var e=ue(e),t=e.year||0,n=e.quarter||0,s=e.month||0,i=e.week||e.isoWeek||0,r=e.day||0,a=e.hour||0,o=e.minute||0,u=e.second||0,l=e.millisecond||0;this._isValid=function(e){var t,n,s=!1,i=Rt.length;for(t in e)if(c(e,t)&&(-1===x.call(Rt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<i;++n)if(e[Rt[n]]){if(s)return!1;parseFloat(e[Rt[n]])!==M(e[Rt[n]])&&(s=!0)}return!0}(e),this._milliseconds=+l+1e3*u+6e4*o+1e3*a*60*60,this._days=+r+7*i,this._months=+s+3*n+12*t,this._data={},this._locale=P(),this._bubble()}function Ut(e){return e instanceof Ct}function Ht(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Ft(e,n){s(e,0,0,function(){var e=this.utcOffset(),t="+";return e<0&&(e=-e,t="-"),t+r(~~(e/60),2)+n+r(~~e%60,2)})}Ft("Z",":"),Ft("ZZ",""),h("Z",ve),h("ZZ",ve),v(["Z","ZZ"],function(e,t,n){n._useUTC=!0,n._tzm=Vt(ve,e)});var Lt=/([\+\-]|\d\d)/gi;function Vt(e,t){var t=(t||"").match(e);return null===t?null:0===(t=60*(e=((t[t.length-1]||[])+"").match(Lt)||["-",0,0])[1]+M(e[2]))?0:"+"===e[0]?t:-t}function Gt(e,t){var n;return t._isUTC?(t=t.clone(),n=(k(e)||V(e)?e:R(e)).valueOf()-t.valueOf(),t._d.setTime(t._d.valueOf()+n),_.updateOffset(t,!1),t):R(e).local()}function Et(e){return-Math.round(e._d.getTimezoneOffset())}function At(){return!!this.isValid()&&this._isUTC&&0===this._offset}_.updateOffset=function(){};var It=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,jt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function C(e,t){var n,s=e;return Ut(e)?s={ms:e._milliseconds,d:e._days,M:e._months}:w(e)||!isNaN(+e)?(s={},t?s[t]=+e:s.milliseconds=+e):(t=It.exec(e))?(n="-"===t[1]?-1:1,s={y:0,d:M(t[S])*n,h:M(t[O])*n,m:M(t[b])*n,s:M(t[T])*n,ms:M(Ht(1e3*t[Te]))*n}):(t=jt.exec(e))?(n="-"===t[1]?-1:1,s={y:Zt(t[2],n),M:Zt(t[3],n),w:Zt(t[4],n),d:Zt(t[5],n),h:Zt(t[6],n),m:Zt(t[7],n),s:Zt(t[8],n)}):null==s?s={}:"object"==typeof s&&("from"in s||"to"in s)&&(t=function(e,t){var n;if(!e.isValid()||!t.isValid())return{milliseconds:0,months:0};t=Gt(t,e),e.isBefore(t)?n=zt(e,t):((n=zt(t,e)).milliseconds=-n.milliseconds,n.months=-n.months);return n}(R(s.from),R(s.to)),(s={}).ms=t.milliseconds,s.M=t.months),n=new Ct(s),Ut(e)&&c(e,"_locale")&&(n._locale=e._locale),Ut(e)&&c(e,"_isValid")&&(n._isValid=e._isValid),n}function Zt(e,t){e=e&&parseFloat(e.replace(",","."));return(isNaN(e)?0:e)*t}function zt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function qt(s,i){return function(e,t){var n;return null===t||isNaN(+t)||(Q(i,"moment()."+i+"(period, number) is deprecated. Please use moment()."+i+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),n=e,e=t,t=n),$t(this,C(e,t),s),this}}function $t(e,t,n,s){var i=t._milliseconds,r=Ht(t._days),t=Ht(t._months);e.isValid()&&(s=null==s||s,t&&Ae(e,Ce(e,"Month")+t*n),r&&Ue(e,"Date",Ce(e,"Date")+r*n),i&&e._d.setTime(e._d.valueOf()+i*n),s)&&_.updateOffset(e,r||t)}C.fn=Ct.prototype,C.invalid=function(){return C(NaN)};Fe=qt(1,"add"),Qe=qt(-1,"subtract");function Bt(e){return"string"==typeof e||e instanceof String}function Jt(e){return k(e)||V(e)||Bt(e)||w(e)||function(t){var e=y(t),n=!1;e&&(n=0===t.filter(function(e){return!w(e)&&Bt(t)}).length);return e&&n}(e)||function(e){var t,n,s=F(e)&&!L(e),i=!1,r=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],a=r.length;for(t=0;t<a;t+=1)n=r[t],i=i||c(e,n);return s&&i}(e)||null==e}function Qt(e,t){var n,s;return e.date()<t.date()?-Qt(t,e):-((n=12*(t.year()-e.year())+(t.month()-e.month()))+(t-(s=e.clone().add(n,"months"))<0?(t-s)/(s-e.clone().add(n-1,"months")):(t-s)/(e.clone().add(1+n,"months")-s)))||0}function Xt(e){return void 0===e?this._locale._abbr:(null!=(e=P(e))&&(this._locale=e),this)}_.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",_.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";Ke=e("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});function Kt(){return this._locale}var en=126227808e5;function tn(e,t){return(e%t+t)%t}function nn(e,t,n){return e<100&&0<=e?new Date(e+400,t,n)-en:new Date(e,t,n).valueOf()}function sn(e,t,n){return e<100&&0<=e?Date.UTC(e+400,t,n)-en:Date.UTC(e,t,n)}function rn(e,t){return t.erasAbbrRegex(e)}function an(){for(var e,t,n,s=[],i=[],r=[],a=[],o=this.eras(),u=0,l=o.length;u<l;++u)e=f(o[u].name),t=f(o[u].abbr),n=f(o[u].narrow),i.push(e),s.push(t),r.push(n),a.push(e),a.push(t),a.push(n);this._erasRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+i.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+r.join("|")+")","i")}function on(e,t){s(0,[e,e.length],0,t)}function un(e,t,n,s,i){var r;return null==e?Be(this,s,i).year:(r=N(e,s,i),function(e,t,n,s,i){e=$e(e,t,n,s,i),t=ze(e.year,0,e.dayOfYear);return this.year(t.getUTCFullYear()),this.month(t.getUTCMonth()),this.date(t.getUTCDate()),this}.call(this,e,t=r<t?r:t,n,s,i))}s("N",0,0,"eraAbbr"),s("NN",0,0,"eraAbbr"),s("NNN",0,0,"eraAbbr"),s("NNNN",0,0,"eraName"),s("NNNNN",0,0,"eraNarrow"),s("y",["y",1],"yo","eraYear"),s("y",["yy",2],0,"eraYear"),s("y",["yyy",3],0,"eraYear"),s("y",["yyyy",4],0,"eraYear"),h("N",rn),h("NN",rn),h("NNN",rn),h("NNNN",function(e,t){return t.erasNameRegex(e)}),h("NNNNN",function(e,t){return t.erasNarrowRegex(e)}),v(["N","NN","NNN","NNNN","NNNNN"],function(e,t,n,s){s=n._locale.erasParse(e,s,n._strict);s?p(n).era=s:p(n).invalidEra=e}),h("y",pe),h("yy",pe),h("yyy",pe),h("yyyy",pe),h("yo",function(e,t){return t._eraYearOrdinalRegex||pe}),v(["y","yy","yyy","yyyy"],D),v(["yo"],function(e,t,n,s){var i;n._locale._eraYearOrdinalRegex&&(i=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[D]=n._locale.eraYearOrdinalParse(e,i):t[D]=parseInt(e,10)}),s(0,["gg",2],0,function(){return this.weekYear()%100}),s(0,["GG",2],0,function(){return this.isoWeekYear()%100}),on("gggg","weekYear"),on("ggggg","weekYear"),on("GGGG","isoWeekYear"),on("GGGGG","isoWeekYear"),h("G",ke),h("g",ke),h("GG",n,t),h("gg",n,t),h("GGGG",ge,ce),h("gggg",ge,ce),h("GGGGG",we,fe),h("ggggg",we,fe),Oe(["gggg","ggggg","GGGG","GGGGG"],function(e,t,n,s){t[s.substr(0,2)]=M(e)}),Oe(["gg","GG"],function(e,t,n,s){t[s]=_.parseTwoDigitYear(e)}),s("Q",0,"Qo","quarter"),h("Q",de),v("Q",function(e,t){t[Y]=3*(M(e)-1)}),s("D",["DD",2],"Do","date"),h("D",n,u),h("DD",n,t),h("Do",function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient}),v(["D","DD"],S),v("Do",function(e,t){t[S]=M(e.match(n)[0])});ge=Re("Date",!0);s("DDD",["DDDD",3],"DDDo","dayOfYear"),h("DDD",ye),h("DDDD",he),v(["DDD","DDDD"],function(e,t,n){n._dayOfYear=M(e)}),s("m",["mm",2],0,"minute"),h("m",n,d),h("mm",n,t),v(["m","mm"],b);var ln,ce=Re("Minutes",!1),we=(s("s",["ss",2],0,"second"),h("s",n,d),h("ss",n,t),v(["s","ss"],T),Re("Seconds",!1));for(s("S",0,0,function(){return~~(this.millisecond()/100)}),s(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),s(0,["SSS",3],0,"millisecond"),s(0,["SSSS",4],0,function(){return 10*this.millisecond()}),s(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),s(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),s(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),s(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),s(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),h("S",ye,de),h("SS",ye,t),h("SSS",ye,he),ln="SSSS";ln.length<=9;ln+="S")h(ln,pe);function dn(e,t){t[Te]=M(1e3*("0."+e))}for(ln="S";ln.length<=9;ln+="S")v(ln,dn);fe=Re("Milliseconds",!1),s("z",0,0,"zoneAbbr"),s("zz",0,0,"zoneName");u=$.prototype;function hn(e){return e}u.add=Fe,u.calendar=function(e,t){1===arguments.length&&(arguments[0]?Jt(arguments[0])?(e=arguments[0],t=void 0):function(e){for(var t=F(e)&&!L(e),n=!1,s=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],i=0;i<s.length;i+=1)n=n||c(e,s[i]);return t&&n}(arguments[0])&&(t=arguments[0],e=void 0):t=e=void 0);var e=e||R(),n=Gt(e,this).startOf("day"),n=_.calendarFormat(this,n)||"sameElse",t=t&&(a(t[n])?t[n].call(this,e):t[n]);return this.format(t||this.localeData().calendar(n,this,R(e)))},u.clone=function(){return new $(this)},u.diff=function(e,t,n){var s,i,r;if(!this.isValid())return NaN;if(!(s=Gt(e,this)).isValid())return NaN;switch(i=6e4*(s.utcOffset()-this.utcOffset()),t=o(t)){case"year":r=Qt(this,s)/12;break;case"month":r=Qt(this,s);break;case"quarter":r=Qt(this,s)/3;break;case"second":r=(this-s)/1e3;break;case"minute":r=(this-s)/6e4;break;case"hour":r=(this-s)/36e5;break;case"day":r=(this-s-i)/864e5;break;case"week":r=(this-s-i)/6048e5;break;default:r=this-s}return n?r:m(r)},u.endOf=function(e){var t,n;if(void 0!==(e=o(e))&&"millisecond"!==e&&this.isValid()){switch(n=this._isUTC?sn:nn,e){case"year":t=n(this.year()+1,0,1)-1;break;case"quarter":t=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=n(this.year(),this.month()+1,1)-1;break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-tn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-tn(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-tn(t,1e3)-1;break}this._d.setTime(t),_.updateOffset(this,!0)}return this},u.format=function(e){return e=e||(this.isUtc()?_.defaultFormatUtc:_.defaultFormat),e=re(this,e),this.localeData().postformat(e)},u.from=function(e,t){return this.isValid()&&(k(e)&&e.isValid()||R(e).isValid())?C({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},u.fromNow=function(e){return this.from(R(),e)},u.to=function(e,t){return this.isValid()&&(k(e)&&e.isValid()||R(e).isValid())?C({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},u.toNow=function(e){return this.to(R(),e)},u.get=function(e){return a(this[e=o(e)])?this[e]():this},u.invalidAt=function(){return p(this).overflow},u.isAfter=function(e,t){return e=k(e)?e:R(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()>e.valueOf():e.valueOf()<this.clone().startOf(t).valueOf())},u.isBefore=function(e,t){return e=k(e)?e:R(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()<e.valueOf():this.clone().endOf(t).valueOf()<e.valueOf())},u.isBetween=function(e,t,n,s){return e=k(e)?e:R(e),t=k(t)?t:R(t),!!(this.isValid()&&e.isValid()&&t.isValid())&&("("===(s=s||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===s[1]?this.isBefore(t,n):!this.isAfter(t,n))},u.isSame=function(e,t){var e=k(e)?e:R(e);return!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()===e.valueOf():(e=e.valueOf(),this.clone().startOf(t).valueOf()<=e&&e<=this.clone().endOf(t).valueOf()))},u.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},u.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},u.isValid=function(){return A(this)},u.lang=Ke,u.locale=Xt,u.localeData=Kt,u.max=_e,u.min=me,u.parsingFlags=function(){return E({},p(this))},u.set=function(e,t){if("object"==typeof e)for(var n=function(e){var t,n=[];for(t in e)c(e,t)&&n.push({unit:t,priority:le[t]});return n.sort(function(e,t){return e.priority-t.priority}),n}(e=ue(e)),s=n.length,i=0;i<s;i++)this[n[i].unit](e[n[i].unit]);else if(a(this[e=o(e)]))return this[e](t);return this},u.startOf=function(e){var t,n;if(void 0!==(e=o(e))&&"millisecond"!==e&&this.isValid()){switch(n=this._isUTC?sn:nn,e){case"year":t=n(this.year(),0,1);break;case"quarter":t=n(this.year(),this.month()-this.month()%3,1);break;case"month":t=n(this.year(),this.month(),1);break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=n(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=tn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=tn(t,6e4);break;case"second":t=this._d.valueOf(),t-=tn(t,1e3);break}this._d.setTime(t),_.updateOffset(this,!0)}return this},u.subtract=Qe,u.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},u.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},u.toDate=function(){return new Date(this.valueOf())},u.toISOString=function(e){var t;return this.isValid()?(t=(e=!0!==e)?this.clone().utc():this).year()<0||9999<t.year()?re(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):a(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",re(t,"Z")):re(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ"):null},u.inspect=function(){var e,t,n;return this.isValid()?(t="moment",e="",this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z"),t="["+t+'("]',n=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(t+n+"-MM-DD[T]HH:mm:ss.SSS"+(e+'[")]'))):"moment.invalid(/* "+this._i+" */)"},"undefined"!=typeof Symbol&&null!=Symbol.for&&(u[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),u.toJSON=function(){return this.isValid()?this.toISOString():null},u.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},u.unix=function(){return Math.floor(this.valueOf()/1e3)},u.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},u.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},u.eraName=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].name;if(t[n].until<=e&&e<=t[n].since)return t[n].name}return""},u.eraNarrow=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].narrow;if(t[n].until<=e&&e<=t[n].since)return t[n].narrow}return""},u.eraAbbr=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].abbr;if(t[n].until<=e&&e<=t[n].since)return t[n].abbr}return""},u.eraYear=function(){for(var e,t,n=this.localeData().eras(),s=0,i=n.length;s<i;++s)if(e=n[s].since<=n[s].until?1:-1,t=this.clone().startOf("day").valueOf(),n[s].since<=t&&t<=n[s].until||n[s].until<=t&&t<=n[s].since)return(this.year()-_(n[s].since).year())*e+n[s].offset;return this.year()},u.year=Pe,u.isLeapYear=function(){return be(this.year())},u.weekYear=function(e){return un.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},u.isoWeekYear=function(e){return un.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},u.quarter=u.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},u.month=Ie,u.daysInMonth=function(){return He(this.year(),this.month())},u.week=u.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},u.isoWeek=u.isoWeeks=function(e){var t=Be(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},u.weeksInYear=function(){var e=this.localeData()._week;return N(this.year(),e.dow,e.doy)},u.weeksInWeekYear=function(){var e=this.localeData()._week;return N(this.weekYear(),e.dow,e.doy)},u.isoWeeksInYear=function(){return N(this.year(),1,4)},u.isoWeeksInISOWeekYear=function(){return N(this.isoWeekYear(),1,4)},u.date=ge,u.day=u.days=function(e){var t,n,s;return this.isValid()?(t=Ce(this,"Day"),null!=e?(n=e,s=this.localeData(),e="string"!=typeof n?n:isNaN(n)?"number"==typeof(n=s.weekdaysParse(n))?n:null:parseInt(n,10),this.add(e-t,"d")):t):null!=e?this:NaN},u.weekday=function(e){var t;return this.isValid()?(t=(this.day()+7-this.localeData()._week.dow)%7,null==e?t:this.add(e-t,"d")):null!=e?this:NaN},u.isoWeekday=function(e){var t,n;return this.isValid()?null!=e?(t=e,n=this.localeData(),n="string"==typeof t?n.weekdaysParse(t)%7||7:isNaN(t)?null:t,this.day(this.day()%7?n:n-7)):this.day()||7:null!=e?this:NaN},u.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},u.hour=u.hours=i,u.minute=u.minutes=ce,u.second=u.seconds=we,u.millisecond=u.milliseconds=fe,u.utcOffset=function(e,t,n){var s,i=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null==e)return this._isUTC?i:Et(this);if("string"==typeof e){if(null===(e=Vt(ve,e)))return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&t&&(s=Et(this)),this._offset=e,this._isUTC=!0,null!=s&&this.add(s,"m"),i!==e&&(!t||this._changeInProgress?$t(this,C(e-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,_.updateOffset(this,!0),this._changeInProgress=null)),this},u.utc=function(e){return this.utcOffset(0,e)},u.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e)&&this.subtract(Et(this),"m"),this},u.parseZone=function(){var e;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(e=Vt(Me,this._i))?this.utcOffset(e):this.utcOffset(0,!0)),this},u.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?R(e).utcOffset():0,(this.utcOffset()-e)%60==0)},u.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},u.isLocal=function(){return!!this.isValid()&&!this._isUTC},u.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},u.isUtc=At,u.isUTC=At,u.zoneAbbr=function(){return this._isUTC?"UTC":""},u.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},u.dates=e("dates accessor is deprecated. Use date instead.",ge),u.months=e("months accessor is deprecated. Use month instead",Ie),u.years=e("years accessor is deprecated. Use year instead",Pe),u.zone=e("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(e,t){return null!=e?(this.utcOffset(e="string"!=typeof e?-e:e,t),this):-this.utcOffset()}),u.isDSTShifted=e("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){var e,t;return g(this._isDSTShifted)&&(q(e={},this),(e=Nt(e))._a?(t=(e._isUTC?l:R)(e._a),this._isDSTShifted=this.isValid()&&0<function(e,t,n){for(var s=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),r=0,a=0;a<s;a++)(n&&e[a]!==t[a]||!n&&M(e[a])!==M(t[a]))&&r++;return r+i}(e._a,t.toArray())):this._isDSTShifted=!1),this._isDSTShifted});d=K.prototype;function cn(e,t,n,s){var i=P(),s=l().set(s,t);return i[n](s,e)}function fn(e,t,n){if(w(e)&&(t=e,e=void 0),e=e||"",null!=t)return cn(e,t,n,"month");for(var s=[],i=0;i<12;i++)s[i]=cn(e,i,n,"month");return s}function mn(e,t,n,s){t=("boolean"==typeof e?w(t)&&(n=t,t=void 0):(t=e,e=!1,w(n=t)&&(n=t,t=void 0)),t||"");var i,r=P(),a=e?r._week.dow:0,o=[];if(null!=n)return cn(t,(n+a)%7,s,"day");for(i=0;i<7;i++)o[i]=cn(t,(i+a)%7,s,"day");return o}d.calendar=function(e,t,n){return a(e=this._calendar[e]||this._calendar.sameElse)?e.call(t,n):e},d.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(te).map(function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e}).join(""),this._longDateFormat[e])},d.invalidDate=function(){return this._invalidDate},d.ordinal=function(e){return this._ordinal.replace("%d",e)},d.preparse=hn,d.postformat=hn,d.relativeTime=function(e,t,n,s){var i=this._relativeTime[n];return a(i)?i(e,t,n,s):i.replace(/%d/i,e)},d.pastFuture=function(e,t){return a(e=this._relativeTime[0<e?"future":"past"])?e(t):e.replace(/%s/i,t)},d.set=function(e){var t,n;for(n in e)c(e,n)&&(a(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},d.eras=function(e,t){for(var n,s=this._eras||P("en")._eras,i=0,r=s.length;i<r;++i){switch(typeof s[i].since){case"string":n=_(s[i].since).startOf("day"),s[i].since=n.valueOf();break}switch(typeof s[i].until){case"undefined":s[i].until=1/0;break;case"string":n=_(s[i].until).startOf("day").valueOf(),s[i].until=n.valueOf();break}}return s},d.erasParse=function(e,t,n){var s,i,r,a,o,u=this.eras();for(e=e.toUpperCase(),s=0,i=u.length;s<i;++s)if(r=u[s].name.toUpperCase(),a=u[s].abbr.toUpperCase(),o=u[s].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(a===e)return u[s];break;case"NNNN":if(r===e)return u[s];break;case"NNNNN":if(o===e)return u[s];break}else if(0<=[r,a,o].indexOf(e))return u[s]},d.erasConvertYear=function(e,t){var n=e.since<=e.until?1:-1;return void 0===t?_(e.since).year():_(e.since).year()+(t-e.offset)*n},d.erasAbbrRegex=function(e){return c(this,"_erasAbbrRegex")||an.call(this),e?this._erasAbbrRegex:this._erasRegex},d.erasNameRegex=function(e){return c(this,"_erasNameRegex")||an.call(this),e?this._erasNameRegex:this._erasRegex},d.erasNarrowRegex=function(e){return c(this,"_erasNarrowRegex")||an.call(this),e?this._erasNarrowRegex:this._erasRegex},d.months=function(e,t){return e?(y(this._months)?this._months:this._months[(this._months.isFormat||Ve).test(t)?"format":"standalone"])[e.month()]:y(this._months)?this._months:this._months.standalone},d.monthsShort=function(e,t){return e?(y(this._monthsShort)?this._monthsShort:this._monthsShort[Ve.test(t)?"format":"standalone"])[e.month()]:y(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},d.monthsParse=function(e,t,n){var s,i;if(this._monthsParseExact)return function(e,t,n){var s,i,r,e=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],s=0;s<12;++s)r=l([2e3,s]),this._shortMonthsParse[s]=this.monthsShort(r,"").toLocaleLowerCase(),this._longMonthsParse[s]=this.months(r,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(i=x.call(this._shortMonthsParse,e))?i:null:-1!==(i=x.call(this._longMonthsParse,e))?i:null:"MMM"===t?-1!==(i=x.call(this._shortMonthsParse,e))||-1!==(i=x.call(this._longMonthsParse,e))?i:null:-1!==(i=x.call(this._longMonthsParse,e))||-1!==(i=x.call(this._shortMonthsParse,e))?i:null}.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),s=0;s<12;s++){if(i=l([2e3,s]),n&&!this._longMonthsParse[s]&&(this._longMonthsParse[s]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[s]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),n||this._monthsParse[s]||(i="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[s]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[s].test(e))return s;if(n&&"MMM"===t&&this._shortMonthsParse[s].test(e))return s;if(!n&&this._monthsParse[s].test(e))return s}},d.monthsRegex=function(e){return this._monthsParseExact?(c(this,"_monthsRegex")||je.call(this),e?this._monthsStrictRegex:this._monthsRegex):(c(this,"_monthsRegex")||(this._monthsRegex=Ee),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},d.monthsShortRegex=function(e){return this._monthsParseExact?(c(this,"_monthsRegex")||je.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(c(this,"_monthsShortRegex")||(this._monthsShortRegex=Ge),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},d.week=function(e){return Be(e,this._week.dow,this._week.doy).week},d.firstDayOfYear=function(){return this._week.doy},d.firstDayOfWeek=function(){return this._week.dow},d.weekdays=function(e,t){return t=y(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"],!0===e?Je(t,this._week.dow):e?t[e.day()]:t},d.weekdaysMin=function(e){return!0===e?Je(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},d.weekdaysShort=function(e){return!0===e?Je(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},d.weekdaysParse=function(e,t,n){var s,i;if(this._weekdaysParseExact)return function(e,t,n){var s,i,r,e=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],s=0;s<7;++s)r=l([2e3,1]).day(s),this._minWeekdaysParse[s]=this.weekdaysMin(r,"").toLocaleLowerCase(),this._shortWeekdaysParse[s]=this.weekdaysShort(r,"").toLocaleLowerCase(),this._weekdaysParse[s]=this.weekdays(r,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(i=x.call(this._weekdaysParse,e))?i:null:"ddd"===t?-1!==(i=x.call(this._shortWeekdaysParse,e))?i:null:-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:"dddd"===t?-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._shortWeekdaysParse,e))||-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:"ddd"===t?-1!==(i=x.call(this._shortWeekdaysParse,e))||-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:-1!==(i=x.call(this._minWeekdaysParse,e))||-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._shortWeekdaysParse,e))?i:null}.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),s=0;s<7;s++){if(i=l([2e3,1]).day(s),n&&!this._fullWeekdaysParse[s]&&(this._fullWeekdaysParse[s]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[s]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[s]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[s]||(i="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[s]=new RegExp(i.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[s].test(e))return s;if(n&&"ddd"===t&&this._shortWeekdaysParse[s].test(e))return s;if(n&&"dd"===t&&this._minWeekdaysParse[s].test(e))return s;if(!n&&this._weekdaysParse[s].test(e))return s}},d.weekdaysRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(c(this,"_weekdaysRegex")||(this._weekdaysRegex=et),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},d.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(c(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=tt),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},d.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(c(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=nt),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},d.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},d.meridiem=function(e,t,n){return 11<e?n?"pm":"PM":n?"am":"AM"},ft("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===M(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),_.lang=e("moment.lang is deprecated. Use moment.locale instead.",ft),_.langData=e("moment.langData is deprecated. Use moment.localeData instead.",P);var _n=Math.abs;function yn(e,t,n,s){t=C(t,n);return e._milliseconds+=s*t._milliseconds,e._days+=s*t._days,e._months+=s*t._months,e._bubble()}function gn(e){return e<0?Math.floor(e):Math.ceil(e)}function wn(e){return 4800*e/146097}function pn(e){return 146097*e/4800}function kn(e){return function(){return this.as(e)}}de=kn("ms"),t=kn("s"),ye=kn("m"),he=kn("h"),Fe=kn("d"),_e=kn("w"),me=kn("M"),Qe=kn("Q"),i=kn("y"),ce=de;function Mn(e){return function(){return this.isValid()?this._data[e]:NaN}}var we=Mn("milliseconds"),fe=Mn("seconds"),ge=Mn("minutes"),Pe=Mn("hours"),d=Mn("days"),vn=Mn("months"),Dn=Mn("years");var Yn=Math.round,Sn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function On(e,t,n,s){var i=C(e).abs(),r=Yn(i.as("s")),a=Yn(i.as("m")),o=Yn(i.as("h")),u=Yn(i.as("d")),l=Yn(i.as("M")),d=Yn(i.as("w")),i=Yn(i.as("y")),r=(r<=n.ss?["s",r]:r<n.s&&["ss",r])||(a<=1?["m"]:a<n.m&&["mm",a])||(o<=1?["h"]:o<n.h&&["hh",o])||(u<=1?["d"]:u<n.d&&["dd",u]);return(r=(r=null!=n.w?r||(d<=1?["w"]:d<n.w&&["ww",d]):r)||(l<=1?["M"]:l<n.M&&["MM",l])||(i<=1?["y"]:["yy",i]))[2]=t,r[3]=0<+e,r[4]=s,function(e,t,n,s,i){return i.relativeTime(t||1,!!n,e,s)}.apply(null,r)}var bn=Math.abs;function Tn(e){return(0<e)-(e<0)||+e}function xn(){var e,t,n,s,i,r,a,o,u,l,d;return this.isValid()?(e=bn(this._milliseconds)/1e3,t=bn(this._days),n=bn(this._months),(o=this.asSeconds())?(s=m(e/60),i=m(s/60),e%=60,s%=60,r=m(n/12),n%=12,a=e?e.toFixed(3).replace(/\.?0+$/,""):"",u=Tn(this._months)!==Tn(o)?"-":"",l=Tn(this._days)!==Tn(o)?"-":"",d=Tn(this._milliseconds)!==Tn(o)?"-":"",(o<0?"-":"")+"P"+(r?u+r+"Y":"")+(n?u+n+"M":"")+(t?l+t+"D":"")+(i||s||e?"T":"")+(i?d+i+"H":"")+(s?d+s+"M":"")+(e?d+a+"S":"")):"P0D"):this.localeData().invalidDate()}var U=Ct.prototype;return U.isValid=function(){return this._isValid},U.abs=function(){var e=this._data;return this._milliseconds=_n(this._milliseconds),this._days=_n(this._days),this._months=_n(this._months),e.milliseconds=_n(e.milliseconds),e.seconds=_n(e.seconds),e.minutes=_n(e.minutes),e.hours=_n(e.hours),e.months=_n(e.months),e.years=_n(e.years),this},U.add=function(e,t){return yn(this,e,t,1)},U.subtract=function(e,t){return yn(this,e,t,-1)},U.as=function(e){if(!this.isValid())return NaN;var t,n,s=this._milliseconds;if("month"===(e=o(e))||"quarter"===e||"year"===e)switch(t=this._days+s/864e5,n=this._months+wn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(pn(this._months)),e){case"week":return t/7+s/6048e5;case"day":return t+s/864e5;case"hour":return 24*t+s/36e5;case"minute":return 1440*t+s/6e4;case"second":return 86400*t+s/1e3;case"millisecond":return Math.floor(864e5*t)+s;default:throw new Error("Unknown unit "+e)}},U.asMilliseconds=de,U.asSeconds=t,U.asMinutes=ye,U.asHours=he,U.asDays=Fe,U.asWeeks=_e,U.asMonths=me,U.asQuarters=Qe,U.asYears=i,U.valueOf=ce,U._bubble=function(){var e=this._milliseconds,t=this._days,n=this._months,s=this._data;return 0<=e&&0<=t&&0<=n||e<=0&&t<=0&&n<=0||(e+=864e5*gn(pn(n)+t),n=t=0),s.milliseconds=e%1e3,e=m(e/1e3),s.seconds=e%60,e=m(e/60),s.minutes=e%60,e=m(e/60),s.hours=e%24,t+=m(e/24),n+=e=m(wn(t)),t-=gn(pn(e)),e=m(n/12),n%=12,s.days=t,s.months=n,s.years=e,this},U.clone=function(){return C(this)},U.get=function(e){return e=o(e),this.isValid()?this[e+"s"]():NaN},U.milliseconds=we,U.seconds=fe,U.minutes=ge,U.hours=Pe,U.days=d,U.weeks=function(){return m(this.days()/7)},U.months=vn,U.years=Dn,U.humanize=function(e,t){var n,s;return this.isValid()?(n=!1,s=Sn,"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(n=e),"object"==typeof t&&(s=Object.assign({},Sn,t),null!=t.s)&&null==t.ss&&(s.ss=t.s-1),e=this.localeData(),t=On(this,!n,s,e),n&&(t=e.pastFuture(+this,t)),e.postformat(t)):this.localeData().invalidDate()},U.toISOString=xn,U.toString=xn,U.toJSON=xn,U.locale=Xt,U.localeData=Kt,U.toIsoString=e("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",xn),U.lang=Ke,s("X",0,0,"unix"),s("x",0,0,"valueOf"),h("x",ke),h("X",/[+-]?\d+(\.\d{1,3})?/),v("X",function(e,t,n){n._d=new Date(1e3*parseFloat(e))}),v("x",function(e,t,n){n._d=new Date(M(e))}),_.version="2.30.1",H=R,_.fn=u,_.min=function(){return Pt("isBefore",[].slice.call(arguments,0))},_.max=function(){return Pt("isAfter",[].slice.call(arguments,0))},_.now=function(){return Date.now?Date.now():+new Date},_.utc=l,_.unix=function(e){return R(1e3*e)},_.months=function(e,t){return fn(e,t,"months")},_.isDate=V,_.locale=ft,_.invalid=I,_.duration=C,_.isMoment=k,_.weekdays=function(e,t,n){return mn(e,t,n,"weekdays")},_.parseZone=function(){return R.apply(null,arguments).parseZone()},_.localeData=P,_.isDuration=Ut,_.monthsShort=function(e,t){return fn(e,t,"monthsShort")},_.weekdaysMin=function(e,t,n){return mn(e,t,n,"weekdaysMin")},_.defineLocale=mt,_.updateLocale=function(e,t){var n,s;return null!=t?(s=ut,null!=W[e]&&null!=W[e].parentLocale?W[e].set(X(W[e]._config,t)):(t=X(s=null!=(n=ct(e))?n._config:s,t),null==n&&(t.abbr=e),(s=new K(t)).parentLocale=W[e],W[e]=s),ft(e)):null!=W[e]&&(null!=W[e].parentLocale?(W[e]=W[e].parentLocale,e===ft()&&ft(e)):null!=W[e]&&delete W[e]),W[e]},_.locales=function(){return ee(W)},_.weekdaysShort=function(e,t,n){return mn(e,t,n,"weekdaysShort")},_.normalizeUnits=o,_.relativeTimeRounding=function(e){return void 0===e?Yn:"function"==typeof e&&(Yn=e,!0)},_.relativeTimeThreshold=function(e,t){return void 0!==Sn[e]&&(void 0===t?Sn[e]:(Sn[e]=t,"s"===e&&(Sn.ss=t-1),!0))},_.calendarFormat=function(e,t){return(e=e.diff(t,"days",!0))<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},_.prototype=u,_.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},_});
//# sourceMappingURL=moment.min.js.map
(function(t, e) {
    "object" === typeof exports && "object" === typeof module ? module.exports = e() : "function" === typeof define && define.amd ? define([], e) : "object" === typeof exports ? exports["VueStarRating"] = e() : t["VueStarRating"] = e()
})("undefined" !== typeof self ? self : this, (function() {
    return function(t) {
        var e = {};

        function r(i) {
            if (e[i]) return e[i].exports;
            var n = e[i] = {
                i: i,
                l: !1,
                exports: {}
            };
            return t[i].call(n.exports, n, n.exports, r), n.l = !0, n.exports
        }
        return r.m = t, r.c = e, r.d = function(t, e, i) {
            r.o(t, e) || Object.defineProperty(t, e, {
                enumerable: !0,
                get: i
            })
        }, r.r = function(t) {
            "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, {
                value: "Module"
            }), Object.defineProperty(t, "__esModule", {
                value: !0
            })
        }, r.t = function(t, e) {
            if (1 & e && (t = r(t)), 8 & e) return t;
            if (4 & e && "object" === typeof t && t && t.__esModule) return t;
            var i = Object.create(null);
            if (r.r(i), Object.defineProperty(i, "default", {
                    enumerable: !0,
                    value: t
                }), 2 & e && "string" != typeof t)
                for (var n in t) r.d(i, n, function(e) {
                    return t[e]
                }.bind(null, n));
            return i
        }, r.n = function(t) {
            var e = t && t.__esModule ? function() {
                return t["default"]
            } : function() {
                return t
            };
            return r.d(e, "a", e), e
        }, r.o = function(t, e) {
            return Object.prototype.hasOwnProperty.call(t, e)
        }, r.p = "", r(r.s = "fb15")
    }({
        "27c2": function(t, e, r) {
            var i = r("4bad");
            e = i(!1), e.push([t.i, ".vue-star-rating-star[data-v-fde73a0c]{display:inline-block}.vue-star-rating-pointer[data-v-fde73a0c]{cursor:pointer}.vue-star-rating[data-v-fde73a0c]{display:flex;align-items:center}.vue-star-rating-inline[data-v-fde73a0c]{display:inline-flex}.vue-star-rating-rating-text[data-v-fde73a0c]{margin-left:7px}.vue-star-rating-rtl[data-v-fde73a0c]{direction:rtl}.vue-star-rating-rtl .vue-star-rating-rating-text[data-v-fde73a0c]{margin-right:10px;direction:rtl}.sr-only[data-v-fde73a0c]{position:absolute;left:-10000px;top:auto;width:1px;height:1px;overflow:hidden}", ""]), t.exports = e
        },
        "2b2b": function(t, e, r) {
            "use strict";
            var i = r("3c76"),
                n = r.n(i);
            n.a
        },
        "3c76": function(t, e, r) {
            var i = r("27c2");
            "string" === typeof i && (i = [
                [t.i, i, ""]
            ]), i.locals && (t.exports = i.locals);
            var n = r("499e").default;
            n("af45d76c", i, !0, {
                sourceMap: !1,
                shadowMode: !1
            })
        },
        "499e": function(t, e, r) {
            "use strict";

            function i(t, e) {
                for (var r = [], i = {}, n = 0; n < e.length; n++) {
                    var o = e[n],
                        a = o[0],
                        s = o[1],
                        l = o[2],
                        c = o[3],
                        d = {
                            id: t + ":" + n,
                            css: s,
                            media: l,
                            sourceMap: c
                        };
                    i[a] ? i[a].parts.push(d) : r.push(i[a] = {
                        id: a,
                        parts: [d]
                    })
                }
                return r
            }
            r.r(e), r.d(e, "default", (function() {
                return p
            }));
            var n = "undefined" !== typeof document;
            if ("undefined" !== typeof DEBUG && DEBUG && !n) throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");
            var o = {},
                a = n && (document.head || document.getElementsByTagName("head")[0]),
                s = null,
                l = 0,
                c = !1,
                d = function() {},
                u = null,
                h = "data-vue-ssr-id",
                f = "undefined" !== typeof navigator && /msie [6-9]\b/.test(navigator.userAgent.toLowerCase());

            function p(t, e, r, n) {
                c = r, u = n || {};
                var a = i(t, e);
                return g(a),
                    function(e) {
                        for (var r = [], n = 0; n < a.length; n++) {
                            var s = a[n],
                                l = o[s.id];
                            l.refs--, r.push(l)
                        }
                        e ? (a = i(t, e), g(a)) : a = [];
                        for (n = 0; n < r.length; n++) {
                            l = r[n];
                            if (0 === l.refs) {
                                for (var c = 0; c < l.parts.length; c++) l.parts[c]();
                                delete o[l.id]
                            }
                        }
                    }
            }

            function g(t) {
                for (var e = 0; e < t.length; e++) {
                    var r = t[e],
                        i = o[r.id];
                    if (i) {
                        i.refs++;
                        for (var n = 0; n < i.parts.length; n++) i.parts[n](r.parts[n]);
                        for (; n < r.parts.length; n++) i.parts.push(m(r.parts[n]));
                        i.parts.length > r.parts.length && (i.parts.length = r.parts.length)
                    } else {
                        var a = [];
                        for (n = 0; n < r.parts.length; n++) a.push(m(r.parts[n]));
                        o[r.id] = {
                            id: r.id,
                            refs: 1,
                            parts: a
                        }
                    }
                }
            }

            function v() {
                var t = document.createElement("style");
                t.setAttribute("nonce", yunoNonce); 
                return t.type = "text/css", a.appendChild(t), t
            }

            function m(t) {
                var e, r, i = document.querySelector("style[" + h + '~="' + t.id + '"]');
                if (i) {
                    if (c) return d;
                    i.parentNode.removeChild(i)
                }
                if (f) {
                    var n = l++;
                    i = s || (s = v()), e = b.bind(null, i, n, !1), r = b.bind(null, i, n, !0)
                } else i = v(), e = C.bind(null, i), r = function() {
                    i.parentNode.removeChild(i)
                };
                return e(t),
                    function(i) {
                        if (i) {
                            if (i.css === t.css && i.media === t.media && i.sourceMap === t.sourceMap) return;
                            e(t = i)
                        } else r()
                    }
            }
            var y = function() {
                var t = [];
                return function(e, r) {
                    return t[e] = r, t.filter(Boolean).join("\n")
                }
            }();

            function b(t, e, r, i) {
                var n = r ? "" : i.css;
                if (t.styleSheet) t.styleSheet.cssText = y(e, n);
                else {
                    var o = document.createTextNode(n),
                        a = t.childNodes;
                    a[e] && t.removeChild(a[e]), a.length ? t.insertBefore(o, a[e]) : t.appendChild(o)
                }
            }

            function C(t, e) {
                var r = e.css,
                    i = e.media,
                    n = e.sourceMap;
                if (i && t.setAttribute("media", i), u.ssrId && t.setAttribute(h, e.id), n && (r += "\n/*# sourceURL=" + n.sources[0] + " */", r += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(n)))) + " */"), t.styleSheet) t.styleSheet.cssText = r;
                else {
                    while (t.firstChild) t.removeChild(t.firstChild);
                    t.appendChild(document.createTextNode(r))
                }
            }
        },
        "4bad": function(t, e, r) {
            "use strict";

            function i(t, e) {
                var r = t[1] || "",
                    i = t[3];
                if (!i) return r;
                if (e && "function" === typeof btoa) {
                    var o = n(i),
                        a = i.sources.map((function(t) {
                            return "/*# sourceURL=".concat(i.sourceRoot || "").concat(t, " */")
                        }));
                    return [r].concat(a).concat([o]).join("\n")
                }
                return [r].join("\n")
            }

            function n(t) {
                var e = btoa(unescape(encodeURIComponent(JSON.stringify(t)))),
                    r = "sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(e);
                return "/*# ".concat(r, " */")
            }
            t.exports = function(t) {
                var e = [];
                return e.toString = function() {
                    return this.map((function(e) {
                        var r = i(e, t);
                        return e[2] ? "@media ".concat(e[2], " {").concat(r, "}") : r
                    })).join("")
                }, e.i = function(t, r, i) {
                    "string" === typeof t && (t = [
                        [null, t, ""]
                    ]);
                    var n = {};
                    if (i)
                        for (var o = 0; o < this.length; o++) {
                            var a = this[o][0];
                            null != a && (n[a] = !0)
                        }
                    for (var s = 0; s < t.length; s++) {
                        var l = [].concat(t[s]);
                        i && n[l[0]] || (r && (l[2] ? l[2] = "".concat(r, " and ").concat(l[2]) : l[2] = r), e.push(l))
                    }
                }, e
            }
        },
        "70a0": function(t, e, r) {
            var i = r("812a");
            "string" === typeof i && (i = [
                [t.i, i, ""]
            ]), i.locals && (t.exports = i.locals);
            var n = r("499e").default;
            n("4599b915", i, !0, {
                sourceMap: !1,
                shadowMode: !1
            })
        },
        "812a": function(t, e, r) {
            var i = r("4bad");
            e = i(!1), e.push([t.i, ".vue-star-rating-star[data-v-ef4bc576]{overflow:visible!important}.vue-star-rating-star-rotate[data-v-ef4bc576]{transition:all .25s}.vue-star-rating-star-rotate[data-v-ef4bc576]:hover{transition:transform .25s;transform:rotate(-15deg) scale(1.3)}", ""]), t.exports = e
        },
        8875: function(t, e, r) {
            var i, n, o;
            (function(r, a) {
                n = [], i = a, o = "function" === typeof i ? i.apply(e, n) : i, void 0 === o || (t.exports = o)
            })("undefined" !== typeof self && self, (function() {
                function t() {
                    var e = Object.getOwnPropertyDescriptor(document, "currentScript");
                    if (!e && "currentScript" in document && document.currentScript) return document.currentScript;
                    if (e && e.get !== t && document.currentScript) return document.currentScript;
                    try {
                        throw new Error
                    } catch (f) {
                        var r, i, n, o = /.*at [^(]*\((.*):(.+):(.+)\)$/gi,
                            a = /@([^@]*):(\d+):(\d+)\s*$/gi,
                            s = o.exec(f.stack) || a.exec(f.stack),
                            l = s && s[1] || !1,
                            c = s && s[2] || !1,
                            d = document.location.href.replace(document.location.hash, ""),
                            u = document.getElementsByTagName("script");
                        l === d && (r = document.documentElement.outerHTML, i = new RegExp("(?:[^\\n]+?\\n){0," + (c - 2) + "}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*", "i"), n = r.replace(i, "$1").trim());
                        for (var h = 0; h < u.length; h++) {
                            if ("interactive" === u[h].readyState) return u[h];
                            if (u[h].src === l) return u[h];
                            if (l === d && u[h].innerHTML && u[h].innerHTML.trim() === n) return u[h]
                        }
                        return null
                    }
                }
                return t
            }))
        },
        ab73: function(t, e, r) {
            "use strict";
            var i = r("70a0"),
                n = r.n(i);
            n.a
        },
        d4aa: function(t, e) {
            class r {
                constructor(t) {
                    this.color = t
                }
                parseAlphaColor() {
                    return /^rgba\((\d{1,3}%?\s*,\s*){3}(\d*(?:\.\d+)?)\)$/.test(this.color) ? this.parseRgba() : /^hsla\(\d+\s*,\s*([\d.]+%\s*,\s*){2}(\d*(?:\.\d+)?)\)$/.test(this.color) ? this.parseHsla() : /^#([0-9A-Fa-f]{4}|[0-9A-Fa-f]{8})$/.test(this.color) ? this.parseAlphaHex() : /^transparent$/.test(this.color) ? this.parseTransparent() : {
                        color: this.color,
                        opacity: "1"
                    }
                }
                parseRgba() {
                    return {
                        color: this.color.replace(/,(?!.*,).*(?=\))|a/g, ""),
                        opacity: this.color.match(/\.\d+|[01](?=\))/)[0]
                    }
                }
                parseHsla() {
                    return {
                        color: this.color.replace(/,(?!.*,).*(?=\))|a/g, ""),
                        opacity: this.color.match(/\.\d+|[01](?=\))/)[0]
                    }
                }
                parseAlphaHex() {
                    return {
                        color: 5 === this.color.length ? this.color.substring(0, 4) : this.color.substring(0, 7),
                        opacity: 5 === this.color.length ? (parseInt(this.color.substring(4, 5) + this.color.substring(4, 5), 16) / 255).toFixed(2) : (parseInt(this.color.substring(7, 9), 16) / 255).toFixed(2)
                    }
                }
                parseTransparent() {
                    return {
                        color: "#fff",
                        opacity: 0
                    }
                }
            }
            t.exports = r
        },
        fb15: function(t, e, r) {
            "use strict";
            if (r.r(e), "undefined" !== typeof window) {
                var i = window.document.currentScript,
                    n = r("8875");
                i = n(), "currentScript" in document || Object.defineProperty(document, "currentScript", {
                    get: n
                });
                var o = i && i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);
                o && (r.p = o[1])
            }
            var a = function() {
                    var t = this,
                        e = t.$createElement,
                        r = t._self._c || e;
                    return r("div", {
                        class: ["vue-star-rating", {
                            "vue-star-rating-rtl": t.rtl
                        }, {
                            "vue-star-rating-inline": t.inline
                        }]
                    }, [r("div", {
                        staticClass: "sr-only"
                    }, [t._t("screen-reader", [r("span", [t._v("Rated " + t._s(t.selectedRating) + " stars out of " + t._s(t.maxRating))])], {
                        rating: t.selectedRating,
                        stars: t.maxRating
                    })], 2), r("div", {
                        staticClass: "vue-star-rating",
                        on: {
                            mouseleave: t.resetRating
                        }
                    }, [t._l(t.maxRating, (function(e) {
                        return r("span", {
                            key: e,
                            class: [{
                                "vue-star-rating-pointer": !t.readOnly
                            }, "vue-star-rating-star"],
                            style: {
                                "margin-right": t.margin + "px"
                            }
                        }, [r("star", {
                            attrs: {
                                fill: t.fillLevel[e - 1],
                                size: t.starSize,
                                points: t.starPoints,
                                "star-id": e,
                                step: t.step,
                                "active-color": t.currentActiveColor,
                                "inactive-color": t.inactiveColor,
                                "border-color": t.borderColor,
                                "active-border-color": t.currentActiveBorderColor,
                                "border-width": t.borderWidth,
                                "rounded-corners": t.roundedCorners,
                                rtl: t.rtl,
                                glow: t.glow,
                                "glow-color": t.glowColor,
                                animate: t.animate
                            },
                            on: {
                                "star-selected": function(e) {
                                    return t.setRating(e, !0)
                                },
                                "star-mouse-move": t.setRating
                            }
                        })], 1)
                    })), t.showRating ? r("span", {
                        class: ["vue-star-rating-rating-text", t.textClass]
                    }, [t._v(" " + t._s(t.formattedRating))]) : t._e()], 2)])
                },
                s = [],
                l = function() {
                    var t = this,
                        e = t.$createElement,
                        r = t._self._c || e;
                    return r("svg", {
                        class: ["vue-star-rating-star", {
                            "vue-star-rating-star-rotate": t.shouldAnimate
                        }],
                        attrs: {
                            height: t.starSize,
                            width: t.starSize,
                            viewBox: t.viewBox
                        },
                        on: {
                            mousemove: t.mouseMoving,
                            click: t.selected,
                            touchstart: t.touchStart,
                            touchend: t.touchEnd
                        }
                    }, [r("linearGradient", {
                        attrs: {
                            id: t.grad,
                            x1: "0",
                            x2: "100%",
                            y1: "0",
                            y2: "0"
                        }
                    }, [r("stop", {
                        attrs: {
                            offset: t.starFill,
                            "stop-color": t.rtl ? t.getColor(t.inactiveColor) : t.getColor(t.activeColor),
                            "stop-opacity": t.rtl ? t.getOpacity(t.inactiveColor) : t.getOpacity(t.activeColor)
                        }
                    }), r("stop", {
                        attrs: {
                            offset: t.starFill,
                            "stop-color": t.rtl ? t.getColor(t.activeColor) : t.getColor(t.inactiveColor),
                            "stop-opacity": t.rtl ? t.getOpacity(t.activeColor) : t.getOpacity(t.inactiveColor)
                        }
                    })], 1), r("filter", {
                        attrs: {
                            id: t.glowId,
                            height: "130%",
                            width: "130%",
                            filterUnits: "userSpaceOnUse"
                        }
                    }, [r("feGaussianBlur", {
                        attrs: {
                            stdDeviation: t.glow,
                            result: "coloredBlur"
                        }
                    }), r("feMerge", [r("feMergeNode", {
                        attrs: {
                            in: "coloredBlur"
                        }
                    }), r("feMergeNode", {
                        attrs: {
                            in: "SourceGraphic"
                        }
                    })], 1)], 1), t.glowColor && t.glow > 0 ? r("polygon", {
                        directives: [{
                            name: "show",
                            rawName: "v-show",
                            value: t.fill > 1,
                            expression: "fill > 1"
                        }],
                        attrs: {
                            points: t.starPointsToString,
                            fill: t.gradId,
                            stroke: t.glowColor,
                            filter: "url(#" + t.glowId + ")",
                            "stroke-width": t.border
                        }
                    }) : t._e(), r("polygon", {
                        attrs: {
                            points: t.starPointsToString,
                            fill: t.gradId,
                            stroke: t.getBorderColor,
                            "stroke-width": t.border,
                            "stroke-linejoin": t.strokeLinejoin
                        }
                    }), r("polygon", {
                        attrs: {
                            points: t.starPointsToString,
                            fill: t.gradId
                        }
                    })], 1)
                },
                c = [],
                d = r("d4aa"),
                u = r.n(d),
                h = {
                    name: "Star",
                    props: {
                        fill: {
                            type: Number,
                            default: 0
                        },
                        points: {
                            type: Array,
                            default () {
                                return []
                            }
                        },
                        size: {
                            type: Number,
                            default: 50
                        },
                        starId: {
                            type: Number,
                            required: !0
                        },
                        activeColor: {
                            type: String,
                            required: !0
                        },
                        inactiveColor: {
                            type: String,
                            required: !0
                        },
                        borderColor: {
                            type: String,
                            default: "#000"
                        },
                        activeBorderColor: {
                            type: String,
                            default: "#000"
                        },
                        borderWidth: {
                            type: Number,
                            default: 0
                        },
                        roundedCorners: {
                            type: Boolean,
                            default: !1
                        },
                        rtl: {
                            type: Boolean,
                            default: !1
                        },
                        glow: {
                            type: Number,
                            default: 0
                        },
                        glowColor: {
                            type: String,
                            default: null,
                            required: !1
                        },
                        animate: {
                            type: Boolean,
                            default: !1
                        }
                    },
                    data() {
                        return {
                            starPoints: [19.8, 2.2, 6.6, 43.56, 39.6, 17.16, 0, 17.16, 33, 43.56],
                            grad: "",
                            glowId: "",
                            isStarActive: !0
                        }
                    },
                    computed: {
                        starPointsToString() {
                            return this.starPoints.join(",")
                        },
                        gradId() {
                            return "url(#" + this.grad + ")"
                        },
                        starSize() {
                            const t = this.roundedCorners && this.borderWidth <= 0 ? parseInt(this.size) - parseInt(this.border) : this.size;
                            return parseInt(t) + parseInt(this.border)
                        },
                        starFill() {
                            return this.rtl ? 100 - this.fill + "%" : this.fill + "%"
                        },
                        border() {
                            return this.roundedCorners && this.borderWidth <= 0 ? 6 : this.borderWidth
                        },
                        getBorderColor() {
                            return this.roundedCorners && this.borderWidth <= 0 ? this.fill <= 0 ? this.inactiveColor : this.activeColor : this.fill <= 0 ? this.borderColor : this.activeBorderColor
                        },
                        maxSize() {
                            return this.starPoints.reduce((function(t, e) {
                                return Math.max(t, e)
                            }))
                        },
                        viewBox() {
                            return "0 0 " + this.maxSize + " " + this.maxSize
                        },
                        shouldAnimate() {
                            return this.animate && this.isStarActive
                        },
                        strokeLinejoin() {
                            return this.roundedCorners ? "round" : "miter"
                        }
                    },
                    created() {
                        this.starPoints = this.points.length ? this.points : this.starPoints, this.calculatePoints(), this.grad = this.getRandomId(), this.glowId = this.getRandomId()
                    },
                    methods: {
                        mouseMoving(t) {
                            "undefined" !== t.touchAction && this.$emit("star-mouse-move", {
                                event: t,
                                position: this.getPosition(t),
                                id: this.starId
                            })
                        },
                        touchStart() {
                            this.$nextTick(() => {
                                this.isStarActive = !0
                            })
                        },
                        touchEnd() {
                            this.$nextTick(() => {
                                this.isStarActive = !1
                            })
                        },
                        getPosition(t) {
                            var e = .92 * this.size;
                            const r = this.rtl ? Math.min(t.offsetX, 45) : Math.max(t.offsetX, 1);
                            var i = Math.round(100 / e * r);
                            return Math.min(i, 100)
                        },
                        selected(t) {
                            this.$emit("star-selected", {
                                id: this.starId,
                                position: this.getPosition(t)
                            })
                        },
                        getRandomId() {
                            return Math.random().toString(36).substring(7)
                        },
                        calculatePoints() {
                            this.starPoints = this.starPoints.map((t, e) => {
                                const r = e % 2 === 0 ? 1.5 * this.border : 0;
                                return this.size / this.maxSize * t + r
                            })
                        },
                        getColor(t) {
                            return new u.a(t).parseAlphaColor().color
                        },
                        getOpacity(t) {
                            return new u.a(t).parseAlphaColor().opacity
                        }
                    }
                },
                f = h;
            r("ab73");

            function p(t, e, r, i, n, o, a, s) {
                var l, c = "function" === typeof t ? t.options : t;
                if (e && (c.render = e, c.staticRenderFns = r, c._compiled = !0), i && (c.functional = !0), o && (c._scopeId = "data-v-" + o), a ? (l = function(t) {
                        t = t || this.$vnode && this.$vnode.ssrContext || this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext, t || "undefined" === typeof __VUE_SSR_CONTEXT__ || (t = __VUE_SSR_CONTEXT__), n && n.call(this, t), t && t._registeredComponents && t._registeredComponents.add(a)
                    }, c._ssrRegister = l) : n && (l = s ? function() {
                        n.call(this, (c.functional ? this.parent : this).$root.$options.shadowRoot)
                    } : n), l)
                    if (c.functional) {
                        c._injectStyles = l;
                        var d = c.render;
                        c.render = function(t, e) {
                            return l.call(e), d(t, e)
                        }
                    } else {
                        var u = c.beforeCreate;
                        c.beforeCreate = u ? [].concat(u, l) : [l]
                    } return {
                    exports: t,
                    options: c
                }
            }
            var g = p(f, l, c, !1, null, "ef4bc576", null),
                v = g.exports,
                m = {
                    components: {
                        star: v
                    },
                    model: {
                        prop: "rating",
                        event: "rating-selected"
                    },
                    props: {
                        increment: {
                            type: Number,
                            default: 1
                        },
                        rating: {
                            type: Number,
                            default: 0
                        },
                        roundStartRating: {
                            type: Boolean,
                            default: !0
                        },
                        activeColor: {
                            type: [String, Array],
                            default: "#ffd055"
                        },
                        inactiveColor: {
                            type: String,
                            default: "#d8d8d8"
                        },
                        maxRating: {
                            type: Number,
                            default: 5
                        },
                        starPoints: {
                            type: Array,
                            default () {
                                return []
                            }
                        },
                        starSize: {
                            type: Number,
                            default: 50
                        },
                        showRating: {
                            type: Boolean,
                            default: !0
                        },
                        readOnly: {
                            type: Boolean,
                            default: !1
                        },
                        textClass: {
                            type: String,
                            default: ""
                        },
                        inline: {
                            type: Boolean,
                            default: !1
                        },
                        borderColor: {
                            type: String,
                            default: "#999"
                        },
                        activeBorderColor: {
                            type: [String, Array],
                            default: null
                        },
                        borderWidth: {
                            type: Number,
                            default: 0
                        },
                        roundedCorners: {
                            type: Boolean,
                            default: !1
                        },
                        padding: {
                            type: Number,
                            default: 0
                        },
                        rtl: {
                            type: Boolean,
                            default: !1
                        },
                        fixedPoints: {
                            type: Number,
                            default: null
                        },
                        glow: {
                            type: Number,
                            default: 0
                        },
                        glowColor: {
                            type: String,
                            default: "#fff"
                        },
                        clearable: {
                            type: Boolean,
                            default: !1
                        },
                        activeOnClick: {
                            type: Boolean,
                            default: !1
                        },
                        animate: {
                            type: Boolean,
                            default: !1
                        }
                    },
                    data() {
                        return {
                            step: 0,
                            fillLevel: [],
                            currentRating: 0,
                            selectedRating: 0,
                            ratingSelected: !1
                        }
                    },
                    computed: {
                        formattedRating() {
                            return null === this.fixedPoints ? this.currentRating : this.currentRating.toFixed(this.fixedPoints)
                        },
                        shouldRound() {
                            return this.ratingSelected || this.roundStartRating
                        },
                        margin() {
                            return this.padding + this.borderWidth
                        },
                        activeColors() {
                            return Array.isArray(this.activeColor) ? this.padColors(this.activeColor, this.maxRating, this.activeColor.slice(-1)[0]) : new Array(this.maxRating).fill(this.activeColor)
                        },
                        currentActiveColor() {
                            return this.activeOnClick ? this.selectedRating > 0 ? this.activeColors[Math.ceil(this.selectedRating) - 1] : this.inactiveColor : this.currentRating > 0 ? this.activeColors[Math.ceil(this.currentRating) - 1] : this.inactiveColor
                        },
                        activeBorderColors() {
                            if (Array.isArray(this.activeBorderColor)) return this.padColors(this.activeBorderColor, this.maxRating, this.activeBorderColor.slice(-1)[0]);
                            let t = this.activeBorderColor ? this.activeBorderColor : this.borderColor;
                            return new Array(this.maxRating).fill(t)
                        },
                        currentActiveBorderColor() {
                            return this.activeOnClick ? this.selectedRating > 0 ? this.activeBorderColors[Math.ceil(this.selectedRating) - 1] : this.borderColor : this.currentRating > 0 ? this.activeBorderColors[Math.ceil(this.currentRating) - 1] : this.borderColor
                        }
                    },
                    watch: {
                        rating(t) {
                            this.currentRating = t, this.selectedRating = t, this.createStars(this.shouldRound)
                        }
                    },
                    created() {
                        this.step = 100 * this.increment, this.currentRating = this.rating, this.selectedRating = this.currentRating, this.createStars(this.roundStartRating)
                    },
                    methods: {
                        setRating(t, e) {
                            if (!this.readOnly) {
                                const r = this.rtl ? (100 - t.position) / 100 : t.position / 100;
                                this.currentRating = (t.id + r - 1).toFixed(2), this.currentRating = this.currentRating > this.maxRating ? this.maxRating : this.currentRating, e ? (this.createStars(!0, !0), this.clearable && this.currentRating === this.selectedRating ? this.selectedRating = 0 : this.selectedRating = this.currentRating, this.$emit("rating-selected", this.selectedRating), this.ratingSelected = !0) : (this.createStars(!0, !this.activeOnClick), this.$emit("current-rating", this.currentRating))
                            }
                        },
                        resetRating() {
                            this.readOnly || (this.currentRating = this.selectedRating, this.createStars(this.shouldRound))
                        },
                        createStars(t = !0, e = !0) {
                            t && this.round();
                            for (var r = 0; r < this.maxRating; r++) {
                                let t = 0;
                                r < this.currentRating && (t = this.currentRating - r > 1 ? 100 : 100 * (this.currentRating - r)), e && (this.fillLevel[r] = Math.round(t))
                            }
                        },
                        round() {
                            var t = 1 / this.increment;
                            this.currentRating = Math.min(this.maxRating, Math.ceil(this.currentRating * t) / t)
                        },
                        padColors(t, e, r) {
                            return Object.assign(new Array(e).fill(r), t)
                        }
                    }
                },
                y = m,
                b = (r("2b2b"), p(y, a, s, !1, null, "fde73a0c", null)),
                C = b.exports,
                R = C;
            e["default"] = R
        }
    })
}));
//# sourceMappingURL=VueStarRating.umd.min.js.map
/*
 MIT
 *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
*****************************************************************************/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(m){var p=0;return function(){return p<m.length?{done:!1,value:m[p++]}:{done:!0}}};$jscomp.arrayIterator=function(m){return{next:$jscomp.arrayIteratorImpl(m)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(m,p,t){if(m==Array.prototype||m==Object.prototype)return m;m[p]=t.value;return m};$jscomp.getGlobal=function(m){m=["object"==typeof globalThis&&globalThis,m,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var p=0;p<m.length;++p){var t=m[p];if(t&&t.Math==Math)return t}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(m,p){var t=$jscomp.propertyToPolyfillSymbol[p];if(null==t)return m[p];t=m[t];return void 0!==t?t:m[p]};
$jscomp.polyfill=function(m,p,t,u){p&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(m,p,t,u):$jscomp.polyfillUnisolated(m,p,t,u))};$jscomp.polyfillUnisolated=function(m,p,t,u){t=$jscomp.global;m=m.split(".");for(u=0;u<m.length-1;u++){var v=m[u];if(!(v in t))return;t=t[v]}m=m[m.length-1];u=t[m];p=p(u);p!=u&&null!=p&&$jscomp.defineProperty(t,m,{configurable:!0,writable:!0,value:p})};
$jscomp.polyfillIsolated=function(m,p,t,u){var v=m.split(".");m=1===v.length;u=v[0];u=!m&&u in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var z=0;z<v.length-1;z++){var A=v[z];if(!(A in u))return;u=u[A]}v=v[v.length-1];t=$jscomp.IS_SYMBOL_NATIVE&&"es6"===t?u[v]:null;p=p(t);null!=p&&(m?$jscomp.defineProperty($jscomp.polyfills,v,{configurable:!0,writable:!0,value:p}):p!==t&&($jscomp.propertyToPolyfillSymbol[v]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(v):$jscomp.POLYFILL_PREFIX+v,v=
$jscomp.propertyToPolyfillSymbol[v],$jscomp.defineProperty(u,v,{configurable:!0,writable:!0,value:p})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(m){if(m)return m;var p=function(v,z){this.$jscomp$symbol$id_=v;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:z})};p.prototype.toString=function(){return this.$jscomp$symbol$id_};var t=0,u=function(v){if(this instanceof u)throw new TypeError("Symbol is not a constructor");return new p("jscomp_symbol_"+(v||"")+"_"+t++,v)};return u},"es6","es3");$jscomp.initSymbolIterator=function(){};
$jscomp.polyfill("Symbol.iterator",function(m){if(m)return m;m=Symbol("Symbol.iterator");for(var p="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),t=0;t<p.length;t++){var u=$jscomp.global[p[t]];"function"===typeof u&&"function"!=typeof u.prototype[m]&&$jscomp.defineProperty(u.prototype,m,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return m},"es6",
"es3");$jscomp.initSymbolAsyncIterator=function(){};$jscomp.iteratorPrototype=function(m){m={next:m};m[Symbol.iterator]=function(){return this};return m};
(function(m,p){"object"===typeof exports&&"undefined"!==typeof module?p(exports,require("vue")):"function"===typeof define&&define.amd?define(["exports","vue"],p):(m=m||self,p(m.VeeValidate={},m.Vue))})(this,function(m,p){function t(a,b,c,d){return new (c||(c=Promise))(function(e,k){function h(g){try{l(d.next(g))}catch(n){k(n)}}function f(g){try{l(d["throw"](g))}catch(n){k(n)}}function l(g){g.done?e(g.value):(new c(function(n){n(g.value)})).then(h,f)}l((d=d.apply(a,b||[])).next())})}function u(a,
b){function c(g){return function(n){return d([g,n])}}function d(g){if(k)throw new TypeError("Generator is already executing.");for(;e;)try{if(k=1,h&&(f=g[0]&2?h["return"]:g[0]?h["throw"]||((f=h["return"])&&f.call(h),0):h.next)&&!(f=f.call(h,g[1])).done)return f;if(h=0,f)g=[g[0]&2,f.value];switch(g[0]){case 0:case 1:f=g;break;case 4:return e.label++,{value:g[1],done:!1};case 5:e.label++;h=g[1];g=[0];continue;case 7:g=e.ops.pop();e.trys.pop();continue;default:if(!(f=e.trys,f=0<f.length&&f[f.length-
1])&&(6===g[0]||2===g[0])){e=0;continue}if(3===g[0]&&(!f||g[1]>f[0]&&g[1]<f[3]))e.label=g[1];else if(6===g[0]&&e.label<f[1])e.label=f[1],f=g;else if(f&&e.label<f[2])e.label=f[2],e.ops.push(g);else{f[2]&&e.ops.pop();e.trys.pop();continue}}g=b.call(a,e)}catch(n){g=[6,n],h=0}finally{k=f=0}if(g[0]&5)throw g[1];return{value:g[0]?g[1]:void 0,done:!0}}var e={label:0,sent:function(){if(f[0]&1)throw f[1];return f[1]},trys:[],ops:[]},k,h,f,l;return l={next:c(0),"throw":c(1),"return":c(2)},"function"===typeof Symbol&&
(l[Symbol.iterator]=function(){return this}),l}function v(){for(var a=0,b=0,c=arguments.length;b<c;b++)a+=arguments[b].length;a=Array(a);var d=0;for(b=0;b<c;b++)for(var e=arguments[b],k=0,h=e.length;k<h;k++,d++)a[d]=e[k];return a}function z(a){return null===a||void 0===a}function A(a,b){if(a instanceof RegExp&&b instanceof RegExp)return A(a.source,b.source)&&A(a.flags,b.flags);if(Array.isArray(a)&&Array.isArray(b)){if(a.length!==b.length)return!1;for(var c=0;c<a.length;c++)if(!A(a[c],b[c]))return!1;
return!0}return B(a)&&B(b)?Object.keys(a).every(function(d){return A(a[d],b[d])})&&Object.keys(b).every(function(d){return A(a[d],b[d])}):a!==a&&b!==b?!0:a===b}function Z(a){return""===a?!1:!z(a)}function y(a){return"function"===typeof a}function E(a){return y(a)&&!!a.__locatorRef}function aa(a,b){var c=Array.isArray(a)?a:M(a);if(y(c.findIndex))return c.findIndex(b);for(var d=0;d<c.length;d++)if(b(c[d],d))return d;return-1}function za(a,b){var c=Array.isArray(a)?a:M(a),d=aa(c,b);return-1===d?void 0:
c[d]}function F(a,b){return-1!==a.indexOf(b)}function M(a){if(y(Array.from))return Array.from(a);for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}function N(a){return y(Object.values)?Object.values(a):Object.keys(a).map(function(b){return a[b]})}function O(a,b){Object.keys(b).forEach(function(c){B(b[c])?(a[c]||(a[c]={}),O(a[c],b[c])):a[c]=b[c]});return a}function P(){return{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,
failed:!1}}function Aa(a){return a}function ba(a,b,c){void 0===b&&(b=0);void 0===c&&(c={cancelled:!1});if(0===b)return a;var d;return function(){for(var e=[],k=0;k<arguments.length;k++)e[k]=arguments[k];clearTimeout(d);d=setTimeout(function(){d=void 0;c.cancelled||a.apply(void 0,e)},b)}}function Q(a,b){return a.replace(/{([^}]+)}/g,function(c,d){return d in b?b[d]:"{"+d+"}"})}function Ba(a){var b;if(null===(b=a.params)||void 0===b?0:b.length)a.params=a.params.map(function(c){return"string"===typeof c?
{name:c}:c});return a}function I(a){var b={};Object.defineProperty(b,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1});return a?B(a)&&a._$$isNormalized?a:B(a)?Object.keys(a).reduce(function(c,d){var e=!0===a[d]?[]:Array.isArray(a[d])?a[d]:B(a[d])?a[d]:[a[d]];!1!==a[d]&&(c[d]=ca(d,e));return c},b):"string"!==typeof a?(console.warn("[vee-validate] rules must be either a string or an object."),b):a.split("|").reduce(function(c,d){var e=[],k=d.split(":")[0];F(d,":")&&(e=d.split(":").slice(1).join(":").split(","));
if(!k)return c;c[k]=ca(k,e);return c},b):b}function ca(a,b){var c=x.getRuleDefinition(a);if(!c)return b;var d={};if(!c.params&&!Array.isArray(b))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(b)&&!c.params)return b;if(!c.params||c.params.length<b.length&&Array.isArray(b)){var e;var k=b.map(function(g,n){var q,w=null===(q=c.params)||void 0===q?void 0:q[n];e=w||e;w||(w=e);return w})}else k=c.params;for(var h=0;h<k.length;h++){var f=k[h],l=f["default"];
Array.isArray(b)?h in b&&(l=b[h]):f.name in b?l=b[f.name]:1===k.length&&(l=b);f.isTarget&&(l=da(l,f.cast));"string"===typeof l&&"@"===l[0]&&(l=da(l.slice(1),f.cast));!E(l)&&f.cast&&(l=f.cast(l));d[f.name]?(d[f.name]=Array.isArray(d[f.name])?d[f.name]:[d[f.name]],d[f.name].push(l)):d[f.name]=l}return d}function da(a,b){var c=function(d){d=d[a];return b?b(d):d};c.__locatorRef=a;return c}function Ca(a){return Array.isArray(a)?a.filter(E):Object.keys(a).filter(function(b){return E(a[b])}).map(function(b){return a[b]})}
function ea(a,b,c){void 0===c&&(c={});var d,e,k,h,f,l;return t(this,void 0,void 0,function(){var g,n,q,w,D,J,R;return u(this,function(fa){switch(fa.label){case 0:return g=null===(d=c)||void 0===d?void 0:d.bails,n=null===(e=c)||void 0===e?void 0:e.skipIfEmpty,q={name:(null===(k=c)||void 0===k?void 0:k.name)||"{field}",rules:I(b),bails:null!==g&&void 0!==g?g:!0,skipIfEmpty:null!==n&&void 0!==n?n:!0,forceRequired:!1,crossTable:(null===(h=c)||void 0===h?void 0:h.values)||{},names:(null===(f=c)||void 0===
f?void 0:f.names)||{},customMessages:(null===(l=c)||void 0===l?void 0:l.customMessages)||{}},[4,Da(q,a,c)];case 1:return w=fa.sent(),D=[],J={},R={},w.errors.forEach(function(L){var ha=L.msg();D.push(ha);J[L.rule]=ha;R[L.rule]=L.msg}),[2,{valid:w.valid,errors:D,failedRules:J,regenerateMap:R}]}})})}function Da(a,b,c){c=(void 0===c?{}:c).isInitial;var d=void 0===c?!1:c;return t(this,void 0,void 0,function(){var e,k,h,f,l,g,n,q;return u(this,function(w){switch(w.label){case 0:return[4,Ea(a,b)];case 1:e=
w.sent();k=e.shouldSkip;h=e.errors;if(k)return[2,{valid:!h.length,errors:h}];f=Object.keys(a.rules).filter(function(D){return!x.isRequireRule(D)});l=f.length;g=0;w.label=2;case 2:if(!(g<l))return[3,5];if(d&&x.isLazy(f[g]))return[3,4];n=f[g];return[4,ia(a,b,{name:n,params:a.rules[n]})];case 3:q=w.sent();if(!q.valid&&q.error&&(h.push(q.error),a.bails))return[2,{valid:!1,errors:h}];w.label=4;case 4:return g++,[3,2];case 5:return[2,{valid:!h.length,errors:h}]}})})}function Ea(a,b){return t(this,void 0,
void 0,function(){var c,d,e,k,h,f,l,g,n;return u(this,function(q){switch(q.label){case 0:c=Object.keys(a.rules).filter(x.isRequireRule);d=c.length;e=[];var w;(w=z(b)||""===b)||(w=Array.isArray(b)&&0===b.length);h=(k=w)&&a.skipIfEmpty;f=!1;l=0;q.label=1;case 1:if(!(l<d))return[3,4];g=c[l];return[4,ia(a,b,{name:g,params:a.rules[g]})];case 2:n=q.sent();if(!B(n))throw Error("Require rules has to return an object (see docs)");n.required&&(f=!0);if(!n.valid&&n.error&&(e.push(n.error),a.bails))return[2,
{shouldSkip:!0,errors:e}];q.label=3;case 3:return l++,[3,1];case 4:return k&&!f&&!a.skipIfEmpty||!a.bails&&!h?[2,{shouldSkip:!1,errors:e}]:[2,{shouldSkip:!f&&k,errors:e}]}})})}function ia(a,b,c){return t(this,void 0,void 0,function(){var d,e,k,h,f;return u(this,function(l){switch(l.label){case 0:d=x.getRuleDefinition(c.name);if(!d||!d.validate)throw Error("No such validator '"+c.name+"' exists.");e=d.castValue?d.castValue(b):b;k=Fa(c.params,a.crossTable);return[4,d.validate(e,k)];case 1:h=l.sent();
if("string"===typeof h)return f=r(r({},k||{}),{_field_:a.name,_value_:b,_rule_:c.name}),[2,{valid:!1,error:{rule:c.name,msg:function(){return Q(h,f)}}}];B(h)||(h={valid:h});return[2,{valid:h.valid,required:h.required,error:h.valid?void 0:Ga(a,b,d,c.name,k)}]}})})}function Ga(a,b,c,d,e){var k,h=(k=a.customMessages[d],null!==k&&void 0!==k?k:c.message);k=Ha(a,c,d);c=Ia(a,c,d,h);h=c.userTargets;var f=c.userMessage,l=r(r(r(r({},e||{}),{_field_:a.name,_value_:b,_rule_:d}),k),h);return{msg:function(){var g=
f||C.defaultMessage;var n=a.name;g="function"===typeof g?g(n,l):Q(g,r(r({},l),{_field_:n}));return g},rule:d}}function Ha(a,b,c){b=b.params;if(!b||0>=b.filter(function(f){return f.isTarget}).length)return{};var d={},e=a.rules[c];!Array.isArray(e)&&B(e)&&(e=b.map(function(f){return e[f.name]}));for(c=0;c<b.length;c++){var k=b[c],h=e[c];E(h)&&(h=h.__locatorRef,d[k.name]=a.names[h]||h,d["_"+k.name+"_"]=a.crossTable[h])}return d}function Ia(a,b,c,d){var e={},k=a.rules[c],h=b.params||[];if(!k)return{};
Object.keys(k).forEach(function(f,l){var g=k[f];if(!E(g))return{};var n=h[l];if(!n)return{};g=g.__locatorRef;e[n.name]=a.names[g]||g;e["_"+n.name+"_"]=a.crossTable[g]});return{userTargets:e,userMessage:d}}function Fa(a,b){if(Array.isArray(a))return a;var c={};Object.keys(a).forEach(function(d){var e=a[d];e=E(e)?e(b):e;c[d]=e});return c}function ja(){S.$emit("change:locale")}function Ja(a){var b,c;if(!a||!("undefined"!==typeof Event&&y(Event)&&a instanceof Event||a&&a.srcElement))return a;a=a.target;
return"file"===a.type&&a.files?M(a.files):(null===(b=a._vModifiers)||void 0===b?0:b.number)?(b=parseFloat(a.value),b!==b?a.value:b):(null===(c=a._vModifiers)||void 0===c?0:c.trim)?"string"===typeof a.value?a.value.trim():a.value:a.value}function T(a){if(a.data){var b=a.data;if("model"in b)return b.model;if(a.data.directives)return za(a.data.directives,function(c){return"model"===c.name})}}function U(a){var b,c,d,e=T(a);if(e)return{value:e.value};e=(null===(b=V(a))||void 0===b?void 0:b.prop)||"value";
if((null===(c=a.componentOptions)||void 0===c?0:c.propsData)&&e in a.componentOptions.propsData)return{value:a.componentOptions.propsData[e]};if((null===(d=a.data)||void 0===d?0:d.domProps)&&"value"in a.data.domProps)return{value:a.data.domProps.value}}function Ka(a){return Array.isArray(a)?a:Array.isArray(a.children)?a.children:a.componentOptions&&Array.isArray(a.componentOptions.children)?a.componentOptions.children:[]}function ka(a){return Array.isArray(a)||void 0===U(a)?Ka(a).reduce(function(b,
c){var d=ka(c);d.length&&b.push.apply(b,d);return b},[]):[a]}function V(a){return a.componentOptions?a.componentOptions.Ctor.options.model:null}function K(a,b,c){z(a[b])?a[b]=[c]:y(a[b])&&a[b].fns?(a=a[b],a.fns=Array.isArray(a.fns)?a.fns:[a.fns],F(a.fns,c)||a.fns.push(c)):(y(a[b])&&(a[b]=[a[b]]),Array.isArray(a[b])&&!F(a[b],c)&&a[b].push(c))}function W(a,b,c){a.componentOptions?a.componentOptions&&(a.componentOptions.listeners||(a.componentOptions.listeners={}),K(a.componentOptions.listeners,b,c)):
(a.data||(a.data={}),z(a.data.on)&&(a.data.on={}),K(a.data.on,b,c))}function la(a,b){var c;return a.componentOptions?(V(a)||{event:"input"}).event:(null===(c=null===b||void 0===b?void 0:b.modifiers)||void 0===c?0:c.lazy)?"change":ma(a)?"input":"change"}function La(a,b){return Object.keys(a).reduce(function(c,d){a[d].forEach(function(e){e.context||(a[d].context=b,e.data||(e.data={}),e.data.slot=d)});return c.concat(a[d])},[])}function na(a,b){return a.$scopedSlots["default"]?a.$scopedSlots["default"](b)||
[]:a.$slots["default"]||[]}function oa(a){return r(r({},a.flags),{errors:a.errors,classes:a.classes,failedRules:a.failedRules,reset:function(){return a.reset()},validate:function(){for(var b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];return a.validate.apply(a,b)},ariaInput:{"aria-invalid":a.flags.invalid?"true":"false","aria-required":a.isRequired?"true":"false","aria-errormessage":"vee_"+a.id},ariaMsg:{id:"vee_"+a.id,"aria-live":a.errors.length?"assertive":"off"}})}function pa(a,b){a.initialized||
(a.initialValue=b);var c=!a._ignoreImmediate&&a.immediate||a.value!==b&&a.normalizedEvents.length||a._needsValidation||!a.initialized&&void 0===b?!0:!1;a._needsValidation=!1;a.value=b;a._ignoreImmediate=!0;if(c){var d=function(){if(a.immediate||a.flags.validated)return X(a);a.validateSilent()};a.initialized?d():a.$once("hook:mounted",function(){return d()})}}function qa(a){return(y(a.mode)?a.mode:ra[a.mode])(a)}function X(a){var b=a.validateSilent();a._pendingValidation=b;return b.then(function(c){b===
a._pendingValidation&&(a.applyResult(c),a._pendingValidation=void 0);return c})}function sa(a){a.$veeOnInput||(a.$veeOnInput=function(k){a.syncValue(k);a.setFlags({dirty:!0,pristine:!1})});var b=a.$veeOnInput;a.$veeOnBlur||(a.$veeOnBlur=function(){a.setFlags({touched:!0,untouched:!1})});var c=a.$veeOnBlur,d=a.$veeHandler,e=qa(a);d&&a.$veeDebounce===a.debounce||(d=ba(function(){a.$nextTick(function(){a._pendingReset||X(a);a._pendingReset=!1})},e.debounce||a.debounce),a.$veeHandler=d,a.$veeDebounce=
a.debounce);return{onInput:b,onBlur:c,onValidate:d}}function Ma(a,b){var c=U(b);a._inputEventName=a._inputEventName||la(b,T(b));pa(a,null===c||void 0===c?void 0:c.value);c=sa(a);var d=c.onBlur,e=c.onValidate;W(b,a._inputEventName,c.onInput);W(b,"blur",d);a.normalizedEvents.forEach(function(k){W(b,k,e)});a.initialized=!0}function Na(a,b){for(var c={},d=Object.keys(b),e=d.length,k=function(f){f=d[f];var l=a&&a[f]||f,g=b[f];if(z(g)||("valid"===f||"invalid"===f)&&!b.validated)return"continue";"string"===
typeof l?c[l]=g:Array.isArray(l)&&l.forEach(function(n){c[n]=g})},h=0;h<e;h++)k(h);return c}function Oa(a){var b=a.$_veeObserver.refs;return a.fieldDeps.reduce(function(c,d){if(!b[d])return c;c.values[d]=b[d].value;c.names[d]=b[d].name;return c},{names:{},values:{}})}function Pa(a){if(a.vid)return a.vid;if(a.name)return a.name;if(a.id)return a.id;if(a.fieldName)return a.fieldName;ta++;return"_vee_"+ta}function Qa(){return{refs:{},observe:function(a){this.refs[a.id]=a},unobserve:function(a){delete this.refs[a]}}}
function ua(a,b,c){void 0===c&&(c=!0);var d=a.$_veeObserver.refs;a._veeWatchers||(a._veeWatchers={});if(!d[b]&&c)return a.$once("hook:mounted",function(){ua(a,b,!1)});!y(a._veeWatchers[b])&&d[b]&&(a._veeWatchers[b]=d[b].$watch("value",function(){a.flags.validated&&(a._needsValidation=!0,a.validate())}))}function va(a){a.$_veeObserver&&a.$_veeObserver.unobserve(a.id,"observer")}function wa(a){a.$_veeObserver&&a.$_veeObserver.observe(a,"observer")}function xa(){return r(r({},P()),{valid:!0,invalid:!1})}
function Ra(){for(var a=v(N(this.refs),this.observers),b={},c=xa(),d={},e=a.length,k=0;k<e;k++){var h=a[k];Array.isArray(h.errors)?(b[h.id]=h.errors,d[h.id]=r({id:h.id,name:h.name,failedRules:h.failedRules},h.flags)):(b=r(r({},b),h.errors),d=r(r({},d),h.fields))}Sa.forEach(function(f){var l=f[0];c[l]=a[f[1]](function(g){return g.flags[l]})});return{errors:b,flags:c,fields:d}}p=p&&p.hasOwnProperty("default")?p["default"]:p;var r=function(){r=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<
d;c++){b=arguments[c];for(var e in b)Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e])}return a};return r.apply(this,arguments)},B=function(a){return null!==a&&a&&"object"===typeof a&&!Array.isArray(a)},G={},x=function(){function a(){}a.extend=function(b,c){var d=Ba(c);G[b]=G[b]?O(G[b],c):r({lazy:!1,computesRequired:!1},d)};a.isLazy=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.lazy)};a.isRequireRule=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.computesRequired)};a.getRuleDefinition=
function(b){return G[b]};return a}(),C=r({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),Y=function(a){C=r(r({},C),a)},ra={aggressive:function(){return{on:["input","blur"]}},eager:function(a){return a.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},
S=new p,Ta=function(){function a(b,c){this.container={};this.locale=b;this.merge(c)}a.prototype.resolve=function(b,c,d){return this.format(this.locale,b,c,d)};a.prototype.format=function(b,c,d,e){var k,h,f,l,g,n,q,w;(d=(null===(f=null===(h=null===(k=this.container[b])||void 0===k?void 0:k.fields)||void 0===h?void 0:h[c])||void 0===f?void 0:f[d])||(null===(g=null===(l=this.container[b])||void 0===l?void 0:l.messages)||void 0===g?void 0:g[d]))||(d="{field} is not valid");c=(w=null===(q=null===(n=this.container[b])||
void 0===n?void 0:n.names)||void 0===q?void 0:q[c],null!==w&&void 0!==w?w:c);return y(d)?d(c,e):Q(d,r(r({},e),{_field_:c}))};a.prototype.merge=function(b){O(this.container,b)};a.prototype.hasRule=function(b){var c,d;return!(null===(d=null===(c=this.container[this.locale])||void 0===c?void 0:c.messages)||void 0===d||!d[b])};return a}(),H,ma=function(a){var b,c=(null===(b=a.data)||void 0===b?void 0:b.attrs)||a.elm;return("input"!==a.tag||c&&c.type)&&"textarea"!==a.tag?F("text password search email tel url number".split(" "),
null===c||void 0===c?void 0:c.type):!0},ta=0,ya=p.extend({inject:{$_veeObserver:{from:"$_veeObserver","default":function(){this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver=Qa());return this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,"default":""},name:{type:String,"default":null},mode:{type:[String,Function],"default":function(){return C.mode}},rules:{type:[Object,String],"default":null},immediate:{type:Boolean,"default":!1},bails:{type:Boolean,"default":function(){return C.bails}},
skipIfEmpty:{type:Boolean,"default":function(){return C.skipOptional}},debounce:{type:Number,"default":0},tag:{type:String,"default":"span"},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1},customMessages:{type:Object,"default":function(){return{}}}},watch:{rules:{deep:!0,handler:function(a,b){this._needsValidation=!A(a,b)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:P(),failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var a=
this;return Object.keys(this.normalizedRules).reduce(function(b,c){var d=Ca(a.normalizedRules[c]).map(function(e){return e.__locatorRef});b.push.apply(b,d);d.forEach(function(e){ua(a,e)});return b},[])},normalizedEvents:function(){var a=this;return(qa(this).on||[]).map(function(b){return"input"===b?a._inputEventName:b})},isRequired:function(){var a=r(r({},this._resolvedRules),this.normalizedRules);a=Object.keys(a).some(x.isRequireRule);this.flags.required=!!a;return a},classes:function(){return Na(C.classes,
this.flags)},normalizedRules:function(){return I(this.rules)}},created:function(){var a=this,b=function(){if(a.flags.validated){var c=a._regenerateMap;if(c){var d=[],e={};Object.keys(c).forEach(function(k){var h=c[k]();d.push(h);e[k]=h});a.applyResult({errors:d,failedRules:e,regenerateMap:c})}else a.validate()}};S.$on("change:locale",b);this.$on("hook:beforeDestroy",function(){S.$off("change:locale",b)})},render:function(a){var b=this;this.registerField();var c=oa(this);c=na(this,c);ka(c).forEach(function(d){var e,
k,h,f,l;if(C.useConstraintAttrs){var g,n=null===(g=d.data)||void 0===g?void 0:g.attrs;if(F(["input","select","textarea"],d.tag)&&n)if(g={},"required"in n&&!1!==n.required&&x.getRuleDefinition("required")&&(g.required="checkbox"===n.type?[!0]:!0),ma(d)){n=r;g=r({},g);var q=null===(l=d.data)||void 0===l?void 0:l.attrs;l={};q&&("email"===q.type&&x.getRuleDefinition("email")&&(l.email=["multiple"in q]),q.pattern&&x.getRuleDefinition("regex")&&(l.regex=q.pattern),0<=q.maxlength&&x.getRuleDefinition("max")&&
(l.max=q.maxlength),0<=q.minlength&&x.getRuleDefinition("min")&&(l.min=q.minlength),"number"===q.type&&(Z(q.min)&&x.getRuleDefinition("min_value")&&(l.min_value=Number(q.min)),Z(q.max)&&x.getRuleDefinition("max_value")&&(l.max_value=Number(q.max))));l=I(n(g,l))}else l=I(g);else l={}}else l={};A(b._resolvedRules,l)||(b._needsValidation=!0);F(["input","select","textarea"],d.tag)&&(b.fieldName=(null===(k=null===(e=d.data)||void 0===e?void 0:e.attrs)||void 0===k?void 0:k.name)||(null===(f=null===(h=d.data)||
void 0===h?void 0:h.attrs)||void 0===f?void 0:f.id));b._resolvedRules=l;Ma(b,d)});return this.slim&&1>=c.length?c[0]:a(this.tag,c)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(a){var b=this;Object.keys(a).forEach(function(c){b.flags[c]=a[c]})},syncValue:function(a){this.value=a=Ja(a);this.flags.changed=this.initialValue!==a},reset:function(){var a=this;this.errors=[];this.initialValue=
this.value;var b=P();b.required=this.isRequired;this.setFlags(b);this.failedRules={};this.validateSilent();this._pendingValidation=void 0;this._pendingReset=!0;setTimeout(function(){a._pendingReset=!1},this.debounce)},validate:function(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];return t(this,void 0,void 0,function(){return u(this,function(c){0<a.length&&this.syncValue(a[0]);return[2,X(this)]})})},validateSilent:function(){return t(this,void 0,void 0,function(){var a,b;return u(this,
function(c){switch(c.label){case 0:return this.setFlags({pending:!0}),a=r(r({},this._resolvedRules),this.normalizedRules),Object.defineProperty(a,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,ea(this.value,a,r(r({name:this.name||this.fieldName},Oa(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return b=c.sent(),this.setFlags({pending:!1,valid:b.valid,invalid:!b.valid}),[2,b]}})})},setErrors:function(a){this.applyResult({errors:a,
failedRules:{}})},applyResult:function(a){var b=a.errors,c=a.failedRules;a=a.regenerateMap;this.errors=b;this._regenerateMap=a;this.failedRules=r({},c||{});this.setFlags({valid:!b.length,passed:!b.length,invalid:!!b.length,failed:!!b.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var a=Pa(this),b=this.id;!this.isActive||b===a&&this.$_veeObserver.refs[b]||(b!==a&&this.$_veeObserver.refs[b]===this&&this.$_veeObserver.unobserve(b),this.id=a,this.$_veeObserver.observe(this))}}}),
Sa=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],Ua=0,Va=p.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver","default":function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,"default":"span"},vid:{type:String,
"default":function(){return"obs_"+Ua++}},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:xa(),fields:{}}},created:function(){var a=this;this.id=this.vid;wa(this);var b=ba(function(c){var d=c.flags,e=c.fields;a.errors=c.errors;a.flags=d;a.fields=e},16);this.$watch(Ra,b)},activated:function(){wa(this)},deactivated:function(){va(this)},beforeDestroy:function(){va(this)},render:function(a){var b=na(this,r(r({},this.flags),
{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=b.length?b[0]:a(this.tag,{on:this.$listeners},b)},methods:{observe:function(a,b){var c;void 0===b&&(b="provider");"observer"===b?this.observers.push(a):this.refs=r(r({},this.refs),(c={},c[a.id]=a,c))},unobserve:function(a,b){void 0===b&&(b="provider");if("provider"===b)this.refs[a]&&this.$delete(this.refs,a);else{var c=aa(this.observers,function(d){return d.id===
a});-1!==c&&this.observers.splice(c,1)}},validate:function(a){a=(void 0===a?{}:a).silent;var b=void 0===a?!1:a;return t(this,void 0,void 0,function(){var c;return u(this,function(d){switch(d.label){case 0:return[4,Promise.all(v(N(this.refs).filter(function(e){return!e.disabled}).map(function(e){return e[b?"validateSilent":"validate"]().then(function(k){return k.valid})}),this.observers.filter(function(e){return!e.disabled}).map(function(e){return e.validate({silent:b})})))];case 1:return c=d.sent(),
[2,c.every(function(e){return e})]}})})},handleSubmit:function(a){return t(this,void 0,void 0,function(){var b;return u(this,function(c){switch(c.label){case 0:return[4,this.validate()];case 1:return(b=c.sent())&&a?[2,a()]:[2]}})})},reset:function(){return v(N(this.refs),this.observers).forEach(function(a){return a.reset()})},setErrors:function(a){var b=this;Object.keys(a).forEach(function(c){var d=b.refs[c];d&&(c=a[c]||[],c="string"===typeof c?[c]:c,d.setErrors(c))});this.observers.forEach(function(c){c.setErrors(a)})}}});
m.ValidationObserver=Va;m.ValidationProvider=ya;m.configure=function(a){Y(a)};m.extend=function(a,b){if(!y(b)&&!y(b.validate)&&!x.getRuleDefinition(a))throw Error("Extension Error: The validator '"+a+"' must be a function or have a 'validate' method.");"object"===typeof b?x.extend(a,b):x.extend(a,{validate:b})};m.localeChanged=ja;m.localize=function(a,b){var c;H||(H=new Ta("en",{}),Y({defaultMessage:function(d,e){return H.resolve(d,null===e||void 0===e?void 0:e._rule_,e||{})}}));"string"===typeof a?
(H.locale=a,b&&H.merge((c={},c[a]=b,c)),ja()):H.merge(a)};m.normalizeRules=I;m.setInteractionMode=function(a,b){Y({mode:a});if(b){if(!y(b))throw Error("A mode implementation must be a function");ra[a]=b}};m.validate=ea;m.version="3.2.3";m.withValidation=function(a,b){void 0===b&&(b=Aa);var c,d="options"in a?a.options:a,e=ya.options;e={name:(d.name||"AnonymousHoc")+"WithValidation",props:r({},e.props),data:e.data,computed:r({},e.computed),methods:r({},e.methods),beforeDestroy:e.beforeDestroy,inject:e.inject};
var k=(null===(c=null===d||void 0===d?void 0:d.model)||void 0===c?void 0:c.event)||"input";e.render=function(h){var f;this.registerField();var l=oa(this),g=r({},this.$listeners),n=T(this.$vnode);this._inputEventName=this._inputEventName||la(this.$vnode,n);var q=U(this.$vnode);pa(this,null===q||void 0===q?void 0:q.value);q=sa(this);var w=q.onBlur,D=q.onValidate;K(g,k,q.onInput);K(g,"blur",w);this.normalizedEvents.forEach(function(J){K(g,J,D)});q=(V(this.$vnode)||{prop:"value"}).prop;l=r(r(r({},this.$attrs),
(f={},f[q]=null===n||void 0===n?void 0:n.value,f)),b(l));return h(d,{attrs:this.$attrs,props:l,on:g},La(this.$slots,this.$vnode.context))};return e};Object.defineProperty(m,"__esModule",{value:!0})});



!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((r=r||self).VeeValidateRules={})}(this,function(r){"use strict";var i={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},a={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},u={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},s={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},o=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return o(r,{locale:n})}):n?(i[n]||i.en).test(e):Object.keys(i).some(function(r){return i[r].test(e)})},e={validate:o,params:[{name:"locale"}]},l=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return l(r,{locale:n})}):n?(s[n]||s.en).test(e):Object.keys(s).some(function(r){return s[r].test(e)})},t={validate:l,params:[{name:"locale"}]},c=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return c(r,{locale:n})}):n?(u[n]||u.en).test(e):Object.keys(u).some(function(r){return u[r].test(e)})},n={validate:c,params:[{name:"locale"}]},A=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return A(r,{locale:n})}):n?(a[n]||a.en).test(e):Object.keys(a).some(function(r){return a[r].test(e)})},f={validate:A,params:[{name:"locale"}]},m=function(r,e){var t=void 0===e?{}:e,n=t.min,i=t.max;return Array.isArray(r)?r.every(function(r){return!!m(r,{min:n,max:i})}):Number(n)<=r&&Number(i)>=r},v={validate:m,params:[{name:"min"},{name:"max"}]},$={validate:function(r,e){var t=e.target;return String(r)===String(t)},params:[{name:"target",isTarget:!0}]},d=function(r,e){var t=e.length;if(Array.isArray(r))return r.every(function(r){return d(r,{length:t})});var n=String(r);return/^[0-9]*$/.test(n)&&n.length===t},y={validate:d,params:[{name:"length",cast:function(r){return Number(r)}}]},g={validate:function(r,e){var u=e.width,s=e.height,t=[];r=Array.isArray(r)?r:[r];for(var n=0;n<r.length;n++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(r[n].name))return Promise.resolve(!1);t.push(r[n])}return Promise.all(t.map(function(r){return t=r,n=u,i=s,a=window.URL||window.webkitURL,new Promise(function(r){var e=new Image;e.onerror=function(){return r(!1)},e.onload=function(){return r(e.width===n&&e.height===i)},e.src=a.createObjectURL(t)});var t,n,i,a})).then(function(r){return r.every(function(r){return r})})},params:[{name:"width",cast:function(r){return Number(r)}},{name:"height",cast:function(r){return Number(r)}}]},Z={validate:function(r,e){var t=(void 0===e?{}:e).multiple,n=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return t&&!Array.isArray(r)&&(r=String(r).split(",").map(function(r){return r.trim()})),Array.isArray(r)?r.every(function(r){return n.test(String(r))}):n.test(String(r))},params:[{name:"multiple",default:!1}]};function p(r){return null==r}function h(r){return Array.isArray(r)&&0===r.length}function x(r){return"function"==typeof Array.from?Array.from(r):function(r){for(var e=[],t=r.length,n=0;n<t;n++)e.push(r[n]);return e}(r)}function _(r){return h(r)||-1!==[!1,null,void 0].indexOf(r)||!String(r).trim().length}var b=function(e,t){return Array.isArray(e)?e.every(function(r){return b(r,t)}):x(t).some(function(r){return r==e})},w={validate:b},S={validate:function(r,e){return!b(r,e)}},N={validate:function(r,e){var t=new RegExp(".("+e.join("|")+")$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.name)}):t.test(r.name)}},j={validate:function(r){var e=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(r)?r.every(function(r){return e.test(r.name)}):e.test(r.name)}},k={validate:function(r){return Array.isArray(r)?r.every(function(r){return/^-?[0-9]+$/.test(String(r))}):/^-?[0-9]+$/.test(String(r))}},z={validate:function(r,e){return r===e.other},params:[{name:"other"}]},F={validate:function(r,e){return r!==e.other},params:[{name:"other"}]},R={validate:function(r,e){var t=e.length;return!p(r)&&("number"==typeof r&&(r=String(r)),r.length||(r=x(r)),r.length===t)},params:[{name:"length",cast:function(r){return Number(r)}}]},O=function(r,e){var t=e.length;return p(r)?0<=t:Array.isArray(r)?r.every(function(r){return O(r,{length:t})}):String(r).length<=t},q={validate:O,params:[{name:"length",cast:function(r){return Number(r)}}]},C=function(r,e){var t=e.max;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return C(r,{max:t})}):Number(r)<=t)},P={validate:C,params:[{name:"max",cast:function(r){return Number(r)}}]},E={validate:function(r,e){var t=new RegExp(e.join("|").replace("*",".+")+"$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.type)}):t.test(r.type)}},L=function(r,e){var t=e.length;return!p(r)&&(Array.isArray(r)?r.every(function(r){return L(r,{length:t})}):String(r).length>=t)},U={validate:L,params:[{name:"length",cast:function(r){return Number(r)}}]},T=function(r,e){var t=e.min;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return T(r,{min:t})}):Number(r)>=t)},V={validate:T,params:[{name:"min",cast:function(r){return Number(r)}}]},I=/^[٠١٢٣٤٥٦٧٨٩]+$/,M=/^[0-9]+$/,B={validate:function(r){function e(r){var e=String(r);return M.test(e)||I.test(e)}return Array.isArray(r)?r.every(e):e(r)}},D=function(r,e){var t=e.regex;return Array.isArray(r)?r.every(function(r){return D(r,{regex:t})}):t.test(String(r))},G={validate:D,params:[{name:"regex",cast:function(r){return"string"==typeof r?new RegExp(r):r}}]},H={validate:function(r,e){var t=(void 0===e?{allowFalse:!0}:e).allowFalse,n={valid:!1,required:!0};return p(r)||h(r)||!1===r&&!t||(n.valid=!!String(r).trim().length),n},params:[{name:"allowFalse",default:!0}],computesRequired:!0},J={validate:function(r,e){var t,n=e.target,i=e.values;return(t=i&&i.length?(Array.isArray(i)||"string"!=typeof i||(i=[i]),i.some(function(r){return r==String(n).trim()})):!_(n))?{valid:!_(r),required:t}:{valid:!0,required:t}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},K={validate:function(r,e){var t=e.size;if(isNaN(t))return!1;var n=1024*t;if(!Array.isArray(r))return r.size<=n;for(var i=0;i<r.length;i++)if(r[i].size>n)return!1;return!0},params:[{name:"size",cast:function(r){return Number(r)}}]};r.alpha=e,r.alpha_dash=t,r.alpha_num=n,r.alpha_spaces=f,r.between=v,r.confirmed=$,r.digits=y,r.dimensions=g,r.email=Z,r.excluded=S,r.ext=N,r.image=j,r.integer=k,r.is=z,r.is_not=F,r.length=R,r.max=q,r.max_value=P,r.mimes=E,r.min=U,r.min_value=V,r.numeric=B,r.oneOf=w,r.regex=G,r.required=H,r.required_if=J,r.size=K,Object.defineProperty(r,"__esModule",{value:!0})});
var tns = function() {
    var t = window,
        Ai = t.requestAnimationFrame || t.webkitRequestAnimationFrame || t.mozRequestAnimationFrame || t.msRequestAnimationFrame || function(t) {
            return setTimeout(t, 16)
        },
        e = window,
        Ni = e.cancelAnimationFrame || e.mozCancelAnimationFrame || function(t) {
            clearTimeout(t)
        };

    function Li() {
        for (var t, e, n, i = arguments[0] || {}, a = 1, r = arguments.length; a < r; a++)
            if (null !== (t = arguments[a]))
                for (e in t) i !== (n = t[e]) && void 0 !== n && (i[e] = n);
        return i
    }

    function Bi(t) {
        return 0 <= ["true", "false"].indexOf(t) ? JSON.parse(t) : t
    }

    function Si(t, e, n, i) {
        if (i) try {
            t.setItem(e, n)
        } catch (t) {}
        return n
    }

    function Hi() {
        var t = document,
            e = t.body;
        return e || ((e = t.createElement("body")).fake = !0), e
    }
    var n = document.documentElement;

    function Oi(t) {
        var e = "";
        return t.fake && (e = n.style.overflow, t.style.background = "", t.style.overflow = n.style.overflow = "hidden", n.appendChild(t)), e
    }

    function Di(t, e) {
        t.fake && (t.remove(), n.style.overflow = e, n.offsetHeight)
    }

    function ki(t, e, n, i) {
        "insertRule" in t ? t.insertRule(e + "{" + n + "}", i) : t.addRule(e, n, i)
    }

    function Ri(t) {
        return ("insertRule" in t ? t.cssRules : t.rules).length
    }

    function Ii(t, e, n) {
        for (var i = 0, a = t.length; i < a; i++) e.call(n, t[i], i)
    }
    var i = "classList" in document.createElement("_"),
        Pi = i ? function(t, e) {
            return t.classList.contains(e)
        } : function(t, e) {
            return 0 <= t.className.indexOf(e)
        },
        zi = i ? function(t, e) {
            Pi(t, e) || t.classList.add(e)
        } : function(t, e) {
            Pi(t, e) || (t.className += " " + e)
        },
        Wi = i ? function(t, e) {
            Pi(t, e) && t.classList.remove(e)
        } : function(t, e) {
            Pi(t, e) && (t.className = t.className.replace(e, ""))
        };

    function qi(t, e) {
        return t.hasAttribute(e)
    }

    function Fi(t, e) {
        return t.getAttribute(e)
    }

    function r(t) {
        return void 0 !== t.item
    }

    function ji(t, e) {
        if (t = r(t) || t instanceof Array ? t : [t], "[object Object]" === Object.prototype.toString.call(e))
            for (var n = t.length; n--;)
                for (var i in e) t[n].setAttribute(i, e[i])
    }

    function Vi(t, e) {
        t = r(t) || t instanceof Array ? t : [t];
        for (var n = (e = e instanceof Array ? e : [e]).length, i = t.length; i--;)
            for (var a = n; a--;) t[i].removeAttribute(e[a])
    }

    function Gi(t) {
        for (var e = [], n = 0, i = t.length; n < i; n++) e.push(t[n]);
        return e
    }

    function Qi(t, e) {
        "none" !== t.style.display && (t.style.display = "none")
    }

    function Xi(t, e) {
        "none" === t.style.display && (t.style.display = "")
    }

    function Yi(t) {
        return "none" !== window.getComputedStyle(t).display
    }

    function Ki(e) {
        if ("string" == typeof e) {
            var n = [e],
                i = e.charAt(0).toUpperCase() + e.substr(1);
            ["Webkit", "Moz", "ms", "O"].forEach(function(t) {
                "ms" === t && "transform" !== e || n.push(t + i)
            }), e = n
        }
        for (var t = document.createElement("fakeelement"), a = (e.length, 0); a < e.length; a++) {
            var r = e[a];
            if (void 0 !== t.style[r]) return r
        }
        return !1
    }

    function Ji(t, e) {
        var n = !1;
        return /^Webkit/.test(t) ? n = "webkit" + e + "End" : /^O/.test(t) ? n = "o" + e + "End" : t && (n = e.toLowerCase() + "end"), n
    }
    var a = !1;
    try {
        var o = Object.defineProperty({}, "passive", {
            get: function() {
                a = !0
            }
        });
        window.addEventListener("test", null, o)
    } catch (t) {}
    var u = !!a && {
        passive: !0
    };

    function Ui(t, e, n) {
        for (var i in e) {
            var a = 0 <= ["touchstart", "touchmove"].indexOf(i) && !n && u;
            t.addEventListener(i, e[i], a)
        }
    }

    function _i(t, e) {
        for (var n in e) {
            var i = 0 <= ["touchstart", "touchmove"].indexOf(n) && u;
            t.removeEventListener(n, e[n], i)
        }
    }

    function Zi() {
        return {
            topics: {},
            on: function(t, e) {
                this.topics[t] = this.topics[t] || [], this.topics[t].push(e)
            },
            off: function(t, e) {
                if (this.topics[t])
                    for (var n = 0; n < this.topics[t].length; n++)
                        if (this.topics[t][n] === e) {
                            this.topics[t].splice(n, 1);
                            break
                        }
            },
            emit: function(e, n) {
                n.type = e, this.topics[e] && this.topics[e].forEach(function(t) {
                    t(n, e)
                })
            }
        }
    }
    Object.keys || (Object.keys = function(t) {
        var e = [];
        for (var n in t) Object.prototype.hasOwnProperty.call(t, n) && e.push(n);
        return e
    }), "remove" in Element.prototype || (Element.prototype.remove = function() {
        this.parentNode && this.parentNode.removeChild(this)
    });
    var $i = function(H) {
        H = Li({
            container: ".slider",
            mode: "carousel",
            axis: "horizontal",
            items: 1,
            gutter: 0,
            edgePadding: 0,
            fixedWidth: !1,
            autoWidth: !1,
            viewportMax: !1,
            slideBy: 1,
            center: !1,
            controls: !0,
            controlsPosition: "top",
            controlsText: ["prev", "next"],
            controlsContainer: !1,
            prevButton: !1,
            nextButton: !1,
            nav: !0,
            navPosition: "top",
            navContainer: !1,
            navAsThumbnails: !1,
            arrowKeys: !1,
            speed: 300,
            autoplay: !1,
            autoplayPosition: "top",
            autoplayTimeout: 5e3,
            autoplayDirection: "forward",
            autoplayText: ["start", "stop"],
            autoplayHoverPause: !1,
            autoplayButton: !1,
            autoplayButtonOutput: !0,
            autoplayResetOnVisibility: !0,
            animateIn: "tns-fadeIn",
            animateOut: "tns-fadeOut",
            animateNormal: "tns-normal",
            animateDelay: !1,
            loop: !0,
            rewind: !1,
            autoHeight: !1,
            responsive: !1,
            lazyload: !1,
            lazyloadSelector: ".tns-lazy-img",
            touch: !0,
            mouseDrag: !1,
            swipeAngle: 15,
            nested: !1,
            preventActionWhenRunning: !1,
            preventScrollOnTouch: !1,
            freezable: !0,
            onInit: !1,
            useLocalStorage: !0,
            nonce: !1
        }, H || {});
        var O = document,
            m = window,
            a = {
                ENTER: 13,
                SPACE: 32,
                LEFT: 37,
                RIGHT: 39
            },
            e = {},
            n = H.useLocalStorage;
        if (n) {
            var t = navigator.userAgent,
                i = new Date;
            try {
                (e = m.localStorage) ? (e.setItem(i, i), n = e.getItem(i) == i, e.removeItem(i)) : n = !1, n || (e = {})
            } catch (t) {
                n = !1
            }
            n && (e.tnsApp && e.tnsApp !== t && ["tC", "tPL", "tMQ", "tTf", "t3D", "tTDu", "tTDe", "tADu", "tADe", "tTE", "tAE"].forEach(function(t) {
                e.removeItem(t)
            }), localStorage.tnsApp = t)
        }
        var y = e.tC ? Bi(e.tC) : Si(e, "tC", function() {
                var t = document,
                    e = Hi(),
                    n = Oi(e),
                    i = t.createElement("div"),
                    a = !1;
                e.appendChild(i);
                try {
                    for (var r, o = "(10px * 10)", u = ["calc" + o, "-moz-calc" + o, "-webkit-calc" + o], l = 0; l < 3; l++)
                        if (r = u[l], i.style.width = r, 100 === i.offsetWidth) {
                            a = r.replace(o, "");
                            break
                        }
                } catch (t) {}
                return e.fake ? Di(e, n) : i.remove(), a
            }(), n),
            g = e.tPL ? Bi(e.tPL) : Si(e, "tPL", function() {
                var t, e = document,
                    n = Hi(),
                    i = Oi(n),
                    a = e.createElement("div"),
                    r = e.createElement("div"),
                    o = "";
                a.className = "tns-t-subp2", r.className = "tns-t-ct";
                for (var u = 0; u < 70; u++) o += "<div></div>";
                return r.innerHTML = o, a.appendChild(r), n.appendChild(a), t = Math.abs(a.getBoundingClientRect().left - r.children[67].getBoundingClientRect().left) < 2, n.fake ? Di(n, i) : a.remove(), t
            }(), n),
            D = e.tMQ ? Bi(e.tMQ) : Si(e, "tMQ", function() {
                if (window.matchMedia || window.msMatchMedia) return !0;
                var t, e = document,
                    n = Hi(),
                    i = Oi(n),
                    a = e.createElement("div"),
                    r = e.createElement("style"),
                    o = "@media all and (min-width:1px){.tns-mq-test{position:absolute}}";
                return r.type = "text/css", a.className = "tns-mq-test", n.appendChild(r), n.appendChild(a), r.styleSheet ? r.styleSheet.cssText = o : r.appendChild(e.createTextNode(o)), t = window.getComputedStyle ? window.getComputedStyle(a).position : a.currentStyle.position, n.fake ? Di(n, i) : a.remove(), "absolute" === t
            }(), n),
            r = e.tTf ? Bi(e.tTf) : Si(e, "tTf", Ki("transform"), n),
            o = e.t3D ? Bi(e.t3D) : Si(e, "t3D", function(t) {
                if (!t) return !1;
                if (!window.getComputedStyle) return !1;
                var e, n = document,
                    i = Hi(),
                    a = Oi(i),
                    r = n.createElement("p"),
                    o = 9 < t.length ? "-" + t.slice(0, -9).toLowerCase() + "-" : "";
                return o += "transform", i.insertBefore(r, null), r.style[t] = "translate3d(1px,1px,1px)", e = window.getComputedStyle(r).getPropertyValue(o), i.fake ? Di(i, a) : r.remove(), void 0 !== e && 0 < e.length && "none" !== e
            }(r), n),
            x = e.tTDu ? Bi(e.tTDu) : Si(e, "tTDu", Ki("transitionDuration"), n),
            u = e.tTDe ? Bi(e.tTDe) : Si(e, "tTDe", Ki("transitionDelay"), n),
            b = e.tADu ? Bi(e.tADu) : Si(e, "tADu", Ki("animationDuration"), n),
            l = e.tADe ? Bi(e.tADe) : Si(e, "tADe", Ki("animationDelay"), n),
            s = e.tTE ? Bi(e.tTE) : Si(e, "tTE", Ji(x, "Transition"), n),
            c = e.tAE ? Bi(e.tAE) : Si(e, "tAE", Ji(b, "Animation"), n),
            f = m.console && "function" == typeof m.console.warn,
            d = ["container", "controlsContainer", "prevButton", "nextButton", "navContainer", "autoplayButton"],
            v = {};
        if (d.forEach(function(t) {
                if ("string" == typeof H[t]) {
                    var e = H[t],
                        n = O.querySelector(e);
                    if (v[t] = e, !n || !n.nodeName) return void(f && console.warn("Can't find", H[t]));
                    H[t] = n
                }
            }), !(H.container.children.length < 1)) {
            var k = H.responsive,
                R = H.nested,
                I = "carousel" === H.mode;
            if (k) {
                0 in k && (H = Li(H, k[0]), delete k[0]);
                var p = {};
                for (var h in k) {
                    var w = k[h];
                    w = "number" == typeof w ? {
                        items: w
                    } : w, p[h] = w
                }
                k = p, p = null
            }
            if (I || function t(e) {
                    for (var n in e) I || ("slideBy" === n && (e[n] = "page"), "edgePadding" === n && (e[n] = !1), "autoHeight" === n && (e[n] = !1)), "responsive" === n && t(e[n])
                }(H), !I) {
                H.axis = "horizontal", H.slideBy = "page", H.edgePadding = !1;
                var P = H.animateIn,
                    z = H.animateOut,
                    C = H.animateDelay,
                    W = H.animateNormal
            }
            var M, q, F = "horizontal" === H.axis,
                T = O.createElement("div"),
                j = O.createElement("div"),
                V = H.container,
                E = V.parentNode,
                A = V.outerHTML,
                G = V.children,
                Q = G.length,
                X = rn(),
                Y = !1;
            k && En(), I && (V.className += " tns-vpfix");
            var N, L, B, S, K, J, U, _, Z, $ = H.autoWidth,
                tt = sn("fixedWidth"),
                et = sn("edgePadding"),
                nt = sn("gutter"),
                it = un(),
                at = sn("center"),
                rt = $ ? 1 : Math.floor(sn("items")),
                ot = sn("slideBy"),
                ut = H.viewportMax || H.fixedWidthViewportWidth,
                lt = sn("arrowKeys"),
                st = sn("speed"),
                ct = H.rewind,
                ft = !ct && H.loop,
                dt = sn("autoHeight"),
                vt = sn("controls"),
                pt = sn("controlsText"),
                ht = sn("nav"),
                mt = sn("touch"),
                yt = sn("mouseDrag"),
                gt = sn("autoplay"),
                xt = sn("autoplayTimeout"),
                bt = sn("autoplayText"),
                wt = sn("autoplayHoverPause"),
                Ct = sn("autoplayResetOnVisibility"),
                Mt = (U = null, _ = sn("nonce"), Z = document.createElement("style"), U && Z.setAttribute("media", U), _ && Z.setAttribute("nonce", _), document.querySelector("head").appendChild(Z), Z.sheet ? Z.sheet : Z.styleSheet),
                Tt = H.lazyload,
                Et = H.lazyloadSelector,
                At = [],
                Nt = ft ? (K = function() {
                    {
                        if ($ || tt && !ut) return Q - 1;
                        var t = tt ? "fixedWidth" : "items",
                            e = [];
                        if ((tt || H[t] < Q) && e.push(H[t]), k)
                            for (var n in k) {
                                var i = k[n][t];
                                i && (tt || i < Q) && e.push(i)
                            }
                        return e.length || e.push(0), Math.ceil(tt ? ut / Math.min.apply(null, e) : Math.max.apply(null, e))
                    }
                }(), J = I ? Math.ceil((5 * K - Q) / 2) : 4 * K - Q, J = Math.max(K, J), ln("edgePadding") ? J + 1 : J) : 0,
                Lt = I ? Q + 2 * Nt : Q + Nt,
                Bt = !(!tt && !$ || ft),
                St = tt ? _n() : null,
                Ht = !I || !ft,
                Ot = F ? "left" : "top",
                Dt = "",
                kt = "",
                Rt = tt ? function() {
                    return at && !ft ? Q - 1 : Math.ceil(-St / (tt + nt))
                } : $ ? function() {
                    for (var t = 0; t < Lt; t++)
                        if (N[t] >= -St) return t
                } : function() {
                    return at && I && !ft ? Q - 1 : ft || I ? Math.max(0, Lt - Math.ceil(rt)) : Lt - 1
                },
                It = en(sn("startIndex")),
                Pt = It,
                zt = (tn(), 0),
                Wt = $ ? null : Rt(),
                qt = H.preventActionWhenRunning,
                Ft = H.swipeAngle,
                jt = !Ft || "?",
                Vt = !1,
                Gt = H.onInit,
                Qt = new Zi,
                Xt = " tns-slider tns-" + H.mode,
                Yt = V.id || (S = window.tnsId, window.tnsId = S ? S + 1 : 1, "tns" + window.tnsId),
                Kt = sn("disable"),
                Jt = !1,
                Ut = H.freezable,
                _t = !(!Ut || $) && Tn(),
                Zt = !1,
                $t = {
                    click: oi,
                    keydown: function(t) {
                        t = pi(t);
                        var e = [a.LEFT, a.RIGHT].indexOf(t.keyCode);
                        0 <= e && (0 === e ? we.disabled || oi(t, -1) : Ce.disabled || oi(t, 1))
                    }
                },
                te = {
                    click: function(t) {
                        if (Vt) {
                            if (qt) return;
                            ai()
                        }
                        var e = hi(t = pi(t));
                        for (; e !== Ae && !qi(e, "data-nav");) e = e.parentNode;
                        if (qi(e, "data-nav")) {
                            var n = Se = Number(Fi(e, "data-nav")),
                                i = tt || $ ? n * Q / Le : n * rt,
                                a = le ? n : Math.min(Math.ceil(i), Q - 1);
                            ri(a, t), He === n && (Pe && fi(), Se = -1)
                        }
                    },
                    keydown: function(t) {
                        t = pi(t);
                        var e = O.activeElement;
                        if (!qi(e, "data-nav")) return;
                        var n = [a.LEFT, a.RIGHT, a.ENTER, a.SPACE].indexOf(t.keyCode),
                            i = Number(Fi(e, "data-nav"));
                        0 <= n && (0 === n ? 0 < i && vi(Ee[i - 1]) : 1 === n ? i < Le - 1 && vi(Ee[i + 1]) : ri(Se = i, t))
                    }
                },
                ee = {
                    mouseover: function() {
                        Pe && (li(), ze = !0)
                    },
                    mouseout: function() {
                        ze && (ui(), ze = !1)
                    }
                },
                ne = {
                    visibilitychange: function() {
                        O.hidden ? Pe && (li(), qe = !0) : qe && (ui(), qe = !1)
                    }
                },
                ie = {
                    keydown: function(t) {
                        t = pi(t);
                        var e = [a.LEFT, a.RIGHT].indexOf(t.keyCode);
                        0 <= e && oi(t, 0 === e ? -1 : 1)
                    }
                },
                ae = {
                    touchstart: xi,
                    touchmove: bi,
                    touchend: wi,
                    touchcancel: wi
                },
                re = {
                    mousedown: xi,
                    mousemove: bi,
                    mouseup: wi,
                    mouseleave: wi
                },
                oe = ln("controls"),
                ue = ln("nav"),
                le = !!$ || H.navAsThumbnails,
                se = ln("autoplay"),
                ce = ln("touch"),
                fe = ln("mouseDrag"),
                de = "tns-slide-active",
                ve = "tns-slide-cloned",
                pe = "tns-complete",
                he = {
                    load: function(t) {
                        kn(hi(t))
                    },
                    error: function(t) {
                        e = hi(t), zi(e, "failed"), Rn(e);
                        var e
                    }
                },
                me = "force" === H.preventScrollOnTouch;
            if (oe) var ye, ge, xe = H.controlsContainer,
                be = H.controlsContainer ? H.controlsContainer.outerHTML : "",
                we = H.prevButton,
                Ce = H.nextButton,
                Me = H.prevButton ? H.prevButton.outerHTML : "",
                Te = H.nextButton ? H.nextButton.outerHTML : "";
            if (ue) var Ee, Ae = H.navContainer,
                Ne = H.navContainer ? H.navContainer.outerHTML : "",
                Le = $ ? Q : Mi(),
                Be = 0,
                Se = -1,
                He = an(),
                Oe = He,
                De = "tns-nav-active",
                ke = "Carousel Page ",
                Re = " (Current Slide)";
            if (se) var Ie, Pe, ze, We, qe, Fe = "forward" === H.autoplayDirection ? 1 : -1,
                je = H.autoplayButton,
                Ve = H.autoplayButton ? H.autoplayButton.outerHTML : "",
                Ge = ["<span class='tns-visually-hidden'>", " animation</span>"];
            if (ce || fe) var Qe, Xe, Ye = {},
                Ke = {},
                Je = !1,
                Ue = F ? function(t, e) {
                    return t.x - e.x
                } : function(t, e) {
                    return t.y - e.y
                };
            $ || $e(Kt || _t), r && (Ot = r, Dt = "translate", o ? (Dt += F ? "3d(" : "3d(0px, ", kt = F ? ", 0px, 0px)" : ", 0px)") : (Dt += F ? "X(" : "Y(", kt = ")")), I && (V.className = V.className.replace("tns-vpfix", "")),
                function() {
                    ln("gutter");
                    T.className = "tns-outer", j.className = "tns-inner", T.id = Yt + "-ow", j.id = Yt + "-iw", "" === V.id && (V.id = Yt);
                    Xt += g || $ ? " tns-subpixel" : " tns-no-subpixel", Xt += y ? " tns-calc" : " tns-no-calc", $ && (Xt += " tns-autowidth");
                    Xt += " tns-" + H.axis, V.className += Xt, I ? ((M = O.createElement("div")).id = Yt + "-mw", M.className = "tns-ovh", T.appendChild(M), M.appendChild(j)) : T.appendChild(j);
                    if (dt) {
                        var t = M || j;
                        t.className += " tns-ah"
                    }
                    if (E.insertBefore(T, V), j.appendChild(V), Ii(G, function(t, e) {
                            zi(t, "tns-item"), t.id || (t.id = Yt + "-item" + e), !I && W && zi(t, W), ji(t, {
                                "aria-hidden": "true",
                                tabindex: "-1"
                            })
                        }), Nt) {
                        for (var e = O.createDocumentFragment(), n = O.createDocumentFragment(), i = Nt; i--;) {
                            var a = i % Q,
                                r = G[a].cloneNode(!0);
                            if (zi(r, ve), Vi(r, "id"), n.insertBefore(r, n.firstChild), I) {
                                var o = G[Q - 1 - a].cloneNode(!0);
                                zi(o, ve), Vi(o, "id"), e.appendChild(o)
                            }
                        }
                        V.insertBefore(e, V.firstChild), V.appendChild(n), G = V.children
                    }
                }(),
                function() {
                    if (!I)
                        for (var t = It, e = It + Math.min(Q, rt); t < e; t++) {
                            var n = G[t];
                            n.style.left = 100 * (t - It) / rt + "%", zi(n, P), Wi(n, W)
                        }
                    F && (g || $ ? (ki(Mt, "#" + Yt + " > .tns-item", "font-size:" + m.getComputedStyle(G[0]).fontSize + ";", Ri(Mt)), ki(Mt, "#" + Yt, "font-size:0;", Ri(Mt))) : I && Ii(G, function(t, e) {
                        var n;
                        t.style.marginLeft = (n = e, y ? y + "(" + 100 * n + "% / " + Lt + ")" : 100 * n / Lt + "%")
                    }));
                    if (D) {
                        if (x) {
                            var i = M && H.autoHeight ? hn(H.speed) : "";
                            ki(Mt, "#" + Yt + "-mw", i, Ri(Mt))
                        }
                        i = cn(H.edgePadding, H.gutter, H.fixedWidth, H.speed, H.autoHeight), ki(Mt, "#" + Yt + "-iw", i, Ri(Mt)), I && (i = F && !$ ? "width:" + fn(H.fixedWidth, H.gutter, H.items) + ";" : "", x && (i += hn(st)), ki(Mt, "#" + Yt, i, Ri(Mt))), i = F && !$ ? dn(H.fixedWidth, H.gutter, H.items) : "", H.gutter && (i += vn(H.gutter)), I || (x && (i += hn(st)), b && (i += mn(st))), i && ki(Mt, "#" + Yt + " > .tns-item", i, Ri(Mt))
                    } else {
                        I && dt && (M.style[x] = st / 1e3 + "s"), j.style.cssText = cn(et, nt, tt, dt), I && F && !$ && (V.style.width = fn(tt, nt, rt));
                        var i = F && !$ ? dn(tt, nt, rt) : "";
                        nt && (i += vn(nt)), i && ki(Mt, "#" + Yt + " > .tns-item", i, Ri(Mt))
                    }
                    if (k && D)
                        for (var a in k) {
                            a = parseInt(a);
                            var r = k[a],
                                i = "",
                                o = "",
                                u = "",
                                l = "",
                                s = "",
                                c = $ ? null : sn("items", a),
                                f = sn("fixedWidth", a),
                                d = sn("speed", a),
                                v = sn("edgePadding", a),
                                p = sn("autoHeight", a),
                                h = sn("gutter", a);
                            x && M && sn("autoHeight", a) && "speed" in r && (o = "#" + Yt + "-mw{" + hn(d) + "}"), ("edgePadding" in r || "gutter" in r) && (u = "#" + Yt + "-iw{" + cn(v, h, f, d, p) + "}"), I && F && !$ && ("fixedWidth" in r || "items" in r || tt && "gutter" in r) && (l = "width:" + fn(f, h, c) + ";"), x && "speed" in r && (l += hn(d)), l && (l = "#" + Yt + "{" + l + "}"), ("fixedWidth" in r || tt && "gutter" in r || !I && "items" in r) && (s += dn(f, h, c)), "gutter" in r && (s += vn(h)), !I && "speed" in r && (x && (s += hn(d)), b && (s += mn(d))), s && (s = "#" + Yt + " > .tns-item{" + s + "}"), (i = o + u + l + s) && Mt.insertRule("@media (min-width: " + a / 16 + "em) {" + i + "}", Mt.cssRules.length)
                        }
                }(), yn();
            var _e = ft ? I ? function() {
                    var t = zt,
                        e = Wt;
                    t += ot, e -= ot, et ? (t += 1, e -= 1) : tt && (it + nt) % (tt + nt) && (e -= 1), Nt && (e < It ? It -= Q : It < t && (It += Q))
                } : function() {
                    if (Wt < It)
                        for (; zt + Q <= It;) It -= Q;
                    else if (It < zt)
                        for (; It <= Wt - Q;) It += Q
                } : function() {
                    It = Math.max(zt, Math.min(Wt, It))
                },
                Ze = I ? function() {
                    var e, n, i, a, t, r, o, u, l, s, c;
                    Jn(V, ""), x || !st ? (ti(), st && Yi(V) || ai()) : (e = V, n = Ot, i = Dt, a = kt, t = Zn(), r = st, o = ai, u = Math.min(r, 10), l = 0 <= t.indexOf("%") ? "%" : "px", t = t.replace(l, ""), s = Number(e.style[n].replace(i, "").replace(a, "").replace(l, "")), c = (t - s) / r * u, setTimeout(function t() {
                        r -= u, s += c, e.style[n] = i + s + l + a, 0 < r ? setTimeout(t, u) : o()
                    }, u)), F || Ci()
                } : function() {
                    At = [];
                    var t = {};
                    t[s] = t[c] = ai, _i(G[Pt], t), Ui(G[It], t), ei(Pt, P, z, !0), ei(It, W, P), s && c && st && Yi(V) || ai()
                };
            return {
                version: "2.9.3",
                getInfo: Ei,
                events: Qt,
                goTo: ri,
                play: function() {
                    gt && !Pe && (ci(), We = !1)
                },
                pause: function() {
                    Pe && (fi(), We = !0)
                },
                isOn: Y,
                updateSliderHeight: Fn,
                refresh: yn,
                destroy: function() {
                    if (Mt.disabled = !0, Mt.ownerNode && Mt.ownerNode.remove(), _i(m, {
                            resize: Cn
                        }), lt && _i(O, ie), xe && _i(xe, $t), Ae && _i(Ae, te), _i(V, ee), _i(V, ne), je && _i(je, {
                            click: di
                        }), gt && clearInterval(Ie), I && s) {
                        var t = {};
                        t[s] = ai, _i(V, t)
                    }
                    mt && _i(V, ae), yt && _i(V, re);
                    var r = [A, be, Me, Te, Ne, Ve];
                    for (var e in d.forEach(function(t, e) {
                            var n = "container" === t ? T : H[t];
                            if ("object" == typeof n && n) {
                                var i = !!n.previousElementSibling && n.previousElementSibling,
                                    a = n.parentNode;
                                n.outerHTML = r[e], H[t] = i ? i.nextElementSibling : a.firstElementChild
                            }
                        }), d = P = z = C = W = F = T = j = V = E = A = G = Q = q = X = $ = tt = et = nt = it = rt = ot = ut = lt = st = ct = ft = dt = Mt = Tt = N = At = Nt = Lt = Bt = St = Ht = Ot = Dt = kt = Rt = It = Pt = zt = Wt = Ft = jt = Vt = Gt = Qt = Xt = Yt = Kt = Jt = Ut = _t = Zt = $t = te = ee = ne = ie = ae = re = oe = ue = le = se = ce = fe = de = pe = he = L = vt = pt = xe = be = we = Ce = ye = ge = ht = Ae = Ne = Ee = Le = Be = Se = He = Oe = De = ke = Re = gt = xt = Fe = bt = wt = je = Ve = Ct = Ge = Ie = Pe = ze = We = qe = Ye = Ke = Qe = Je = Xe = Ue = mt = yt = null, this) "rebuild" !== e && (this[e] = null);
                    Y = !1
                },
                rebuild: function() {
                    return $i(Li(H, v))
                }
            }
        }

        function $e(t) {
            t && (vt = ht = mt = yt = lt = gt = wt = Ct = !1)
        }

        function tn() {
            for (var t = I ? It - Nt : It; t < 0;) t += Q;
            return t % Q + 1
        }

        function en(t) {
            return t = t ? Math.max(0, Math.min(ft ? Q - 1 : Q - rt, t)) : 0, I ? t + Nt : t
        }

        function nn(t) {
            for (null == t && (t = It), I && (t -= Nt); t < 0;) t += Q;
            return Math.floor(t % Q)
        }

        function an() {
            var t, e = nn();
            return t = le ? e : tt || $ ? Math.ceil((e + 1) * Le / Q - 1) : Math.floor(e / rt), !ft && I && It === Wt && (t = Le - 1), t
        }

        function rn() {
            return m.innerWidth || O.documentElement.clientWidth || O.body.clientWidth
        }

        function on(t) {
            return "top" === t ? "afterbegin" : "beforeend"
        }

        function un() {
            var t = et ? 2 * et - nt : 0;
            return function t(e) {
                if (null != e) {
                    var n, i, a = O.createElement("div");
                    return e.appendChild(a), i = (n = a.getBoundingClientRect()).right - n.left, a.remove(), i || t(e.parentNode)
                }
            }(E) - t
        }

        function ln(t) {
            if (H[t]) return !0;
            if (k)
                for (var e in k)
                    if (k[e][t]) return !0;
            return !1
        }

        function sn(t, e) {
            if (null == e && (e = X), "items" === t && tt) return Math.floor((it + nt) / (tt + nt)) || 1;
            var n = H[t];
            if (k)
                for (var i in k) e >= parseInt(i) && t in k[i] && (n = k[i][t]);
            return "slideBy" === t && "page" === n && (n = sn("items")), I || "slideBy" !== t && "items" !== t || (n = Math.floor(n)), n
        }

        function cn(t, e, n, i, a) {
            var r = "";
            if (void 0 !== t) {
                var o = t;
                e && (o -= e), r = F ? "margin: 0 " + o + "px 0 " + t + "px;" : "margin: " + t + "px 0 " + o + "px 0;"
            } else if (e && !n) {
                var u = "-" + e + "px";
                r = "margin: 0 " + (F ? u + " 0 0" : "0 " + u + " 0") + ";"
            }
            return !I && a && x && i && (r += hn(i)), r
        }

        function fn(t, e, n) {
            return t ? (t + e) * Lt + "px" : y ? y + "(" + 100 * Lt + "% / " + n + ")" : 100 * Lt / n + "%"
        }

        function dn(t, e, n) {
            var i;
            if (t) i = t + e + "px";
            else {
                I || (n = Math.floor(n));
                var a = I ? Lt : n;
                i = y ? y + "(100% / " + a + ")" : 100 / a + "%"
            }
            return i = "width:" + i, "inner" !== R ? i + ";" : i + " !important;"
        }

        function vn(t) {
            var e = "";
            !1 !== t && (e = (F ? "padding-" : "margin-") + (F ? "right" : "bottom") + ": " + t + "px;");
            return e
        }

        function pn(t, e) {
            var n = t.substring(0, t.length - e).toLowerCase();
            return n && (n = "-" + n + "-"), n
        }

        function hn(t) {
            return pn(x, 18) + "transition-duration:" + t / 1e3 + "s;"
        }

        function mn(t) {
            return pn(b, 17) + "animation-duration:" + t / 1e3 + "s;"
        }

        function yn() {
            if (ln("autoHeight") || $ || !F) {
                var t = V.querySelectorAll("img");
                Ii(t, function(t) {
                    var e = t.src;
                    Tt || (e && e.indexOf("data:image") < 0 ? (t.src = "", Ui(t, he), zi(t, "loading"), t.src = e) : kn(t))
                }), Ai(function() {
                    zn(Gi(t), function() {
                        L = !0
                    })
                }), ln("autoHeight") && (t = In(It, Math.min(It + rt - 1, Lt - 1))), Tt ? gn() : Ai(function() {
                    zn(Gi(t), gn)
                })
            } else I && $n(), bn(), wn()
        }

        function gn() {
            if ($ && 1 < Q) {
                var i = ft ? It : Q - 1;
                ! function t() {
                    var e = G[i].getBoundingClientRect().left,
                        n = G[i - 1].getBoundingClientRect().right;
                    Math.abs(e - n) <= 1 ? xn() : setTimeout(function() {
                        t()
                    }, 16)
                }()
            } else xn()
        }

        function xn() {
            F && !$ || (jn(), $ ? (St = _n(), Ut && (_t = Tn()), Wt = Rt(), $e(Kt || _t)) : Ci()), I && $n(), bn(), wn()
        }

        function bn() {
            if (Vn(), T.insertAdjacentHTML("afterbegin", '<div class="tns-liveregion tns-visually-hidden" aria-live="polite" aria-atomic="true">slide <span class="current">' + Hn() + "</span>  of " + Q + "</div>"), B = T.querySelector(".tns-liveregion .current"), se) {
                var t = gt ? "stop" : "start";
                je ? ji(je, {
                    "data-action": t
                }) : H.autoplayButtonOutput && (T.insertAdjacentHTML(on(H.autoplayPosition), '<button type="button" data-action="' + t + '">' + Ge[0] + t + Ge[1] + bt[0] + "</button>"), je = T.querySelector("[data-action]")), je && Ui(je, {
                    click: di
                }), gt && (ci(), wt && Ui(V, ee), Ct && Ui(V, ne))
            }
            if (ue) {
                if (Ae) ji(Ae, {
                    "aria-label": "Carousel Pagination"
                }), Ii(Ee = Ae.children, function(t, e) {
                    ji(t, {
                        "data-nav": e,
                        tabindex: "-1",
                        "aria-label": ke + (e + 1),
                        "aria-controls": Yt
                    })
                });
                else {
                    for (var e = "", n = le ? "" : 'style="display:none"', i = 0; i < Q; i++) e += '<button type="button" data-nav="' + i + '" tabindex="-1" aria-controls="' + Yt + '" ' + n + ' aria-label="' + ke + (i + 1) + '"></button>';
                    e = '<div class="tns-nav" aria-label="Carousel Pagination">' + e + "</div>", T.insertAdjacentHTML(on(H.navPosition), e), Ae = T.querySelector(".tns-nav"), Ee = Ae.children
                }
                if (Ti(), x) {
                    var a = x.substring(0, x.length - 18).toLowerCase(),
                        r = "transition: all " + st / 1e3 + "s";
                    a && (r = "-" + a + "-" + r), ki(Mt, "[aria-controls^=" + Yt + "-item]", r, Ri(Mt))
                }
                ji(Ee[He], {
                    "aria-label": ke + (He + 1) + Re
                }), Vi(Ee[He], "tabindex"), zi(Ee[He], De), Ui(Ae, te)
            }
            oe && (xe || we && Ce || (T.insertAdjacentHTML(on(H.controlsPosition), '<div class="tns-controls" aria-label="Carousel Navigation" tabindex="0"><button type="button" data-controls="prev" tabindex="-1" aria-controls="' + Yt + '">' + pt[0] + '</button><button type="button" data-controls="next" tabindex="-1" aria-controls="' + Yt + '">' + pt[1] + "</button></div>"), xe = T.querySelector(".tns-controls")), we && Ce || (we = xe.children[0], Ce = xe.children[1]), H.controlsContainer && ji(xe, {
                "aria-label": "Carousel Navigation",
                tabindex: "0"
            }), (H.controlsContainer || H.prevButton && H.nextButton) && ji([we, Ce], {
                "aria-controls": Yt,
                tabindex: "-1"
            }), (H.controlsContainer || H.prevButton && H.nextButton) && (ji(we, {
                "data-controls": "prev"
            }), ji(Ce, {
                "data-controls": "next"
            })), ye = Qn(we), ge = Qn(Ce), Kn(), xe ? Ui(xe, $t) : (Ui(we, $t), Ui(Ce, $t))), An()
        }

        function wn() {
            if (I && s) {
                var t = {};
                t[s] = ai, Ui(V, t)
            }
            mt && Ui(V, ae, H.preventScrollOnTouch), yt && Ui(V, re), lt && Ui(O, ie), "inner" === R ? Qt.on("outerResized", function() {
                Mn(), Qt.emit("innerLoaded", Ei())
            }) : (k || tt || $ || dt || !F) && Ui(m, {
                resize: Cn
            }), dt && ("outer" === R ? Qt.on("innerLoaded", Pn) : Kt || Pn()), Dn(), Kt ? Bn() : _t && Ln(), Qt.on("indexChanged", Wn), "inner" === R && Qt.emit("innerLoaded", Ei()), "function" == typeof Gt && Gt(Ei()), Y = !0
        }

        function Cn(t) {
            Ai(function() {
                Mn(pi(t))
            })
        }

        function Mn(t) {
            if (Y) {
                "outer" === R && Qt.emit("outerResized", Ei(t)), X = rn();
                var e, n = q,
                    i = !1;
                k && (En(), (e = n !== q) && Qt.emit("newBreakpointStart", Ei(t)));
                var a, r, o, u, l = rt,
                    s = Kt,
                    c = _t,
                    f = lt,
                    d = vt,
                    v = ht,
                    p = mt,
                    h = yt,
                    m = gt,
                    y = wt,
                    g = Ct,
                    x = It;
                if (e) {
                    var b = tt,
                        w = dt,
                        C = pt,
                        M = at,
                        T = bt;
                    if (!D) var E = nt,
                        A = et
                }
                if (lt = sn("arrowKeys"), vt = sn("controls"), ht = sn("nav"), mt = sn("touch"), at = sn("center"), yt = sn("mouseDrag"), gt = sn("autoplay"), wt = sn("autoplayHoverPause"), Ct = sn("autoplayResetOnVisibility"), e && (Kt = sn("disable"), tt = sn("fixedWidth"), st = sn("speed"), dt = sn("autoHeight"), pt = sn("controlsText"), bt = sn("autoplayText"), xt = sn("autoplayTimeout"), D || (et = sn("edgePadding"), nt = sn("gutter"))), $e(Kt), it = un(), F && !$ || Kt || (jn(), F || (Ci(), i = !0)), (tt || $) && (St = _n(), Wt = Rt()), (e || tt) && (rt = sn("items"), ot = sn("slideBy"), (r = rt !== l) && (tt || $ || (Wt = Rt()), _e())), e && Kt !== s && (Kt ? Bn() : function() {
                        if (!Jt) return;
                        if (Mt.disabled = !1, V.className += Xt, $n(), ft)
                            for (var t = Nt; t--;) I && Xi(G[t]), Xi(G[Lt - t - 1]);
                        if (!I)
                            for (var e = It, n = It + Q; e < n; e++) {
                                var i = G[e],
                                    a = e < It + rt ? P : W;
                                i.style.left = 100 * (e - It) / rt + "%", zi(i, a)
                            }
                        Nn(), Jt = !1
                    }()), Ut && (e || tt || $) && (_t = Tn()) !== c && (_t ? (ti(Zn(en(0))), Ln()) : (! function() {
                        if (!Zt) return;
                        et && D && (j.style.margin = "");
                        if (Nt)
                            for (var t = "tns-transparent", e = Nt; e--;) I && Wi(G[e], t), Wi(G[Lt - e - 1], t);
                        Nn(), Zt = !1
                    }(), i = !0)), $e(Kt || _t), gt || (wt = Ct = !1), lt !== f && (lt ? Ui(O, ie) : _i(O, ie)), vt !== d && (vt ? xe ? Xi(xe) : (we && Xi(we), Ce && Xi(Ce)) : xe ? Qi(xe) : (we && Qi(we), Ce && Qi(Ce))), ht !== v && (ht ? (Xi(Ae), Ti()) : Qi(Ae)), mt !== p && (mt ? Ui(V, ae, H.preventScrollOnTouch) : _i(V, ae)), yt !== h && (yt ? Ui(V, re) : _i(V, re)), gt !== m && (gt ? (je && Xi(je), Pe || We || ci()) : (je && Qi(je), Pe && fi())), wt !== y && (wt ? Ui(V, ee) : _i(V, ee)), Ct !== g && (Ct ? Ui(O, ne) : _i(O, ne)), e) {
                    if (tt === b && at === M || (i = !0), dt !== w && (dt || (j.style.height = "")), vt && pt !== C && (we.innerHTML = pt[0], Ce.innerHTML = pt[1]), je && bt !== T) {
                        var N = gt ? 1 : 0,
                            L = je.innerHTML,
                            B = L.length - T[N].length;
                        L.substring(B) === T[N] && (je.innerHTML = L.substring(0, B) + bt[N])
                    }
                } else at && (tt || $) && (i = !0);
                if ((r || tt && !$) && (Le = Mi(), Ti()), (a = It !== x) ? (Qt.emit("indexChanged", Ei()), i = !0) : r ? a || Wn() : (tt || $) && (Dn(), Vn(), Sn()), r && !I && function() {
                        for (var t = It + Math.min(Q, rt), e = Lt; e--;) {
                            var n = G[e];
                            It <= e && e < t ? (zi(n, "tns-moving"), n.style.left = 100 * (e - It) / rt + "%", zi(n, P), Wi(n, W)) : n.style.left && (n.style.left = "", zi(n, W), Wi(n, P)), Wi(n, z)
                        }
                        setTimeout(function() {
                            Ii(G, function(t) {
                                Wi(t, "tns-moving")
                            })
                        }, 300)
                    }(), !Kt && !_t) {
                    if (e && !D && (et === A && nt === E || (j.style.cssText = cn(et, nt, tt, st, dt)), F)) {
                        I && (V.style.width = fn(tt, nt, rt));
                        var S = dn(tt, nt, rt) + vn(nt);
                        u = Ri(o = Mt) - 1, "deleteRule" in o ? o.deleteRule(u) : o.removeRule(u), ki(Mt, "#" + Yt + " > .tns-item", S, Ri(Mt))
                    }
                    dt && Pn(), i && ($n(), Pt = It)
                }
                e && Qt.emit("newBreakpointEnd", Ei(t))
            }
        }

        function Tn() {
            if (!tt && !$) return Q <= (at ? rt - (rt - 1) / 2 : rt);
            var t = tt ? (tt + nt) * Q : N[Q],
                e = et ? it + 2 * et : it + nt;
            return at && (e -= tt ? (it - tt) / 2 : (it - (N[It + 1] - N[It] - nt)) / 2), t <= e
        }

        function En() {
            for (var t in q = 0, k)(t = parseInt(t)) <= X && (q = t)
        }

        function An() {
            !gt && je && Qi(je), !ht && Ae && Qi(Ae), vt || (xe ? Qi(xe) : (we && Qi(we), Ce && Qi(Ce)))
        }

        function Nn() {
            gt && je && Xi(je), ht && Ae && Xi(Ae), vt && (xe ? Xi(xe) : (we && Xi(we), Ce && Xi(Ce)))
        }

        function Ln() {
            if (!Zt) {
                if (et && (j.style.margin = "0px"), Nt)
                    for (var t = "tns-transparent", e = Nt; e--;) I && zi(G[e], t), zi(G[Lt - e - 1], t);
                An(), Zt = !0
            }
        }

        function Bn() {
            if (!Jt) {
                if (Mt.disabled = !0, V.className = V.className.replace(Xt.substring(1), ""), Vi(V, ["style"]), ft)
                    for (var t = Nt; t--;) I && Qi(G[t]), Qi(G[Lt - t - 1]);
                if (F && I || Vi(j, ["style"]), !I)
                    for (var e = It, n = It + Q; e < n; e++) {
                        var i = G[e];
                        Vi(i, ["style"]), Wi(i, P), Wi(i, W)
                    }
                An(), Jt = !0
            }
        }

        function Sn() {
            var t = Hn();
            B.innerHTML !== t && (B.innerHTML = t)
        }

        function Hn() {
            var t = On(),
                e = t[0] + 1,
                n = t[1] + 1;
            return e === n ? e + "" : e + " to " + n
        }

        function On(t) {
            null == t && (t = Zn());
            var n, i, a, r = It;
            if (at || et ? ($ || tt) && (i = -(parseFloat(t) + et), a = i + it + 2 * et) : $ && (i = N[It], a = i + it), $) N.forEach(function(t, e) {
                e < Lt && ((at || et) && t <= i + .5 && (r = e), .5 <= a - t && (n = e))
            });
            else {
                if (tt) {
                    var e = tt + nt;
                    at || et ? (r = Math.floor(i / e), n = Math.ceil(a / e - 1)) : n = r + Math.ceil(it / e) - 1
                } else if (at || et) {
                    var o = rt - 1;
                    if (at ? (r -= o / 2, n = It + o / 2) : n = It + o, et) {
                        var u = et * rt / it;
                        r -= u, n += u
                    }
                    r = Math.floor(r), n = Math.ceil(n)
                } else n = r + rt - 1;
                r = Math.max(r, 0), n = Math.min(n, Lt - 1)
            }
            return [r, n]
        }

        function Dn() {
            if (Tt && !Kt) {
                var t = On();
                t.push(Et), In.apply(null, t).forEach(function(t) {
                    if (!Pi(t, pe)) {
                        var e = {};
                        e[s] = function(t) {
                            t.stopPropagation()
                        }, Ui(t, e), Ui(t, he), t.src = Fi(t, "data-src");
                        var n = Fi(t, "data-srcset");
                        n && (t.srcset = n), zi(t, "loading")
                    }
                })
            }
        }

        function kn(t) {
            zi(t, "loaded"), Rn(t)
        }

        function Rn(t) {
            zi(t, pe), Wi(t, "loading"), _i(t, he)
        }

        function In(t, e, n) {
            var i = [];
            for (n || (n = "img"); t <= e;) Ii(G[t].querySelectorAll(n), function(t) {
                i.push(t)
            }), t++;
            return i
        }

        function Pn() {
            var t = In.apply(null, On());
            Ai(function() {
                zn(t, Fn)
            })
        }

        function zn(n, t) {
            return L ? t() : (n.forEach(function(t, e) {
                !Tt && t.complete && Rn(t), Pi(t, pe) && n.splice(e, 1)
            }), n.length ? void Ai(function() {
                zn(n, t)
            }) : t())
        }

        function Wn() {
            Dn(), Vn(), Sn(), Kn(),
                function() {
                    if (ht && (He = 0 <= Se ? Se : an(), Se = -1, He !== Oe)) {
                        var t = Ee[Oe],
                            e = Ee[He];
                        ji(t, {
                            tabindex: "-1",
                            "aria-label": ke + (Oe + 1)
                        }), Wi(t, De), ji(e, {
                            "aria-label": ke + (He + 1) + Re
                        }), Vi(e, "tabindex"), zi(e, De), Oe = He
                    }
                }()
        }

        function qn(t, e) {
            for (var n = [], i = t, a = Math.min(t + e, Lt); i < a; i++) n.push(G[i].offsetHeight);
            return Math.max.apply(null, n)
        }

        function Fn() {
            var t = dt ? qn(It, rt) : qn(Nt, Q),
                e = M || j;
            e.style.height !== t && (e.style.height = t + "px")
        }

        function jn() {
            N = [0];
            var n = F ? "left" : "top",
                i = F ? "right" : "bottom",
                a = G[0].getBoundingClientRect()[n];
            Ii(G, function(t, e) {
                e && N.push(t.getBoundingClientRect()[n] - a), e === Lt - 1 && N.push(t.getBoundingClientRect()[i] - a)
            })
        }

        function Vn() {
            var t = On(),
                n = t[0],
                i = t[1];
            Ii(G, function(t, e) {
                n <= e && e <= i ? qi(t, "aria-hidden") && (Vi(t, ["aria-hidden", "tabindex"]), zi(t, de)) : qi(t, "aria-hidden") || (ji(t, {
                    "aria-hidden": "true",
                    tabindex: "-1"
                }), Wi(t, de))
            })
        }

        function Gn(t) {
            return t.nodeName.toLowerCase()
        }

        function Qn(t) {
            return "button" === Gn(t)
        }

        function Xn(t) {
            return "true" === t.getAttribute("aria-disabled")
        }

        function Yn(t, e, n) {
            t ? e.disabled = n : e.setAttribute("aria-disabled", n.toString())
        }

        function Kn() {
            if (vt && !ct && !ft) {
                var t = ye ? we.disabled : Xn(we),
                    e = ge ? Ce.disabled : Xn(Ce),
                    n = It <= zt,
                    i = !ct && Wt <= It;
                n && !t && Yn(ye, we, !0), !n && t && Yn(ye, we, !1), i && !e && Yn(ge, Ce, !0), !i && e && Yn(ge, Ce, !1)
            }
        }

        function Jn(t, e) {
            x && (t.style[x] = e)
        }

        function Un(t) {
            return null == t && (t = It), $ ? (it - (et ? nt : 0) - (N[t + 1] - N[t] - nt)) / 2 : tt ? (it - tt) / 2 : (rt - 1) / 2
        }

        function _n() {
            var t = it + (et ? nt : 0) - (tt ? (tt + nt) * Lt : N[Lt]);
            return at && !ft && (t = tt ? -(tt + nt) * (Lt - 1) - Un() : Un(Lt - 1) - N[Lt - 1]), 0 < t && (t = 0), t
        }

        function Zn(t) {
            var e;
            if (null == t && (t = It), F && !$)
                if (tt) e = -(tt + nt) * t, at && (e += Un());
                else {
                    var n = r ? Lt : rt;
                    at && (t -= Un()), e = 100 * -t / n
                }
            else e = -N[t], at && $ && (e += Un());
            return Bt && (e = Math.max(e, St)), e += !F || $ || tt ? "px" : "%"
        }

        function $n(t) {
            Jn(V, "0s"), ti(t)
        }

        function ti(t) {
            null == t && (t = Zn()), V.style[Ot] = Dt + t + kt
        }

        function ei(t, e, n, i) {
            var a = t + rt;
            ft || (a = Math.min(a, Lt));
            for (var r = t; r < a; r++) {
                var o = G[r];
                i || (o.style.left = 100 * (r - It) / rt + "%"), C && u && (o.style[u] = o.style[l] = C * (r - t) / 1e3 + "s"), Wi(o, e), zi(o, n), i && At.push(o)
            }
        }

        function ni(t, e) {
            Ht && _e(), (It !== Pt || e) && (Qt.emit("indexChanged", Ei()), Qt.emit("transitionStart", Ei()), dt && Pn(), Pe && t && 0 <= ["click", "keydown"].indexOf(t.type) && fi(), Vt = !0, Ze())
        }

        function ii(t) {
            return t.toLowerCase().replace(/-/g, "")
        }

        function ai(t) {
            if (I || Vt) {
                if (Qt.emit("transitionEnd", Ei(t)), !I && 0 < At.length)
                    for (var e = 0; e < At.length; e++) {
                        var n = At[e];
                        n.style.left = "", l && u && (n.style[l] = "", n.style[u] = ""), Wi(n, z), zi(n, W)
                    }
                if (!t || !I && t.target.parentNode === V || t.target === V && ii(t.propertyName) === ii(Ot)) {
                    if (!Ht) {
                        var i = It;
                        _e(), It !== i && (Qt.emit("indexChanged", Ei()), $n())
                    }
                    "inner" === R && Qt.emit("innerLoaded", Ei()), Vt = !1, Pt = It
                }
            }
        }

        function ri(t, e) {
            if (!_t)
                if ("prev" === t) oi(e, -1);
                else if ("next" === t) oi(e, 1);
            else {
                if (Vt) {
                    if (qt) return;
                    ai()
                }
                var n = nn(),
                    i = 0;
                if ("first" === t ? i = -n : "last" === t ? i = I ? Q - rt - n : Q - 1 - n : ("number" != typeof t && (t = parseInt(t)), isNaN(t) || (e || (t = Math.max(0, Math.min(Q - 1, t))), i = t - n)), !I && i && Math.abs(i) < rt) {
                    var a = 0 < i ? 1 : -1;
                    i += zt <= It + i - Q ? Q * a : 2 * Q * a * -1
                }
                It += i, I && ft && (It < zt && (It += Q), Wt < It && (It -= Q)), nn(It) !== nn(Pt) && ni(e)
            }
        }

        function oi(t, e) {
            if (Vt) {
                if (qt) return;
                ai()
            }
            var n;
            if (!e) {
                for (var i = hi(t = pi(t)); i !== xe && [we, Ce].indexOf(i) < 0;) i = i.parentNode;
                var a = [we, Ce].indexOf(i);
                0 <= a && (n = !0, e = 0 === a ? -1 : 1)
            }
            if (ct) {
                if (It === zt && -1 === e) return void ri("last", t);
                if (It === Wt && 1 === e) return void ri("first", t)
            }
            e && (It += ot * e, $ && (It = Math.floor(It)), ni(n || t && "keydown" === t.type ? t : null))
        }

        function ui() {
            Ie = setInterval(function() {
                oi(null, Fe)
            }, xt), Pe = !0
        }

        function li() {
            clearInterval(Ie), Pe = !1
        }

        function si(t, e) {
            ji(je, {
                "data-action": t
            }), je.innerHTML = Ge[0] + t + Ge[1] + e
        }

        function ci() {
            ui(), je && si("stop", bt[1])
        }

        function fi() {
            li(), je && si("start", bt[0])
        }

        function di() {
            Pe ? (fi(), We = !0) : (ci(), We = !1)
        }

        function vi(t) {
            t.focus()
        }

        function pi(t) {
            return mi(t = t || m.event) ? t.changedTouches[0] : t
        }

        function hi(t) {
            return t.target || m.event.srcElement
        }

        function mi(t) {
            return 0 <= t.type.indexOf("touch")
        }

        function yi(t) {
            t.preventDefault ? t.preventDefault() : t.returnValue = !1
        }

        function gi() {
            return a = Ke.y - Ye.y, r = Ke.x - Ye.x, t = Math.atan2(a, r) * (180 / Math.PI), e = Ft, n = !1, i = Math.abs(90 - Math.abs(t)), 90 - e <= i ? n = "horizontal" : i <= e && (n = "vertical"), n === H.axis;
            var t, e, n, i, a, r
        }

        function xi(t) {
            if (Vt) {
                if (qt) return;
                ai()
            }
            gt && Pe && li(), Je = !0, Xe && (Ni(Xe), Xe = null);
            var e = pi(t);
            Qt.emit(mi(t) ? "touchStart" : "dragStart", Ei(t)), !mi(t) && 0 <= ["img", "a"].indexOf(Gn(hi(t))) && yi(t), Ke.x = Ye.x = e.clientX, Ke.y = Ye.y = e.clientY, I && (Qe = parseFloat(V.style[Ot].replace(Dt, "")), Jn(V, "0s"))
        }

        function bi(t) {
            if (Je) {
                var e = pi(t);
                Ke.x = e.clientX, Ke.y = e.clientY, I ? Xe || (Xe = Ai(function() {
                    ! function t(e) {
                        if (!jt) return void(Je = !1);
                        Ni(Xe);
                        Je && (Xe = Ai(function() {
                            t(e)
                        }));
                        "?" === jt && (jt = gi());
                        if (jt) {
                            !me && mi(e) && (me = !0);
                            try {
                                e.type && Qt.emit(mi(e) ? "touchMove" : "dragMove", Ei(e))
                            } catch (t) {}
                            var n = Qe,
                                i = Ue(Ke, Ye);
                            if (!F || tt || $) n += i, n += "px";
                            else {
                                var a = r ? i * rt * 100 / ((it + nt) * Lt) : 100 * i / (it + nt);
                                n += a, n += "%"
                            }
                            V.style[Ot] = Dt + n + kt
                        }
                    }(t)
                })) : ("?" === jt && (jt = gi()), jt && (me = !0)), ("boolean" != typeof t.cancelable || t.cancelable) && me && t.preventDefault()
            }
        }

        function wi(i) {
            if (Je) {
                Xe && (Ni(Xe), Xe = null), I && Jn(V, ""), Je = !1;
                var t = pi(i);
                Ke.x = t.clientX, Ke.y = t.clientY;
                var a = Ue(Ke, Ye);
                if (Math.abs(a)) {
                    if (!mi(i)) {
                        var n = hi(i);
                        Ui(n, {
                            click: function t(e) {
                                yi(e), _i(n, {
                                    click: t
                                })
                            }
                        })
                    }
                    I ? Xe = Ai(function() {
                        if (F && !$) {
                            var t = -a * rt / (it + nt);
                            t = 0 < a ? Math.floor(t) : Math.ceil(t), It += t
                        } else {
                            var e = -(Qe + a);
                            if (e <= 0) It = zt;
                            else if (e >= N[Lt - 1]) It = Wt;
                            else
                                for (var n = 0; n < Lt && e >= N[n];) e > N[It = n] && a < 0 && (It += 1), n++
                        }
                        ni(i, a), Qt.emit(mi(i) ? "touchEnd" : "dragEnd", Ei(i))
                    }) : jt && oi(i, 0 < a ? -1 : 1)
                }
            }
            "auto" === H.preventScrollOnTouch && (me = !1), Ft && (jt = "?"), gt && !Pe && ui()
        }

        function Ci() {
            (M || j).style.height = N[It + rt] - N[It] + "px"
        }

        function Mi() {
            var t = tt ? (tt + nt) * Q / it : Q / rt;
            return Math.min(Math.ceil(t), Q)
        }

        function Ti() {
            if (ht && !le && Le !== Be) {
                var t = Be,
                    e = Le,
                    n = Xi;
                for (Le < Be && (t = Le, e = Be, n = Qi); t < e;) n(Ee[t]), t++;
                Be = Le
            }
        }

        function Ei(t) {
            return {
                container: V,
                slideItems: G,
                navContainer: Ae,
                navItems: Ee,
                controlsContainer: xe,
                hasControls: oe,
                prevButton: we,
                nextButton: Ce,
                items: rt,
                slideBy: ot,
                cloneCount: Nt,
                slideCount: Q,
                slideCountNew: Lt,
                index: It,
                indexCached: Pt,
                displayIndex: tn(),
                navCurrentIndex: He,
                navCurrentIndexCached: Oe,
                pages: Le,
                pagesCached: Be,
                sheet: Mt,
                isOn: Y,
                event: t || {}
            }
        }
        f && console.warn("No slides found in", H.container)
    };
    return $i
}();
//# sourceMappingURL=../sourcemaps/tiny-slider.js.map
Vue.component('yuno-page-grid', {
    props: {
        authorizedRoles: {
            type: Array,
            required: false,
            default: () => []
        },
        hasPageHeader: {
            type: Boolean,
            required: false,
            default: true
        },
        hasPageFooter: {
            type: Boolean,
            required: false,
            default: true
        },
        hasSearchBar: {
            type: Boolean,
            required: false,
            default: true
        },
        zohoMeta: {
            type: Object,
            required: false,
            default: null
        },
        hasLHSMenu: {
            type: Boolean,
            required: false,
            default: true
        }
    },
    template: `
        <div :class="[header.isMenuOpen ? 'menuOpen' : '']">
            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>
            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>
            <div class="pageGrid" :class="[!hasLHSMenu ? 'noLHSMenu' : '']">
                <yuno-header-v2 
                    @userInfo="onUserInfo" 
                    @isMini="onMini" 
                    v-if="loginStatus && hasPageHeader"
                >
                </yuno-header-v2>
                <slot name="aboveMain"></slot>
                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? 'miniSidebar' : '', loginStatus ? 'postLogin' : 'preLogin', loginStatus && !hasPageHeader && !hasPageFooter ? 'noHeaderFooter' : '']">
                    <template v-if="userInfo.loading">
                        <div class="container hasTopGap">
                            <figure class="infiniteSpinner">
                                <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                            </figure>
                        </div>
                    </template>
                    <template v-if="userInfo.success || !user.isLoggedin">
                        <template v-if="isUserAuthorized">
                            <slot name="main"></slot>     
                        </template>
                        <template v-else>
                            <div class="container">
                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>
                            </div>
                        </template>
                    </template>
                </main>
            </div>
            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> 
            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>
            <slot name="belowFooter"></slot>
        </div>
    `,
    data() {
        return {
            isMiniSidebar: false,
            loginStatus: isLoggedIn !== '0' ? true : false
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'userInfo',
            'user',
            'header',
            'footer'
        ]),
        isUserAuthorized : {
            get() {
                if (YUNOCommon.findInArray(this.$props.authorizedRoles, this.userRole.data)) { //
                    return true;
                } else if (this.$props.authorizedRoles.length === 0) {
                    return true;
                } else {
                    return false;
                }
            }
        },
        emptyStates() {
            return {
                state: "notAuthorized"
            }
        },
        isPageLoading() {
            return this.userInfo.loading || this.header.loading || this.footer.loading;
        },
        wpThemeURL() {
            return this.$store.state.themeURL
        },
    },
    async created() {

    },
    destroyed() {

    },
    mounted() {
        
    },
    methods: {
        onUserInfo(data) {
            this.$emit('onUserInfo', data);
            // if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
            //     this.fetchModules(data.role);
            // }
            
        },
        onMini(data) {
            this.isMiniSidebar = data;
        },
    }
});
Vue.component('yuno-page-header', {
    props: {
        hasSearchBar: {
            type: Boolean,
            required: false,
            default: true
        }
    },
    template: `
        <div class="yunoPageHeader">
            <figure class="logo">
                <img width="68" height="32" :src="wpThemeURL + '/assets/images/yunoLogo.svg'" alt="Yuno Learning">
            </figure>
            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>
            <ul class="actions">
                <li v-if="manageOrgSwitchVisiblity()">
                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>
                    <b-dropdown 
                        v-model="selectedOrg" 
                        position="is-bottom-left"
                        v-if="header.success && userInfo.success"
                        aria-role="list"
                        :class="['orgSwitchWrapper']"
                    >
                        <template #trigger>
                            <div class="orgSwitch">
                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">
                                <span class="name">{{ selectedOrg.name }}</span>
                                <span class="icon"></span>
                            </div>
                        </template>
                        <b-dropdown-item 
                            aria-role="menuitem"
                            v-for="(org, i) in activeUser.org_id"
                            :key="i"
                            @click="manageOrg(org)"
                            :value="org"
                        >
                            
                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        
                            
                        </b-dropdown-item>
                    </b-dropdown>
                </li>
                <li>
                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>
                    <b-dropdown
                        v-model="navigation"
                        position="is-bottom-left"
                        v-if="header.success && userInfo.success"
                        aria-role="menu"
                    >
                        <template #trigger>
                            <div class="userIcon">
                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">
                            </div>
                        </template>
                        <b-dropdown-item custom aria-role="menuitem" :class="['normal']">
                            <figure class="userCard">
                                <div class="imgWrapper">
                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">
                                </div>
                                <figcaption>
                                    <h3>{{ activeUser.yuno_display_name }}</h3>
                                    <p>{{ activeUser.email }}</p>
                                    <p>{{ activeUser.role }}</p>
                                </figcaption>
                            </figure>
                        </b-dropdown-item>
                        <b-dropdown-item 
                            has-link 
                            aria-role="menuitem"
                            v-for="(menu, i) in accountMenu.items"
                            @click="manageMenuItem($event, menu)"
                            :key="i"
                        >
                            <a :href="menu.url">
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        
                            </a>
                        </b-dropdown-item>
                        
                    </b-dropdown>
                </li>
            </ul>
        </div>
    `,
    data() {
        return {
            navigation: "",
            selectedOrg: null,
            isLoading: true
        }
    },
    computed: {
        ...Vuex.mapState([
            'header',
            'userInfo',
            'userRole',
            'subform3'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
        getHomeURL() {
            return this.$store.state.homeURL
        },
        accountMenu() {
            return YUNOCommon.findObjectByKey(this.header.data, "section", "Account")
        },
        activeUser() {
            return this.userInfo.data
        },
    },
    watch: {
        'userInfo.data': {
            handler(newValue, oldValue) {
                if (newValue !== oldValue) {
                    this.init();   
                }
            },
            deep: true
        }
    },
    async created() {

    },
    destroyed() {

    },
    mounted() {
        
    },
    methods: {
        manageMenuItem(e, data) {
            if (data.label === "Switch Account") {
                localStorage.setItem('userState', window.location.pathname);
                localStorage.setItem('isChooseAccountState', true);
                sessionStorage.clear();
            }
        },
        manageOrgSwitchVisiblity() {
            if (this.userRole.data === "org-admin" && this.userInfo.data.org_id.length > 1) {
                return true;
            } else  {
                return false;
            }
        },
        manageOrg(org) {
            this.updateActiveOrg(org.id);
        },
        orgUpdated(options) {
            const response = options?.response?.data;

            if (response?.code === 201) {
                sessionStorage.clear();
                window.location.reload(true);
            } else if (response?.message) { 
                console.log(response.message)
            }
        },
        updateActiveOrg(orgID) {
            this.$buefy.loading.open();

            const options = {
                apiURL: YUNOCommon.config.academy("activeOrg", false), 
                module: "gotData", 
                store: "subform3", 
                payload: {
                    "user_id": isLoggedIn,
                    "org_id": orgID
                }, 
                callback: true, 
                callbackFunc: (options) => this.orgUpdated(options)
            };

            this.dispatchData('postData', options);
        },
        dispatchData(action, options) {
            this.$store.dispatch(action, options);
        },
        init() {
            if (this.userInfo.data.role === "org-admin") {
                const activeOrg = YUNOCommon.findObjectByKey(this.userInfo.data.org_id, "id", Number(this.activeOrg()));
                this.selectedOrg = activeOrg;    
            }
        },
        searchBar() {
            if (this.userRole.data === "Learner") {
                return true;    
            } else {
                return false;
            }
            
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            }
        }
    }
});
Vue.component('yuno-header-v2', {
    props: ["data", "options"],
    template: `
        <div class="sidebarWrapper">
            <div class="sidebar-page yunoSidebar" :class="[isMobile ? 'isMobile' : 'isDesktop', reduce ? 'collapseView' : 'expandView']">
                <section class="sidebar-layout">
                    <b-sidebar
                        position="static"
                        :mobile="mobile"
                        :expand-on-hover="expandOnHover"
                        :reduce="reduce"
                        :delay="expandWithDelay ? 500 : null"
                        type="is-light"
                        open
                    >
                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? 'isMobile' : 'isDesktop']">
                            <span class="material-icons">
                                <template v-if="isMobile">
                                    menu
                                </template>
                                <template v-else>
                                    expand_less
                                </template>
                            </span>
                        </a>
                        <figure class="logo" v-if="!isPageGrid">
                            <a href="#">
                                <img width="106" height="50" :src="wpThemeURL + '/assets/images/yunoLogo.svg'" alt="Yuno Learning">
                            </a>
                        </figure>
                        <yuno-main-nav
                            :options="{'isMini': reduce}"
                            :isPageGrid="isPageGrid"
                        >
                        </yuno-main-nav>
                    </b-sidebar>
                </section>
                <b-modal 
                    :active.sync="config.unauthorizedModal" 
                    :width="450" 
                    :can-cancel="['escape', 'x']" 
                    :on-cancel="unauthorizedModalClose"
                    class="yunoModal">
                        <div class="modalHeader">
                            <h2 class="modalTitle">Session Expired</h2>
                        </div>
                        <div class="modalBody">
                            <div class="wrapper">
                                <p>{{sessionExpired}}</p>
                            </div>
                        </div>
                        <div class="modalFooter">
                            <div class="unauthorizedLogin">
                                <a 
                                    @click.prevent="setState()"
                                    href="#">
                                    <span class="g_icon"></span>
                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>
                                </a>
                            </div>
                        </div>
                </b-modal>
            </div>
        </div>
    `,
    data() {
        return {
            isMobile: false,
            menuLoading: 3,
            expandOnHover: false,
            expandWithDelay: false,
            mobile: "reduce",
            reduce: false,
            tokenExpiry: {
                payload: {
                    "userID": isLoggedIn,
                    "token": this.$store.state.config.yunoAPIToken
                }
            },
            sessionExpired: YUNOCommon.config.errorMsg.sesstionExpired,
            storage: {
                name: "activeUser",
                version: 1
            },
            isPageGrid: true,
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'userRole',
            'userProfile',
            'config',
            'header',
            'apiTokenExpiryTime',
            'apiTokenRefresh',
            'referralCode'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
        getHomeURL() {
            return this.$store.state.homeURL
        },
    },
    async created() {
        window.addEventListener("resize", this.manageOnResize);
        this.emitEvents();
    },
    destroyed() {
        window.removeEventListener("resize", this.manageOnResize);
    },
    mounted() {
        this.checkMenuState();
        this.manageOnResize();
        this.fetchModule();
    },
    methods: {
        emitEvents() {
            Event.$on('fetchReferralCode', () => {
                this.referralCode.success = false;
                this.referralCode.error = null;
                this.referralCode.errorData = [];
                this.referralCode.data = [];
                this.fetchReferralCode();
            });
        },
        manageOnResize() {
            let windowWidth = window.outerWidth;

            if (windowWidth >= 768) {
                this.isMobile = false;
            } else {
                this.isMobile = true;
                this.reduce = true;
            }
        },
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        gotReferralCode(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            }
        },
        fetchReferralCode() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.referrerID(isLoggedIn, 0),
                module: "gotData",
                store: "referralCode",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        checkMenuState() {
            if (this.$parent && this.$parent.$options.name !== "yuno-page-grid") {
                this.isPageGrid = false;
            }

            const store = sessionStorage.getItem('isLHSMenu');

            if (store !== null && !this.isMobile) {
                this.reduce = store === 'true' ? true : false;
                this.sidebarToggle(true);
            } else {
                this.reduce = false
            }
        },
        sidebarToggle(noToggle) {
            if (!noToggle) {
                if (this.reduce) {
                    sessionStorage.setItem("isLHSMenu", false);
                    this.reduce = false
                } else {
                    sessionStorage.setItem("isLHSMenu", true);
                    this.reduce = true
                }    
            }

            this.$emit('isMini', this.reduce);
        },
        chooseAccountState() {
            localStorage.setItem('userState', window.location.pathname);
            localStorage.setItem('isChooseAccountState', true);
        },
        unauthorizedModalClose() {
            window.location.href = "/logout";
        },
        fetchModule() {
            this.getStorage();
        },
        initTokenTime(minutes) {
            let setMinutes = parseInt(minutes - 10),
                timeout = parseInt(60000 * setMinutes); // minutes to miliseconds;

            setTimeout(() => {
                this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
            }, timeout);
        },
        doneRefreshAPIToken(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const data = options.response.data.data;

                this.config.yunoAPIToken = "Bearer "+ data.token +"";
                this.tokenExpiry.payload.token = "Bearer "+ data.token +"";
                this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
            };
        },
        refreshAPIToken(payload) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.apiTokenRefresh(),
                module: "gotData",
                store: "apiTokenRefresh",
                payload: JSON.stringify(payload),
                callback: true,
                callbackFunc: function(options) {
                    return instance.doneRefreshAPIToken(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        gotAPITokenExpiryTime(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const data = options.response.data.data,
                    minTime = 10;

                if (data.minutes <= minTime) {
                    let payload = {
                        user_id: isLoggedIn,
                        id_token: this.config.yunoAPIToken
                    };

                    this.refreshAPIToken(payload);
                } else {
                    this.initTokenTime(data.minutes);
                };
            };
        },
        fetchAPITokenExpiryTime(payload) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.apiTokenExpiry(isLoggedIn),
                module: "gotData",
                store: "apiTokenExpiryTime",
                payload: JSON.stringify(payload),
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotAPITokenExpiryTime(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        extractSlugFromURL(str) {
            // Remove the trailing slash from URl if it exists
            const currentUrl = str.replace(/\/$/, ''),
                urlParts = currentUrl.split("/");

            // Remove empty string from the end if the URL ends with a slash
            if (urlParts[urlParts.length - 1] === "") {
                urlParts.pop();
            }

            // Get the value between the last two slashes
            
            const value = urlParts[urlParts.length - 1];

            return value;
        },
        manageCurrentPage(data) {
            const normalizeUrl = url => url.replace(/\/$/, '');
            const currentUrl = normalizeUrl(window.location.origin + window.location.pathname);
        
            // Iterate through the array and set is_active to true if url matches currentUrl
            data.forEach(section => {
                section.items.forEach(item => {
                    item.is_active = currentUrl === normalizeUrl(item.url);
        
                    let isAnySubItemActive = false;
                    item.sub_items.forEach(subItem => {
                        subItem.is_active = currentUrl === normalizeUrl(subItem.url);
        
                        // Determine if any subItem is active
                        if (subItem.is_active && subItem.parent_id === item.id) {
                            isAnySubItemActive = true;
                        } 
                    });
        
                    // Set item.is_expended based on subItem activity and item activity
                    if (isAnySubItemActive) {
                        item.is_expended = true;
                    } else {
                        item.is_expended = false;
                    }
                });
            });
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            }
        },
        gotPostLoginMenu(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200 || !options) {
                let data = "";

                if (!options) {
                    data = this.header.data;
                    this.header.success = true;
                } else {
                    data = options.response.data.data;
                }

                this.manageCurrentPage(data);
                this.header.data = data;
                this.setStorage()
                this.$emit('menuLoaded');
            };
        },
        fetchPostLoginMenu(role) {
            const props = {
                userID: isLoggedIn,
                orgID: this.userInfo.data.role === "org-admin" ? this.activeOrg() : 0,
            };

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.header("menu", props),
                module: "gotData",
                store: "header",
                addToModule: false,
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotPostLoginMenu(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        manageOrgAdmin(userData) {
            const { host } = YUNOCommon.config;
            const { has_org, org_id } = userData;
            const activeOrg = sessionStorage.getItem("activeOrg");

            if (activeOrg === null) {
                if (!has_org) {
                    window.location.href = `${host()}/create-organization-account`;
                } else if (org_id.length > 1) {
                    window.location.href = `${host()}/select-an-organization`;
                    sessionStorage.setItem('redirectURL', window.location.pathname + window.location.search);
                } else {
                    sessionStorage.setItem('activeOrg', JSON.stringify(org_id[0].id));
                }    
            }
        },
        gotUserInfo(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200 || !options) {
                let data = "";

                if (!options) {
                    data = this.userInfo.data;
                    this.userInfo.success = true;
                } else {
                    data = options.response.data.data;
                };

                if (this.header.data.length !== 0) {
                    this.gotPostLoginMenu(false);
                } else {
                    this.fetchPostLoginMenu(data.role);
                }
                
                this.userRole.data = data.role;
                this.userProfile.data = data;
                this.userProfile.success = true;

                if (data.role === "Instructor") {
                    // this.fetchReferralCode();
                }

                if (data.role === "Learner") {
                    this.fetchReferralCode();
                }

                if (data.role === "Learner" && data.is_signup_completed === "pending") {
                    const userState = localStorage.getItem('userState'),
                        windowURL = window.location.pathname + window.location.search;

                    if (windowURL !== userState) {
                        window.location.href = YUNOCommon.config.host() + "/sign-up";
                        setTimeout(() => { 
                            localStorage.removeItem('skipSignUp');
                        }, 10);
                    };
                }

                if (data.role === "org-admin") {
                    // this.manageOrgAdmin(data);    
                }

                this.$emit('userInfo', data);
            }
        },
        fetchUserInfo() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                module: "gotData",
                store: "userInfo",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotUserInfo(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        getStorage() {
            const storage = this.storage;
            let version = Number(JSON.parse(JSON.stringify(storage.version)));
                lastStorage = storage.name + "V" + (--version);

            sessionStorage.removeItem(lastStorage);
            const store = sessionStorage.getItem(storage.name + "V" + storage.version);

            if (store !== null) {
                const data = JSON.parse(store);
                this.header.data = data.menu;
            };

            this.loginStatus();
        },
        setStorage() {
            const storage = this.storage;
            const store = {
                menu: this.header.data
            };
            

            if (this.userInfo.data.is_signup_completed === "completed") {
                sessionStorage.setItem(storage.name + "V" + storage.version, JSON.stringify(store));
            };
        },
        loginStatus() {
            let userID = Number(isLoggedIn); // Logged-in user id
            
            if (userID !== 0) {
                this.user.isLoggedin = true;
                
                if (this.userInfo.data.length !== 0) {
                    this.gotUserInfo(false)
                } else {
                    this.fetchUserInfo();
                }
                
                // this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
                this.$emit('login', this.user.isLoggedin);
            } else {
                const storage = this.storage;

                sessionStorage.removeItem(storage.name + "V" + storage.version);
                this.user.isLoggedin = false;
                this.$emit('login', this.user.isLoggedin);
            }
        },
    }
});
Vue.component('yuno-main-nav', {
    props: ["data", "options", "isPageGrid"],
    template: `
        <b-menu class="is-custom-mobile">
            <nav class="menuWrapper">
                <template v-if="header.loading || userInfo.loading">
                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>
                </template>
                <template v-if="header.success">
                    <template v-if="header.error">
                        {{ header.errorData }}
                    </template>
                    <template v-else>
                        <template v-if="isPageGrid">
                            <b-menu-list 
                                :key="i"
                                :label="section.section"
                                v-for="(section, i) in header.data"
                                v-if="section.section !== 'Account'"
                            >       
                                <template v-for="(menu, j) in section.items">
                                    <b-menu-item 
                                        :key="'menu-' + j"
                                        :href="menu.url"
                                        :expanded="menu.is_expended"
                                        :active="menu.is_active"
                                        tag="a"
                                        :class="[menu.sub_items.length !== 0  ? 'hasSubmenu' : '', generateClass(menu), section.slug]"
                                        @click="manageNavItem($event, menu)"
                                    >
                                        <template #label="props">
                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">
                                                <template v-if="props.expanded">
                                                    arrow_drop_down
                                                </template>
                                                <template v-else>
                                                    arrow_drop_up
                                                </template>
                                            </span>
                                            <template v-if="menu.slug === 'generate-code'">
                                                <template v-if="referralCode.loading">
                                                    <b-skeleton active></b-skeleton>
                                                </template>
                                                <template v-if="referralCode.success">
                                                    <template v-if="referralCode.error">
                                                        <template v-if="generateCode.loading">
                                                            <b-skeleton active></b-skeleton>
                                                        </template>
                                                        <template v-else>
                                                            <template v-if="options.isMini">
                                                                <b-tooltip label="Generate Code"
                                                                    type="is-dark"
                                                                    position="is-right">
                                                                    <div class="referralField" @click="generateReferralCode()">
                                                                        <span class="referralIcon"></span>
                                                                    </div>
                                                                </b-tooltip>
                                                            </template>
                                                            <template v-else>
                                                                <div class="referralField">
                                                                    <span class="referralIcon"></span>
                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">
                                                                        Generate Code
                                                                    </a>
                                                                </div>
                                                            </template>
                                                        </template>
                                                    </template>    
                                                    <template v-else>
                                                        <template v-if="options.isMini">
                                                            <b-tooltip label="Referral Code"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <div class="referralField isMini">
                                                                    <b-field>
                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                    </b-field>
                                                                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                        <span>Copy</span>
                                                                    </a>
                                                                </div>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <div class="referralField">
                                                                <span class="referralIcon"></span>
                                                                <b-field>
                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                </b-field>
                                                                <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                    <span class="caption">Copy</span>
                                                                </a>
                                                            </div>
                                                        </template>
                                                    </template>    
                                                </template>
                                            </template>
                                            <template v-else>
                                                <template v-if="options.isMini">
                                                    <b-tooltip :label="menu.label"
                                                        type="is-dark"
                                                        position="is-right">
                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>
                                                    </b-tooltip>
                                                </template>
                                                <template v-else>
                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>
                                                </template>
                                            </template>
                                        </template>
                                        <template v-if="menu.sub_items !== undefined">
                                            <template v-for="(submenu, k) in menu.sub_items">
                                                <b-menu-item
                                                    :key="'submenu-' + k"
                                                    :active="submenu.is_active"
                                                    :href="submenu.url"
                                                    tag="a"
                                                >
                                                    <template #label="props">
                                                        <template v-if="options.isMini">
                                                            <b-tooltip :label="submenu.label"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>
                                                        </template>
                                                    </template>
                                                </b-menu-item>
                                            </template>
                                        </template>
                                    </b-menu-item>
                                </template>
                            </b-menu-list> 
                        </template>
                        <template v-else>
                            <b-menu-list 
                                :key="i"
                                :label="section.section"
                                v-for="(section, i) in header.data"
                            >       
                                <template v-if="section.section === 'Account'">
                                    <template v-if="header.loading">
                                        <figure class="menuFooter loading">
                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>
                                            <figcaption>
                                                <p class="userName"><b-skeleton active></b-skeleton></p>
                                            </figcaption>
                                        </figure>
                                    </template>
                                    <template v-if="header.success">
                                        <figure class="menuFooter" :class="[options.isMini ? 'isMini' : '']">
                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">
                                            <figcaption>
                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>
                                                <p class="userEmail">{{ userInfo.data.email }}</p>
                                            </figcaption>
                                        </figure>
                                    </template>
                                </template>
                                <template v-for="(menu, j) in section.items">
                                    <b-menu-item 
                                        :key="'menu-' + j"
                                        :href="menu.url"
                                        :expanded="menu.is_expended"
                                        :active="menu.is_active"
                                        tag="a"
                                        :class="[menu.sub_items.length !== 0  ? 'hasSubmenu' : '', generateClass(menu), section.slug]"
                                        @click="manageNavItem($event, menu)"
                                    >
                                        <template #label="props">
                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">
                                                <template v-if="props.expanded">
                                                    arrow_drop_down
                                                </template>
                                                <template v-else>
                                                    arrow_drop_up
                                                </template>
                                            </span>
                                            <template v-if="menu.slug === 'generate-code'">
                                                <template v-if="referralCode.loading">
                                                    <b-skeleton active></b-skeleton>
                                                </template>
                                                <template v-if="referralCode.success">
                                                    <template v-if="referralCode.error">
                                                        <template v-if="generateCode.loading">
                                                            <b-skeleton active></b-skeleton>
                                                        </template>
                                                        <template v-else>
                                                            <template v-if="options.isMini">
                                                                <b-tooltip label="Generate Code"
                                                                    type="is-dark"
                                                                    position="is-right">
                                                                    <div class="referralField" @click="generateReferralCode()">
                                                                        <span class="referralIcon"></span>
                                                                    </div>
                                                                </b-tooltip>
                                                            </template>
                                                            <template v-else>
                                                                <div class="referralField">
                                                                    <span class="referralIcon"></span>
                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">
                                                                        Generate Code
                                                                    </a>
                                                                </div>
                                                            </template>
                                                        </template>
                                                    </template>    
                                                    <template v-else>
                                                        <template v-if="options.isMini">
                                                            <b-tooltip label="Referral Code"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <div class="referralField isMini">
                                                                    <b-field>
                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                    </b-field>
                                                                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                        <span>Copy</span>
                                                                    </a>
                                                                </div>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <div class="referralField">
                                                                <span class="referralIcon"></span>
                                                                <b-field>
                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                </b-field>
                                                                <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                    <span class="caption">Copy</span>
                                                                </a>
                                                            </div>
                                                        </template>
                                                    </template>    
                                                </template>
                                            </template>
                                            <template v-else>
                                                <template v-if="options.isMini">
                                                    <b-tooltip :label="menu.label"
                                                        type="is-dark"
                                                        position="is-right">
                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>
                                                    </b-tooltip>
                                                </template>
                                                <template v-else>
                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>
                                                </template>
                                            </template>
                                        </template>
                                        <template v-if="menu.sub_items !== undefined">
                                            <template v-for="(submenu, k) in menu.sub_items">
                                                <b-menu-item
                                                    :key="'submenu-' + k"
                                                    :active="submenu.is_active"
                                                    :href="submenu.url"
                                                    tag="a"
                                                >
                                                    <template #label="props">
                                                        <template v-if="options.isMini">
                                                            <b-tooltip :label="submenu.label"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>
                                                        </template>
                                                    </template>
                                                </b-menu-item>
                                            </template>
                                        </template>
                                    </b-menu-item>
                                </template>
                            </b-menu-list>  
                        </template>
                    </template>
                </template>
            </nav>
        </b-menu>
    `,
    data() {
        return {
            menuLoading: 3
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'userInfo',
            'header',
            'referralCode',
            'generateCode'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        gotReferralCode(options) {
            this.generateCode.loading = false;

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const response = options.response.data;
                Event.$emit('fetchReferralCode');
            } else {
                const response = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${response.message}`,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        generateReferralCode() {
            this.generateCode.loading = true;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generateRefferralCode(),
                module: "gotData",
                store: "generateCode",
                payload: {
                    user_id: Number(isLoggedIn),
                    role: this.userRole.data
                },
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        manageNavItem(e, data) {
            if (data.sub_items.length !== 0) {
                e.preventDefault();
            }

            if (data.slug === "generate-code") {
                e.preventDefault();
            };

            if (data.label === "Switch Account") {
                localStorage.setItem('userState', window.location.pathname);
                localStorage.setItem('isChooseAccountState', true);
                // sessionStorage.removeItem("activeUserV1");
                sessionStorage.clear();
            }
        },
        manageLabel(role) {
            if (role === "Learner") {
                return "Learn"
            } else {
                return "Insights"
            }
        },
        generateClass(data) {
            const itemClass = data.label.replace(/\s/g, '').toLowerCase();
            return itemClass;
        }
    }
});
Vue.component('yuno-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper">
            <b-menu-list :label="manageLabel(userRole.data)">
                <template v-for="(menu, i) in data">
                    <b-menu-item 
                        :key="'menu-' + i"
                        :active="menu.isActive"
                        :expanded="menu.isExpanded"
                        :class="[menu.submenu !== undefined ? 'hasSubmenu' : '', generateClass(menu)]"
                        :href="menu.url"
                        tag="a"
                    >
                        <template #label="props">
                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">
                                <template v-if="props.expanded">
                                    expand_more
                                </template>
                                <template v-else>
                                    expand_less
                                </template>
                            </span>
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                        <template v-if="menu.submenu !== undefined">
                            <template v-for="(submenu, j) in menu.submenu">
                                <b-menu-item
                                    :key="'submenu-' + j"
                                    :active="submenu.isActive"
                                    :href="submenu.url"
                                    tag="a"
                                >
                                    <template #label="props">
                                        <template v-if="options.isMini">
                                            <b-tooltip :label="submenu.label"
                                                type="is-dark"
                                                position="is-right">
                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>
                                            </b-tooltip>
                                        </template>
                                        <template v-else>
                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>
                                        </template>
                                    </template>
                                </b-menu-item>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list>  
        </nav>
    `,
    data() {
        return {
            
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        manageLabel(role) {
            if (role === "Learner") {
                return "Learn"
            } else {
                return "Insights"
            }
        },
        generateClass(data) {
            const itemClass = data.label.replace(/\s/g, '').toLowerCase();
            return itemClass;
        }
    }
});
Vue.component('yuno-referral-code', {
    props: ["data", "options"],
    template: `
        <div>
            <template v-if="options.isMini">
                <b-tooltip label="Referral Code"
                    type="is-dark"
                    position="is-right">
                    <div class="referralField isMini">
                        <b-field>
                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                        </b-field>
                        <a href="#" @click.prevent="copyToClipboard('referralCode')">
                            <span>Copy</span>
                        </a>
                    </div>
                </b-tooltip>
            </template>
            <template v-else>
                <div class="referralField">
                    <span class="referralIcon"></span>
                    <b-field>
                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                    </b-field>
                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                        <span class="caption">Copy</span>
                    </a>
                </div>
            </template>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        }
    }
});

Vue.component('yuno-referral-code-generate', {
    props: ["data", "options"],
    template: `
        <div class="fluid">
            <template v-if="referralCode.error">
                <template v-if="moduleWithoutTab.success">
                    <template v-if="moduleWithoutTab.loading">
                        <div class="referralField">
                            <span class="referralIcon"></span>
                            <b-skeleton active></b-skeleton>
                        </div>
                    </template>
                    <template v-if="moduleWithoutTab.success">
                        <yuno-referral-code :options="options"></yuno-referral-code>    
                    </template>
                </template>
                <template v-else>
                    <template v-if="options.isMini">
                        <b-tooltip label="Generate Code"
                            type="is-dark"
                            position="is-right">
                            <div class="referralField" @click="generateCode()">
                                <span class="referralIcon"></span>
                            </div>
                        </b-tooltip>
                    </template>
                    <template v-else>
                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">
                            <span class="referralIcon"></span>
                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">
                                Generate Code
                            </a>
                        </div>
                        <template v-if="moduleWithoutTab.loading">
                            <div class="referralField">
                                <span class="referralIcon"></span>
                                <b-skeleton active></b-skeleton>
                            </div>
                        </template>
                    </template>
                </template>
            </template>
            <template v-else>
                <yuno-referral-code :options="options"></yuno-referral-code>
            </template>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        gotReferralCode(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;

                this.referralCode.data = data;
            }
        },
        fetchReferralCode() {
            this.moduleWithoutTab.data = [];
            this.moduleWithoutTab.error = null;
            this.moduleWithoutTab.success = false;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.referrerID(isLoggedIn, 0),
                module: "gotData",
                store: "moduleWithoutTab",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        gotCode(options) {
            this.moduleWithoutTab.loading = false;

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const response = options.response.data;

                // this.$buefy.toast.open({
                //     duration: 5000,
                //     message: `${response.message}`,
                //     position: 'is-bottom'
                // });

                this.fetchReferralCode();
            } else {
                const response = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${response.message}`,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        generateCode() {
            this.moduleWithoutTab.loading = true;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generateRefferralCode(),
                module: "gotData",
                store: "moduleWithoutTab",
                payload: {
                    user_id: Number(isLoggedIn),
                    role: this.userRole.data
                },
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotCode(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
    }
});

Vue.component('yuno-referral-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper referral">
            <b-menu-list :label="manageLabel(userRole.data)">
                <b-menu-item 
                    href="#"
                    tag="a"
                >
                    <template #label="props">
                        <template v-if="userRole.data === 'Instructor'">
                            <yuno-referral-code :options="options"></yuno-referral-code>
                        </template>
                        <template v-if="userRole.data === 'Learner'">
                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>
                        </template>
                    </template>
                </b-menu-item>
                <template v-for="(menu, i) in otherItems">
                    <b-menu-item 
                        :key="'menu-static' + i"
                        :active="menu.isActive"
                        :href="menu.url"
                        v-if="isItemAvailable(menu.role)"
                        tag="a"
                    >
                        <template #label="props">
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list>
        </nav>
    `,
    data() {
        return {
            otherItems: [
                {
                    label: "Earnings",
                    slug: "earnings",
                    role: ["Instructor", "Learner"],
                    icon: "currency_rupee",
                    iconType: "material-icons-outlined",
                    url: YUNOCommon.config.pickHost() + "/earnings/",
                    isActive: false,
                    callbackFunc: false
                },
                {
                    label: "How it works",
                    slug: "howItWorks",
                    role: ["Instructor", "Learner"],
                    icon: "help_outline",
                    iconType: "material-icons-outlined",
                    url: YUNOCommon.config.pickHost() + "/how-it-works/",
                    isActive: false,
                    callbackFunc: false
                }
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        manageLabel(role) {
            if (role === "Learner") {
                return "Referral"
            } else {
                return "Referral Earnings"
            }
        },
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
    }
});
Vue.component('yuno-static-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper">
            <b-menu-list label="Account">
                <template v-for="(menu, i) in data">
                    <b-menu-item 
                        :key="'menu-static' + i"
                        :active="menu.isActive"
                        :href="menu.url"
                        v-if="isItemAvailable(menu.role)"
                        tag="a"
                    >
                        <template #label="props">
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list> 
        </nav>
    `,
    data() {
        return {
            
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        }
    }
});
Vue.component('yuno-course-search-bar', {
    props: ["data", "options"],
    template: `
        <div class="hasSearchBar">
            <template v-if="userInfo.loading">
                <b-skeleton height="31px"></b-skeleton>
            </template>
            <div class="searchBarWrapper" v-if="userInfo.success && userInfo.data.role === 'Learner'">
                <validation-observer  
                    tag="div" 
                    ref="searchObserver" 
                    v-slot="{ handleSubmit, invalid }">
                    <form id="searchForm" @submit.prevent="handleSubmit(initForm)">
                        <b-field class="searchFieldWrapper">
                            <validation-provider 
                                tag="div"
                                class="searchField"
                                :customMessages="{ isNotBlank: errorMsg.subject }"
                                :rules="{required:true, isNotBlank:categories.selected}" 
                                v-slot="{ errors, classes }">
                                <b-autocomplete
                                    :class="classes"
                                    v-model="categories.current"
                                    :data="categories.data"
                                    autocomplete="courseSearch"
                                    :loading="categories.isLoading"
                                    placeholder="Search..."
                                    @typing="searchOnTyping"
                                    @select="onSelect($event)"
                                    :clearable="true"
                                >
                                    <template slot-scope="props">
                                        <template v-if="props.option.course_url">
                                            <div class="suggestion courseBlock">
                                                <figure>
                                                    <div class="imageWrapper">
                                                        <img :src="props.option.imageurl" :alt="props.option.title">
                                                    </div>
                                                    <figcaption>
                                                        <p class="courseTitle">{{ props.option.title }}</p>
                                                        <p class="courseDetail">
                                                            <span class="caption">Course</span>
                                                            <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>
                                                        </p>
                                                    </figcaption>
                                                </figure>
                                            </div>
                                        </template>
                                        <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">
                                            <div class="suggestion categoryBlock">
                                                <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>
                                                <p class="courseDetail">
                                                    <span class="caption">{{ props.option.course_count + " courses available" }}</span>
                                                </p>
                                            </div>
                                        </template>
                                        <template v-if="props.option.parent_cat_slug && props.option.course_count">
                                            <div class="suggestion categoryBlock">
                                                <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>
                                                <p class="courseDetail">
                                                    <span class="caption">{{ props.option.course_count + " courses available" }}</span>
                                                </p>
                                            </div>
                                        </template>
                                    </template>
                                </b-autocomplete>
                            </validation-provider>
                            <div class="ctaWrapper">
                                <b-button
                                    native-type="submit"
                                    class="doSearch">
                                    <span class="material-icons-outlined">search</span>
                                </b-button>  
                            </div>
                        </b-field>
                    </form>
                </validation-observer>
            </div>
        </div>
    `,
    data() {
        return {
            errorMsg: {
                subject: "Please select the subject from list"
            },
            categories: {
                data: [],
                selected: null,
                current: "",
                isLoading: false
            },
            payload: {
                search: ""
            },
            searchParams: {
                limit: 20,
                offset: 0,
                personalization: "all",
                category: [],
                category_level_1: [],
                category_level_2: [],
                class_days_time: [
                    {
                        selected: [],
                        slug: "class_days"
                    },
                        {
                        selected: [],
                        slug: "class_time"
                    }
                ],
                instructor_id: 0,
                price_per_hour: 10000,
                total_duration: 24
            },
            popularSearch: []
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'userRole',
            'userProfile',
            'header',
            'module',
            'searchSuggestions'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onSelect(e) {
            if (!e) return;
        
            if (e.course_url) {
                window.location.href = e.course_url;
                return;
            }
        
            if (e.course_count) {
                this.categories.selected = e;
                this.payload.search = e.id;
                this.searchParams.category = [e.id];
                this.searchParams.category_level_1 = [];
                this.searchParams.category_level_2 = [];
        
                if (e.parent_cat_slug) {
                    this.searchParams.category = [e.parent_cat_id];
                    this.searchParams.category_level_1 = [e.category_level_1];
                    this.searchParams.category_level_2 = [e.id];
                }
        
                this.initForm();
            }
        },
        gotCourseSuggestions(options) {
            this.categories.isLoading = false;
            if (options.response?.data?.code === 200) {
                const { course, category, sub_category} = options.response.data.data;

                if (category) {
                    this.categories.data.push(...category);
                }

                if (sub_category) {
                    this.categories.data.push(...sub_category);
                }

                if (course) {
                    this.categories.data.push(...course);
                }
            }
        },
        fetchCourseSuggestions(query) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generic("courseSuggestions", query),
                module: "gotData",
                store: "searchSuggestions",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotCourseSuggestions(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        searchOnTyping: _.debounce(function (name) {
            if (name.length > 2) {
                this.categories.isLoading = true;
                this.fetchCourseSuggestions(name);    
            } else {
                this.categories.data = []
                return
            }

        }, 700),
        initForm() {
            const selected = this.categories.selected,
                data = {
                    filter: "category",
                    id: selected.category_id,
                    label: selected.category,
                    parent_id: 0,
                    slug: selected.categorySlug
                },
                filter = {
                    filter: "category"
                };

            if (this.$props.hassearchbar === undefined) {
                window.location.href = YUNOCommon.config.host() + "/search/?state="+ encodeURI(JSON.stringify(this.searchParams)) +"";
            } else {
                // Event.$emit('initHeaderSearch', data, filter);
            }
        },
    }
});
const YUNOInsightsCard = (function($) {

    const insightsCard = function() {
        Vue.component('yuno-insights-card', {
            props: ["data", "options"],
            template: `
                <section class="insightsData">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 col-md-4"
                                v-for="(card, i) in data.cards"
                                :key="i">
                                <div class="insightCard" :class="[card.customClass]">
                                    <span :class="[card.iconType]"> {{card.icon}} </span>
                                    <div class="value">
                                        <span class="labelBig">{{ card.label }}</span>
                                        <span class="labelSmall">{{ card.helper }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            async created() {
                
            },
            mounted() {
                
            },
            methods: {
                
            }
        });
    };

    return {
        insightsCard: insightsCard
    };
})(jQuery);




const YUNOFeaturesList = (function($) {

    const featuresList = function() {
        Vue.component('yuno-features-list', {
            props: ["data", "options"],
            template: `
                <section class="featuresList">
                    <div class="container">
                        <h2 
                            class="sectionTitle" 
                            :class="[data.desciption === undefined || data.desciption === '' ? 'gapBtm' : '']"
                        >
                            {{data.title}}
                        </h2>
                        <p class="description" v-if="data.desciption !== undefined && data.desciption !== ''">{{data.desciption}}</p>
                        <ul class="cardsGrid">
                            <li
                                v-for="(card, i) in data.cards"
                                :key="i" 
                                :class="card.customClass">
                                <figure class="cardImgWrapper">
                                    <img :src="card.img" :alt="card.title">
                                </figure>
                                <h3 class="cardCaption">{{card.title}}</h3>
                                <ul class="checkList">
                                    <li v-for="(item, j) in card.list" :key="j">
                                        {{item}}
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            async created() {
                
            },
            mounted() {
                
            },
            methods: {
                
            }
        });
    };

    return {
        featuresList: featuresList
    };
})(jQuery);
const YUNOReviewCard = (function($) {
    
    const reviewCard = function() {
        Vue.component('star-rating', VueStarRating.default)

        Vue.component('yuno-review-card', {
            props: ["dataid", "options"],
            template: `
            <section id="reviews" class="reviewSection" :class="[options.viewType !== undefined ? options.viewType : '', options.customClass]">
                <template v-if="instructorReviews.loading && !isLoadMore">
                    <div class="seactionHeader">
                        <h3 v-if="options.title === undefined || options.title !== false" class="sectionTitle" :class="options.title !== undefined ? options.titleClass : ''">
                            <template v-if="options.title !== undefined">
                                {{options.title}}
                            </template>
                            <template v-else>
                                Reviews <span class="helper"></span>
                            </template>
                        </h3>
                        <div class="filterWrap" v-if="options.sorting && options.sorting !== 'featured'">
                            <b-dropdown
                                v-model="sortBy.selected"
                                aria-role="list"
                                @change="onSorting($event)"
                                class="filterMenu">
                                <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
                                    <span>{{sortBy.selected}}</span>
                                    <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                </button>
                                <template v-for="(item, itemIndex) in sortBy.data">
                                    <b-dropdown-item 
                                        :value="item.slug" 
                                        :key="itemIndex"
                                        @click="sortingItems(item.label)"
                                        aria-role="listitem">
                                        <span>{{item.label}}</span>
                                    </b-dropdown-item>
                                </template>
                            </b-dropdown> 
                        </div>
                    </div>
                    <template
                        v-if="options.viewType === 'gridView'">
                        <div class="reviewCardGrid">
                            <article class="reviewCard" v-for="(review, reviewIndex) in 3" :key="reviewIndex">
                                <figure class="thumbnail">
                                    <b-skeleton height="250px"></b-skeleton>
                                </figure>
                                <figure class="userImg">
                                    <b-skeleton circle width="60px" height="60px"></b-skeleton>
                                    <figcaption>
                                        <b-skeleton class="reviewerName" width="40%" height="20%" :animated="true"></b-skeleton>
                                        <b-skeleton class="reviewDate" width="30%" height="20%" :animated="true"></b-skeleton>
                                    </figcaption>
                                </figure>
                                <div class="cardInfo">
                                    <div class="reviewContent">
                                        <b-skeleton active></b-skeleton>
                                    </div>
                                </div>
                            </article>
                        </div>
                    </template>
                    <template
                        v-else>
                    >
                        <article class="reviewCard">
                            <figure class="userImg">
                                <b-skeleton circle width="60px" height="60px"></b-skeleton>
                                <figcaption>
                                    <b-skeleton class="reviewerName" width="40%" height="20%" :animated="true"></b-skeleton>
                                    <b-skeleton class="reviewDate" width="30%" height="20%" :animated="true"></b-skeleton>
                                </figcaption>
                            </figure>
                            <div class="starRating">
                                <b-skeleton  width="30%" height="20%" :animated="true"></b-skeleton>
                            </div>
                            <div class="reviewContent">
                                <b-skeleton height="80px"></b-skeleton>
                            </div>
                        </article>
                        <article class="reviewCard">
                            <figure class="userImg">
                                <b-skeleton circle width="60px" height="60px"></b-skeleton>
                                <figcaption>
                                    <b-skeleton class="reviewerName" width="40%" height="20%" :animated="true"></b-skeleton>
                                    <b-skeleton class="reviewDate" width="30%" height="20%" :animated="true"></b-skeleton>
                                </figcaption>
                            </figure>
                            <div class="starRating">
                                <b-skeleton  width="30%" height="20%" :animated="true"></b-skeleton>
                            </div>
                            <div class="reviewContent">
                                <b-skeleton height="80px"></b-skeleton>
                            </div>
                        </article>
                    </template>
                </template>
                <template v-if="instructorReviews.success">
                    <template v-if="instructorReviews.error === null">  
                        <div class="seactionHeader" :class="[options.hasBasedOn ? 'hasBasedOn' : '']">
                            <h3 v-if="options.title === undefined || options.title !== false" class="sectionTitle" :class="options.title !== undefined ? options.titleClass : ''">
                                <template v-if="options.title !== undefined">
                                    {{options.title}}
                                </template>
                                <template v-else>
                                    Reviews <span class="helper">({{instructorReviews.count}})</span>
                                </template>
                            </h3>
                            <div class="filterWrap" v-if="options.sorting && options.sorting !== 'featured'">
                                <b-dropdown
                                    v-model="sortBy.selected"
                                    aria-role="list"
                                    @change="onSorting($event)"
                                    class="filterMenu">
                                    <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
                                        <span>{{sortBy.selected}}</span>
                                        <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                    </button>
                                    <template v-for="(item, itemIndex) in sortBy.data">
                                        <b-dropdown-item 
                                            :value="item.slug" 
                                            :key="itemIndex"
                                            @click="sortingItems(item.label)"
                                            aria-role="listitem">
                                            <span>{{item.label}}</span>
                                        </b-dropdown-item>
                                    </template>
                                </b-dropdown> 
                            </div>
                        </div>
                        <p class="basedon" v-if="options.hasBasedOn"><a :href="options.externalURL" target="_blank">Based on {{instructorReviews.total_review}} reviews</a></p>
                        <div class="overallStars" v-if="options.overallStars">
                            <star-rating :rating="Number(instructorReviews.reviews_avg_rating)" active-color="#F9B600" :read-only="true" :round-start-rating="false" :star-size="30" :show-rating="false"></star-rating>
                            <div class="vue-star-rating-rating-text">{{ instructorReviews.reviews_avg_rating }}</div>
                        </div>
                        <template v-if="options.viewType === undefined">
                            <article class="reviewCard" v-for="(review, reviewIndex) in instructorReviews.data" :key="reviewIndex">
                                <figure class="userImg">
                                    <img v-if="review.image !== ''" :src="review.image" :alt="'Review by' + review.name">
                                    <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>
                                    <figcaption>
                                        <h3 class="reviewerName">{{review.name}}</h3>
                                        <div class="reviewDate">{{ review.date }}</div>
                                    </figcaption>
                                </figure>
                                <div class="starRating">
                                    <div class="stars">
                                        <star-rating :rating="review.rating" :read-only="true" :increment="0.01" :star-size="25" :show-rating="false"></star-rating>
                                    </div>
                                </div>
                                <div class="reviewContent">
                                    {{ review.review_text }}
                                </div>
                            </article>
                        </template>
                        <template v-else-if="options.viewType === 'listView'">
                            <article class="reviewCard" v-for="(review, reviewIndex) in instructorReviews.data" :key="reviewIndex">
                                <figure class="userImg">
                                    <img v-if="review.image !== ''" :src="review.image" :alt="'Review by' + review.name">
                                    <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>
                                    <figcaption>
                                        <h3 class="reviewerName">{{review.name}} <a class="watchVideo" v-if="review.video_testimonial !== ''" @click.prevent="videoTestimonialModal(review)" :href="review.video_testimonial"><b-tag rounded>Watch Video</b-tag></a></h3>
                                        <div class="reviewDate">{{ review.date }}</div>
                                        <div class="starRating">
                                            <star-rating active-color="#F9B600" :rating="review.rating" :read-only="true" :increment="0.01" :star-size="14" :show-rating="false"></star-rating>
                                        </div>
                                        <div class="reviewContent">
                                            {{ review.review_text }}
                                        </div>
                                        <div class="verified" v-if="review.verified_enrolment">
                                            <b-tooltip label="Yuno has verified that the student enrolled in the instructor's classes"
                                                type="is-dark"
                                                multilined
                                                position="is-top">
                                                <b-tag rounded>Verified Enrolment</b-tag>
                                            </b-tooltip>
                                        </div>
                                    </figcaption>
                                </figure>
                            </article>
                        </template>
                        <template v-else-if="options.viewType === 'gridView'">
                            <div class="reviewCardGrid">
                                <article class="reviewCard" v-for="(review, reviewIndex) in instructorReviews.data" :key="reviewIndex">
                                    <figure class="thumbnail">
                                        <a href="#" @click.prevent="videoTestimonialModal(review)"><img :src="getThumbnail(review.video_testimonial)" :alt="'Review by' + review.name"></a>
                                    </figure>
                                    <figure class="userImg">
                                        <img v-if="review.image !== ''" :src="review.image" :alt="'Review by' + review.name">
                                        <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>
                                        <figcaption>
                                            <h3 class="reviewerName">{{review.name}} 
                                                <a class="watchVideo" v-if="false" @click.prevent="videoTestimonialModal(review)" :href="review.video_testimonial"><b-tag rounded>Watch Video</b-tag></a>
                                            </h3>
                                            <div class="reviewDate">{{ review.date }}</div>
                                            <div class="starRating">
                                                <div class="stars">
                                                    <star-rating active-color="#F9B600" :rating="review.rating" :read-only="true" :increment="0.01" :star-size="18" :show-rating="false"></star-rating>
                                                </div>
                                            </div>
                                        </figcaption>
                                    </figure>
                                    <div class="cardInfo">
                                        <div class="reviewContent">
                                            {{ review.review_text }}
                                        </div>
                                        <div class="verified" v-if="review.verified_enrolment">
                                            <b-tooltip label="Yuno has verified that the student enrolled in the instructor's classes"
                                                type="is-dark"
                                                multilined
                                                position="is-top">
                                                <b-tag rounded>Verified Enrolment</b-tag>
                                            </b-tooltip>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        </template>
                        <template v-if="options.hasShowMore !== undefined && options.hasShowMore">
                            <div class="showMore"><a href="#" @click.prevent="showmore()">Show more reviews</a></div>
                        </template>
                        <template v-else>
                            <div class="d-flex justify-content-center" :class="options.loadMoreParentClass !== undefined ? options.loadMoreParentClass : ''" v-if="instructorReviews.currentCount !== instructorReviews.count">
                                <b-button 
                                    class="yunoPrimaryCTA wired big"
                                    @click="loadMore()"
                                    :loading="isLoadMore ? true : false"
                                    :disabled="isLoadMore ? true : false">
                                    See more reviews
                                </b-button>
                            </div>
                        </template>
                    </template>
                    <template v-else>    
                        <section id="reviews" class="reviewSection">
                            <div class="seactionHeader">
                                <h3 class="sectionTitle">
                                    Reviews
                                </h3>
                            </div>
                            <article class="yunoCardWide gapTop15">
                                <div class="yunoCardBody">
                                    <yuno-empty-states :options="emptyStates"></yuno-empty-states>
                                </div>
                            </article>
                        </section>
                    </template>
                </template>
                <b-modal 
                    :active.sync="video.modal" 
                    :width="800" 
                    :can-cancel="['escape', 'x']"
                    @close="videoTestimonialClose" 
                    class="yunoModal lightTheme">
                        <template v-if="video.modal">
                            <div class="modalHeader">
                                <h2 class="modalTitle">{{video.data.name}} - Testimonial</h2>
                            </div>
                            <div class="modalBody">
                                
                                    <div class="videoLPPlayer">
                                        <iframe width="800" height="450" :src="video.data.video_testimonial + '?autoplay=1'" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                    </div>
                                
                            </div>
                        </template>
                </b-modal>
                <b-modal 
                    :active.sync="showmoreModal.modal" 
                    :width="800" 
                    :can-cancel="['escape', 'x']"
                    :on-cancel="showmoreClose"
                    class="yunoModal loginSignupModal">
                        <template v-if="showmoreModal.modal">
                            <div class="modalBody reviewsList">
                                <template v-if="allReviews.loading">
                                    <div class="loaderWrapper big">
                                        <div class="smallLoader"></div>
                                    </div>
                                </template>
                                <template v-if="allReviews.success">
                                    <div class="reviewsHeader">
                                        <h2>Rated 5-Star on Google</h2>
                                        <div class="ratingWrapper">
                                            <star-rating :rating="Number(allReviews.reviews_avg_rating)" active-color="#F9B600" :read-only="true" :round-start-rating="false" :star-size="24" :show-rating="false"></star-rating>
                                            <div class="vue-star-rating-rating-text">{{ allReviews.reviews_avg_rating }}</div>
                                        </div>
                                        <p><a :href="options.externalURL" target="_blank">Based on {{allReviews.total_review}} reviews</a></p>
                                        <a href="#" v-if="video.isActive" class="backToList" @click.prevent="backToList()"><span class="material-icons">arrow_back_ios_new</span></a>
                                    </div>
                                    <div class="videoWrapper" 
                                        v-if="video.isActive" 
                                        :class="[video.isActive ? 'slide-in-right' : '', video.isSlideActive ? 'slide-out-right' : '']">
                                        <div class="videoLPPlayer">
                                            <iframe width="800" height="450" :src="video.data.video_testimonial + '?autoplay=1'" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                        </div>
                                    </div>
                                    <div v-if="showmoreModal.isActive">
                                        <article class="reviewCard" v-for="(review, reviewIndex) in allReviews.data" :key="reviewIndex">
                                            <figure class="userImg">
                                                <img v-if="review.image !== ''" :src="review.image" :alt="'Review by' + review.name">
                                                <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>
                                                <figcaption>
                                                    <h3 class="reviewerName">{{review.name}} <a class="watchVideo" v-if="review.video_testimonial !== ''" @click.prevent="showVideo(review)" :href="review.video_testimonial"><b-tag rounded>Watch Video</b-tag></a></h3>
                                                    <div class="reviewDate">{{ readableDate(review.created_at) }}</div>
                                                    <div class="starRating">
                                                        <star-rating active-color="#F9B600" :rating="Number(review.value)" :read-only="true" :round-start-rating="false" :star-size="14" :show-rating="false"></star-rating>
                                                    </div>
                                                    <div class="reviewContent">
                                                        {{ review.review_text }}
                                                    </div>
                                                    <div class="verified" v-if="review.verified_enrolment">
                                                        <b-tooltip label="Yuno has verified that the student enrolled in the instructor's classes"
                                                            type="is-dark"
                                                            multilined
                                                            position="is-top">
                                                            <b-tag rounded>Verified Enrolment</b-tag>
                                                        </b-tooltip>
                                                    </div>
                                                </figcaption>
                                            </figure>
                                        </article>
                                        <div class="d-flex justify-content-center" v-if="allReviews.currentCount !== allReviews.count">
                                            <b-button 
                                                class="yunoPrimaryCTA wired big"
                                                @click="loadMore(true)"
                                                :loading="isLoadMore ? true : false"
                                                :disabled="isLoadMore ? true : false">
                                                See more reviews
                                            </b-button>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </template>
                </b-modal>
            </section>
            `,
            data() {
                return {
                    isLoadMore: false,
                    sortBy: {
                        selected: "Newest First",
                        data: [
                            {
                                label: "Newest First",
                                slug: "newest"
                            },
                            {
                                label: "Highest Rated First",
                                slug: "highest"
                            },
                            {
                                label: "Oldest First",
                                slug: "oldest"
                            },
                            {
                                label: "Lowest Rated First",
                                slug: "lowest"
                            }
                        ]
                    },
                    video: {
                        modal: false,
                        data: [],
                        allReviews: false,
                        isActive: false,
                        isSlideActive: false
                    },
                    showmoreModal: {
                        modal: false,
                        data: [],
                        isActive: true,
                        scrollTop: ""
                    }
                }
            },
            computed: {
                ...Vuex.mapState([
                    'instructorReviews',
                    'userProfile',
                    'allReviews'
                ]),
                emptyStates() {
                    return {
                        title: "Reviews not found",
                        state: "dataNotFound",
                        description: "This instructor doesn't have any reviews yet.",
                        maxheight: false
                    }
                },
            },
            created() {
                
            },
            mounted() {
                this.setUpReviewtab();
            },
            methods: {
                getThumbnail(str) {
                    let thumbnail = YUNOCommon.getFromString(str, /embed\/(.*)/),
                        url = "https://i.ytimg.com/vi/"+ thumbnail +"/hqdefault.jpg";

                    return url;
                },
                limitCharacter(htmlString, maxLength) {
                    // Remove tags by replacing them with a space and then trim the result
                    let textWithoutTags = htmlString.replace(/<\/[^>]+>/gi, ' ').replace(/<[^>]+>/gi, '').trim();
                    
                    // Replace multiple spaces with a single space
                    textWithoutTags = textWithoutTags.replace(/\s\s+/g, ' ');
        
                    // Truncate to the maxLength if necessary and add ellipsis
                    if (textWithoutTags.length > maxLength) {
                        // Ensure we don't break in the middle of a word
                        // Find the last space before the maxLength
                        const lastSpaceIndex = textWithoutTags.lastIndexOf(' ', maxLength - 1);
                        // Use the lastSpaceIndex to truncate the string, and add an ellipsis
                        return textWithoutTags.substring(0, lastSpaceIndex) + '...';
                    }
        
                    // Return the text if it's less than the maxLength
                    return textWithoutTags;
                },
                readableDate(date) {
                    return moment(date).format("MMM DD, YYYY");
                },
                showmore() {
                    this.showmoreModal.modal = true;

                    if (this.allReviews.data.length === 0) {
                        this.fetchAllReviews();    
                    }
                },
                showmoreClose() {
                    this.showmoreModal.data = [];
                },
                videoTestimonialClose() {
                    this.video.data = [];
                },
                backToList() {
                    this.video.isSlideActive = true;

                    setTimeout(() => {
                        this.video.isActive = false;
                        this.video.data = [];
                        this.video.isSlideActive = false;
                        this.showmoreModal.isActive = true;

                        setTimeout(() => {
                            document.querySelector(".reviewsList").scrollTop = this.showmoreModal.scrollTop;
                        }, 100);
                    }, 500);
                },
                showVideo(data) {
                    this.showmoreModal.scrollTop = "";
                    this.video.data = data;
                    this.video.isActive = true;
                    this.showmoreModal.isActive = false;
                    this.showmoreModal.scrollTop = document.querySelector(".reviewsList").scrollTop;
                },
                videoTestimonialModal(data) {
                    this.video.data = data;
                    this.video.modal = true;
                    this.showmoreModal.modal = false;
                    this.showmoreModal.data = [];
                },
                sortingItems(item) {
                    this.sortBy.selected = item;
                },
                onSorting($event) {
                    const getInstructorReviewsObj = this.instructorReviews;
                    getInstructorReviewsObj.sort = $event;
                    getInstructorReviewsObj.offset = 0;
                    getInstructorReviewsObj.data = [];

                    this.fetchReviews();
                },
                loadMore(hasAllReviews) {
                    this.isLoadMore = true;

                    if (hasAllReviews === undefined || !hasAllReviews) {
                        this.fetchReviews();    
                    } else {
                        this.fetchAllReviews();
                    }
                    
                },
                setUpReviewtab() {
                    const getInstructorReviewsObj = this.instructorReviews;

                    getInstructorReviewsObj.count = "";
                    getInstructorReviewsObj.currentCount = "";
                    getInstructorReviewsObj.limit = this.$props.options.limit; 
                    getInstructorReviewsObj.offset = 0;
                    getInstructorReviewsObj.sort = "newest";

                    this.fetchReviews();
                },
                structuredData(structuredDataObj) {
                    const script = document.createElement('script');
                    
                    script.setAttribute('type', 'application/ld+json');

                    let inlineScript = document.createTextNode(JSON.stringify(structuredDataObj));

                    script.appendChild(inlineScript); 
                    document.head.appendChild(script);
                },
                gotReviews(options) {
                    this.isLoadMore = false;

                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        let response = options.response.data,
                            structuredDataText = {
                                "@context": "https://schema.org",
                                "@type": "LocalBusiness",
                                "name": "Yuno Learning",
                                "image": "https://www.yunolearning.com/wp-content/themes/yunolearning-child/assets/images/yuno_logo.png",
                                "telephone": "+917411781928",
                                "priceRange": "₹₹₹₹",
                                "address": {
                                    "@type": "PostalAddress",
                                    "streetAddress": "Yuno Learning Global Pvt. Ltd. 006-Upper Ground Floor, MGF Metropolis Mall",
                                    "addressLocality": "Gurugram",
                                    "addressRegion": "Haryana",
                                    "postalCode": "122002",
                                    "addressCountry": "IN"
                                },
                                "aggregateRating": {
                                    "@type": "AggregateRating",
                                    "ratingValue": Number(response.reviews_avg_rating),
                                    "reviewCount": response.count
                                },
                                "review": []
                            };

                        const dataObj = this.instructorReviews,
                            getData = dataObj.data;

                        dataObj.count = response.count;
                        dataObj.currentCount = getData.length;
                        dataObj.offset = getData.length;
                        dataObj.rating = Number(response.reviews_avg_rating);
                        dataObj.total_review = response.total_review !== undefined ? response.total_review : 0;
                        dataObj.reviews_avg_rating = response.reviews_avg_rating !== undefined ? response.reviews_avg_rating : 0;

                        for (let i = 0; i < getData.length; i++) {
                            const review = getData[i];

                            let dataObj = {
                                "@type": "Review",
                                "author": {
                                    "@type": "Person",
                                    "name": review.name
                                },
                                "datePublished": review.created_at,
                                "description": review.review_text,
                                "name": this.userProfile.data.first_name,
                                "reviewRating": {
                                    "@type": "Rating",
                                    "bestRating": "5",
                                    "ratingValue": review.value,
                                    "worstRating": "1"
                                }
                            };

                            review.rating = Number(review.value);
                            review.date = moment(review.created_at).format("MMM DD, YYYY"); //Make sure moment.js included
                            structuredDataText.review.push(dataObj);
                        };

                        this.structuredData(structuredDataText);
                    };
                },
                fetchReviews() {
                    const instance = this,
                        getObj = this.instructorReviews;
                    
                    let url = "",
                        hasFilter = "",
                        hasSorting = "";

                    if (this.$props.options.type !== "channel") {
                        hasFilter = this.$props.options.type;
                    } else {
                        hasFilter = false
                    }

                    if (this.$props.options.sorting) {
                        // hasSorting = getObj.sort;
                        hasSorting = this.$props.options.sorting;
                    } else {
                        hasSorting = false;
                    }

                    url = YUNOCommon.config.reviewAPI(hasFilter, this.$props.dataid, this.$props.options.view, getObj.limit, getObj.offset, hasSorting);

                    const options = {
                        apiURL: url,
                        module: "gotData",
                        store: "instructorReviews",
                        pushData: true,
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotReviews(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                gotAllReviews(options) {
                    this.isLoadMore = false;

                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        let response = options.response.data;
                        const dataObj = this.allReviews,
                            getData = dataObj.data;

                        dataObj.count = response.count;
                        dataObj.currentCount = getData.length;
                        dataObj.offset = getData.length;
                        dataObj.total_review = response.total_review;
                        dataObj.reviews_avg_rating = response.reviews_avg_rating;
                    };
                },
                fetchAllReviews() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.reviewAPI("learner", 0, "list-view", this.allReviews.limit, this.allReviews.offset, "all"),
                        module: "gotData",
                        store: "allReviews",
                        pushData: true,
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotAllReviews(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                }
            }
        });
    };

    return {
        reviewCard: reviewCard
    };
})(jQuery);


Vue.component('yuno-hero-banner-v2', {
    props: ["data", "options"],
    template: `
        <section class="heroBanner">
            <div class="container">
                <div class="wrapper">
                    <div class="innerWrapper">
                        <div class="starRating" v-if="data.rating.isActive">
                            <div class="iconsWrapper">
                                <span class="material-icons">star</span>
                                <span class="material-icons">star</span>
                                <span class="material-icons">star</span>
                                <span class="material-icons">star</span>
                                <span class="material-icons">star</span>
                            </div>
                            <p class="smallCaption">{{ data.rating.label }}</p>
                        </div>
                        <h1 class="largestTitle">{{ data.title }}</h1>
                        <validation-observer 
                            tag="div" 
                            class="searchBarWrapper"
                            ref="searchObserver" 
                            v-slot="{ handleSubmit, invalid }">
                            <form id="searchForm" @submit.prevent="handleSubmit(initForm)">
                                <b-field class="searchFieldWrapper">
                                    <validation-provider 
                                        tag="div"
                                        class="searchField"
                                        :customMessages="{ isNotBlank: errorMsg.subject }"
                                        :rules="{required:true, isNotBlank:categories.selected}" 
                                        v-slot="{ errors, classes }">
                                        <b-autocomplete
                                            :class="classes"
                                            v-model="categories.current"
                                            :data="categories.data"
                                            autocomplete="courseSearch"
                                            :loading="categories.isLoading"
                                            placeholder="Search for..."
                                            @typing="searchOnTyping"
                                            @select="onSelect($event)"
                                            :clearable="true"
                                        >
                                            <template slot-scope="props">
                                                <template v-if="props.option.course_url">
                                                    <div class="suggestion courseBlock">
                                                        <figure>
                                                            <div class="imageWrapper" v-if="false">
                                                                <img :src="props.option.imageurl" :alt="props.option.title">
                                                            </div>
                                                            <figcaption>
                                                                <p class="courseTitle">{{ props.option.title }}</p>
                                                                <p class="courseDetail">
                                                                    <span class="caption">Course</span>
                                                                    <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>
                                                                </p>
                                                            </figcaption>
                                                        </figure>
                                                    </div>
                                                </template>
                                                <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">
                                                    <div class="suggestion categoryBlock">
                                                        <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>
                                                        <p class="courseDetail">
                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>
                                                        </p>
                                                    </div>
                                                </template>
                                                <template v-if="props.option.parent_cat_slug && props.option.course_count">
                                                    <div class="suggestion categoryBlock">
                                                        <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>
                                                        <p class="courseDetail">
                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>
                                                        </p>
                                                    </div>
                                                </template>
                                            </template>
                                        </b-autocomplete>
                                    </validation-provider>
                                    <div class="ctaWrapper">
                                        <b-button
                                            native-type="submit"
                                            class="doSearch">
                                            <span class="material-icons-outlined">search</span>
                                        </b-button>  
                                    </div>
                                </b-field>
                            </form>
                        </validation-observer>
                        <div class="categoriesWrapper" :class="{ 'has-scroll': scrollState.isScrollable }">
                            <b-button class="prev" @click="handlePrevClick">
                                <span class="material-icons">chevron_left</span>
                            </b-button>
                            <ul class="categories">
                                <template v-if="categoryList.loading">
                                    <li v-for="(category, i) in 4" :key="i">
                                        <b-skeleton active width="220px" height="66px"></b-skeleton>
                                    </li>
                                </template>
                                <template v-else-if="categoryList.success && categoryList.error === null">
                                    <li v-for="(category, i) in categoryList.data" :key="i" >
                                        <a :href="category.url" class="inner">{{ category.name }} <sup v-if="false">®</sup></a>
                                    </li>
                                </template>
                            </ul>
                            <b-button class="next" @click="handleNextClick">
                                <span class="material-icons">chevron_right</span>
                            </b-button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    `,
    data() {
        return {
            heroStyle: {
                "background-image": "url("+ this.$props.data.bgImg +")"
            },
            categories: {
                data: [],
                selected: null,
                current: "",
                isLoading: false
            },
            errorMsg: {
                subject: "Please select the subject from list"
            },
            payload: {
                search: ""
            },
            searchParams: {
                limit: 20,
                offset: 0,
                personalization: "all",
                category: [],
                category_level_1: [],
                category_level_2: [],
                class_days_time: [
                    {
                        selected: [],
                        slug: "class_days"
                    },
                        {
                        selected: [],
                        slug: "class_time"
                    }
                ],
                instructor_id: 0,
                price_per_hour: 10000,
                total_duration: 24
            },
            scrollState: {
                currentPosition: 0,
                scrollAmount: 200, // Default scroll amount
                visibleItems: 4,
                totalItems: 0,
                isScrollable: false
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'searchSuggestions',
            'categoryList'
        ]),
        isScrollable() {
            const categoriesList = this.$el?.querySelector('.categories');
            return categoriesList ? categoriesList.scrollWidth > categoriesList.clientWidth : false;
        }
    },
    async created() {
        
    },
    mounted() {
        this.fetchCategories();
        // Wait for categories to be loaded and rendered
        this.$watch('categoryList.data', () => {
            this.$nextTick(() => {
                this.initializeScroll();
                this.checkScrollable();
            });
        }, { immediate: true });

        // Add resize observer to check scrollable state on window resize
        window.addEventListener('resize', this.checkScrollable);
    },
    beforeDestroy() {
        // Clean up scroll event listener
        const categoriesList = this.$el.querySelector('.categories');
        if (categoriesList) {
            categoriesList.removeEventListener('scroll', this.updateButtonStates);
        }
        window.removeEventListener('resize', this.checkScrollable);
    },
    methods: {
        initForm() {
            const selected = this.categories.selected,
                data = {
                    filter: "category",
                    id: selected.category_id,
                    label: selected.category,
                    parent_id: 0,
                    slug: selected.categorySlug
                },
                filter = {
                    filter: "category"
                };

            if (this.$props.hassearchbar === undefined) {
                // this.searchParams.category = [selected.category_id];
                window.location.href = YUNOCommon.config.host() + "/search/?state="+ encodeURI(JSON.stringify(this.searchParams)) +"";
            } else {
                Event.$emit('initHeaderSearch', data, filter);
            }
        },
        gotCourseSuggestions(options) {
            this.categories.isLoading = false;
            if (options.response?.data?.code === 200) {
                const { course, category, sub_category} = options.response.data.data;

                if (category) {
                    this.categories.data.push(...category);
                }

                if (sub_category) {
                    this.categories.data.push(...sub_category);
                }

                if (course) {
                    this.categories.data.push(...course);
                }
            }
        },
        fetchCourseSuggestions(query) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generic("courseSuggestions", query),
                module: "gotData",
                store: "searchSuggestions",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotCourseSuggestions(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        searchOnTyping: _.debounce(function (name) {
            if (name.length > 2) {
                this.categories.isLoading = true;
                this.categories.data = [];
                this.fetchCourseSuggestions(name);    
            } else {
                this.categories.data = []
                return
            }

        }, 700),
        onSelect(e) {
            if (!e) return;
        
            if (e.course_url) {
                window.location.href = e.course_url;
                return;
            }
        
            if (e.course_count) {
                this.categories.selected = e;
                this.payload.search = e.id;
                this.searchParams.category = [e.id];
                this.searchParams.category_level_1 = [];
                this.searchParams.category_level_2 = [];

                if (e.parent_cat_slug) {
                    this.searchParams.category = [e.parent_cat_id];
                    this.searchParams.category_level_1 = [e.category_level_1];
                    this.searchParams.category_level_2 = [e.id];
                }
        
                this.initForm();
            }
        },
        gotCategories(options) {
            const { code, data } = options.response?.data || {};
            
            if (code === 200 ) {
                
            };
        },
        fetchCategories() {
            const options = { 
                apiURL: YUNOCommon.config.categoriesAPi("?isAll=true"),
                module: "gotData",
                store: "categoryList",
                callback: true,
                callbackFunc: (options) => this.gotCategories(options)
            };

            this.$store.dispatch('fetchData', options); 
        },
        initializeScroll() {
            const categoriesList = this.$el.querySelector('.categories');
            if (categoriesList) {
                this.scrollState.totalItems = this.categoryList.data.length;
                // Add scroll event listener
                categoriesList.addEventListener('scroll', this.updateButtonStates);
                // Initial button state update
                this.updateButtonStates();
            }
        },
        updateButtonStates() {
            const prevButton = this.$el.querySelector('.prev');
            const nextButton = this.$el.querySelector('.next');
            const categoriesList = this.$el.querySelector('.categories');
            
            if (prevButton && nextButton && categoriesList) {
                const hasOverflow = categoriesList.scrollWidth > categoriesList.clientWidth;
                
                // Enable/disable prev button
                prevButton.disabled = !hasOverflow || categoriesList.scrollLeft <= 0;
                
                // Enable/disable next button
                const maxScroll = categoriesList.scrollWidth - categoriesList.clientWidth;
                nextButton.disabled = !hasOverflow || Math.ceil(categoriesList.scrollLeft) >= maxScroll;
            }
        },
        handlePrevClick() {
            const categoriesList = this.$el.querySelector('.categories');
            if (categoriesList) {
                const scrollAmount = Math.min(this.scrollState.scrollAmount, categoriesList.scrollLeft);
                categoriesList.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            }
        },
        handleNextClick() {
            const categoriesList = this.$el.querySelector('.categories');
            if (categoriesList) {
                const maxScroll = categoriesList.scrollWidth - categoriesList.clientWidth;
                const remainingScroll = maxScroll - categoriesList.scrollLeft;
                const scrollAmount = Math.min(this.scrollState.scrollAmount, remainingScroll);
                
                categoriesList.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }
        },
        checkScrollable() {
            const categoriesList = this.$el?.querySelector('.categories');
            if (categoriesList) {
                this.scrollState.isScrollable = categoriesList.scrollWidth > categoriesList.clientWidth;
            }
        },
    }
});
const YUNOCategories = (function($) {
    
    const categories = function() {
        Vue.component('yuno-categories', {
            props: ["data"],
            template: `
                <section id="yunoCategories" class="yunoCategories">
                    <div class="container">
                        <h1 class="sectionTitle">{{data.title}}</h1>
                        <div class="row categoryWrapper">
                            <div 
                                v-for="(category, categoryIndex) in data.data.slice(0, 3)"
                                :key="categoryIndex" 
                                class="col-12 col-md-12"
                                :class="setColumns">
                                <article class="categoryCard">
                                    <figure class="cardImg">
                                        <img width="348" height="196" :src="category.image + imgParameter" :alt="category.name">
                                    </figure>
                                    <div class="cardBody">
                                        <h2 class="cardTitle">{{category.name}}</h2>
                                        <p class="cardDescription">{{setDescription(category.desciption)}}</p>
                                    </div>
                                    <div class="cardFooter">
                                        <b-button tag="a"
                                            :href="category.url"
                                            class="yunoPrimaryCTA wired small">
                                            <span class="makeIndent">To explore our {{category.name}} channel </span>Learn more
                                        </b-button>
                                    </div>
                                </article>
                            </div>
                        </div>
                    </div>
                </section>
            `,
            computed: {
                imgParameter() {
                    return YUNOCommon.config.addVerion(true)
                },
                setColumns: {
                    get() {
                        let grid = "";
                
                        switch (this.$props.data.data.length) {
                            case 2:
                                grid = "col-lg-6";
                                break;
                            case 3:
                                grid = "col-lg-4";
                                break;
                            case 4:
                                grid = "col-lg-4";
                                break;
                            default:
                                // For lengths greater than 4
                                if (this.$props.data.data.length > 4) {
                                    grid = "col-lg-4";
                                }
                                break;
                        }
                
                        return grid;
                    }
                }
            },
            methods: {
                removeTagsAndTruncate(input, maxLength = 200) {
                    // Replace closing tags with a space
                    const stringWithSpaces = input.replace(/<\/[^>]+>/g, " ");

                     // Remove all remaining tags
                    const stringWithoutTags = stringWithSpaces.replace(/<[^>]+>/g, "");
                  
                    if (stringWithoutTags.length > maxLength) {
                      return stringWithoutTags.substr(0, maxLength) + "...";
                    }
                  
                    return stringWithoutTags;
                },
                setDescription(str) {
                    return this.removeTagsAndTruncate(str)
                },
            }
        });
    };

    return {
        categories: categories
    };
})(jQuery);


const YUNOCarouselList = (function($) {
    
    const carouselList = function() {
        Vue.component('yuno-carousel-list', {
            props: ["data"],
            template: `
                <section id="yunoCarouselList" class="yunoCarouselList">
                    <div class="container">
                        <h2 class="sectionTitle">{{data.title}}</h2>
                        <div class="carouselListControls" id="carouselListControls">
                            <button type="button" data-controls="prev" tabindex="-1" aria-controls="tns1"><i class="fa fa-long-arrow-left" aria-hidden="true"></i></button>
                            <button type="button" data-controls="next" tabindex="-1" aria-controls="tns1"><i class="fa fa-long-arrow-right" aria-hidden="true"></i></button>
                        </div>
                        <div id="carouselList" class="carouselList">
                            <div 
                                class="carouselCard"
                                v-for="(item, carouselIndex) in data.data"
                                :key="carouselIndex">
                                <div class="innerWrap">
                                    <figure class="cardImg">
                                        <img width="253" height="140" :src="item.MediaURL + imgParameter" :alt="item.ProductTitle">
                                    </figure>
                                    <div class="cardBody">
                                        <h3 class="cardTitle"><span v-html="item.ProductTitle"></span></h3>
                                        <p class="cardPrice">&#8377; {{item.UnitPrice}}</p>
                                    </div>
                                    <div class="cardFooter">
                                        <a :href="item.CourseLink">View Details</a>
                                    </div>
                                </div>  
                            </div>
                        </div>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                imgParameter() {
                    return YUNOCommon.config.addVerion(true)
                }
            },
            mounted() {
                let slider = tns({
                    container: "#carouselList",
                    controlsContainer: "#carouselListControls",
                    loop: false,
                    responsive: {
                        500: {
                            items: 1
                        },
                        768: {
                            items: 2
                        },
                        992: {
                            items: 3
                        },
                        1200: {
                            items: 4
                        }
                    },
                    swipeAngle: false,
                    speed: 400,
                    nav: false,
                    mouseDrag: true,
                    nonce: yunoNonce
                });
            }
        });
    };

    return {
        carouselList: carouselList
    };
})(jQuery);


const YUNOInstructorList = (function($) {
    
    const instructorList = function() {
        Vue.component('yuno-instructor-list', {
            props: ["data"],
            template: `
                <div>
                    <section id="yunoInstructorList" class="yunoInstructorList">
                        <div class="container">
                            <h2 class="sectionTitle">{{data.title}}</h2>
                            <p class="sectionDes">{{data.description}}</p>
                            <div class="sliderWrapper">
                                <ul id="instructorCarousel" class="userList">
                                    <li 
                                        class="item"
                                        v-for="(list, instructorIndex) in data.data"
                                        :key="instructorIndex">
                                        <a :href="list.url" target="_blank">
                                            <figure class="userImg">
                                                <img :src="list.image + imgParameter" :alt="list.name">
                                                <figcaption>
                                                    <h3 class="username">{{list.name}}</h3>
                                                    <h4 class="experience" v-if="false">{{list.experience}}</h4>
                                                    <div class="stars">
                                                        <star-rating :rating="Number(list.rating)" active-color="#F9B600" :read-only="true" :round-start-rating="false" :star-size="18" :show-rating="false"></star-rating>
                                                        <div class="vue-star-rating-rating-text" v-if="list.rating !== '0'">{{ list.rating }}</div>
                                                    </div>
                                                </figcaption>
                                            </figure>
                                            <p class="description" v-if="list.about !== ''">{{ getAbout(list.about) + '...' }}</p>
                                        </a>
                                    </li>
                                </ul>
                                <div class="carouselListControls">
                                    <button class="prev" type="button" data-controls="prev" tabindex="-1" aria-controls="tns1">
                                        <span class="material-icons-outlined">chevron_left</span>
                                    </button>
                                    <button class="next" type="button" data-controls="next" tabindex="-1" aria-controls="tns1">
                                        <span class="material-icons-outlined">chevron_right</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>
                </div> 
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                imgParameter() {
                    return YUNOCommon.config.addVerion(true)
                }
            },
            mounted() {
                this.initSlider();
            },
            methods: {
                getAbout(str) {
                    const removeTags = YUNOCommon.removeTagsFromString(str)
                    return removeTags.substring(0, 100);
                },
                initSlider() {
                    let slider = tns({
                        container: "#instructorCarousel",
                        controlsContainer: ".carouselListControls",
                        loop: true,
                        responsive: {
                            500: {
                                items: 1
                            },
                            768: {
                                items: 2
                            },
                            992: {
                                items: 3
                            },
                            1200: {
                                items: 3
                            }
                        },
                        swipeAngle: false,
                        speed: 400,
                        nav: false,
                        mouseDrag: true,
                        autoplay: true,
                        autoplayTimeout: 6000,
                        nonce: yunoNonce
                    });
                },
            }
        });
    };

    return {
        instructorList: instructorList
    };
})(jQuery);


Vue.component('yuno-partner-list', {
    props: ["data"],
    template: `
        <div>
            <section id="yunoPartnerList" class="yunoPartnerList">
                <div class="container">
                    <h2 class="sectionTitle">{{data.title}}</h2>
                    <div class="sliderWrapper">
                        <ul id="partnerCarousel" class="userList">
                            <li 
                                class="item"
                                v-for="(list, i) in data.items"
                                :key="i"
                                >
                                    <figure class="imgWrapper">
                                        <img :src="list.image + imgParameter" :alt="list.name">
                                    </figure>
                            </li>
                        </ul>
                        <div id="partnerCarouselControls" class="partnerCarouselControls">
                            <button class="prev" type="button" data-controls="prev" tabindex="-1" aria-controls="tns1">
                                <span class="material-icons-outlined">chevron_left</span>
                            </button>
                            <button class="next" type="button" data-controls="next" tabindex="-1" aria-controls="tns1">
                                <span class="material-icons-outlined">chevron_right</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div> 
    `,
    data() {
        return {
            
        }
    },
    computed: {
        imgParameter() {
            return YUNOCommon.config.addVerion(true)
        }
    },
    mounted() {
        this.initSlider();
    },
    methods: {
        getAbout(str) {
            const removeTags = YUNOCommon.removeTagsFromString(str)
            return removeTags.substring(0, 100);
        },
        initSlider() {
            let slider = tns({
                container: "#partnerCarousel",
                controlsContainer: ".partnerCarouselControls",
                loop: true,
                responsive: {
                    500: {
                        items: 1
                    },
                    768: {
                        items: 2
                    },
                    992: {
                        items: 3
                    },
                    1200: {
                        items: 3
                    }
                },
                swipeAngle: false,
                speed: 400,
                nav: false,
                mouseDrag: true,
                autoplay: true,
                autoplayTimeout: 6000,
                nonce: yunoNonce,
                nonce: yunoNonce
            });
        },
    }
});


const YUNOWhyLearn = (function($) {
    
    const whyLearn = function() {
        Vue.component('yuno-why-learn', {
            props: ["data"],
            template: `
                <section id="yunoWhyLearn" class="yunoWhyLearn">
                    <div class="container">
                        <h2 class="sectionTitle" :class="[data.description !== undefined ? 'hasDescription' : '']">{{data.title}}</h2>
                        <p v-if="data.description !== undefined" class="description">{{ data.description }}</p>
                        <ul class="yunoTiles">
                            <li 
                                v-for="(tile, tileIndex) in data.list"
                                :key="tileIndex"
                                :class="tile.icon">
                                <div class="innerWrapper">
                                    <span :class="[tile.iconType, 'icon']">{{tile.icon}}</span>
                                    <div class="info">
                                        <h3>{{tile.title}}</h3>
                                        <p>{{tile.description}}</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            mounted() {
                
            }
        });
    };

    return {
        whyLearn: whyLearn
    };
})(jQuery);


const YUNOHomeHero = (function($) {
    const homeHero = function() {
        Vue.component('yuno-home-hero', {
            props: ["data", "imageOnly", "customClass"],
            template: `
                <section id="homeHeroCarousel" class="homeHeroCarousel">
                    <template v-if="imageOnly !== undefined && imageOnly">
                        <div 
                            v-for="(slide, slideIndex) in data.list"
                            :key="slideIndex"
                            class="yunoHero noCaption"
                            :class="customClass !== undefined ? customClass : 'noClass'"
                            :style="{backgroundImage: 'url(' + slide.img + imgParameter + ')'}">
                        </div>
                    </template>
                    <template v-else>
                        <div
                            v-for="(slide, slideIndex) in data.list" 
                            :key="slideIndex"
                            class="yunoHero" 
                            :style="{backgroundImage: 'url(' + slide.img + imgParameter + ')'}">
                            <div class="container">
                                <div class="row">
                                    <div class="col-12 col-md-7 col-lg-7">
                                        <div class="heroCopy">
                                            <small class="helper">{{slide.category}}</small>
                                            <h2 class="title">{{slide.title}}</h2>
                                            <h3 class="subTitle">{{slide.subTitle}}</h3>
                                            <b-button tag="a"
                                                :href="slide.ctaURL"
                                                class="yunoSecondaryCTA">
                                                {{slide.cta}}
                                            </b-button>  
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                imgParameter() {
                    return YUNOCommon.config.addVerion(true)
                }
            },
            async created() {
                
            },
            mounted() {
                let slider = tns({
                    container: "#homeHeroCarousel",
                    loop: true,
                    responsive: {
                        500: {
                            items: 1
                        },
                        768: {
                            items: 1
                        },
                        992: {
                            items: 1
                        },
                        1200: {
                            items: 1
                        }
                    },
                    swipeAngle: false,
                    speed: 400,
                    nav: false,
                    mouseDrag: true,
                    autoplay: true,
                    nav: false,
                    navPosition: "bottom",
                    autoplayTimeout: 6000,
                    nonce: yunoNonce
                });
            },
            methods: {
                
            }
        });
    };

    return {
        homeHero: homeHero
    };
})(jQuery);




Vue.component('yuno-app-banner', {
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    template: `
        <div class="yunoAppV2">
            <div class="container">
                <figure>
                    <div class="imgWrapper">
                        <img :src="data.screenshot" :alt="data.title" width="349" height="374">
                    </div>
                    <figcaption>
                        <div class="wrapper">
                            <h2 class="largestTitle">{{ data.title }}</h2>
                            <p class="smallerCaption">{{ data.subtitle }}</p>
                            <ul class="list-unstyled">
                                <li>
                                    <img :src="data.qrCode" alt="Yuno Android APP QR Code" width="96" height="96">
                                </li>
                                <li>
                                    <a :href="data.androidApp" target="_blank"><img :src="data.appCTA" alt="Yuno Android APP Store" width="180" height="52"></a>
                                </li>                                       
                            </ul>
                        </div>
                    </figcaption>
                </figure>
            </div>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});
const YUNOHome = (function($) {
    
    const home = function() {
        YUNOCategories.categories();
        YUNOCarouselList.carouselList();
        YUNOInstructorList.instructorList();
        YUNOWhyLearn.whyLearn();
        YUNOHomeHero.homeHero();
        YUNOReviewCard.reviewCard();
        YUNOInsightsCard.insightsCard();
        YUNOFeaturesList.featuresList();

        Vue.component('yuno-home', {
            template: `
                <yuno-page-grid
                    @onUserInfo="onUserInfo"
                >
                    <template v-slot:main>
                        <yuno-hero-banner-v2 v-if="!user.isLoggedin" :data="heroData"></yuno-hero-banner-v2>
                        <yuno-home-hero v-if="user.isLoggedin && userInfo.error === null" :data="homeHero"></yuno-home-hero>
                        <div class="container" v-if="instructorReviews.error === null">
                            <yuno-review-card :dataid="0" :options="reviewsOptions"></yuno-review-card>
                        </div>
                        <yuno-partner-list :data="partnerList"></yuno-partner-list>
                        <yuno-insights-card v-if="false" :data="insightsCards"></yuno-insights-card>
                        <template v-if="isOtherModuleLoading">
                            <section id="yunoCategories" class="yunoCategories">
                                <div class="container">
                                    <div class="row categoryWrapper">
                                        <div 
                                            v-for="(category, categoryIndex) in 3"
                                            :key="categoryIndex" 
                                            class="col-12 col-md-4"
                                        >
                                            <article class="categoryCard">
                                                <figure class="cardImg">
                                                    <b-skeleton height="196px"></b-skeleton>
                                                </figure>
                                                <div class="cardBody">
                                                    <h2 class="cardTitle"><b-skeleton active></b-skeleton></h2>
                                                </div>
                                            </article>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </template>
                        <template v-if="isOtherModuleReady">
                            <yuno-categories :data="homeCategories"></yuno-categories>
                            <yuno-carousel-list v-if="false" :data="homeCarouselList"></yuno-carousel-list>
                            <yuno-instructor-list :data="instructorslList"></yuno-instructor-list>
                            <yuno-features-list :data="whyLiveClasses"></yuno-features-list>
                            <yuno-app-banner :data="appBanner"></yuno-app-banner>
                            <yuno-why-learn :data="whyLearn"></yuno-why-learn>
                        </template>
                    </template>
                </yuno-page-grid>
            `,
            data() {
                return {
                    marginTop: 15,
                    isMiniSidebar: false,
                    appBanner: {
                        screenshot: this.$store.state.themeURL + "/assets/images/yunoApp.webp",
                        qrCode: this.$store.state.themeURL + "/assets/images/appQR.webp",
                        appCTA: this.$store.state.themeURL + "/assets/images/googlePlayCTA.webp",
                        androidApp: "https://play.google.com/store/apps/details?id=com.yunolearning.learn&pli=1",
                        title:  "Learn Anytime, Anywhere with the Yuno App",
                        subtitle: "Available on Android"
                    },
                    partnerList: {
                        title: "Trusted by Leading Companies",
                        items: [
                            {
                                image: this.$store.state.themeURL + "/assets/images/image_2023_04_03T10_34_51_664Z.png",
                                name: "Upgrad"
                            },
                            {
                                image: this.$store.state.themeURL + "/assets/images/image_2023_04_03T10_34_20_504Z.png",
                                name: "Imarticus"
                            },
                            {
                                image: this.$store.state.themeURL + "/assets/images/image_2023_04_03T10_33_53_966Z.png",
                                name: "EagleiAdvisor"
                            },
                            {
                                image: this.$store.state.themeURL + "/assets/images/image_2023_04_03T10_36_45_965Z.png",
                                name: "Code Quotient"
                            },
                            {
                                image: this.$store.state.themeURL + "/assets/images/logo-xornor.png",
                                name: "Xornor"
                            },
                            {
                                image: this.$store.state.themeURL + "/assets/images/ishyaLogo.jpg",
                                name: "Ishya Foundation"
                            },
                            {
                                image: this.$store.state.themeURL + "/assets/images/aaryanOverseas.png",
                                name: "Aaryan"
                            },
                        ]
                    },
                    searchbarOptions: {
                        isActive: true,
                        data: []
                    },
                    whyLiveClasses: {
                        title: "Benefits of Personalized Live Classes",
                        cards: [
                            {
                                img: this.$store.state.themeURL + "/assets/images/Small-group-classes.jpg",
                                title: "Small group classes",
                                customClass: "hasRightBorder",
                                list: [
                                    "You can choose your instructor and the specific batch time that fits your schedule",
                                    "Because the classes are in really small groups, you will receive personalised attention from your instructor",
                                    "Small group classes are the most affordable way for you to access personalized learning"
                                ]
                            },
                            {
                                img: this.$store.state.themeURL + "/assets/images/1-to-1-Classes.jpg",
                                title: "1 to 1 Classes",
                                customClass: "",
                                list: [
                                    "You can choose your instructor and the specific class time that fits your schedule",
                                    "Since you are the only student in the class, you can work with your instructor to decide your class schedule",
                                    "Although more expensive than group classes, 1-to-1 classes are the most personalized option on Yuno"
                                ]
                            }
                        ]
                    },
                    insightsCards: {
                        cards: [
                            {
                                iconType: "material-icons-outlined",
                                icon: "school",
                                label: "40,000+",
                                helper: "Students",
                                customClass: "students"
                            },
                            {
                                iconType: "material-icons-outlined",
                                icon: "devices",
                                label: "125,000+",
                                helper: "live classes",
                                customClass: "liveClasses"
                            },
                            {
                                iconType: "material-icons-outlined",
                                icon: "how_to_reg",
                                label: "87%",
                                helper: "attendence",
                                customClass: "attendence"
                            }
                        ]
                    },
                    reviewsOptions: {
                        title: "Rated 5 Stars on Google",
                        titleClass: "fatSize",
                        type: "toprated",
                        view: "list-view",
                        sorting: "featured",
                        limit: 3,
                        loadMoreParentClass: "marginBtm30",
                        viewType: "gridView",
                        hasShowMore: true,
                        hasBasedOn: true,
                        externalURL: "https://www.google.com/search?q=yuno+learning&sxsrf=ALiCzsaa3y8Tp4vq4iSyUOlNKIzl1srhxg%3A1665188984807&ei=eMRAY8jVMOOtqtsPwd-5sAc&ved=0ahUKEwjI8bCvsM_6AhXjlmoFHcFvDnYQ4dUDCA4&uact=5&oq=yuno+learning&gs_lcp=Cgdnd3Mtd2l6EAMyBAgjECcyBAgjECcyBQgAEIAEMgUIABCABDIGCAAQHhAWMgYIABAeEBYyBggAEB4QFjIFCAAQhgM6CggAEEcQ1gQQsAM6DQgAEEcQ1gQQsAMQyQNKBAhBGABKBAhGGABQxQNYxQNgpAdoAnABeACAAa4BiAGuAZIBAzAuMZgBAKABAcgBCMABAQ&sclient=gws-wiz#lrd=0x390fedb7a6342f61:0xd0865bda29b46987,1,,,",
                        overallStars: true,
                        customClass: "yunoHomeReviews"
                    },
                    heroData: {
                        title: "Learn from top-rated instructors in small groups or 1-on-1",
                        rating: {
                            isActive: true,
                            label: "Rated 5-Star on Google",
                        },
                        bgImg: this.$store.state.themeURL + "/assets/images/herov2BG.png",
                        categories: [
                            {
                                label: "English Speaking",
                                url: "/english-speaking/"
                            },
                            {
                                label: "IELTS",
                                url: "/ielts/"
                            },
                            {
                                label: "PTE",
                                url: "/pte/"
                            },
                            {
                                label: "TOEFL",
                                url: "/toefl/"
                            },
                            {
                                label: "Duolingo",
                                url: "/duolingo/"
                            }
                        ]
                    },
                    homeSignin: {
                        category: "general",
                        productcode: "",
                        leadstatus: "",
                        content_type: "",
                        content_id: ""
                    }
                }
            },
            computed: {
                ...Vuex.mapState([
                    'user',
                    'userInfo',
                    'header',
                    'userProfile',
                    'userRole',
                    'footer',
                    'homeCategories',
                    'homeCarouselList',
                    'instructorslList',
                    'whyLearn',
                    'homeHero',
                    'instructorReviews',
                    'module'
                ]),
                isPageLoading: {
                    get() {
                        const module =  this.userInfo.loading 
                                        || this.header.loading 
                                        || this.footer.loading 
                        return module
                    }
                },
                isPageReady: {
                    get() {
                        let module = "";

                        if (this.user.isLoggedin) {
                            module =    this.header.success
                                        && this.footer.success 
                                        && this.userInfo.success
                        } else {
                            module =    this.header.success
                                        && this.footer.success 
                                        
                        }

                        return module
                    }
                },
                isOtherModuleLoading: {
                    get() {
                        let module =  this.homeCategories.loading 
                                        || this.instructorslList.loading 
                        return module
                    }
                },
                isOtherModuleReady: {
                    get() {
                        let module =  this.homeCategories.success 
                                        && this.instructorslList.success 
                        return module
                    }
                },
                isUserLoading: {
                    get() {
                        const module =  this.userInfo.loading 
        
                        return module
                    }
                },
                isUserReady: {
                    get() {
                        let module = "";
        
                        if (this.user.isLoggedin) {
                            module = this.userInfo.success
                        } else {
                            module = true 
                        }
        
                        return module
                    }
                }
            },
            async created() {
                this.loginStatus();
                // this.fetchFooter();
                this.emitEvents();
            },
            mounted() {
                // this.searchbarOptions.data = this.module;
            },
            methods: {
                gotUserInfo(options) {
                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        const data = options.response.data.data;
                    }
                },
                fetchUserInfo() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                        module: "gotData",
                        store: "userInfo",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotUserInfo(options)
                        }
                    };
        
                    this.$store.dispatch('fetchData', options);
                },
                loginStatus() {
                    let userID = Number(isLoggedIn); // Logged-in user id
                    
                    if (userID !== 0) {
                        this.user.isLoggedin = true;
                        this.fetchUserInfo();
                    } else {
                        this.user.isLoggedin = false;
                    }
                },
                searchBar() {
                    if (this.user.isLoggedin && this.userRole.data === "Learner") {
                        return true
                    } else {
                        return false
                    }
                },
                onLogin(staus) {
        
                },
                onUserInfo(data) {
                    this.fetchCategories();
                    this.fetchFeaturedInstructor();
                },
                onMini(data) {
                    this.isMiniSidebar = data;
                },
                onMenuLoaded() {
                    
                },
                emitEvents() {
                    Event.$on('checkLoggedInState', (status) => {
                        if (status) {
                            Event.$on('gotUserRole', (role, userData) => {
                                Event.$on('gotUserMenu', () => {
                                    this.fetchCategories();
                                    this.fetchFeaturedInstructor();
                                });
                            });
                        } else {
                            this.fetchCategories();
                            this.fetchFeaturedInstructor();
                        };
                    });
                },
                structuredData(structuredDataObj) {
                    const script = document.createElement('script');
                    
                    script.setAttribute('type', 'application/ld+json');

                    let inlineScript = document.createTextNode(JSON.stringify(structuredDataObj));

                    script.appendChild(inlineScript); 
                    document.head.appendChild(script);
                },
                fetchFooter() {
                    const options = {
                        apiURL: YUNOCommon.config.footerAPI(),
                        module: "gotData",
                        store: "footer",
                        callback: false
                    };

                    this.$store.dispatch('fetchData', options);
                },
                categoriesRichSnippet() {
                    let getData = this.homeCategories.data,
                        structuredDataText = {
                            "@context": "https://schema.org",
                            "@type": "Dataset",
                            "name": "Top Courses on Yuno",
                            "description": "Explore the most popular subject categories on Yuno. Each category offers multiple courses to choose from and each course consists of personalised live classes",
                            "hasPart": [],
                            "license" : "https://creativecommons.org/licenses/by/4.0/",
                            "creator":{
                                "@type":"Organization",
                                "name": "Yuno Learning"
                            }
                        }

                    for (var i = 0; i < getData.length; i++) {
                        let dataObj = {
                            "@type": "Dataset",
                            "url": getData[i].url,
                            "name": getData[i].name,
                            "description": getData[i].desciption,
                            "image": getData[i].image,
                            "license" : "https://creativecommons.org/licenses/by/4.0/",
                            "creator":{
                                "@type":"Organization",
                                "name": "Yuno Learning"
                            }
                        }

                        structuredDataText.hasPart.push(dataObj);
                    };

                    this.structuredData(structuredDataText);
                },
                gotCategories(options) {
                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        // this.categoriesRichSnippet();
                    }
                },
                fetchCategories() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.categoriesAPi(),
                        module: "gotData",
                        store: "homeCategories",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotCategories(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                featuredCoursesRichSnippet() {
                    let getData = this.homeCarouselList.data,
                        structuredDataText = {
                            "@context": "https://schema.org",
                            "@type": "ItemList",
                            "name": this.homeCarouselList.title,
                            "itemListElement": []
                        }

                    for (var i = 0; i < getData.length; i++) {
                        let dataObj = {
                            "@type": "ListItem",
                            "position": parseInt(i + 1),
                            "item": {
                                "@type": "Product",
                                "url": getData[i].CourseLink,
                                "name": getData[i].ProductTitle,
                                "image": getData[i].MediaURL,
                                "offers": {
                                    "@type": "Offer",
                                    "url": getData[i].CourseLink,
                                    "priceCurrency": "INR",
                                    "price": getData[i].UnitPrice
                                }
                            }
                        }

                        structuredDataText.itemListElement.push(dataObj);
                    };

                    this.structuredData(structuredDataText);
                },
                gotFeaturedCourses(options) {
                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        this.featuredCoursesRichSnippet();
                    }
                },
                fetchFeaturedCourses() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.featuredCoursesAPi(),
                        module: "gotData",
                        store: "homeCarouselList",
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotFeaturedCourses(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
                fetchFeaturedInstructor() {
                    const options = {
                        apiURL: YUNOCommon.config.featuredInstructorAPi('home'),
                        module: "gotData",
                        store: "instructorslList",
                        callback: false
                    };

                    this.$store.dispatch('fetchData', options);
                }
            }
        });
    };

    return {
        home: home
    };
})(jQuery);



