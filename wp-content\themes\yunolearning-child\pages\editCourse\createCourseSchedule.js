Vue.component('yuno-create-course-schedule', {
    template: `
        <section class="moduleEdit">
            <div class="sectionTitle">
                <div class="inlineBlock">
                    <h2 class="h3">{{ pageTitleData.title }}</h2>
                </div>
            </div>
            <yuno-activity :data="settings" @addActivity="onAddActivity"></yuno-activity>
            <div class="row">
                <template v-if="isEdit">
                    <template v-if="publishedResources.loading">
                        <div class="smallLoader"></div>
                    </template>
                    <template v-if="publishedResources.success">
                        <div class="col-12 col-md-6">
                            <yuno-schedule-form 
                                @onRemoveActivity="onRemoveActivity"
                                @initForm="onFormSubmit"
                                :options="{'loading': isFormLoading, 'isEdit': isEdit, 'duration': durationOpt}"
                                :data="payload">
                            </yuno-schedule-form>
                        </div>
                        <div class="col-12 col-md-6">
                            <yuno-schedule-preview :data="payload" :options="{'isEdit': isEdit, 'duration': durationOpt}"></yuno-schedule-preview>
                        </div>
                    </template>
                </template>
                <template v-else>
                    <div class="col-12 col-md-6">
                        <yuno-schedule-form 
                            v-if="isScheduleReady" 
                            @onRemoveActivity="onRemoveActivity"
                            @initForm="onFormSubmit"
                            :options="{'loading': isFormLoading, 'isEdit': isEdit, 'duration': durationOpt}"
                            :data="payload">
                        </yuno-schedule-form>
                    </div>
                    <div class="col-12 col-md-6">
                        <yuno-schedule-preview :data="payload" :options="{'isEdit': isEdit, 'duration': durationOpt}" v-if="isScheduleReady"></yuno-schedule-preview>
                    </div>
                </template>
            </div>
        </section>
    `,
    data() {
        return {
            isModuleLoading: true,
            pageTitleData: {
                title: "Course Schedule",
            },
            isEdit: false,
            isFormLoading: false,
            section: {
                order: "",
                id: "",
                title: "",
                excerpt: "",
                description: "",
                duration: "",
                activity: "",
                sub_cat: [],
                is_active: false,
                is_remove: false
            },
            durationOpt: [
                {
                    label: "15 Minutes",
                    slug: "15"
                },
                {
                    label: "30 Minutes",
                    slug: "30"
                },
                {
                    label: "45 Minutes",
                    slug: "45"
                },
                {
                    label: "1 Hour",
                    slug: "60"
                },
                {
                    label: "1 Hour 15 Minutes",
                    slug: "75"
                },
                {
                    label: "1 Hour 30 Minutes",
                    slug: "90"
                },
                {
                    label: "1 Hour 45 Minutes",
                    slug: "105"
                },
                {
                    label: "2 Hours",
                    slug: "120"
                }
            ],
            payload: {
                course_id: YUNOCommon.getQueryParameter("courseid"),
                id: YUNOCommon.getQueryParameter("scheduleid"),
                course_schedule: []
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'userInfo',
            'user',
            'course',
            'categoryList',
            'settings',
            'createResource',
            'publishedResources'
        ]),
        courseObj() {
            return this.course.data
        },
        isScheduleReady: {
            get() {
                let module = "";

                module =    this.categoryList.success 
                            && this.settings.success

                return module
            }
        }
    },
    async created() {

    },
    destroyed() {

    },
    mounted() {
        this.initEditMode();
    },
    methods: {
        initEditMode() {
            this.isModuleLoading = false;
            this.publishedResources.data = [];
            this.publishedResources.success = false;
            this.publishedResources.error = null;
            this.fetchModules();
        },
        formPosted(options) {
            this.isFormLoading = false;
            
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const response = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${response.message}`,
                    position: 'is-bottom'
                });
            } else {
                const response = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${response.message}`,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        onFormSubmit() {
            this.isFormLoading = true;
            
            const instance = this;
            const options = {
                apiURL: this.getFormSubmitURL(),
                module: "gotData",
                store: "createResource",
                payload: this.refinePayload(),
                callback: true,
                callbackFunc: function(options) {
                    return instance.formPosted(options)
                }
            };

            this.$store.dispatch(this.isEdit ? 'putData' : 'postData', options);
        },
        getFormSubmitURL() {
            const role = this.userInfo.data.role;
        
            const urlMapping = {
                "org-admin": this.isEdit ? YUNOCommon.config.org("courseSchedule", false, false, false, false, false, false, "edit") : YUNOCommon.config.org("courseSchedule", false, false, false, false, false, false, "create"),
                "yuno-admin": this.isEdit ? YUNOCommon.config.courseSchedule("edit") : YUNOCommon.config.courseSchedule("create")
            };
        
            return urlMapping[role] || null;
        },
        refinePayload() {
            let payload = JSON.parse(JSON.stringify(this.payload)),
                courseSchedule = payload.course_schedule;

            for (let i = 0; i < courseSchedule.length; i++) {
                const schedule = courseSchedule[i],
                    subCats = schedule.sub_cat;

                for (let j = 0; j < subCats.length; j++) {
                    const subCat = subCats[j];
                    
                    if (subCat.selected !== "") {
                        subCat.sub_cat = [subCat.selected]
                        delete subCat.selected 
                    } else {
                        subCat.sub_cat = []
                    }
                }
            };

            return payload;
        },
        onRemoveActivity(data) {
            YUNOCommon.removeObjInArr(this.payload.course_schedule, "id", data.id);

            for (let i = 0; i < this.payload.course_schedule.length; i++) {
                const item = this.payload.course_schedule[i];
                
                item.order = i + 1;
                item.id = item.activity.slug + "-" + item.order;
            }
        },
        generateOrder() {
            let order = "";
            if (this.payload.course_schedule.length === 0) {
                order = 0
            } else {
                order = this.payload.course_schedule.length
            }

            return order;
        },
        onAddActivity(data) {
            let block = JSON.parse(JSON.stringify(this.section));

            // console.log(data);

            block.order = this.generateOrder();
            block.activity = data;
            block.id = data.slug + "-" + this.generateOrder();
            block.sub_cat = JSON.parse(JSON.stringify(this.categoryList.data));
            this.payload.course_schedule.push(block);

            setTimeout(() => {
                YUNOCommon.scrollToElement("#"+ block.id +"", 500, 200);    
            }, 100);
        },
        gotActivities(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            };
        },
        fetchActivities() {
            this.settings.data = [];
            this.settings.success = false;
            this.settings.error = null;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.activityList(),
                module: "gotData",
                store: "settings",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotActivities(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        gotSubCategories(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;

                for (let i = 0; i < data.length; i++) {
                    const item = data[i];
                    item.selected = "";
                }

                this.categoryList.data = data;

                if (YUNOCommon.getQueryParameter("scheduleid") !== "0") {
                    this.fetchSchedule();
                };
            } else {
                if (YUNOCommon.getQueryParameter("scheduleid") !== "0") {
                    this.fetchSchedule();
                };
            };
        },
        fetchSubCategories() {
            this.categoryList.data = [];
            this.categoryList.success = false;
            this.categoryList.error = null;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.subCategoriyList(YUNOCommon.getQueryParameter("courseid")),
                module: "gotData",
                store: "categoryList",
                addToModule: false,
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotSubCategories(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        prefillSubCats(data, formData) {
            const schedules = formData.course_schedule,
                activeSchedules = data.course_schedule;

            for (let i = 0; i < schedules.length; i++) {
                const schedule = schedules[i],
                    activeSchedule = activeSchedules[i],
                    subCats = schedule.sub_cat,
                    activeSubCats = activeSchedule.sub_cat;

                for (let j = 0; j < subCats.length; j++) {
                    const subCat = subCats[j],
                        activeSubCat = activeSubCats[j];

                    if (activeSubCat !== undefined && activeSubCat.sub_cat.length !== 0) {
                        subCat.selected = activeSubCat.sub_cat[0]
                    } else {
                        subCat.selected = "";
                    };
                }
            }

        },
        prefillForm(data) {
            let formData = JSON.parse(JSON.stringify(data))
                courseSchedule = formData.course_schedule;
                

            for (let i = 0; i < courseSchedule.length; i++) {
                const schedule = courseSchedule[i],
                categories = JSON.parse(JSON.stringify(this.categoryList.data));
                
                schedule.sub_cat = [];

                for (let k = 0; k < categories.length; k++) {
                    const category = categories[k];
                    schedule.sub_cat.push(category);
                }
            }

            this.prefillSubCats(data, formData);
            this.payload = formData;
        },
        gotSchedule(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
                this.prefillForm(data);
            };
        },
        fetchSchedule() {
            this.publishedResources.data = [];
            this.publishedResources.success = false;
            this.publishedResources.error = null;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.courseScheduleForm(YUNOCommon.getQueryParameter("courseid"), YUNOCommon.getQueryParameter("scheduleid")),
                module: "gotData",
                store: "publishedResources",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotSchedule(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        manageLoader(state) {
            this.loader.isActive = state;
            this.loader.overlay = state;  
        },
        fetchModules() {
            this.fetchActivities();
            this.fetchSubCategories();

            if (YUNOCommon.getQueryParameter("scheduleid") !== "0") {
                this.isEdit = true
            } else {
                this.isEdit = false
            }
        },
    }
});