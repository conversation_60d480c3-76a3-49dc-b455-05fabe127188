<?php 
class OwaspscanActivities
{
    /**
     * Removes unwanted fields from the response before echoing it.
     *
     * Specifically removes the _links field, and the author, author_name, and
     * author_email fields (if present).
     *
     * @param  object $response The response object to be modified.
     * @param  object $server   The server object.
     * @param  object $request  The request object.
     *
     * @return array The modified response data.
     */
    function fun_pre_echo_response($response, $server, $request)
    {
        // Convert response to array if it's an object
        $data = json_decode(json_encode($response), true);

        // Remove _links if it exists
        if (isset($data['_links'])) {
            unset($data['_links']);
        }
        unset($data['author']);
        unset($data['author_name']);  // If using custom author fields
        unset($data['author_email']); // In case author email is exposed
        return $data;
    }
    /**
     * Generates a Content Security Policy (CSP) header that allows scripts and styles
     * from trusted sources, as well as images and frames from trusted sources.
     *
     * The policy is defined as a string, with each directive separated by a semi-colon.
     * The directives are:
     * - default-src: Defines the default source for all types of content.
     * - script-src: Defines the sources for script tags.
     * - style-src: Defines the sources for style tags.
     * - font-src: Defines the sources for font tags.
     * - img-src: Defines the sources for image tags.
     * - connect-src: Defines the sources for XMLHttpRequest and WebSocket connections.
     * - frame-src: Defines the sources for iframe tags.
     * - object-src: Defines the sources for object tags.
     * - base-uri: Defines the sources for the base tag.
     * - form-action: Defines the sources for form actions.
     * - upgrade-insecure-requests: Upgrades all insecure requests to HTTPS.
     *
     * @return string The CSP header as a string.
     */
    function getCSPHeader() {

        // Generate a unique nonce for each request
        $nonce = generate_csp_nonce();
        $csp = "default-src 'self'; ";
        $csp_part = "script-src 'self' 'unsafe-inline' 'unsafe-eval' ";
        if (preg_match('#^/ielts/practice-tests/[^/]+/$#', $_SERVER['REQUEST_URI'])) {
            $csp_part = "script-src 'self' 'unsafe-inline' 'unsafe-eval' ";
        }
        $csp .= $csp_part;
        //$csp .= "script-src 'self' 'nonce-".$nonce."' 'unsafe-eval' ";
        $csp .= "https://cdn-in.pagesense.io/js/yunolearning/ca8b7e32c2db47f6bc560b1a8e1bcc8f.js ";
        $csp .= "https://connect.facebook.net ";
        $csp .= "https://maps.googleapis.com ";
        $csp .= "https://static.cloudflareinsights.com ";
        $csp .= "https://cdn.jsdelivr.net ";
        $csp .= "https://checkout.razorpay.com ";
        $csp .= "https://www.googleadservices.com ";
        $csp .= "https://player.vimeo.com ";
        $csp .= "https://www.googletagmanager.com ";
        $csp .= "https://extend.vimeocdn.com; ";

        $csp .= "style-src 'self' 'unsafe-inline' ";
        //$csp .= "style-src 'self' 'unsafe-inline' 'nonce-".$nonce."' ";
        
        //$csp .= "style-src 'self' 'unsafe-inline' 'nonce-".$nonce."' 'unsafe-hashes' 'sha256-i6PBPP0zCDWCZiNFKh1VPPdsWRBYplCcwF/w7cED1ZI=' 'sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=' 'sha256-tEXNQkd5RrIKhAYB1m/jFU8bAqegWBQABEexcRWUr9Q=' 'sha256-MlPLRRsaPxVRsPuWUougFf5bFG82y8skPPSO9rhm0S4=' 'sha256-Id7ETHdBACuSC6eceAHXkL1/4dLqb+ETE3E5E7Q1lDM=' ";
        $csp .= "https://cdn.jsdelivr.net ";
        $csp .= "https://checkout.razorpay.com ";
        $csp .= "https://fonts.googleapis.com; ";
        
        $csp .= "font-src 'self' https://fonts.gstatic.com; ";
        
        $csp .= "img-src 'self' * data: ";
        $csp .= "https://lh5.googleusercontent.com ";
        $csp .= "https://lh3.googleusercontent.com ";
        $csp .= "https://lh4.googleusercontent.com ";
        $csp .= "https://i.ytimg.com ";
        $csp .= "https://www.google.co.in ";
        $csp .= "https://res.cloudinary.com ";
        $csp .=  rtrim(WP_HOME, '/')." ";
        $csp .= "https://www.googletagmanager.com; ";
        
        $csp .= "connect-src 'self' ";
        $csp .= "https://www.googleapis.com ";
        $csp .= "https://global.upload.vimeo.com ";
        $csp .= "https://ai-laravel-yxdza.kinsta.app ";
        $csp .= "https://vcdev.yunolearning.com ";
        $csp .= "https://vcstage.yunolearning.com ";
        $csp .= "https://pagesense-collect.zoho.in ";
        $csp .= "https://maps.googleapis.com ";
        $csp .= "https://www.google.co.in ";
        $csp .= "https://www.google.com ";
        $csp .= "https://analytics.google.com ";
        $csp .= "https://player.vimeo.com ";
        $csp .= "https://asia-files.tus.vimeo.com ";
        $csp .= "https://www.facebook.com ";
        $csp .= "https://api.vimeo.com ";
        $csp .= "https://api.cloudinary.com ";
        $csp .= "https://stats.g.doubleclick.net ";
        $csp .= "https://lumberjack.razorpay.com ";
        $csp .=  rtrim(WP_HOME, '/')." ";
        $csp .= "https://api.razorpay.com; ";
        
        // Added Razorpay to frame-src
        $csp .= "frame-src * ";
        $csp .= "https://cdn-in.pagesense.io ";
        $csp .= "https://player.vimeo.com ";
        $csp .= "https://www.googletagmanager.com ";
        $csp .= "https://api.razorpay.com ";
        $csp .= "https://www.youtube.com ";
        $csp .= "https://td.doubleclick.net ";
        $csp .= "https://www.youtube-nocookie.com; ";

        $csp .= "frame-ancestors 'self' ";
        $csp .= "https://dev.yunolearning.com ";
        $csp .= "https://coeuslearningsolutions.yunolearning.com ";
        $csp .= "https://prasar-dev.yunolearning.com ";
        $csp .= "https://prasar.yunolearning.com ";
        $csp .= "https://shikshakdwar.yunolearning.com ";
        $csp .= "https://ace-your-test.yunolearning.com ";
        $csp .= "https://ingress.yunolearning.com ";
        $csp .= "https://unigo-education.yunolearning.com ";
        $csp .= "https://upsquil-edutech.yunolearning.com ";
        $csp .= "https://professors-classes.yunolearning.com; ";
        
        $csp .= "object-src 'none'; ";
        $csp .= "base-uri 'self'; ";
        $csp .= "form-action 'self'; ";
        $csp .= "upgrade-insecure-requests;";
        $csp .= " media-src 'self' https://s3.ap-south-1.amazonaws.com;";

        return $csp;
    }
/**
 * Sends security-related HTTP headers to enhance the security of the web application.
 *
 * This function sets various HTTP headers to mitigate common web security vulnerabilities
 * and enforce security policies. The headers include:
 * - X-Frame-Options: Prevents clickjacking by restricting framing to the same origin.
 * - X-Content-Type-Options: Prevents MIME type sniffing by ensuring the content type is followed.
 * - Strict-Transport-Security: Enforces HTTPS by setting a max-age for the HSTS policy.
 * - Referrer-Policy: Controls the amount of referrer information sent with requests.
 * - X-XSS-Protection: Provides XSS protection for older browsers.
 * - Permissions-Policy: Restricts browser features like geolocation and microphone access.
 * - Content-Security-Policy: Defines a security policy for content sources, inline scripts,
 *   and other elements to prevent cross-site scripting (XSS) and data injection attacks.
 */

    function fun_send_headers()
    {
        //if (strpos($_SERVER['REQUEST_URI'], '/assets/') !== false) {
        header_remove('Access-Control-Allow-Origin');
        header('Access-Control-Allow-Origin: '.WP_HOME);
        //}
        // Prevent Clickjacking
        //header('X-Frame-Options: SAMEORIGIN');

        // Prevent MIME Sniffing
        header('X-Content-Type-Options: nosniff');

        // Enforce HTTPS
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

        // Referrer Policy
        header('Referrer-Policy: no-referrer-when-downgrade');

        // XSS Protection (for older browsers)
        header('X-XSS-Protection: 1; mode=block');

        // Restrict Browser Features (formerly Feature-Policy)
        header('Permissions-Policy: geolocation=(), microphone=()');

        // Content Security Policy (CSP) - Customize as needed
        header("Content-Security-Policy: " . $this->getCSPHeader());
    }
}
?>