<?php

namespace V4;
use Google\Client as Google_Client;

/**
 * Class model
 */

class ClassModel extends Model
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch','es');
        $this->loadLibary('schema');
        $this->loadLibary('locale');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('utility');
        $this->loadModel('user');
    }

    public function getClassTitle($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            $classDataResponse = $this->es->read('privateclass', 'privateclass-' . $query['id']);
        } else {
            return false;
        }
        
        if ($classDataResponse['status_code'] == 200) {
            $body = $classDataResponse['body']['_source']['data']['details'];

            $classResponse = [
                "id" => $body['record_id'],
                "title" => $body['title'],
                "short_description" => $body['short_description'],
                "long_description" => $body['long_description']
            ];

            return  $this->schema->validate($classResponse, 'Class_Title', $filter);
        }
        return false;
    }

    /**
    * Retrieves the attendance details for a learner in a specific class.
    * @since 1.0.0
    * @access public
    * @param array $query Array containing 'class_id' and 'learner_id' for attendance lookup.
    * @return array|bool Returns an array with attendance details on success or false if required data is missing or an error occurs.
    * <AUTHOR>
    */


    public function getClassLearnerAttendanceDetails($query)
    {
        if (empty($query['class_id']) || empty($query['learner_id'])) {
            return false; 
        }

        $attendanceQuery = [
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'term' => [
                                'data.details.class_id' => $query['class_id']
                            ]
                        ],
                        [
                            'term' => [
                                'data.details.user_id' => $query['learner_id']
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $attendanceResponse = $this->es->customQuery($attendanceQuery, 'classattendevent');
        if ($attendanceResponse['status_code'] == 200) {
            $attendanceData = $attendanceResponse['body']['hits']['hits'][0]['_source']['data']['details'] ?? null;
        } else {
            return false;
        }

        $learnerDetails = $this->load->subData("user","getUser",$query['learner_id'],['schema' => 'User_Minimal', 'noResponse' => 'User_Minimal']);
        if (!$learnerDetails) {
            return false; 
        }

        $classDurationSeconds = (int) ($attendanceData['class_duration'] ?? 0);
        $userDuration = (int) ($attendanceData['total_time_taken_by_user'] ?? 0);
        $userDurationSeconds = $userDuration * 60;

        if ($classDurationSeconds > 0) {
            $durationPercentage = round(($userDurationSeconds / $classDurationSeconds) * 100, 2);
        } else {
            $durationPercentage = 0.0;
        }

        return [
            'learner' => $learnerDetails,
            'duration' => $userDuration,
            'duration_percentage' => $durationPercentage,
            'class_review' => [
                'id' => 0,
                'source' => [
                    'source' => '',
                    'name' => ''
                ]
            ]
        ];
    }


    /**
    * Retrieves upcoming classes for an instructor based on the provided query parameters.
    * @since 1.0.0
    * @access public
    * @param array $query Array containing query parameters including custom Elasticsearch query and query string parameters.
    * @param array $filter Optional filter settings to be applied during schema validation.
    * @return array|bool Returns a validated array of class details on success or false if the query fails or no classes are found.
    * <AUTHOR>
    */

    public function getUpcomingClassesInstructor($query, $filter = [])
    {
        $this->loadModel('course');
        $this->loadModel('learner');
        $this->loadModel('VirtualClassroom');

        if ($query['custom']) {

            $classCountResponse = $this->es->count('privateclass', $query['custom']);
            if ($classCountResponse['status_code'] == 200) {
                $classDataResponse = $this->es->customQuery($query['custom'], 'privateclass', $query['qryStr']);

                if ($classDataResponse['status_code'] == 200) {
                    $classes = $classDataResponse['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }

        $schemaClasses = [
            'Refer#Instructor_Live_Upcoming_Class_Basic'
        ];

        if (count($classes)) {
            $classCount = $classCountResponse['body']['count'];
            foreach ($classes as $class) {
                $details = $class['_source']['data']['details'];

                $classId = $details['record_id'] ?? null;
                $classEndTime = $details['class_end_date_time'] ?? null;
                $classStartTime = $details['class_date_time'] ?? null;

                $learners = $this->learnerModel->getLearnersOfClass($classId);
                $learnerDetails = [];
                
                if (!empty($learners)) {
                    foreach ($learners as $learner) {
                        $learnerData = $this->load->subData("learner", "getLearner", $learner['user_id'], ['key' => 'user', 'noResponse' => 'User_Minimal']);
            
                        if ($learnerData) {
                            $learnerDetails[] = $learnerData;
                        } 
                    }
                }

                $currDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");

                $currDateTimeTimestamp = strtotime($currDateTime); 
                $classEndTimeTimestamp = strtotime($classEndTime); 
                $classStartTimeTimestamp = strtotime($classStartTime);

                if ($currDateTimeTimestamp < $classStartTimeTimestamp) {
                    $temporalStatus = "upcoming"; 
                } elseif (
                    $currDateTimeTimestamp >= $classStartTimeTimestamp &&
                    $currDateTimeTimestamp <= $classEndTimeTimestamp
                ) {
                    $temporalStatus = "live"; 
                } else {
                    $temporalStatus = "past"; 
                }
                
                $academyId = $this->courseModel->getAcademyId($details['course_id']);
                $response[] = [
                    'id' => $classId,
                    'type' => ($details['event_type'] === 'privateclass') ? 'PRIVATE' : strtoupper($details['event_type']),
                    'temporal_status' => $temporalStatus,
                    'private_url' => $details['url'] ?? '',
                    'class_title' => $this->getClassTitle($classId, ['schema' => 'Class_Title']),
                    'scheduled' => [
                        'start' => [
                            'time' => $this->dt->convertToSystemDT($details['class_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => $this->dt->convertToSystemDT($details['class_end_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'duration' => intval($details['duration'] ?? 0)
                    ],
                    'actual' => [
                        'start' => [
                            'time' => $details['class_actual_date_time'] ? $this->dt->convertToSystemDT($details['class_actual_date_time'], "Y-m-d\TH:i:s\Z") : '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => $details['class_actual_end_date_time'] ? $this->dt->convertToSystemDT($details['class_actual_end_date_time'], "Y-m-d\TH:i:s\Z") : '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'duration' => isset($details['class_actual_date_time'], $details['class_actual_end_date_time']) ? round((strtotime($details['class_actual_end_date_time']) - strtotime($details['class_actual_date_time'])) / 60) : 0
                    ],
                    'instructor' => $this->userModel->getUser($details['class_instructors'], ['schema' => 'User_Minimal']),
                    'batch' => !empty($details['batch_id']) && $details['batch_id'] > 0
                        ? $this->load->subData("batch", "getBatch", $details['batch_id'],['schema' => 'Batch_Minimal','noResponse' => ['id' => 0, 'title' => '', 'temporal_state' => '']])
                        : ['id' => 0, 'title' => '', 'temporal_state' => ''],
                    'course' => $this->load->subData("course","getCourse",$details['course_id'],['schema' => 'Course_Minimal', 'noResponse' => ['id' => 0, 'title' => '', 'url' => '']]),
                    'academy' => $this->load->subData("academy","getAcademy",$academyId,['schema' => 'Academy_Basic','noResponse' => ['id' => 0, 'name' => '', 'logo_url' => ['url' => '', 'alt_text' => ''], 'fav_icon_url' => ['url' => '', 'alt_text' => '']]]),
                    'enrollments' => !empty($learnerDetails) ? $learnerDetails : [ $this->load->subData("learner", "getLearner", 0, ['key' => 'user', 'noResponse' => 'User_Minimal'])],
                    'guest_url' => $details['guest_url'] ?? '',
                    'virtual_classroom' => [
                        'meeting_app' => $this->load->subData("virtualClassroom", "getVirtualClassroom", $details['class_instructors'], ['noResponse' => 'Virtual_Classroom_Minimal']),
                        'meeting_id' => isset($details['meeting_id']) ? (string)$details['meeting_id'] : '',
                        'meeting_url' => $details['zoom_url'] ?? $details['meet_url'] ?? ''
                    ]
                ];
            }

            if(isset($filter['schema'])){
                $filter['schema'] = ['count' => 'integer', 'data' => $filter['schema']];
            }
            return $this->schema->validate(['count' => $classCount, 'data' => $response], ['count' => 'integer', 'data' => $schemaClasses], $filter);
        }

        return false;
    }

    /**
    * Retrieves past classes for an instructor based on the provided query parameters.
    * @since 1.0.0
    * @access public
    * @param array $query Array containing custom Elasticsearch query parameters and query string parameters.
    * @param array $filter Optional filter settings used during schema validation.
    * @return array|bool Returns a validated array of past class details on success, or false if the query fails or no classes are found.
    * <AUTHOR>
    */

    public function getPastClassesInstructor($query, $filter = [])
    {
        $this->loadModel('course');
        $this->loadModel('learner');

        if ($query['custom']) {

            $classCountResponse = $this->es->count('privateclass', $query['custom']);
            if ($classCountResponse['status_code'] == 200) {
                $classDataResponse = $this->es->customQuery($query['custom'], 'privateclass', $query['qryStr']);

                if ($classDataResponse['status_code'] == 200) {
                    $classes = $classDataResponse['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }

        } else {
            return false;
        }

        $schemaClasses = [
            'Refer#Instructor_Past_Class_Basic'
        ];
        
        if (count($classes)) {
            $classCount = $classCountResponse['body']['count'];
            foreach ($classes as $class) {
                $details = $class['_source']['data']['details'];
                $classId = $details['record_id'] ?? null;
                $classEndTime = $details['class_end_date_time'] ?? null;

                $learners = $this->learnerModel->getLearnersOfClass($classId);
                $learnerDetails = [];
                $learnerDetailsMinimal = [];
                $learnerAttDetails = [];
                if (!empty($learners)) {
                    foreach ($learners as $learner) {
                        $learnerData = $this->load->subData("learner", "getLearner", $learner['user_id'], ['key' => 'user', 'noResponse' => 'User_Minimal']);
                        $learnerDataMinimal = $this->load->subData("user", "getUser", ['id' => $learner['user_id']], ['schema' => 'User_Minimal']);
                        $learnersAttendanceDetail = $this->getClassLearnerAttendanceDetails(
                            ['class_id' => $classId, 'learner_id' => $learner['user_id']]
                        );
            
                        if ($learnerData) {
                            $learnerDetails[] = $learnerData;
                        } 
            
                        if ($learnerDataMinimal) {
                            $learnerDetailsMinimal[] = $learnerDataMinimal;
                        }
            
                        if ($learnersAttendanceDetail) {
                            $learnerAttDetails[] = $learnersAttendanceDetail;
                        }
                    }
                }
                
                $currDateTime = $this->dt->currentActiveDT();
                $timezoneName = $this->locale->activeTimezone();

                $currDateTimeActive = $this->dt->convertToActiveDT($currDateTime, 'Y-m-d H:i:s');
                $classEndTimeActive = $this->dt->convertToActiveDT($classEndTime, 'Y-m-d H:i:s');

                $currDateTimeTimestamp = strtotime($currDateTimeActive); 
                $classEndTimeTimestamp = strtotime($classEndTimeActive); 

                if ($currDateTimeTimestamp < $classEndTimeTimestamp) {
                    $temporalStatus = "upcoming";
                } else if ($currDateTimeTimestamp > $classEndTimeTimestamp) {
                    $temporalStatus = "past";
                } else {
                    $temporalStatus = "ongoing";
                }
                $academyId = $this->courseModel->getAcademyId($details['course_id']);
                // echo '<pre>';
                // print_r($details);
                // echo '</pre>';
                // die; 
                $response[] = [
                    'id' => $classId,
                    'type' => ($details['event_type'] === 'privateclass') ? 'PRIVATE' : strtoupper($details['event_type']),
                    'temporal_status' => $temporalStatus,
                    'private_url' => $details['url'] ?? '',
                    'class_title' => $this->getClassTitle($classId, ['schema' => 'Class_Title']),
                    'scheduled' => [
                        'start' => [
                            'time' => $this->dt->convertToSystemDT($details['class_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => $this->dt->convertToSystemDT($details['class_end_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'duration' => intval($details['duration'] ?? 0)
                    ],
                    'actual' => [
                        'start' => [
                            'time' => $details['class_actual_date_time'] ? $this->dt->convertToSystemDT($details['class_actual_date_time'], "Y-m-d\TH:i:s\Z") : '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => $details['class_actual_end_date_time'] ? $this->dt->convertToSystemDT($details['class_actual_end_date_time'], "Y-m-d\TH:i:s\Z") : '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'duration' => isset($details['class_actual_date_time'], $details['class_actual_end_date_time']) ? round((strtotime($details['class_actual_end_date_time']) - strtotime($details['class_actual_date_time'])) / 60) : 0
                    ],
                    'instructor' => $this->userModel->getUser($details['class_instructors'], ['schema' => 'User_Minimal']),
                    'batch' => !empty($details['batch_id']) && $details['batch_id'] > 0 
                        ? $this->load->subData("batch", "getBatch", $details['batch_id'],['schema' => 'Batch_Minimal','noResponse' => ['id' => 0, 'title' => '', 'temporal_state' => '']])
                        : ['id' => 0, 'title' => '', 'temporal_state' => ''],
                    'course' => $this->load->subData("course","getCourse",$details['course_id'],['schema' => 'Course_Minimal', 'noResponse' => ['id' => 0, 'title' => '', 'url' => '']]),
                    'academy' => $this->load->subData("academy","getAcademy",$academyId,['schema' => 'Academy_Basic','noResponse' => ['id' => 0, 'name' => '', 'logo_url' => ['url' => '', 'alt_text' => ''], 'fav_icon_url' => ['url' => '', 'alt_text' => '']]]),
                    'enrollments' => !empty($learnerDetails) ? $learnerDetails : [ $this->load->subData("learner", "getLearner", 0, ['key' => 'user', 'noResponse' => 'User_Minimal'])],
                    'aggregate_rating' => [
                        'rating' => 0.0, 
                        'max_rating' => 0.0 
                    ],
                    'in_person' => [
                        'place' =>  $this->load->subData("place", "getPlace", $details['place_id'], [
                            'noResponse' => 'Place_Minimal'
                        ]),
                        'classroom' =>  $this->load->subData("classroom", "getClassroom", $details['classroom_id'], [
                            'noResponse' => 'Classroom_Minimal'
                        ])
                    ],
                    'recording' => [
                        'id' => (string) ($details['_recording_url_id'] ?? '0'),
                        'title' => '', 
                        'url' => $details['_recording_url'] ?? '',
                        'thumbnail' => [[
                            'url' => '',
                            'alt_text' => ''
                        ]],
                    ]
                ];
            }
            if(isset($filter['schema'])){
                $filter['schema'] = ['count' => 'integer', 'data' => $filter['schema']];
            }
            return $this->schema->validate(['count' => $classCount, 'data' => $response], ['count' => 'integer', 'data' => $schemaClasses], $filter);
        }
        return false;
    }

    /**
    * Retrieves upcoming classes for a learner based on the provided query parameters.
    * @since 1.0.0
    * @access public
    * @param array $query Array containing custom Elasticsearch query parameters and query string parameters.
    * @param array $filter Optional filter settings for schema validation.
    * @return array|bool Returns a validated array of class details on success, or false if the query fails or no classes are found.
    * <AUTHOR>
    */

    public function getUpcomingClassesLearners($query, $filter = [])
    {
        $this->loadModel('course');
        $this->loadModel('learner');

        $learner = $query['learner_id'];
        if ($query['custom']) {

            $classCountResponse = $this->es->count('privateclass', $query['custom']);
            if ($classCountResponse['status_code'] == 200) {
                $classDataResponse = $this->es->customQuery($query['custom'], 'privateclass', $query['qryStr']);

                if ($classDataResponse['status_code'] == 200) {
                    $classes = $classDataResponse['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }


        } else {
            return false;
        }

        $schemaClasses = [
            'Refer#Learner_Live_Upcoming_Class_Basic'
        ];

        if (count($classes)) {
            $classCount = $classCountResponse['body']['count'];
            foreach ($classes as $class) {
                $details = $class['_source']['data']['details'];

                $classId = $details['record_id'] ?? null;
                $classEndTime = $details['class_end_date_time'] ?? null;

                $currDateTime = $this->dt->currentActiveDT();

                $currDateTimeTimestamp = strtotime($currDateTime); 
                $classEndTimeTimestamp = strtotime($classEndTime); 

                if ($currDateTimeTimestamp < $classEndTimeTimestamp) {
                    $temporalStatus = "upcoming";
                } else if ($currDateTimeTimestamp > $classEndTimeTimestamp) {
                    $temporalStatus = "past";
                } else {
                    $temporalStatus = "ongoing";
                }
                
                $academyId = $this->courseModel->getAcademyId($details['course_id']);
                $response[] = [
                    'id' => (int)$classId,
                    'type' => ($details['event_type'] === 'privateclass') ? 'PRIVATE' : strtoupper($details['event_type']),
                    'temporal_status' => $temporalStatus,
                    'class_title' => $this->getClassTitle($classId, ['schema' => 'Class_Title']),
                    'scheduled' => [
                        'start' => [
                            'time' => $this->dt->convertToSystemDT($details['class_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => $this->dt->convertToSystemDT($details['class_end_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'duration' => intval($details['duration'] ?? 0)
                    ],
                    'instructor' => $this->userModel->getUser($details['class_instructors'], ['schema' => 'User_Minimal']),
                    'batch' => !empty($details['batch_id']) && $details['batch_id'] > 0 
                        ? $this->load->subData("batch", "getBatch", $details['batch_id'],['schema' => 'Batch_Minimal','noResponse' => ['id' => 0, 'title' => '', 'temporal_state' => '']])
                        : ['id' => 0, 'title' => '', 'temporal_state' => ''],
                    'course' => $this->load->subData("course","getCourse",$details['course_id'],['schema' => 'Course_Minimal', 'noResponse' => ['id' => 0, 'title' => '', 'url' => '']]),
                    'academy' => $this->load->subData("academy","getAcademy",$academyId,['schema' => 'Academy_Basic','noResponse' => ['id' => 0, 'name' => '', 'logo_url' => ['url' => '', 'alt_text' => ''], 'fav_icon_url' => ['url' => '', 'alt_text' => '']]]),

                    'private_url' => $details['url'] ?? '',
                    'guest_url' => $details['guest_url'] ?? '',
                    'virtual_classroom' => [
                        'meeting_app' => 
                        $this->load->subData("virtualClassroom", "getVirtualClassroom", $details['class_instructors'], ['noResponse' => 'Virtual_Classroom']),
                        
                        'meeting_id' => isset($details['meeting_id']) ? (string)$details['meeting_id'] : '',

                        'meeting_url' => $details['zoom_url'] ?? ''
                    ],
                    'in_person' => [
                        'place' =>  $this->load->subData("place", "getPlace", $details['place_id'], [
                            'noResponse' => 'Place_Minimal'
                        ]),
                        'classroom' =>  $this->load->subData("classroom", "getClassroom", $details['classroom_id'], [
                            'noResponse' => 'Classroom_Minimal'
                        ])
                    ]
                ];
            }
            if(isset($filter['schema'])){
                $filter['schema'] = ['count' => 'integer', 'data' => $filter['schema']];
            }
            return $this->schema->validate(['count' => $classCount, 'data' => $response], ['count' => 'integer', 'data' => $schemaClasses], $filter);
            
        }
        return false;
    }

    /**
    * Retrieves past classes for a learner based on provided query parameters.
    * @since 1.0.0
    * @access public
    * @param array $query Array containing custom Elasticsearch query parameters and query string parameters.
    * @param array $filter Optional filter settings for schema validation.
    * @param mixed $learner Learner identifier used to fetch attendance details.
    * @return array|bool Returns a validated array of past class details on success, or false if the query fails or no classes are found.
    * <AUTHOR>
    */

    public function getPastClassesLearners($query, $filter = [])
    {
        $this->loadModel('course');
        $this->loadModel('learner');
        
        $learner = $query['learner_id'];
        if ($query['custom']) {
           
            $enrollmentCountResponse = $this->es->count('privateclass', $query['custom']);
            if ($enrollmentCountResponse['status_code'] == 200) {
                $enrollmentDataResponse = $this->es->customQuery($query['custom'], 'privateclass', $query['qryStr']);

                if ($enrollmentDataResponse['status_code'] == 200) {
                    $classes = $enrollmentDataResponse['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }


        } else {
            return false;
        }

        $schemaClasses = [
            'Refer#Learner_Past_Class_Basic'
        ];
        
        if (count($classes)) {
            $classCount = $enrollmentCountResponse['body']['count'];
            foreach ($classes as $class) {
                $details = $class['_source']['data']['details'];
                $classId = $details['record_id'] ?? null;
                $classEndTime = $details['class_end_date_time'] ?? null;

                $learnersAttendanceDetail = $this->getClassLearnerAttendanceDetails(
                    ['class_id' => $classId, 'learner_id' => $learner]);
                
                $currDateTime = $this->dt->currentActiveDT();
                $timezoneName = $this->locale->activeTimezone();

                $currDateTimeActive = $this->dt->convertToActiveDT($currDateTime, 'Y-m-d H:i:s');
                $classEndTimeActive = $this->dt->convertToActiveDT($classEndTime, 'Y-m-d H:i:s');

                $currDateTimeTimestamp = strtotime($currDateTimeActive); 
                $classEndTimeTimestamp = strtotime($classEndTimeActive); 

                if ($currDateTimeTimestamp < $classEndTimeTimestamp) {
                    $temporalStatus = "upcoming";
                } else if ($currDateTimeTimestamp > $classEndTimeTimestamp) {
                    $temporalStatus = "past";
                } else {
                    $temporalStatus = "ongoing";
                }

                $recordingUrlId = $details['_recording_url_id'] ?? '';
                if (is_array($recordingUrlId)) {
                    $recordingId = isset($recordingUrlId[0]) ? (string)$recordingUrlId[0] : '';
                } else {
                    $recordingId = (string)$recordingUrlId;
                }

                $academyId = $this->courseModel->getAcademyId($details['course_id']);
                $response[] = [
                    'id' => (int)$classId,
                    'type' => ($details['event_type'] === 'privateclass') ? 'PRIVATE' : strtoupper($details['event_type']),
                    'temporal_status' => $temporalStatus,
                    'private_url' => $details['url'] ?? '',
                    'class_title' => $this->getClassTitle($classId, ['schema' => 'Class_Title']),
                    'scheduled' => [
                        'start' => [
                            'time' => $this->dt->convertToSystemDT($details['class_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => $this->dt->convertToSystemDT($details['class_end_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'duration' => intval($details['duration'] ?? 0)
                    ],
                    'actual' => [
                        'start' => [
                            'time' => isset($details['class_actual_date_time']) 
                            ? $this->dt->convertToSystemDT($details['class_actual_date_time'], "Y-m-d\TH:i:s\Z") 
                            : '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'end' => [
                            'time' => isset($details['class_actual_end_date_time']) 
                            ? $this->dt->convertToSystemDT($details['class_actual_end_date_time'], "Y-m-d\TH:i:s\Z") 
                            : '',
                            'timezone' => $this->locale->activeTimezone()
                        ],
                        'duration' => isset($details['class_actual_date_time'], $details['class_actual_end_date_time']) ? round((strtotime($details['class_actual_end_date_time']) - strtotime($details['class_actual_date_time'])) / 60) : 0
                    ],
                    'attendance' => [
                        'duration' => $learnersAttendanceDetail['duration'],
                        'duration_percentage' => $learnersAttendanceDetail['duration_percentage'],
                        'status' => ($learnersAttendanceDetail['duration'] > 0) ? true : false,
                    ],
                    'instructor' => $this->userModel->getUser($details['class_instructors'], ['schema' => 'User_Minimal']),

                    'batch' => !empty($details['batch_id']) && $details['batch_id'] > 0 
                        ? $this->load->subData("batch", "getBatch", $details['batch_id'],['schema' => 'Batch_Minimal','noResponse' => ['id' => 0, 'title' => '', 'temporal_state' => '']])
                        : ['id' => 0, 'title' => '', 'temporal_state' => ''],
                    'course' => $this->load->subData("course","getCourse",$details['course_id'],['schema' => 'Course_Minimal', 'noResponse' => ['id' => 0, 'title' => '', 'url' => '']]),
                    'academy' => $this->load->subData("academy","getAcademy",$academyId,['schema' => 'Academy_Basic','noResponse' => ['id' => 0, 'name' => '', 'logo_url' => ['url' => '', 'alt_text' => ''], 'fav_icon_url' => ['url' => '', 'alt_text' => '']]]),
                    'overall_rating' => 0,
                    'in_person' => [
                        'place' =>  $this->load->subData("place", "getPlace", $details['place_id'], [
                            'noResponse' => 'Place_Minimal'
                        ]),
                        'classroom' =>  $this->load->subData("classroom", "getClassroom", $details['classroom_id'], [
                            'noResponse' => 'Classroom_Minimal'
                        ])
                    ],
                    'recording' => [
                        'id' => $recordingId,
                        'title' => '', 
                        'url' => $details['_recording_url'] ?? '',
                        'thumbnail' => [[
                            'url' => '',
                            'alt_text' => ''
                        ]],
                    ]
                ];
            }
            if(isset($filter['schema'])){
                $filter['schema'] = ['count' => 'integer', 'data' => $filter['schema']];
            }
            return $this->schema->validate(['count' => $classCount, 'data' => $response], ['count' => 'integer', 'data' => $schemaClasses], $filter);
        }
        return false;
    }

    /**
    * Adds/updates a custom post for a class event.
    * @since 1.0.0
    * @access public
    * @param array $data Array containing post details such as title, content, status, event dates, and time components.
    * @return int|WP_Error Returns the post ID on success, or a WP_Error object on failure.
    * <AUTHOR>
    */

    public function addCustomPostClass(array $data)
    {
        $defaultArgs = [
            'post_title'   => $data['post_title'] ?? '',
            'post_content' => $data['post_content'] ?? '',
            'post_status'  => $data['post_status'] ?? '',
            'post_type'    => 'tribe_events',
        ];

        $args = array_merge($defaultArgs, $data);

        $postID = isset($data['ID']) ? wp_update_post($args, true) : wp_insert_post($args, true);

        if (is_wp_error($postID)) {
            error_log("Failed to save class event: " . $postID->get_error_message());
            return $postID;
        }

        add_post_meta($postID, 'EventStartDate', $data['EventStartDate']);
        add_post_meta($postID, 'EventEndDate', $data['EventEndDate']);
        add_post_meta($postID, 'EventStartHour', $data['EventStartHour']);
        add_post_meta($postID, 'EventStartMinute', $data['EventStartMinute']);
        add_post_meta($postID, 'EventStartMeridian', $data['EventStartMeridian']);
        add_post_meta($postID, 'EventEndHour', $data['EventEndHour']);
        add_post_meta($postID, 'EventEndMinute', $data['EventEndMinute']);
        add_post_meta($postID, 'EventEndMeridian', $data['EventEndMeridian']);
        add_post_meta($postID, '_EventStartDate', $data['EventStartDate'] . ":00");
        add_post_meta($postID, '_EventEndDate', $data['EventEndDate'] . ":00");
        add_post_meta($postID, '_EventDuration', $data['EventDuration'] * 60);

        return $postID;
    }

    /**
    * Updates metadata for a class event post.
    * @since 1.0.0
    * @access public
    * @param int The ID of the event post to update ($EventID).
    * @param array $metaData An associative array of metadata keys and values to update.
    * @return bool Returns true if all metadata updates succeed, otherwise false.
    * <AUTHOR>
    */

    public function updateClassPostMeta($EventID, array $metaData)
    {
        foreach ($metaData as $key => $value) {
            if (!update_post_meta($EventID, $key, $value)) {
                // error_log("Failed to update meta key: {$key} for EventID: {$EventID}");
                return false;
            }
        }
        return true;
    }

    /**
    * Inserts class data into the database.
    * @since 1.0.0
    * @access public
    * @param array An array containing class data to be inserted, including start_datetime, class_id, data, and event_type.
    * @return int|bool Returns the inserted row ID on success, or false on failure.
    * <AUTHOR>
    */

    public function insertClassPendingData($data)
    {
        global $wpdb;

        $insertData = [
            'start_datetime' => $data['start_datetime'],
            'class_id' => $data['class_id'],
            'data' => json_encode($data['data']),
            'event_type' => $data['event_type'],
        ];

        $result = $wpdb->insert("{$wpdb->prefix}class_pending_data", $insertData);

        if ($result === false) {
            error_log("Failed to insert into wp_class_pending_data: " . $wpdb->last_error);
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
    * Inserts a new WhatsApp notification record for a class.
    * @since 1.0.0
    * @access public
    * @param array Associative array containing user_id, class_id, class_title, class_start_datetime, class_url, status_one_hour, and status_ten_minutes.
    * @return int|bool Returns the inserted record ID on success, or false on failure.
    */

    public function insertWhatsappNotification($data)
    {
        global $wpdb;

        $insertData = [
            'user_id' => $data['user_id'],
            'class_id' => $data['class_id'],
            'class_title' => $data['class_title'],
            'class_start_datetime' => $data['class_start_datetime'],
            'class_url' => $data['class_url'],
            'status_one_hour' => $data['status_one_hour'] ?? 0,
            'status_ten_minutes' => $data['status_ten_minutes'] ?? 0,
        ];

        $result = $wpdb->insert("{$wpdb->prefix}whatsapp_notification", $insertData);

        if ($result === false) {
            error_log("Failed to insert into wp_whatsapp_notification: " . $wpdb->last_error);
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
    * Inserts Zoom recording details into the database.
    * @since 1.0.0
    * @access public
    * @param array Associative array containing recording details (id, webinarID, RecordingShareUrl, recordingVideoUrl, recordingAudioUrl, uuid, status, accountID, hostID).
    * @return int|bool Returns the inserted record ID on success, or false on failure.
    */

    public function insertZoomRecordings($data)
    {
        global $wpdb;

        $insertData = [
            'ID' => $data['id'],
            'WebinarID' => $data['webinarID'],
            'Recording_Share_Url' => $data['RecordingShareUrl'],
            'Recording_video_Url' => $data['recordingVideoUrl'],
            'Recording_audio_Url' => $data['recordingAudioUrl'],
            'Uuid' => $data['uuid'],
            'Status' => $data['status'],
            'accountID' => $data['accountID'],
            'HostID' => $data['hostID']
        ];

        $result = $wpdb->insert("{$wpdb->prefix}zoom_recordings", $insertData);

        if ($result === false) {
            error_log("Failed to insert into wp_zoom_recordings: " . $wpdb->last_error);
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
    * Inserts Zoom API data into the database.
    * @since 1.0.0
    * @access public
    * @param array Associative array containing p_id, zoom_id, zoom_url, start_url, parent_id, guest_url, and start_date.
    * @return int|bool Returns the inserted record ID on success, or false on failure.
    */

    public function insertYunoZoomApi($data)
    {
        global $wpdb;

        $insertData = [
            'p_id' => $data['p_id'],
            'zoom_id' => $data['zoom_id'],
            'zoom_url' => $data['zoom_url'],
            'start_url' => $data['start_url'],
            'parent_id' => $data['parent_id'],
            'guest_url' => $data['guest_url'],
            'start_date' => $data['start_date'],
        ];

        $result = $wpdb->insert("{$wpdb->prefix}yuno_zoomapi", $insertData);

        if ($result === false) {
            error_log("Failed to insert into wp_yuno_zoomapi: " . $wpdb->last_error);
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
     * Gets the formatted start date for a class
     * 
     * @param int|null $postId The ID of the post/event to get the date for
     * @param bool $displayTime Whether to include time in the output
     * @param string|null $dateFormat Custom date format string
     * @param string|null $timezone Custom timezone
     * @return string Formatted date string
     */
    public function getStartDate($postId = null, $displayTime = true, $dateFormat = null , $timezone = null)
    {
        $dateFormat = $dateFormat ?? '';

        if (empty($postId)) {
            $postId = get_the_ID();
        }
        if (empty($dateFormat)) {
            $dateFormat = "F j @ g:i a";
        }
        $event_start_date = get_post_meta($postId, '_EventStartDate', true);
        $originalDatetime = $this->dt->currentSystemDT();
        if (!empty($dateFormat)) {
            $formattedDatetime = $this->dt->convertToActiveDT($event_start_date, $dateFormat) ?? '';
        } else {
            $formattedDatetime = $originalDatetime;
        }
        return $formattedDatetime;
    }

    /**
     * Gets the formatted end date for a class/event
     * 
     * @param int|null $postId The ID of the post/event to get the end date for
     * @param bool $displayTime Whether to include time in the output (currently unused)
     * @param string|null $dateFormat Custom date format string
     * @param string|null $timezone Custom timezone (currently unused)
     * @return string|DateTime Formatted date string or DateTime object
     */
    public function getEndDate($postId = null, $displayTime = true, $dateFormat = null , $timezone = null)
    {
        $dateFormat = $dateFormat ?? '';
        if (empty($postId)) {
            $postId = get_the_ID();
        }
        if (empty($dateFormat)) {
            $dateFormat = "F j @ g:i a";
        }
        $event_end_date = get_post_meta($postId, '_EventEndDate', true);
        $originalDatetime = $this->dt->currentSystemDT();

        if (!empty($dateFormat)) {
            $formattedDatetime = $this->dt->convertToActiveDT($event_end_date, $dateFormat) ?? '';
        } else {
            $formattedDatetime = $originalDatetime;
        }
        return $formattedDatetime;
    }

    /**
    * Retrieves post meta data for a given post.
    * @since 1.0.0
    * @access public
    * @param int|null The ID of the post.
    * @param mixed $postKey Meta key or flag to retrieve specific data.
    * @return mixed Returns the post meta data if available, or an empty string if not.
    */

    public function getPostData($postId = null, $postKey = true)
    {
        if (empty($postId)) {
            return false;
        }
        $postData = get_post_meta($postId, $postKey, true);
        if (!empty($postData)) {
            $postData = $postData;
        } else {
            $postData = '';
        }
        return $postData;
    }

    /**
    * Retrieves user meta data.
    * @since 1.0.0
    * @access public
    * @param int|null The user ID.
    * @param mixed $key Meta key or flag to retrieve specific data.
    * @return mixed Returns the user meta data if available, or an empty string if not.
    */

    public function getUserData($id = null, $key = true)
    {

        if (empty($id)) {
            return false;
        }
        $userData = get_user_meta($id, $key, true);
        if (!empty($userData)) {
            $data = $userData;
        } else {
            $data = '';
        }
        return $data;
    }


    /**
    * Creates a Google Meet meeting for a class event.
    * @since 1.0.0
    * @access public
    * @param mixed $eventID The ID of the event.
    * @param string $userAccessToken The user's access token for Google Meet API.
    * @param string $timeZone The timezone for the meeting.
    * @param string $classTitle The title of the class.
    * @param string $classDescription The description of the class.
    * @param string $eventStartDate The start datetime of the event.
    * @param string $eventEndDate The end datetime of the event.
    * @param array $classLearnerEmails Array of learner email addresses.
    * @param mixed $batchId The batch ID associated with the event.
    * @param mixed $userID The user ID initiating the meeting.
    * @return array|bool Returns the decoded response on success, or false on failure.
    */

    public function createGoogleMeet($eventID, $classDuration, $userAccessToken, $timeZone, $classTitle, $classDescription, $eventStartDate, $eventEndDate, $classLearnerEmails, $batchId, $userID)
    {
        $apiUrl = GOOGLE_MEET_LARAVEL_URL . '/api/create-meeting';
        $user_email = get_user_meta($userID, 'yuno_gplus_email', true);
        $courseId = get_post_meta($eventID, 'class_course', true);
        $apiData = array(
            'accessToken'    => $userAccessToken,
            'summary'        => $classTitle,
            'description'    => $classDescription,
            'startDateTime'  => $eventStartDate,
            'endDateTime'    => $eventEndDate,
            'timeZone'       => $timeZone,
            'attendees'      => $classLearnerEmails,
            'class_id'       => $eventID,
            'batch_id'       => $batchId,
            'course_id'      => $courseId,
            'user_id'        => $userID,
            'duration'       => $classDuration,
            'user_email'     => $user_email
        );

        $headers = array(
            'Content-Type' => 'application/json',
        );

        $params = array(
            'method' => 'POST',
            'timeout' => 5,      
            'blocking' => true,  
        );

        $body = array(
            'body' => wp_json_encode($apiData),
        );

        $response = $this->utility->remotePost($apiUrl, $body, $headers, $params);

        if (is_wp_error($response)) {
            return false;
        } 

        $responseCode = wp_remote_retrieve_response_code($response);
        $responseBody = wp_remote_retrieve_body($response);

        if ($responseCode === 201) {
            return json_decode($responseBody, true);
        } else {
            return false;
        }
    }

    /**
     * Adds class data to Elasticsearch.
     *
     * Creates an ES document for a class event with a custom ID based on the event ID. If the document is
     * successfully created (HTTP status 201), the response is enriched with details such as class title,
     * scheduled start/end times, instructor information, and URLs for private and guest access, as well as
     * virtual classroom data.
     *
     * @since 1.0.0
     * @access public
     * @param string $index The Elasticsearch index name.
     * @param array $data The payload containing class data details.
     * @param int $eventID The unique identifier for the class event.
     * @param string $type The type of the class event.
     * @param mixed $virtualClasses Virtual classroom details.
     * @return array|false Returns the enriched Elasticsearch response on success, or false on failure.
     */

     public function addClassDataES($index, $data, $eventID, $type, $virtualClasses){
        $this->loadModel('instructor');
        $this->loadModel('VirtualClassroom');
        $esId = 'privateclass-'.$eventID;
        $esResponse = $this->es->create($index, $data, $esId);
        if ($esResponse['status_code'] == 201) {
            $classData = $this->getClass($esId);
            $esResponse['data'] = $classData;
            
            return $esResponse;
        }

        return false;
    }

    /**
    * Retrieves the Google Meet access token for a user.
    *
    * @since 1.0.0
    * @access public
    * @param int    $userId   The user ID.
    * @param string $timezone The timezone to set for token operations.
    * @param mixed  $orgId    Optional organization ID; if null, fetched from user meta.
    * @return string Returns the new access token on success, or the existing token if no update occurred.
    */

    public function getGoogleMeetAccessToken($userId, $timezone, $orgId = null) {
        try {
            $accessToken = "";
            
            $filteredVirtualClassroom = [];
    
            $metaKey = 'virtual_classroom_data'; 
            $data = get_user_meta($userId, $metaKey, true);
            if(empty($data)){
                return false;
            }
            if (count($data) > 0) {
                foreach ($data['data'] as $item) {     
                    if (isset($item['virtual_classroom']['meet'])) { 
                        $filteredVirtualClassroom = $item['virtual_classroom']['meet'];
                        if (isset($item['org_id'])) {
                            $orgId = $item['org_id'];
                        }
                        break; 
                    }
                } 
                $email = get_user_meta($userId, 'yuno_gplus_email', true);
                $name = get_user_meta($userId, 'yuno_display_name', true);
                if (is_null($orgId)) {
                    $orgId = (int)get_user_meta($userId, 'active_org', true) ?? 0; 
                }
                
                if ($email == $filteredVirtualClassroom['email']) {
                    $clientId = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_ID;
                    $clientSecret = AWS_COGNITO_IDENTITY_PROVIDER_CLIENT_SECRET;
                } else {
                    $clientId = YUNO_OAUTH_APP_CLIENT_ID;
                    $clientSecret = YUNO_OAUTH_APP_SECRET_KEY;     
                }
    
                $refreshToken = $filteredVirtualClassroom['refresh_token'];
                $expiresIn = $filteredVirtualClassroom['expires_in'];
                $accessToken = $filteredVirtualClassroom['access_token'];
    
                $client = new Google_Client();
                $client->setClientId($clientId);
                $client->setClientSecret($clientSecret);
                $client->setAccessType('offline');  
    
                $client->refreshToken($refreshToken);
                $newToken = $client->getAccessToken();
                
                if ($newToken) {
                    $orgAcademies = [];
                    $academies = get_post_meta($orgId, "academies", true);
                    if (is_array($academies)) {
                        $orgAcademies = $academies;
                    }
    
                    $meetEntry = [
                        'org_id' => $orgId,
                        'academies' => $orgAcademies,
                        'virtual_classroom' => [
                            'meet' => [
                                'access_token' => $newToken['access_token'],
                                'refresh_token' => $newToken['refresh_token'],
                                'id_token' => $newToken['id_token'],
                                'token_type' => $newToken['token_type'],
                                'expires_in' => time() + $newToken['expires_in'],
                                'email' => $filteredVirtualClassroom['email'],
                                'name' => $name,
                                'scope' => $newToken['scope']
                            ]
                        ]
                    ];
                    
                    $this->saveVirtualAuthAccess($userId, $meetEntry);
                    return $newToken['access_token'];
                }
            }
            return $accessToken;
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $userId],
                'request' => ['user_id' => $userId],
                'data' => []
            ];
            $this->log_error($logDetails);
            exit('An error occurred while saving the authentication token.');
        }
    } 

    /**
    * Saves the virtual classroom authentication access details for a user.
    *
    * @since 1.0.0
    * @access public
    * @param int   $userId   The user ID.
    * @param array $newEntry The new authentication details to save.
    * @return void
    */

    public function saveVirtualAuthAccess($userId,$newEntry) {
        try {
            $metaKey = 'virtual_classroom_data'; 
            $existingData = get_user_meta($userId, $metaKey, true);
            if (empty($existingData)) {
                $existingData = ['data' => []];
            }
            $entryExists = false;
            foreach ($existingData['data'] as $key => $entry) {
                if ($entry['org_id'] == $newEntry['org_id']) {
                    $existingData['data'][$key] = array_merge($existingData['data'][$key], $newEntry);
                    $entryExists = true;
                    break;
                }
            }
            foreach ($existingData['data'] as $key => $entry) {
                    $existingData['data'][$key] = $newEntry;
                    $entryExists = true;
                    break;
            }
            if (!$entryExists) {
                $existingData['data'][] = $newEntry;
            }
            update_user_meta($userId, $metaKey, $existingData);
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            $logDetails = [
                'logtype' => 'error',
                'module' => 'ES',
                'action' => 'Virtual - login | signup',
                'message' => $message,
                'user' => ['user_id' => $userId],
                'request' => ['org_id' => $newEntry['org_id']],
                'data' => []
            ];
            $this->log_error($logDetails);
            exit('An error occurred while saving the authentication token.');
        }
    }
    
    /**
    * Creates a Zoom meeting for a class event.
    *
    * @since 1.0.0
    * @access public
    * @param string $meetingTitle The title of the meeting.
    * @param string $classStartDate The start date and time of the class.
    * @param int    $classDuration The duration of the meeting in minutes.
    * @param string $timeZone The timezone for the meeting.
    * @param string $hostEmail The email of the meeting host.
    * @param int    $userID The user ID initiating the meeting.
    * @return array|bool Returns the Zoom API response on success, or false on failure.
    */

    public function createZoomMeeting($meetingTitle, $classStartDate, $classDuration, $timeZone, $hostEmail, $userID) {
        $zoomExpiresIn = $this->getUserData($userID, "zoom_expires_in");
        if ($zoomExpiresIn < time()) {
            $this->gettingZoomOauthAppTokenAfterExpiry($userID);
        }
    
        $zoomUserStatus = get_user_meta($userID, 'zoom_user_status', true);
        if ($zoomUserStatus == "free") {
            return false;
        } 
        
        $yunoZoomBearerToken = get_user_meta($userID, 'zoom_access_token', true);
        $url = "https://api.zoom.us/v2/users/$hostEmail/meetings";
        
        $headers = [
            'Authorization' => 'Bearer ' . $yunoZoomBearerToken,
            'Content-Type'  => 'application/json',
        ];
        
        $body = [
            'body' => wp_json_encode([
                'topic'      => $meetingTitle,
                'type'       => 2,
                'start_time' => $classStartDate,
                'duration'   => $classDuration,
                'timezone'   => $timeZone,
                'password'   => null,
                'agenda'     => $meetingTitle,
                'recurrence' => [
                    'type'             => 1,
                    'repeat_interval'  => 2,
                    'weekly_days'      => 3,
                    'monthly_day'      => 3,
                    'monthly_week'     => 2,
                    'monthly_week_day' => 1,
                    'end_times'        => 1,
                    'end_date_time'    => '',
                ],
                'settings'   => [
                    'host_video'                     => true,
                    'participant_video'              => true,
                    'cn_meeting'                     => false,
                    'in_meeting'                     => false,
                    'join_before_host'               => false,
                    'waiting_room'                   => false,
                    'mute_upon_entry'                => false,
                    'watermark'                      => false,
                    'use_pmi'                        => false,
                    'approval_type'                  => 0,
                    'registration_type'              => null,
                    'audio'                          => 'voip',
                    'auto_recording'                 => 'cloud',
                    'enforce_login'                  => false,
                    'enforce_login_domains'          => null,
                    'alternative_hosts'              => $hostEmail,
                    'registrants_email_notification' => false,
                    'registrants_confirmation_email' => false,
                ],
            ])
        ];
        
        $params = [
            'method'   => 'POST',
            'timeout'  => 5,
            'blocking' => true,
        ];
        
        $response = $this->utility->remotePost($url, $body, $headers, $params);
        
        if (is_wp_error($response)) {
            error_log("Zoom API Error: " . $response->get_error_message());
            return false;
        }
        
        $statusCode = wp_remote_retrieve_response_code($response);
        if ($statusCode != 201) { 
            error_log("Zoom API Response Error ({$statusCode}): " . wp_remote_retrieve_body($response));
            return false;
        }
        
        $responseBody = json_decode(wp_remote_retrieve_body($response), true);
        return $responseBody;
    }
    

    /**
    * Refreshes the Zoom OAuth token if it has expired.
    *
    * @since 1.0.0
    * @access public
    * @param int $userId The user ID.
    * @return string|bool Returns the new access token on success, or false on failure.
    */

    public function gettingZoomOauthAppTokenAfterExpiry($userId)
    {
        $current_epoch_time = time(); 
        $zoomExpiresIn = $this->getUserData($userId, "zoom_expires_in");
        if ($zoomExpiresIn < time()) {
            $refreshToken = $this->getUserData($userId, "zoom_refresh_token");
            $zoomurl = "https://zoom.us/oauth/token?grant_type=refresh_token&refresh_token=" . $refreshToken;

            $headers = [
                'Content-Type: application/x-www-form-urlencoded',
                'Authorization: Basic ' . ZOOM_OAUTH_APP_TOKEN
            ];
            $params = '';
            $response = $this->utility->curlRequest($zoomurl, 'POST',  $params, $headers);

            if (isset($response->error)) {
                return false;
            }
            if (empty($refreshToken)) {
                return false;
            }

            if (is_string($response['response'])) {
                $data = json_decode($response['response'], true);
            } else {
                $data = $response['response'];
            }
        
            $refreshToken = $data['refresh_token'];
            if (!empty($refreshToken)) {
                update_user_meta($userId, 'zoom_access_token', $data['access_token']);
                update_user_meta($userId, 'zoom_refresh_token', $refreshToken);
                update_user_meta($userId, 'zoom_expires_in', ($data['expires_in'] - 14) / 60 + $current_epoch_time);
                update_user_meta($userId, 'zoom_scope', $data['scope']);
            }
            $accessToken = $data['access_token'];
        } else {
            $accessToken = $this->getUserData($userId, "zoom_access_token");
        }

        return $accessToken;
    }

    /**
     * Processes pending tasks for a class event by enrolling learners and triggering notifications.
     *
     * This function checks if each learner in the provided list is enrolled in the class. If not, it manually enrolls 
     * them and updates the class's post meta with the enrolled learners. 
     * triggers a custom event for enrollment, and prepares notification payloads (for WhatsApp and push notifications).
     *
     * @since 1.0.0
     * @access public
     * @param array  $Classlearner           Array of learner IDs to enroll.
     * @param int    $eventID                The class event ID.
     * @param string $classTitle             The class title.
     * @param string $classExcerpt           The class excerpt.
     * @param string $eventStartDate         The event start date.
     * @param string $eventEndDate           The event end date.
     * @param array  $classLearnerEmails     Array of learner emails for invitation.
     * @param string $classCourseCategory    The course category for the class.
     * @param int    $userID                 The instructor/user ID.
     * @param string $ClassLink              The URL link to the class.
     * @param string $dayOfWeek              The day of the week when the class starts.
     * @param string $ClassStartDate         The class start date.
     * @param string $ClassStartTimeForWhatsApp The formatted start time for WhatsApp notifications.
     * @param string $instructorName         The instructor's name.
     * @param string $timeZone               The active timezone.
     * @return void
     */
    public function classPendingTasks($Classlearner, $eventID, $classTitle, $classExcerpt,$eventStartDate, $eventEndDate, $classLearnerEmails, $classCourseCategory, $userID, $ClassLink, $dayOfWeek, $ClassStartDate, $ClassStartTimeForWhatsApp, $instructorName, $timeZone)
    {
        if ($Classlearner != '') {
            $en_array = array();
            foreach ($Classlearner as $privateUserId) {
                $enrollOrNot = $this->userEnrollmentCheck($eventID, $privateUserId);
                if ($enrollOrNot == 0) {
                    $enrArray = $this->manuallyEnrollUserToClass($privateUserId, $eventID, $classTitle, $classExcerpt, $eventStartDate, $eventEndDate, $timeZone);
                }
            }
        }
        update_post_meta($eventID, 'YunoClassPrivateLearners', $Classlearner);
        if ($classLearnerEmails != '') {
            foreach ($classLearnerEmails as $inviteEmails) {
                if ($enrollOrNot == 0) {
                    $enrArray = $this->enrollInviteUserToClass($inviteEmails, $eventID, $classTitle, $classExcerpt, $eventStartDate, $eventEndDate, $classCourseCategory, $timeZone);
                }
            }
        }

        $ids = [];
        foreach ($Classlearner as $key => $value) {
            $ids[] = (string) $value;
        }

        $event_details = [
            'user_id'   => $userID,   
            'learners'  => $ids,
            'action'    => 'Enrolled in Class',    
            'target'    => 'Class',      
            'target_id' => $eventID,   
            'timestamp' => $this->dt->currentSystemDT()  
        ];

        /**
         * Logs custom events to a daily log file
         * 
         * - Takes event details as input (user_id, action, target, etc.)
         * - Checks if all required fields are present
         * - Creates a new log file for each day
         * - Saves event details in JSON format
         * - Uses file locking to prevent data corruption
         * - Cleans up old log files automatically
         */
        trigger_custom_event($event_details); 
        
        return;
    }

    /**
    * Checks whether a user is enrolled in a given class.
    *
    * @since 1.0.0
    * @access public
    * @param mixed $eventId The event or class ID.
    * @param int   $userId  The user ID.
    * @return string Returns "1" if enrolled, or "0" if not enrolled.
    */

    public function userEnrollmentCheck($eventId, $userId){
        try {
            $enrollmentQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            [
                                "match" => [
                                    "data.details.user_id" => $userId
                                ]
                            ],
                            [
                                "match" => [
                                    "data.details.id" => $eventId
                                ]
                            ],
                            [
                                "match" => [
                                    "data.details.event_type" => "enrolledclassevent"
                                ]
                            ]
                        ]
                    ]
                ],
                "size" => 1 
            ];
    
            $result = $this->es->customQuery($enrollmentQuery, 'enrolledclassevent');
    
            if (isset($result['body']['hits']['total']['value']) && 
                intval($result['body']['hits']['total']['value']) > 0 && 
                intval($userId) != 0) {
                return "1";
            }
            return "0";
        } catch (Exception $e) {
            return "0";
        }
    }

    /**
    * Enrolls a user to a class manually.
    *
    * @since 1.0.0
    * @access public
    * @param int    $userId       The user ID.
    * @param mixed  $classId      The class or event ID.
    * @param string $classTitle   The title of the class.
    * @param string $classExcerpt The excerpt or description of the class.
    * @param string $classStartDate The class start date and time.
    * @param string $classEndDate The class end date and time.
    * @param string $timeZone     The timezone to use.
    * @return string JSON encoded response with enrollment status and message.
    */

    public function manuallyEnrollUserToClass($userId, $classId, $classTitle, $classExcerpt, $classStartDate, $classEndDate, $timeZone) {
        global $wpdb;
    
        if (empty($classId)) {
            return json_encode(['code' => 500, 'msg' => 'Class_Url_Not_Valid']);
        }
        if (empty($userId)) {
            return json_encode(['code' => 500, 'msg' => 'Email_Not_Valid']);
        }
    
        $user = get_userdata($userId);
        if (!$user) {
            return json_encode(['code' => 500, 'msg' => 'User_Not_Found']);
        }
    
        $email = $user->user_email;
        $googleMeetId = get_post_meta($classId, 'InstructorScheduleClassMeetID', true);
        $meeting_id = $googleMeetId ?: get_post_meta($classId, 'InstructorScheduleClassZoomID', true);

        $googleMeetUrl = get_post_meta($classId, 'YunoClassGoogleMeetLink', true);
        $meeting_url = $googleMeetUrl ?: get_post_meta($classId, 'InstructorScheduleClassZoomURL', true);

        $currentDateTime = date('Y-m-d H:i:s');
    
        $userMeta = [
            'zoho_lead_id' => get_user_meta($userId, 'zoho_lead_id', true),
            'yuno_user_calendar_id' => get_user_meta($userId, 'yuno_user_calendar_id', true),
            'yuno_user_access_token' => get_user_meta($userId, 'yuno_user_access_token', true),
            'yuno_user_refresh_token' => get_user_meta($userId, 'yuno_user_refresh_token', true),
            'demo_class_count' => (int)get_user_meta($userId, 'demo_class_count', true),
            'Product_Type' => get_user_meta($userId, 'Product_Type', true),
            'current_user_type' => get_user_meta($userId, 'current_user_type', true),
            'zoho_contact_id' => get_user_meta($userId, 'zoho_contact_id', true),
        ];
    
        $isEnrolled = $wpdb->get_row($wpdb->prepare(
            "SELECT classId FROM wp_enrolled WHERE userId = %d AND classId = %d",
            $userId, $classId
        ), ARRAY_A);
    
        if ($isEnrolled) {
            return json_encode(['code' => 403, 'msg' => 'Already_Enrolled']);
        }
    
        $result = $wpdb->insert('wp_enrolled', [
            'classId' => $classId,
            'userId' => $userId,
            'email' => $email,
            'meeting_id' => $meeting_id,
            'meeting_link' => $meeting_url ?: '',
            'enrolledDate' => $currentDateTime,
            'attendance' => 0,
        ]);
    
        if (!$result) {
            return json_encode(['code' => 500, 'msg' => 'Enroll_Not_Successfully']);
        }
    
        $userType = strtolower($userMeta['Product_Type']) === 'free' ? 'Free' : (strtolower($userMeta['Product_Type']) === 'premium' ? 'Premium' : 'Trial');
        update_user_meta($userId, 'Product_Type', $userType);
    
        $templateId = (int)$wpdb->get_var("SELECT id FROM {$wpdb->prefix}email_templates WHERE email_type = 'SCHDCLASS'");
        
        $mailData = [
            'class_id'    => $classId,
            'user_id'     => $userId, 
            'class_title' => $classTitle,
            'template_id' => $templateId,
            'email' => $email
        ];
        $this->postClassEnrollmentEvent($mailData);

        $demoCount = $userMeta['demo_class_count'] ? $userMeta['demo_class_count'] + 1 : 1;
        update_user_meta($userId, 'demo_class_count', $demoCount);
    
        $moduleName = ($userMeta['current_user_type'] === 'instructor' || $userMeta['current_user_type'] === 'contact') ? 'Accounts' : 'Leads';
        $zohoId = ($userMeta['current_user_type'] === 'instructor' || $userMeta['current_user_type'] === 'contact') ? $userMeta['zoho_contact_id'] : $userMeta['zoho_lead_id'];
    
        return json_encode(['code' => 200, 'msg' => 'Enroll_SuccessFully']);
    }

    /**
    * Enrolls an invited user to a class.
    *
    * @since 1.0.0
    * @access public
    * @param string $email             The invitee's email address.
    * @param mixed  $classId           The class or event ID.
    * @param string $classTitle        Title of the class.
    * @param string $classExcerpt      Excerpt or description of the class.
    * @param string $classStartDate    Start date of the class.
    * @param string $classEndDate      End date of the class.
    * @param string $classCourseCategory Category of the class course.
    * @param string $timeZone          Timezone to set.
    * @return string JSON encoded response with enrollment status and message.
    */

    public function enrollInviteUserToClass($email, $classId, $classTitle, $classExcerpt, $classStartDate, $classEndDate, $classCourseCategory, $timeZone) {
        global $wpdb;
    
        $currentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
        $userId = ''; 
    
        $googleMeetId = get_post_meta($classId, 'InstructorScheduleClassMeetID', true);
        $meetingId = $googleMeetId ?: get_post_meta($classId, 'InstructorScheduleClassZoomID', true);
    
        $enrolled = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM wp_enrolled WHERE email = %s AND classId = %d",
            $email, $classId
        ));
    
        if ($enrolled > 0) {
            return json_encode(['code' => 403, 'msg' => 'Already_Enrolled']);
        }
    
        try {
            // Enroll the user
            $result = $wpdb->insert('wp_enrolled', [
                'classId' => $classId,
                'userId' => $userId,
                'email' => $email,
                'meeting_id' => $meetingId,
                'enrolledDate' => $currentDateTime
            ]);
    
            if ($result === false) {
                return false;
            }
    
            $emailTemplate = $wpdb->prefix . "email_templates";
            $templateId = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $emailTemplate WHERE email_type = 'SCHDCLASS'"
            ));
    
            $encodeValue = base64_encode($meetingId . '@@' . $email . '@@' . $classId . '@@' . $classCourseCategory);
            $siteURL = str_replace("http://", "https://", site_url());
    
            $mailData = [
                'class_id'    => $classId,
                'user_id'     => $userId, 
                'email'       => $email,
                'invite_url'  => $siteURL . "/invite/" . $encodeValue,
                'class_title' => $classTitle,
                'template_id' => $templateId,
            ];
            
            $this->postClassEnrollmentEvent($mailData);
    
            return json_encode(['code' => 200, 'msg' => 'Enroll_SuccessFully']);
    
        } catch (Exception $e) {
            return false;
        }
    }

    /**
    * Retrieves data from a database table.
    *
    * @since 1.0.0
    * @access public
    * @param string $tableName  The name of the table.
    * @param string $columns    The columns to select (comma-separated).
    * @param array  $conditions Optional associative array of conditions (column => value).
    * @return array Returns an array of results.
    */

    public function getDbData($tableName, $columns, $conditions = []) {
        global $wpdb;
    
        $query = "SELECT {$columns} FROM {$tableName}";
        if (!empty($conditions)) {
            $whereClauses = [];
            $queryParams = [];
    
            foreach ($conditions as $column => $value) {
                $whereClauses[] = "{$column} = %s";
                $queryParams[] = $value;
            }
    
            $query .= " WHERE " . implode(' AND ', $whereClauses);
            $preparedQuery = $wpdb->prepare($query, ...$queryParams);
        } else {
            $preparedQuery = $query;
        }
        return $wpdb->get_results($preparedQuery, ARRAY_A);
    }

    /**
     * Inserts a new learner-instructor relationship into the database
     * 
     * @param array $data An associative array containing:
     *                    - instructor_id: The ID of the instructor
     *                    - learner_id: The ID of the learner
     * 
     * @return int|false Returns the ID of the newly inserted record on success,
     *                   or false if the insertion fails
     */
    public function insertMyLearners($data) {
        global $wpdb;
    
        $insertData = [
            'instructor_id' => $data['instructor_id'],
            'learner_id'    => $data['learner_id'],
        ];
    
        $result = $wpdb->insert("{$wpdb->prefix}my_learners", $insertData);
    
        if ($result === false) {
            error_log("Failed to insert into wp_my_learners: " . $wpdb->last_error);
            return false;
        }
    
        return $wpdb->insert_id;
    }

    /**
     * Updates an existing Zoom meeting with new details.
     *
     * This function updates a Zoom meeting's details including title, start time, duration,
     * and other settings. It handles token refresh if needed and validates the instructor's
     * Zoom account status before making the update.
     *
     * @since 1.0.0
     * @access public
     * @param string $meetingTitle   The new title for the Zoom meeting
     * @param string $classStartDate The new start date and time for the meeting
     * @param int    $classDuration  The duration of the meeting in minutes
     * @param string $timezone       The timezone for the meeting
     * @param string $zoomClassId    The Zoom meeting ID to update
     * @return array|bool Returns the updated meeting details on success, or false on failure
     * <AUTHOR>
     */
    public function updateZoomMeeting($meetingTitle, $classStartDate, $classDuration, $timezone, $zoomClassId) {
        global $wpdb;
        $resultPost = $wpdb->get_row(
            "SELECT post_id FROM wp_postmeta WHERE meta_value = '" . esc_sql($zoomClassId) . "' AND meta_key='InstructorScheduleClassZoomID'"
        );
        if ( ! $resultPost || empty($resultPost->post_id) ) {
            return false;
        }
        $class_id    = $resultPost->post_id;
        $instructorId = get_post_meta($class_id, '_ecp_custom_13', true);
        $zoomExpiresIn = get_user_meta($instructorId, 'zoom_expires_in', true);
        if ($zoomExpiresIn < time()) {
            $this->gettingZoomOauthAppTokenAfterExpiry($instructorId);
        }
        $zoomUserStatus = get_user_meta($instructorId, 'zoom_user_status', true);
        if ($zoomUserStatus === "free") {
            return false;
        }
        $yunoZoomBearerToken = get_user_meta($instructorId, 'zoom_access_token', true);
        
        $url = "https://api.zoom.us/v2/meetings/$zoomClassId";
        
        $data = [
            'topic'      => $meetingTitle,
            'type'       => 2,
            'start_time' => $classStartDate,
            'duration'   => $classDuration,
            'timezone'   => $timezone,
            'agenda'     => $meetingTitle,
            'recurrence' => [
                'type'             => 1,
                'repeat_interval'  => 2,
                'weekly_days'      => 3,
                'monthly_day'      => 3,
                'monthly_week'     => 2,
                'monthly_week_day' => 1,
                'end_times'        => 1,
                'end_date_time'    => '', 
            ],
            'settings'   => [
                'host_video'           => true,
                'participant_video'    => true,
                'cn_meeting'           => false,
                'in_meeting'           => false,
                'join_before_host'     => false,
                'mute_upon_entry'      => false,
                'watermark'            => false,
                'use_pmi'              => false,
                'audio'                => 'voip',
                'enforce_login'        => false,
            ],
        ];
        
        $args = [
            'method'   => 'PATCH',
            'headers'  => [
                'Content-Type'  => 'application/json',
                'Authorization' => 'Bearer ' . $yunoZoomBearerToken,
            ],
            'body'     => wp_json_encode($data),
            'timeout'  => 5,
            'blocking' => true,
        ];
        $response = wp_remote_request($url, $args);
        if (is_wp_error($response)) {
            error_log("Zoom API Error: " . $response->get_error_message());
            return false;
        }
        
        $statusCode = wp_remote_retrieve_response_code($response);
        if (!in_array($statusCode, [200, 204])) {
            error_log("Zoom API Response Error ({$statusCode}): " . wp_remote_retrieve_body($response));
            return false;
        }
        
        $responseBody = json_decode(wp_remote_retrieve_body($response), true);
        return $responseBody;
    }

    /**
     * Updates an existing Google Meet meeting with new details.
     * 
     * This function sends a request to update an existing Google Meet meeting with new information
     * such as title, description, dates, and attendees. It uses the Laravel API endpoint to handle
     * the actual Google Calendar API interaction.
     *
     * @since 1.0.0
     * @access public
     * @param int    $eventID           The ID of the class event to update
     * @param string $userAccessToken   The Google API access token for authentication
     * @param string $timeZone          The timezone for the meeting (e.g., 'UTC', 'America/New_York')
     * @param string $classTitle        The new title for the meeting
     * @param string $classDescription  The new description for the meeting
     * @param string $eventStartDate    The new start date and time for the meeting
     * @param string $eventEndDate      The new end date and time for the meeting
     * @param array  $classLearnerEmails Array of email addresses for meeting attendees
     * @param int    $batchId           The batch ID associated with the class
     * @param int    $userID            The ID of the user initiating the update
     * @return array|bool Returns the decoded API response on success, or false on failure
     * <AUTHOR>
     */
    public function updateGoogleMeet($eventID, $userAccessToken, $timeZone, $classTitle, $classDescription, $eventStartDate, $eventEndDate, $classLearnerEmails, $batchId, $userID)
    {
        $apiUrl = GOOGLE_MEET_LARAVEL_URL . '/api/update-meeting';
        $eventId = $this->getPostData($eventID, "YunoClassGoogleCalendarId") ?? 0;
        $apiData = array(
            'accessToken'    => $userAccessToken,
            'eventId'        => $eventId,
            'summary'        => $classTitle,
            'description'    => $classDescription,
            'startDateTime'  => $eventStartDate,
            'endDateTime'    => $eventEndDate,
            'timeZone'       => $timeZone,
            'attendees'      => $classLearnerEmails,
            'class_id'       => $eventID,
            'batch_id'       => $batchId,
            'user_id'        => $userID
        );

        $headers = array(
            'Content-Type' => 'application/json',
        );

        $params = array(
            'method' => 'POST',
            'timeout' => 5,      
            'blocking' => true,  
        );

        $body = array(
            'body' => wp_json_encode($apiData),
        );

        $response = $this->utility->remotePost($apiUrl, $body, $headers, $params);
        if (is_wp_error($response)) {
            return false;
        } 

        $responseCode = wp_remote_retrieve_response_code($response);
        $responseBody = wp_remote_retrieve_body($response);

        if ($responseCode === 201 || $responseCode === 200) {
            return json_decode($responseBody, true);
        } else {
            return false;
        }
    }

    /**
     * Updates class data in Elasticsearch and returns formatted response
     * 
     * @param string $index The Elasticsearch index name
     * @param array $data The class data to be updated
     * @param string $eventID The unique identifier for the class
     * 
     * @return array|false Returns formatted response array on success, false on failure
     */
    public function updateClassDataES($index, $data, $eventID) {
        $this->loadModel('instructor');
        $this->loadModel('VirtualClassroom');
        $esId = 'privateclass-' . $eventID;

        $esResponse = $this->es->update($index, $esId, $data);
    
        if (isset($esResponse['status_code']) && $esResponse['status_code'] == 200) {
            $esResponse['data'] = [
                'id' => $eventID,
                'class_title' => $this->getClassTitle($eventID, ['schema' => 'Class_Title']),
                'scheduled' => [
                    'start' => [
                        'time' => $this->dt->convertToSystemDT($data['data']['details']['class_date_time'], "Y-m-d H:i:s") ?? '',
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'end' => [
                        'time' => $this->dt->convertToSystemDT($data['data']['details']['class_end_date_time'], "Y-m-d H:i:s") ?? '',
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'duration' => intval($data['data']['details']['duration'] ?? 0)
                ],
                'instructor' => $this->userModel->getUser($data['data']['details']['user_id'], ['schema' => 'User_Minimal']),
                'private_url' => $data['data']['details']['url'] ?? '',
                'guest_url'   => $data['data']['details']['guest_url'] ?? '',
                'virtual_classroom' => $this->VirtualClassroomModel->getVirtualClassroom($data['data']['details']['class_instructors'])
            ];
    
            return $esResponse;
        }
    
        return false;
    }

    /**
     * Sends an email notification to organization users when a class scheduling attempt fails
     * 
     * @param int|null $academy The academy ID or post object
     * @param int|null $userID The ID of the instructor who attempted to schedule the class
     * 
     * @return bool Always returns false
     */
    public function sendClassFailureAlert($academy = null, $userID = null)
    {
        $orgId = $this->getPostData($academy, "org_id") ?? 0;
        if (!empty($orgId)) {
            $users = $this->getPostData($orgId, 'user_data', true);
                $emailData = [
                    "instructor_email" => get_user_meta($userID, "yuno_gplus_email", true),
                    "instructor_name" => get_user_meta($userID, 'yuno_display_name', true),
                    "organization_name" => get_the_title($orgId),
                    "error_message" => "You cannot schedule a class"
                ];                               
        email_notification('CLASS_FAILURE_ALERT', $users, $emailData);  
        }
        return false;
    }

    /**
     * Handles pending update tasks after a class update.
     *
     * Enrolls newly added learners, triggers notifications for new enrollments and schedule changes,
     * and sends email notifications if the class schedule has changed.
     *
     * @since 1.0.0
     * @access public
     * @param array  $previousPrivateUser     Array of previously enrolled learner IDs.
     * @param array  $classLearner            Array of current learner IDs.
     * @param int    $classId                 The class event ID.
     * @param string $classTitle              The class title.
     * @param string $classExcerpt            The class excerpt.
     * @param string $eventStartDate          The event start date.
     * @param string $eventEndDate            The event end date.
     * @param array  $classLearnerEmails      Array of learner email addresses.
     * @param string $classCourseCategory     The course category of the class.
     * @param int    $userID                  The instructor's ID.
     * @param string $classLink               The URL link to the class.
     * @param string $dayOfWeek               The day of the week when the class is scheduled.
     * @param string $classStartDate          The class start date.
     * @param string $classStartTimeForWhatsApp Formatted start time for WhatsApp notifications.
     * @param string $instructorName          The instructor's name.
     * @param string $timeZone                The active timezone.
     * @return void
     */
    public function classPendingUpdateTasks($previousPrivateUser, $classLearner, $classId, $classTitle, $classExcerpt, $eventStartDate, $eventEndDate, $classLearnerEmails, $classCourseCategory, $userID, $classLink, $dayOfWeek, $classStartDate, $classStartTimeForWhatsApp, $instructorName, $timeZone) 
    {
        global $wpdb;
    
        if (!empty($previousPrivateUser) && !empty($classLearner)) {
            $newAddUser = array_diff($classLearner, $previousPrivateUser);
            if (!empty($newAddUser)) {
                foreach ($newAddUser as $newUserId) {
                    $enrollOrNot = $this->userEnrollmentCheck($classId, $newUserId);
                    if ($enrollOrNot == 0) {
                        $this->manuallyEnrollUserToClass($newUserId, $classId, $classTitle, $classExcerpt, $eventStartDate, $eventEndDate, $timeZone);
                    }
                }
                $ids = [];
                foreach ($newAddUser as $userId) {
                    $ids[] = (string) $userId;
                }

                $event_details = [
                    'user_id'   => $userID,   
                    'learners'  => $ids,
                    'action'    => 'Enrolled in Class',    
                    'target'    => 'Class',      
                    'target_id' => $classId,   
                    'timestamp' => $this->dt->currentSystemDT()  
                ];
        
                trigger_custom_event($event_details); 
            }
    
            $classStartDateUpdated = get_post_meta($classId, '_EventStartDate', true);
            if (!isset($classStartDateUpdated)) {
                $classStartDateUpdated = 0;
            }
    
            $toTime = strtotime($eventStartDate);
            $fromTime = strtotime($eventEndDate);
            $classStartDateTime = $this->getStartDate($classId, true, 'Y-m-d H:i:s');
            $classStartTime = strtotime($classStartDateTime);
            $classEndDateTime = $this->getEndDate($classId, true, 'Y-m-d H:i:s');
            $classEndTime = strtotime($classEndDateTime);
    
            if ($toTime !== $classStartTime || $fromTime !== $classEndTime) {
                $emailTemplate = $wpdb->prefix . "email_templates";
                $emTemp = $wpdb->get_row("SELECT id FROM $emailTemplate WHERE email_type = 'SCHDCLASSCHANGE'");
                $templateId = $emTemp->id;
                foreach ($classLearner as $userId) {

                    if (!in_array($userId, $previousPrivateUser)) {
                        $email = $this->getUserData($userId, 'yuno_gplus_email') ?? '';
                        
                        $mailData = [
                            'class_id'    => $classId,
                            'user_id'     => $userId, 
                            'email'       => $email,
                            'class_title' => $classTitle,
                            'template_id' => $templateId,
                        ];
                        
                        $this->postClassEnrollmentEvent($mailData);
                    }
                }
            }
        }
    
        return;
    }

    /**
    * Posts class enrollment event data to Elasticsearch and handles email notifications.
    *
    * This function processes class enrollment events by:
    * 1. Formatting class data (title, dates, duration, etc.)
    * 2. Preparing email templates with dynamic content
    * 3. Posting event data to Elasticsearch
    * 4. Managing Zoom meeting participants if applicable
    *
    * @since 1.0.0
    * @access public
    * @param array $mailData Array containing:
    * class_id, user_id, email, template_id, class_title, invite_url
    * @return bool Returns false if required data is missing or on failure
    */
    public function postClassEnrollmentEvent($mailData) {
        global $wpdb;
        
        $timeZone = $this->locale->activeTimezone();
    
        $classId = $mailData['class_id'] ?? 0;
        $userId  = $mailData['user_id'] ?? 0;
        $to      = $mailData['email'] ?? '';
        $templateId = $mailData['template_id'] ?? 0;
        $classTitle = $mailData['class_title'] ?? '';

        $privateclasstype = $this->getPostData($classId, '_private_class');
        $webinarclasstype = $this->getPostData($classId, '_webinar_class');
        if($privateclasstype == "1"){
            $classtype = 'private';
        } else if($webinarclasstype == "1"){
            $classtype = 'webinar';
        } else{
            $classtype = 'demo';
        }   
        
        $classStartDate = $this->getPostData($classId, '_EventStartDate');
        $classEndDate   = $this->getPostData($classId, '_EventEndDate');
        
        $formattedStartDate = $this->dt->convertToActiveDT($classStartDate, "Y-m-d H:i:s");
        $EventStartDay   = $this->dt->convertToActiveDT($classStartDate, 'd');
        $EventStartMonth = $this->dt->convertToActiveDT($classStartDate, 'M');
        $EventStartYear  = $this->dt->convertToActiveDT($classStartDate, 'Y');
        $eventStartTime  = $this->dt->convertToActiveDT($classStartDate, 'h:i A');
        
        $durationSeconds = $this->getPostData($classId, '_EventDuration');
        $durationMinutes = intval($durationSeconds);
        if($durationMinutes < 3600){
            $durationUnit = 'Minutes';
            $EventDuration = ltrim(gmdate("i", $durationMinutes), '0');
        } else if($durationMinutes == 3600) {
            $durationUnit = 'Hour';
            $EventDuration = ltrim(gmdate("H:i", $durationMinutes), '0');
        } else {
            $durationUnit = 'Hours';
            $EventDuration = ltrim(gmdate("H:i", $durationMinutes), '0');
        }
        
        $categories = wp_get_post_terms($classId, 'course_category');
        $catIds = [];
        foreach ($categories as $cat) {
            $catIds[] = $cat->term_id;
        }
        $CatName = '';
        if(count($catIds) > 0) {
            $term = get_term(min($catIds),'course_category');
            $CatName = $term->slug;
        }

        $currentDate = $this->dt->currentActiveDT("Y-m-d H:i:s");
        $diff        = strtotime($formattedStartDate) - strtotime($currentDate);
        $dayCount    = abs(round($diff / 86400));
        if ($dayCount == 0) {
            $day = "Today";
        } else if ($dayCount == 1) {
            $day = "$day day";
        } else {
            $day = "$dayCount days";
        }
        
        $classInstructors = $this->getPostData($classId, '_ecp_custom_13');
        $instructorName = "NA";
        if (!empty($classInstructors)) {
            $firstName = get_user_meta($classInstructors, 'yuno_first_name', true);
            $lastName  = get_user_meta($classInstructors, 'yuno_last_name', true);
            $instructorName = trim(ucwords($firstName . " " . $lastName));
        }

        $CreatedBy = $this->getPostData($classId, 'Created_by');
        if (!empty($CreatedBy)) {
            $actionEdit = site_url()."/ielts/schedule-class/?edit&classID=".$classId;
            $actionDelete = site_url()."/wp-json/yuno/v1/delete/class/".$classId."/".$classInstructors;
        } else {
            $action="";
            $actionEdit = "";
            $actionDelete = "";
        }
        
        $emailTemplateTable = $wpdb->prefix . "email_templates";
        $em_temp = $wpdb->get_row(
            $wpdb->prepare("SELECT id, template, email_type, subject FROM $emailTemplateTable WHERE id = %d", $templateId)
        );
        if(!$em_temp) {
            return false; 
        }
        
        $template_html = $em_temp->template;
        $template_html = str_replace("[class_title]", $classTitle, $template_html);
        $template_html = str_replace("[start_time]", $eventStartTime, $template_html);
        $template_html = str_replace("[month]", $EventStartMonth, $template_html);
        $template_html = str_replace("[day_of_month]", $EventStartDay, $template_html);
        $template_html = str_replace("[year]", $EventStartYear, $template_html);
        $template_html = str_replace("[INSTRUCTOR_NAME]", $instructorName, $template_html);
        $notificationSettingsURL = site_url('settings');
        $template_html = str_replace("[NOTIFICATION_SETTINGS]", $notificationSettingsURL, $template_html);
        
        if($userId != 0) {
            $userObj = get_user_by('id', $userId);
            $lastNameUser = $this->getUserData($userId, 'yuno_last_name') ?? '';
            $displayName = $this->getUserData($userId, 'yuno_first_name') ?? '';
            $template_html = str_replace("[display_name]", $displayName, $template_html);
        } else {
            $template_html = str_replace("[display_name]", '', $template_html);
        }
        $template_html = str_replace("[site_url]", WP_SITEURL, $template_html);
        if($userId != 0){
            $classURL = get_the_permalink($classId);
            $template_html = str_replace("[classURL]", $classURL, $template_html);
        } else {
            $template_html = str_replace("[classURL]", $mailData['invite_url'] ?? '', $template_html);
        }
        
        if($userId != 0){
            $eventType = ($templateId == 1) ? "enrolled_in_class" : "class_rescheduled";
            $unsubscribeUrl = site_url('/email-unsubscribe/?data=' . base64_encode($to . "###" . $eventType));
            $template_html = str_replace("[UNSUBSCRIBE]", $unsubscribeUrl, $template_html);
        }
        
        $subject = $em_temp->subject;
        $mailmessage = $template_html;
        
        $curlDataPost = [
            "data" => [
                "details" => [
                    "user_id"         => $userId,
                    "id"              => $classId,
                    "name"            => ($userId != 0) ? $this->getUserData($userId, 'yuno_display_name') : '',
                    "event_type"      => "enrolledclassevent",
                    "event_label"     => "Enrolled in Class",
                    "title"           => html_entity_decode(get_the_title($classId), ENT_NOQUOTES, 'UTF-8'),
                    "date"            => [
                        "day"   => $EventStartDay,
                        "month" => $EventStartMonth,
                        "year"  => $EventStartYear
                    ],
                    "grid_date"       => $EventStartMonth . ' ' . $EventStartDay . ', ' . $EventStartYear,
                    "url"             => get_the_permalink($classId),
                    "class_type"      => $classtype ?? '',
                    "time"            => $eventStartTime,
                    "duration"        => $EventDuration . ' ' . $durationUnit,
                    "category"        => $CatName,
                    "start"           => $day,
                    "edit_url"        => $actionEdit ?? '',
                    "batch_id"        => $this->getPostData($classId, 'Batch_ID'),
                    "delete_url"      => $actionDelete ?? '',
                    "meet_url"        => $this->getPostData($classId, 'YunoClassGoogleMeetLink'),
                    "learner_meet_url"=> $this->getPostData($classId, 'YunoClassGoogleMeetLink'),
                    "zoom_url"        => $this->getPostData($classId, 'InstructorScheduleClassStartZoomURL'),
                    "class_instructors" => $classInstructors,
                    "class_end_date"  => $classEndDate,
                    "instructor_name" => $instructorName,
                    "instructor_image"=> ($classInstructors) ? get_user_meta($classInstructors, 'googleplus_profile_img', true) : '',
                    "guest_url"       => $this->getPostData($classId, 'InstructorScheduleClassGuestZoomURL'),
                    "enrollment_count"=> count($wpdb->get_results("SELECT userId FROM {$wpdb->prefix}enrolled WHERE classId = $classId")),
                    "related_courses" => $this->getPostData($classId, 'Yuno_Class_Related_Courses'),
                    "class_detail"    => get_the_permalink($classId)
                ],
                "@timestamp" => date("Y-m-d H:i:s")
            ]
        ]; 
        $this->es->create('enrolledclassevent', $curlDataPost); 
    
        if ($userId != 0) {
            $meetingId = $this->getPostData($classId, 'InstructorScheduleClassZoomID');
            $orgId = $this->getPostData($classId, "org_id") ?? 0;
            if (!empty($orgId)) {
                $org_vc_app = $this->getPostData($orgId, "org_vc_app");
                if ($org_vc_app == "zoom") {
                    add_participants_to_zoom_meetings($meetingId, $to, $displayName, $lastNameUser, $userId, $classId);
                }
            }
        } 
        else {
            return false;
        }   
    }

    /**
     * Retrieves class details from ES using a custom query or class ID.
     *
     * Fetches class data, enrollment, and attendance info, and computes the class status.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query Custom query parameters or a class ID.
     * @param array $filter Optional filters for schema validation.
     * @return array|false Formatted class details or false on failure.
     * <AUTHOR>
     */

     public function getClass($query, $filter = []){
        $this->loadModel('course');
        $this->loadModel('learner');

        if (!is_array($query)) {
            $query = ['id' => $query];
        }
        
        if (isset($query['custom'])) {
            $classDataResponse = $this->es->customQuery($query['custom'], 'privateclass');
            if ($classDataResponse['status_code'] !== 200 ||
                !isset($classDataResponse['body']['hits']['hits'][0])
            ) {
                return false;
            }
            $details = $classDataResponse['body']['hits']['hits'][0]['_source']['data']['details'];
        } elseif (isset($query['id'])) {
            $classDataResponse = $this->es->read('privateclass', $query['id']);
            if ($classDataResponse['status_code'] !== 200) {
                return false;
            }
            $details = $classDataResponse['body']['_source']['data']['details'];
        } else {
            return false;
        }

        $classId        = $details['record_id']   ?? 0;
        $classStartTime = $details['class_date_time']     ?? '';
        $classEndTime   = $details['class_end_date_time']   ?? '';

        $learners = $this->learnerModel->getLearnersOfClass($classId);
        $learnerDetails = [];
        $learnerAttDetails = []; 
        if (!empty($learners)) {
            foreach ($learners as $learner) {
                $learnerData = $this->load->subData("learner", "getLearner", $learner['user_id'], [
                    'key' => 'user',
                    'noResponse' => 'User_Minimal'
                ]);
                if ($learnerData) {
                    $learnerDetails[] = $learnerData;
                }
                $attendance = $this->getClassLearnerAttendanceDetails([
                    'class_id'  => $classId, 
                    'learner_id'=> $learner['user_id']
                ]);
                if ($attendance) {
                    $learnerAttDetails[] = $attendance;
                }
            }
        }else{
            $learnerData = $this->load->subData("learner", "getLearner", 0, [
                'key' => 'user',
                'noResponse' => 'User_Minimal'
            ]);
            $learnerDetails[] = $learnerData;
        }

        $currDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
        $currTS       = strtotime($currDateTime);
        $startTS      = strtotime($classStartTime);
        $endTS        = strtotime($classEndTime);
        
        if ($currTS < $startTS) {
            $temporalStatus = "upcoming"; 
        } elseif ($currTS >= $startTS && $currTS <= $endTS) {
            $temporalStatus = "live"; 
        } else {
            $temporalStatus = "past"; 
        }
        // $academyId = $this->courseModel->getAcademyId($details['course_id']);
        $academyId = $details['academy_id'] ?? 0;

        $orgId = !empty($academyId) ? $this->getPostData($academyId, "org_id") : 0;
        $orgQuery = [
            "query" => [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                ["match" => ["data.details.record_id" => $orgId]]
                            ]
                        ]
                    ]
                ]
            ],
            "_source" => ["data.details.org_vc_app"]
        ];
        
        $platform = $this->es->customQuery($orgQuery, 'org');
        $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';

        $meetingUrl = '';
        if ($orgPlatform === 'zoom') {
            $meetingUrl = $details['zoom_url'] ?? '';
        } elseif ($orgPlatform === 'gmeet') {
            $meetingUrl = $details['meet_url'] ?? '';
        } else {
            $meetingUrl = '';
        }

        if ($temporalStatus === "past") {
            $response = [
                'id' => $classId,
                'type' => ($details['event_type'] === 'privateclass') ? 'PRIVATE' : strtoupper($details['event_type']),
                'temporal_status' => $temporalStatus,
                'private_url' => $details['url'] ?? '',
                'class_title' => $this->getClassTitle($classId, ['schema' => 'Class_Title']),
                'scheduled' => [
                    'start' => [
                        'time' => $this->dt->convertToSystemDT($details['class_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'end' => [
                        'time' => $this->dt->convertToSystemDT($details['class_end_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'duration' => intval($details['duration'] ?? 0)
                ],
                'actual' => [
                    'start' => [
                        'time' => isset($details['class_actual_date_time']) 
                        ? $this->dt->convertToSystemDT($details['class_actual_date_time'], "Y-m-d\TH:i:s\Z") 
                        : '',
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'end' => [
                        'time' => isset($details['class_actual_end_date_time']) 
                        ? $this->dt->convertToSystemDT($details['class_actual_end_date_time'], "Y-m-d\TH:i:s\Z") 
                        : '',
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'duration' => isset($details['class_actual_date_time'], $details['class_actual_end_date_time'])
                        ? round((strtotime($details['class_actual_end_date_time']) - strtotime($details['class_actual_date_time'])) / 60)
                        : 0
                ],
                'instructor' => $this->load->subData("user", "getUser", $details['class_instructors'], [
                    'schema' => 'User_Minimal',
                    'noResponse' => 'User_Minimal'
                ]),
                'batch' => $this->load->subData("batch", "getBatch", $details['batch_id'], [
                    'schema' => 'Batch_Minimal',
                    'noResponse' => ['id' => 0, 'title' => '', 'temporal_state' => '']
                ]),
                'course' => $this->load->subData("course", "getCourse", $details['course_id'], [
                    'schema' => 'Course_Minimal', 
                    'noResponse' => ['id' => 0, 'title' => '', 'url' => '']
                ]),
                'academy' => $this->load->subData("academy", "getAcademy", $academyId, [
                    'schema' => 'Academy_Basic',
                    'noResponse' => ['id' => 0, 'name' => '', 'logo_url' => ['url' => '', 'alt_text' => ''], 'fav_icon_url' => ['url' => '', 'alt_text' => '']]
                ]),
                'enrollments' => !empty($learnerDetails) ? $learnerDetails : [],
                'attendance_of_each' => !empty($learnerAttDetails)
                ? array_values(array_filter(array_map(function ($attendance) {
                    return isset($attendance['learner']) ? [
                        'learner' => $attendance['learner'],
                        'duration' => $attendance['duration'] ?? 0,
                        'duration_percentage' => $attendance['duration_percentage'] ?? 0,
                        'class_review' => $attendance['class_review'] ?? ''
                    ] : [];
                }, $learnerAttDetails)))
                : [
                    [
                        'learner' => $this->load->subData("learner", "getLearner", 0, ['key' => 'user', 'noResponse' => 'User_Minimal']),
                        'duration' => 0,
                        'duration_percentage' => 0,
                        'class_review' => [
                            'id' => 0,
                            'source' => [
                                'source' => '',
                                'name' => ''
                            ]
                        ]
                    ]
                ],
                'aggregate_rating' => [
                    'rating' => 0.0, 
                    'max_rating' => 0.0 
                ],
                'virtual_classroom' => [
                    'meeting_app' => $this->load->subData("virtualClassroom", "getVirtualClassroom", $details['class_instructors'], [
                        'noResponse' => 'Virtual_Classroom'
                    ]),
                    'meeting_id' => isset($details['meeting_id']) ? (string)$details['meeting_id'] : '',
                ],
                'in_person' => [
                    'place' =>  $this->load->subData("place", "getPlace", $details['place_id'], [
                        'noResponse' => 'Place_Minimal'
                    ]),
                    'classroom' =>  $this->load->subData("classroom", "getClassroom", $details['classroom_id'], [
                        'noResponse' => 'Classroom_Minimal'
                    ])
                ],
                'recording' => [
                    'platform' => '', 
                    'id' => (string) ($details['_recording_url_id'] ?? '0'),
                    'title' => '', 
                    'url' => $details['_recording_url'] ?? '',
                    'upload_date' => [
                        'time' => '',
                        'timezone' => ''
                    ], 
                    'embed_code' => '', 
                    'thumbnail' => [[
                        'url' => '',
                        'alt_text' => ''
                    ]],
                    'duration'=> 0
                ]
            ];
            return $this->schema->validate($response, 'Past_Class', $filter);
        } else {
            $response = [
                'id' => $classId,
                'type' => ($details['event_type'] === 'privateclass') ? 'PRIVATE' : strtoupper($details['event_type'] ?? 'PRIVATE'),
                'temporal_status' => $temporalStatus,
                'class_title' => $this->getClassTitle($classId, ['schema' => 'Class_Title']),
                'scheduled' => [
                    'start' => [
                        'time'     => $this->dt->convertToSystemDT($details['class_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'end' => [
                        'time' => $this->dt->convertToSystemDT($details['class_end_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                        'timezone' => $this->locale->activeTimezone()
                    ],
                    'duration' => intval($details['duration'] ?? 0)
                ],
                'instructor' => $this->load->subData("user", "getUser", $details['class_instructors'], [
                    'schema' => 'User_Minimal',
                    'noResponse' => 'User_Minimal'
                ]),
                'batch' => $this->load->subData("batch", "getBatch", $details['batch_id'], [
                    'schema' => 'Batch_Minimal',
                    'noResponse' => ['id' => 0, 'title' => '', 'temporal_state' => '']
                ]),
                'course' => $this->load->subData("course", "getCourse", $details['course_id'], [
                    'schema' => 'Course_Minimal',
                    'noResponse' => ['id' => 0, 'title' => '', 'url' => '']
                ]),
                'academy' => $this->load->subData("academy", "getAcademy", $academyId, [
                    'schema' => 'Academy_Basic',
                    'noResponse' => ['id' => 0, 'name' => '', 'logo_url' => ['url' => '', 'alt_text' => ''], 'fav_icon_url' => ['url' => '', 'alt_text' => '']]
                ]),
                'enrollments' => $learnerDetails,
                'private_url' => $details['url'] ?? '',
                'guest_url' => $details['guest_url'] ?? '',
                'virtual_classroom' => [
                    'meeting_app' => $this->load->subData("virtualClassroom", "getVirtualClassroom", $details['class_instructors'], [
                        'noResponse' => 'Virtual_Classroom'
                    ]),
                    'meeting_id' => isset($details['meeting_id']) ? (string)$details['meeting_id'] : '',
                    'meeting_url' => $meetingUrl
                ],
                'in_person' => [
                    'place' =>  $this->load->subData("place", "getPlace", $details['place_id'], [
                        'noResponse' => 'Place_Minimal'
                    ]),
                    'classroom' =>  $this->load->subData("classroom", "getClassroom", $details['classroom_id'], [
                        'noResponse' => 'Classroom_Minimal'
                    ])
                ]
            ];
            return $this->schema->validate($response, 'Live_Upcoming_Class', $filter);
        }
        return false;
    }

    /**
     * Creates a demo class with the provided data, handling all business logic including
     * virtual classroom creation, post creation, meta updates, and Elasticsearch indexing.
     *
     * @since 1.0.0
     * @access public
     * @param array $classData Array containing all necessary class data
     * @return array Response array with success/error status and relevant data
     * <AUTHOR>
     */
    public function createDemoClass($classData) {
        try {
            $this->loadModel('instructor');
            $this->loadModel('VirtualClassroom');
            // Process learners
            $classLearnerIds = [];
            $classLearnerEmails = [];
            $ClassSchdulelearnerEmail = [];
            $groupIds = [];
            
            foreach ($classData['learners'] as $group) {
                if (isset($group['user_count']) && $group['user_count'] > 0) {
                    foreach ($group['users'] as $user) {
                        $classLearnerIds[] = intval($user['user_id']);
                        $groupIds[] = intval($user['group_id']);
                    }
                } elseif (isset($group['id']) && !empty($group['id'])) {
                    $classLearnerIds[] = intval($group['id']);
                    if (isset($group['email']) && is_email($group['email'])) {
                        $classLearnerEmails[] = sanitize_email($group['email']);
                        $ClassSchdulelearnerEmail[]['email'] = $group['email'];
                    }
                } elseif (empty($group['id']) && isset($group['email']) && is_email($group['email'])) {
                    $classLearnerEmails[] = sanitize_email($group['email']);
                }
            }
            $classLearnerIds = array_unique($classLearnerIds);
            $groupIds = array_unique($groupIds);
            $classLearnerEmails = array_unique($classLearnerEmails);

            // Validate instructors
            $instructorIDs = array_map('intval', $classData['instructor_id']);
            $instructorIDs = array_unique($instructorIDs);
            $validInstructorIDs = [];
            foreach ($instructorIDs as $id) {
                $user = $this->userModel->getUser($id, ['schema' => 'User_Minimal']);
                if ($user) {
                    $validInstructorIDs[] = $id;
                }
            }
            if (empty($validInstructorIDs)) {
                return ['error' => 'No valid instructors found in instructorID array.'];
            }
            $userID = $validInstructorIDs[array_rand($validInstructorIDs)];

            // Get instructor details
            $instructorQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.user_id" => $userID]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user.name",
                    "data.details.user.email",
                    "data.details.user.image"
                ]
            ];
            
            $instructor = $this->es->customQuery($instructorQuery, 'signedup');
            $instructorData = $instructor['body']['hits']['hits'][0]['_source']['data']['details']['user'] ?? [];
            
            if (empty($instructorData)) {
                return ['error' => 'Failed to get instructor details'];
            }

            // Process dates and times
            $timeZone = $this->locale->activeTimezone();
            $classDate = $classData['class_date_time'];
            $classTime = $classData['class_date_time'];

            $ClassDate = substr($classDate, 0, strpos($classDate, "GMT"));
            $ClassTime = substr($classTime, 0, strpos($classTime, "GMT"));
            
            $ClassStartDate = $this->dt->convertToActiveDT($ClassDate, "Y-m-d");
            $ClassStartTime = $this->dt->convertToActiveDT($ClassTime, "H:i");
            $ClassStartTimeForWhatsApp = $this->dt->convertToActiveDT($ClassTime, "h:i A");
            $ClassStartTimeWithSecond = $this->dt->convertToActiveDT($ClassTime, "H:i:s");
            
            // Validate class time is not in past
            $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;
            $CurrentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            // if (strtotime($ClassStartDateTimeIn24HourFormat) < strtotime($CurrentDateTime)) {
            //     return ['error' => 'The class time should not be in the past'];
            // }

            // Calculate end times
            $endDateTimeExpression = trim($ClassStartDateTimeIn24HourFormat) . " +" . $classData['class_duration'] . " minutes";
            $ClassEndDateTimeIn24HourFormat = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i");
            $gcalStartDate = $ClassStartDate . ' ' . $ClassStartTimeWithSecond;
            $gcalEndDate = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i:s");
            $ClassEndDate = $this->dt->convertToActiveDT($ClassEndDateTimeIn24HourFormat, "Y-m-d");

            $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s"); 
            $eventEndDate = $this->dt->convertToActiveDT($gcalEndDate, "Y-m-d\TH:i:s"); 
            
            // Get virtual classroom platform
            $orgId = !empty($classData['academy_id']) ? $this->getPostData($classData['academy_id'], "org_id") : 0;
            $orgQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.record_id" => $orgId]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => ["data.details.org_vc_app"]
            ];
            
            $platform = $this->es->customQuery($orgQuery, 'org');
            $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';

            // Setup virtual classroom
            $vcQuery['custom'] = [
                'id'       => $userID,
                'orgId'    => $orgId,  
                'platform' => $orgPlatform
            ];
            
            $virtualClasses = $this->VirtualClassroomModel->getVirtualClassroom($vcQuery);
            
            if (!$virtualClasses || empty($virtualClasses['connection']) || $virtualClasses['connection'] != 1) {
                return ['error' => 'Virtual classroom setup failed'];
            }

            // Create WordPress post
            $myPost = [
                'post_title' => $classData['class_title'],
                'post_content' => $classData['description'],
                'post_status' => "publish",
                'post_author' => 2,
                'post_excerpt' => $classData['excerpt'],
                'EventStartDate' => $ClassStartDateTimeIn24HourFormat,
                'EventEndDate' => $ClassEndDateTimeIn24HourFormat,
                'EventDuration' => $classData['class_duration'],
                'EventStartHour' => $this->dt->convertToActiveDT($ClassTime, "h"),
                'EventStartMinute' => $this->dt->convertToActiveDT($ClassTime, "i"),
                'EventStartMeridian' => $this->dt->convertToActiveDT($ClassStartTime, "a"),
                'EventEndHour' => $this->dt->convertToActiveDT($endDateTimeExpression, "h"),
                'EventEndMinute' => $this->dt->convertToActiveDT($endDateTimeExpression, "i"),
                'EventEndMeridian' => $this->dt->convertToActiveDT($endDateTimeExpression, "a"),
            ];

            $eventID = $this->addCustomPostClass($myPost);
            if (is_wp_error($eventID)) {

                $err = $eventID->get_error_message();
                // print_r($err);exit;
                return ['error' => 'Failed to create class post'];
            }

            // Set terms and meta
            wp_set_post_terms($eventID, $classData['category_id'], 'course_category');
            wp_set_object_terms($eventID, $classData['category_id'], 'tribe_events_cat');

            $metaData = [
                '_private_class' => '1',
                '_ecp_custom_13' => $userID
            ];
            
            if (!empty($classData['academy_id'])) {
                $metaData['Academy_ID'] = $classData['academy_id'];
            }
            if (!empty($orgId)) {
                $metaData['org_id'] = $orgId;
            }
            
            $this->updateClassPostMeta($eventID, $metaData);

            if (!empty($classData['batch_id'])) {
                $this->updateClassPostMeta($eventID, ['Batch_ID' => $classData['batch_id']]);
            }

            if (!empty($groupIds)) {
                $this->updateClassPostMeta($eventID, [
                    'Group_ID' => $groupIds,
                    'All_Groups_ID' => $groupIds,
                ]);
            }

            // Create virtual classroom meeting
            $vcPlatform = isset($virtualClasses['platform']) ? strtolower($virtualClasses['platform']) : '';
            $meetingURL = '';
            
            if ($vcPlatform === 'zoom') {
                $meetingURL = $this->createZoomMeeting(
                    $classData['class_title'], 
                    $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y-m-d\TH:i:s"),
                    $classData['class_duration'],
                    $timeZone,
                    $instructorData['email'],
                    $userID
                );
            } elseif (in_array($vcPlatform, ['google meet', 'google_meet', 'gmeet'])) {
                $usrTokenGoogle = $this->getGoogleMeetAccessToken($userID, $timeZone, $orgId);
                $meetingURL = $this->createGoogleMeet(
                    $eventID,
                    $classData['class_duration'],
                    $usrTokenGoogle,
                    $timeZone,
                    $classData['class_title'],
                    $classData['description'],
                    $eventStartDate,
                    $eventEndDate,
                    $ClassSchdulelearnerEmail,
                    $classData['batch_id'],
                    $userID
                );

                // print_r($meetingURL);exit;
            }

            if (!$meetingURL) {
                return ['error' => 'Failed to create virtual classroom meeting'];
            }

            // Process meeting URL and update meta
            $meetUrl = '';
            $zoomUrl = '';
            $guest_join_url = '';
            $meeting_id = '';
            
            if ($vcPlatform === 'google meet' || $vcPlatform === 'gmeet') {
                $guest_join_url = $meetingURL['Recording_Share_Url'] ?? '';
                if (!empty($guest_join_url)) {
                    $meetCode = preg_replace('/https:\/\/meet\.google\.com\//', '', $guest_join_url);
                    $meetCode = strtoupper(str_replace('-', '', $meetCode));
                    $meeting_id = $meetCode;
                }
                $this->updateClassPostMeta($eventID, [
                    'YunoClassGoogleMeetLink' => $meetingURL['Recording_Share_Url'] ?? '',
                    'YunoClassGoogleCalendarId' => $meetingURL['WebinarID'] ?? '',
                    'InstructorScheduleClassMeetID' => $meetCode
                ]);
                $meetUrl = $guest_join_url;
            } else {
                $meeting_id = $meetingURL['id'];
                $meeting_join_url = $meetingURL['join_url'];
                $meeting_start_url = $meetingURL['start_url'];
                $zoomUrl = $meeting_start_url;
                $this->updateClassPostMeta($eventID, [
                    'InstructorScheduleClassZoomURL' => $meeting_join_url,
                    'InstructorScheduleClassStartZoomURL' => $meeting_start_url,
                    'InstructorScheduleClassZoomID' => $meeting_id
                ]);
            }

            // Create guest URL
            $encodeValue = base64_encode($meeting_id . '@@' . 'undefined' . '@@' . $eventID . '@@' . '');
            list($protocol, $siteURL) = explode("://", site_url());
            $guest_url = "https://" . $siteURL . "/open/" . $encodeValue;
            update_post_meta($eventID, 'InstructorScheduleClassGuestZoomURL', $guest_url);

            // Insert into zoom API table
            $this->insertYunoZoomApi([
                'p_id' => $eventID,
                'zoom_id' => $meeting_id,
                'zoom_url' => $meeting_join_url ?? '',
                'start_url' => $meeting_start_url ?? '',
                'parent_id' => $eventID,
                'guest_url' => $guest_url,
                'start_date' => $ClassStartDate . ' ' . $ClassStartTimeWithSecond,
            ]);

            // Create notifications
            $ClassLink = get_the_permalink($eventID);
            $this->insertWhatsappNotification([
                "user_id" => $userID,
                "class_id" => $eventID,
                "class_title" => $classData['class_title'],
                "class_start_datetime" => str_replace("T", " ", $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y-m-d\TH:i:s")),
                "class_url" => $ClassLink,
                "status_one_hour" => 0,
                "status_ten_minutes" => 0,
            ]);

            // Calculate days until class
            $diff = strtotime($ClassStartDate) - strtotime($CurrentDateTime);
            $day = abs(round($diff / 86400));
            $dayDisplay = $day == 0 ? "Today" : ($day == 1 ? "1 day" : "$day days");

            // Index in Elasticsearch
            $elasticsearchPayload = [
                'data' => [
                    'details' => [
                        'record_id' => $eventID,
                        'user_id' => $userID,
                        'event_type' => $classData['meeting_type'],
                        "launch_status" => false,
                        'event_label' => 'Demo Class Created',
                        'resource_type' => 'tribe_events',
                        'title' => $classData['class_title'],
                        'date' => [
                            'month' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "M"),
                            'year' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y"),
                            'day' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "d"),
                        ],
                        'grid_date' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, 'M d, Y'),
                        'url' => get_permalink($eventID),
                        'class_type' => $classData['meeting_type'],
                        'time' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, 'h:i A'),
                        'duration' => "{$classData['class_duration']} Minutes",
                        'category' => !empty($classData['category_id']) ? [$classData['category_id']] : [],
                        'start' => $dayDisplay,
                        'edit_url' => esc_url_raw(site_url("/ielts/schedule-class/?edit&classID={$eventID}")),
                        'batch_id' => $classData['batch_id'],
                        'delete_url' => esc_url_raw(site_url("/wp-json/yuno/v1/delete/class/{$eventID}/{$userID}")),
                        'meet_url' => $meetUrl,
                        'zoom_url' => $zoomUrl,
                        'class_instructors' => $userID,
                        'instructor_name' => $instructorData['name'],
                        'instructor_image' => $instructorData['image'],
                        'guest_url' => $this->getPostData($eventID, "InstructorScheduleClassGuestZoomURL"),
                        'enrollment_count' => count($classLearnerIds),
                        'related_courses' => $classData['related_courses'],
                        'event_date' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y-m-d\TH:i:s"),
                        'course_id' => $classData['related_courses'],
                        'academy_id' => $classData['academy_id'],
                        'class_detail' => get_permalink($eventID),
                        'meeting_id' => $meeting_id,
                        'class_date_time' => $this->getPostData($eventID, "_EventStartDate"),
                        'learner_avg_class_rating' => 0,
                        'staff_avg_class_rating' => 0,
                        'class_end_date_time' => $this->getPostData($eventID, "_EventEndDate")
                    ],
                    '@timestamp' => $this->dt->currentSystemDT()
                ]
            ];

            $esResponse = $this->addClassDataES('privateclass', $elasticsearchPayload, $eventID, $classData['meeting_type'], $virtualClasses);

            // Create pending tasks
            $this->classPendingTasks(
                $classLearnerIds,
                $eventID,
                $classData['class_title'],
                $classData['excerpt'],
                $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y-m-d\TH:i:s"),
                $gcalEndDate,
                $classLearnerEmails,
                '',
                $userID,
                $ClassLink,
                $this->dt->convertToActiveDT(strtotime($ClassStartDate), "l"),
                $ClassStartDate,
                $ClassStartTimeForWhatsApp,
                $instructorData['name'],
                $timeZone
            );

            // Return success response
            return [
                'data' => $esResponse['data']
                
            ];

        } catch (Exception $e) {
            error_log("Error in createDemoClass: " . $e->getMessage());
            return ['error' => 'An error occurred while creating the demo class'];
        }
    }

    /**
     * Validates virtual classroom setup for a class
     *
     * @since 1.0.0
     * @access private
     * @param int $instructorId Instructor ID
     * @param int $academyId Academy ID
     * @param string $platform Virtual classroom platform
     * @return array|bool Returns virtual classroom data if valid, false otherwise
     */
    private function validateVirtualClassroom($instructorId, $academyId, $platform) {
        $this->loadModel('VirtualClassroom');
        $vcQuery = [
            'custom' => [
                'id' => $instructorId,
                'orgId' => $academyId,
                'platform' => $platform
            ]
        ];

        $virtualClasses = $this->VirtualClassroomModel->getVirtualClassroom($vcQuery);
        
        if (!$virtualClasses || empty($virtualClasses['connection']) || $virtualClasses['connection'] != 1) {
            if (!empty($academyId)) {
                $this->sendClassFailureAlert($academyId, $instructorId);
            }
            return false;
        }

        return $virtualClasses;
    }

    /**
     * Creates a private class with all necessary integrations.
     *
     * @since 1.0.0
     * @access public
     * @param array $data Sanitized input data for class creation
     * @return array Returns array with success/error status and data
     */
    public function createPrivateClass($data) {
        try {
            // Prepare class data
            $this->loadModel('instructor');
            $this->loadModel('VirtualClassroom');
            $this->loadModel('user');
            $this->loadModel('course');
            $timeZone = $this->locale->activeTimezone();
            $classDate = substr($data['class_date_time'], 0, strpos($data['class_date_time'], "GMT"));
            $classTime = substr($data['class_date_time'], 0, strpos($data['class_date_time'], "GMT"));
            
            $ClassStartDate = $this->dt->convertToActiveDT($classDate, "Y-m-d");
            $ClassStartTime = $this->dt->convertToActiveDT($classTime, "H:i");
            $ClassStartTimeWithSecond = $ClassStartTime . ':00';
            $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;

            $endDateTimeExpression = trim($ClassStartDateTimeIn24HourFormat) . " +" . $data['class_duration'] . " minutes";
            $ClassEndDateTimeIn24HourFormat = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i");

            $gcalStartDate = $ClassStartDate . ' ' . $ClassStartTimeWithSecond;
            $gcalEndDate = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i:s");
            $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s"); 
            $eventEndDate = $this->dt->convertToActiveDT($gcalEndDate, "Y-m-d\TH:i:s"); 

            // Create class post

            $postArray = [
                'post_title' => $data['class_title'],
                'post_content' => $data['description'],
                'post_status' => "publish",
                'post_author' => 2,
                'post_excerpt' => $data['excerpt'],
                'EventStartDate' => $ClassStartDateTimeIn24HourFormat,
                'EventEndDate' => $ClassEndDateTimeIn24HourFormat,
                'EventDuration' => $data['class_duration'],
                'EventStartHour' => $this->dt->convertToActiveDT($classTime, "h"),
                'EventStartMinute' => $this->dt->convertToActiveDT($classTime, "i"),
                'EventStartMeridian' => $this->dt->convertToActiveDT($ClassStartTime, "a"),
                'EventEndHour' => $this->dt->convertToActiveDT($endDateTimeExpression, "h"),
                'EventEndMinute' => $this->dt->convertToActiveDT($endDateTimeExpression, "i"),
                'EventEndMeridian' => $this->dt->convertToActiveDT($endDateTimeExpression, "a"),
            ];

            $EventID = $this->addCustomPostClass($postArray);
            if (!$EventID) {
                return ['error' => 'Failed to create class post'];
            }
            $orgId = !empty($data['academy_id']) ? $this->getPostData($data['academy_id'], "org_id") : 0;

            if (isset($data['course_id'])) {
                $courseInfo = $this->courseModel->getCourse($data['course_id'], [
                    'schema' => 'Course_Minimal',
                    'noResponse' => ['id' => 0, 'title' => '', 'url' => '']
                ]);
                $courseName = isset($courseInfo['title']) ? $courseInfo['title'] : '';
            } else {
                $courseName = '';
            }

            // Prepare metadata
            $metaData = [
                'class_date_time' => $data['class_date_time'],
                'class_duration' => $data['class_duration'],
                'class_start_date' => $ClassStartDate,
                'class_start_time' => $ClassStartTime,
                'class_end_date' => $this->dt->convertToActiveDT($ClassEndDateTimeIn24HourFormat, "Y-m-d"),
                'class_end_time' => $this->dt->convertToActiveDT($ClassEndDateTimeIn24HourFormat, "H:i"),
                'class_course_category' => $data['category_id'],
                '_ecp_custom_13' => $data['instructor_id'],
                'class_batch' => $data['batch_id'],
                'class_course' => $data['course_id'],
                'Academy_ID' => $data['academy_id'],
                'org_id' => $orgId,
                'class_meeting_type' => $data['meeting_type'],
                '_private_class' => '1',
                'is_online' => $data['is_online'],
                'is_in_person' => $data['is_in_person'],
                'place_id' => $data['place_id'],
                'classroom_id' => $data['classroom_id'] ?? null,
                'Yuno_Class_Related_Courses' => $courseName,
                'Batch_ID' => $data['batch_id']
            ];

            // Handle virtual classroom integration
            $virtualClassroom = null;
            $meetUrl = '';
            $zoomUrl = '';
            $meeting_id = '';

            // Get organization platform
            
            $orgQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.record_id" => $orgId]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => ["data.details.org_vc_app"]
            ];
            
            $platform = $this->es->customQuery($orgQuery, 'org');
            $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';

            // Validate virtual classroom
            $virtualClasses = $this->validateVirtualClassroom(
                $data['instructor_id'],
                $orgId,
                $orgPlatform
            );

            $vcPlatform = isset($virtualClasses['platform']) ? strtolower($virtualClasses['platform']) : '';

            $classLearnerIds = [];
            $classLearnerEmails = [];
            $ClassScheduleLearnerEmail = [];
            $groupIds = [];
            $invalidLearners = [];

            foreach ($data['learners'] as $group) {
                if (isset($group['user_count']) && $group['user_count'] > 0) {
                    foreach ($group['users'] as $user) {
                        $userId = intval($user['user_id']);
                        $groupIds[] = intval($user['group_id']);

                        $userData = $this->userModel->getUser($userId);

                        if (!empty($userData) && is_array($userData['role']) && in_array('learner', $userData['role'])) {
                            $classLearnerIds[] = $userId;

                            $email = $user['email'] ?? $userData['email'];
                            if ($email && is_email($email)) {
                                $sanitizedEmail = sanitize_email($email);
                                $classLearnerEmails[] = $sanitizedEmail;
                                $ClassScheduleLearnerEmail[]['email'] = $sanitizedEmail;
                            } else {
                                $invalidLearners[] = $userId;
                            }
                        } else {
                            $invalidLearners[] = $userId;
                        }
                    }
                } elseif (isset($group['id']) && !empty($group['id'])) {
                    $userId = intval($group['id']);
                    $userData = $this->userModel->getUser($userId);

                    if (!empty($userData) && is_array($userData['role']) && in_array('learner', $userData['role'])) {
                        $classLearnerIds[] = $userId;

                        $email = $group['email'] ?? $userData['email'];
                        if ($email && is_email($email)) {
                            $sanitizedEmail = sanitize_email($email);
                            $classLearnerEmails[] = $sanitizedEmail;
                            $ClassScheduleLearnerEmail[]['email'] = $sanitizedEmail;
                        } else {
                            $invalidLearners[] = $userId;
                        }
                    } else {
                        $invalidLearners[] = $userId;
                    }
                } elseif (empty($group['id']) && isset($group['email']) && is_email($group['email'])) {
                    $classLearnerEmails[] = sanitize_email($group['email']);
                }
            }

            $classLearnerIds = array_unique($classLearnerIds);
            $groupIds = array_unique($groupIds);
            $classLearnerEmails = array_unique($classLearnerEmails);
            $invalidLearners = array_unique($invalidLearners);

            if (strtolower($data['meeting_type']) === 'privateclass') {
                // Get instructor details for virtual classroom
                $instructorQuery = [
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "bool" => [
                                    "must" => [
                                        ["match" => ["data.details.user_id" => $data['instructor_id']]]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "_source" => [
                        "data.details.user.name",
                        "data.details.user.email",
                        "data.details.user.image"
                    ]
                ];
    

                $instructor = $this->es->customQuery($instructorQuery, 'signedup');

                if (!empty($instructor['body']['hits']['hits'])) {
                    $instructorData = $instructor['body']['hits']['hits'][0]['_source']['data']['details']['user'];
                }
                    
                // Create virtual classroom based on platform
                if (!empty($data['is_online'])) {

                    if (!$virtualClasses) {
                        return ['error' => 'You cannot schedule a class'];
                    }

                    if ($vcPlatform === 'google meet' || $vcPlatform === 'gmeet') {
                        $userAccessToken = $this->getGoogleMeetAccessToken($data['instructor_id'], $timeZone);
                        if ($userAccessToken) {
                            $virtualClassroom = $this->createGoogleMeet(
                                $EventID,
                                $data['class_duration'],
                                $userAccessToken,
                                $timeZone,
                                $data['class_title'],
                                $data['class_description'],
                                $eventStartDate,
                                $eventEndDate,
                                $ClassScheduleLearnerEmail,
                                $data['batch_id'],
                                $data['instructor_id']
                            );
                            if ($virtualClassroom) {
                                $guest_join_url = $virtualClassroom['Recording_Share_Url'] ?? '';
                                if (!empty($guest_join_url)) {
                                    $meetCode = preg_replace('/https:\/\/meet\.google\.com\//', '', $guest_join_url);
                                    $meetCode = strtoupper(str_replace('-', '', $meetCode));
                                    $meeting_id = $meetCode;
                                }
                                $this->updateClassPostMeta($EventID, [
                                    'YunoClassGoogleMeetLink' => $virtualClassroom['Recording_Share_Url'] ?? '',
                                    'YunoClassGoogleCalendarId' => $virtualClassroom['WebinarID'] ?? '',
                                    'InstructorScheduleClassMeetID' => $meetCode
                                ]);
                                $meetUrl = $guest_join_url;

                                $encodeValue = base64_encode($meeting_id . '@@' . 'undefined' . '@@' . $EventID . '@@' . '');
                                list($protocol, $siteURL) = explode("://", site_url());
                                $guest_url = "https://" . $siteURL . "/open/" . $encodeValue;
                                update_post_meta($EventID, 'InstructorScheduleClassGuestZoomURL', $guest_url);
                            }else{
                                return ['error' => 'An error occurred while creating the class'];
                            }
                        }
                    } else if ($vcPlatform === 'zoom') {
                        $virtualClassroom = $this->createZoomMeeting(
                            $data['class_title'],
                            $ClassStartDateTimeIn24HourFormat,
                            $data['class_duration'],
                            $timeZone,
                            $instructorData['email'],
                            $data['instructor_id']
                        );
                        if ($virtualClassroom) {
                            $zoomUrl = $virtualClassroom['join_url'];
                            $meeting_id = $virtualClassroom['id'];
                        }
                    }else{
                        return ['error' => 'An error occurred while creating the class'];
                    }
                }
                
            }
            $this->updateClassPostMeta($EventID, $metaData);
            // Prepare Elasticsearch payload
            $elasticsearchPayload = [
                'data' => [
                    'details' => [
                        'record_id' => $EventID,
                        'user_id' => $data['instructor_id'],
                        'event_type' => $data['meeting_type'],
                        "launch_status" => false,
                        'event_label' => 'Private Class Created',
                        'resource_type' => 'tribe_events',
                        'title' => $data['class_title'],
                        'date' => [
                            'month' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "M"),
                            'year' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y"),
                            'day' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "d"),
                        ],
                        'grid_date' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, 'M d, Y'),
                        'url' => get_permalink($EventID),
                        'class_type' => $data['meeting_type'],
                        'place_id' => $data['place_id'] ?? 0,
                        'classroom_id' => $data['classroom_id'] ?? 0,
                        'teaching_mode' => [
                            'online' => $data['is_online'],
                            'in_person' => $data['is_in_person']
                        ],
                        'time' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, 'h:i A'),
                        'duration' => "{$data['class_duration']} Minutes",
                        'category' => !empty($data['category_id']) ? [$data['category_id']] : [],
                        'start' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, 'Y-m-d\TH:i:s'),
                        'edit_url' => esc_url_raw(site_url("/ielts/schedule-class/?edit&classID={$EventID}")),
                        'batch_id' => $data['batch_id'],
                        'delete_url' => esc_url_raw(site_url("/wp-json/yuno/v1/delete/class/{$EventID}/{$data['instructor_id']}")),
                        'meet_url' => $meetUrl,
                        'zoom_url' => $zoomUrl,
                        'class_instructors' => $data['instructor_id'],
                        'instructor_name' => $instructorData['name'] ?? '',
                        'instructor_image' => $instructorData['image'] ?? '',
                        'guest_url' => $this->getPostData($EventID, "InstructorScheduleClassGuestZoomURL"),
                        'enrollment_count' => count($classLearnerIds),
                        'related_courses' => $courseName,
                        'event_date' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y-m-d\TH:i:s"),
                        'course_id' => $data['course_id'],
                        'academy_id' => $data['academy_id'],
                        'class_detail' => get_permalink($EventID),
                        'meeting_id' => $meeting_id,
                        'class_date_time' => $this->getPostData($EventID, "_EventStartDate"),
                        'learner_avg_class_rating' => 0,
                        'staff_avg_class_rating' => 0,
                        'class_end_date_time' => $this->getPostData($EventID, "_EventEndDate")
                    ],
                    '@timestamp' => $this->dt->currentSystemDT()
                ]
            ];

            // Index in Elasticsearch
            $esResponse = $this->addClassDataES('privateclass', $elasticsearchPayload, $EventID, 'privateclass', $virtualClassroom);

            // Schedule notifications and tasks
            $this->classPendingTasks(
                $classLearnerIds,
                $EventID,
                $data['class_title'],
                $data['class_excerpt'],
                $ClassStartDateTimeIn24HourFormat,
                $ClassEndDateTimeIn24HourFormat,
                $classLearnerEmails,
                $data['category_id'],
                $data['instructor_id'],
                $meetUrl ?: $zoomUrl,
                $this->dt->convertToActiveDT(strtotime($ClassStartDate), "l"),
                $ClassStartDate,
                $this->dt->convertToActiveDT($ClassStartTime, "h:i A"),
                $instructorData['name'] ?? '',
                $timeZone
            );

            return [
                'data' => $esResponse['data'],
                'invalidlearners' => $invalidLearners
            ];

        } catch (Exception $e) {
            error_log("Error in createPrivateClass: " . $e->getMessage());
            return ['error' => 'An error occurred while creating the class'];
        }
    }

    /**
     * Creates a webinar with the provided data, handling all business logic including
     * virtual classroom creation, post creation, meta updates, and Elasticsearch indexing.
     *
     * @since 1.0.0
     * @access public
     * @param array $data Array containing all necessary webinar data
     * @return array Response array with success/error status and relevant data
     * <AUTHOR>
     */
    public function createWebinar($data) {
        try {
            // Prepare class data
            $this->loadModel('instructor');
            $this->loadModel('VirtualClassroom');
            $this->loadModel('invoice');
            $timeZone = $this->locale->activeTimezone();
            $classDate = substr($data['class_date_time'], 0, strpos($data['class_date_time'], "GMT"));
            $classTime = substr($data['class_date_time'], 0, strpos($data['class_date_time'], "GMT"));
            
            $ClassStartDate = $this->dt->convertToActiveDT($classDate, "Y-m-d");
            $ClassStartTime = $this->dt->convertToActiveDT($classTime, "H:i");
            $ClassStartTimeWithSecond = $ClassStartTime . ':00';
            $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;

            $endDateTimeExpression = trim($ClassStartDateTimeIn24HourFormat) . " +" . $data['class_duration'] . " minutes";
            $ClassEndDateTimeIn24HourFormat = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i");

            $gcalStartDate = $ClassStartDate . ' ' . $ClassStartTimeWithSecond;
            $gcalEndDate = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i:s");
            $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s"); 
            $eventEndDate = $this->dt->convertToActiveDT($gcalEndDate, "Y-m-d\TH:i:s"); 

            // Create class post
            $postArray = [
                'post_title' => $data['class_title'],
                'post_content' => $data['description'],
                'post_status' => "publish",
                'post_author' => 2,
                'post_excerpt' => $data['excerpt'],
                'EventStartDate' => $ClassStartDateTimeIn24HourFormat,
                'EventEndDate' => $ClassEndDateTimeIn24HourFormat,
                'EventDuration' => $data['class_duration'],
                'EventStartHour' => $this->dt->convertToActiveDT($classTime, "h"),
                'EventStartMinute' => $this->dt->convertToActiveDT($classTime, "i"),
                'EventStartMeridian' => $this->dt->convertToActiveDT($ClassStartTime, "a"),
                'EventEndHour' => $this->dt->convertToActiveDT($endDateTimeExpression, "h"),
                'EventEndMinute' => $this->dt->convertToActiveDT($endDateTimeExpression, "i"),
                'EventEndMeridian' => $this->dt->convertToActiveDT($endDateTimeExpression, "a"),
            ];

            $EventID = $this->addCustomPostClass($postArray);
            if (!$EventID) {
                return ['error' => 'Failed to create class post'];
            }

            $priceList = [];
            foreach ($data['price'] as $price) {
                $currencyCode = $price['currency'] ?? $this->locale->activeCurrency('code');
                $amount = $price['amount'] ?? 0;

                $priceDetail = $this->invoiceModel->getListPrice([
                    'ccCode' => $currencyCode,
                    'basePrice' => $amount
                ]);

                if ($priceDetail) {
                    $priceList[] = [
                        'currency_code' => $priceDetail['currency']['code'] ?? $currencyCode,
                        'exclusive_tax' => $priceDetail['exclusive_tax'] ?? 0,
                        'inclusive_tax' => $priceDetail['inclusive_tax'] ?? 0,
                        'tax' => [
                            'type' => $priceDetail['tax']['type'] ?? '',
                            'label' => $priceDetail['tax']['label'] ?? '',
                            'percentage' => $priceDetail['tax']['percentage'] ?? 0,
                            'amount' => $priceDetail['tax']['amount'] ?? 0
                        ]
                    ];
                }
            }


            // Prepare metadata
            $metaData = [
                'description'             => $data['description'],
                '_webinar_class'          => '1',
                '_webinar_category'       => $data['category_ids'] ?? [],
                'Category_Tree_Structure' => $data['category'] ?? '',
                'PromoVideo'              => $data['promo_video'] ?? '',
                'targetAgeGroup'          => $data['TargetAgeGroup'] ?? '',
                'targetProfession'        => $data['Targetprofession'] ?? '',
                'What_Best_Describe_You'       => $data['best_describe_you'] ?? '',
                'Accessible_on'           => $data['accessible_on'] ?? '',
                'class_date_time'         => $ClassStartDateTimeIn24HourFormat,
                'class_end_date_time'     => $ClassEndDateTimeIn24HourFormat,
                'class_duration'          => $data['class_duration'],
                'class_title'             => $data['class_title'],
                'class_instructors'       => $data['instructor_id'],
                'event_type'              => 'webinar',
                'Related_Courses'               => $data['course_id'] ?? 0,
                'timezone'                => $timeZone,
                'Created_by'              => 'Yuno Content Admin',
                'WEBINAR_PRIVACY'         => '{"anyone_logged_in_users":true,"active_enrolled_users":false}',
                'list_price'              => $priceList

            ];

            if (!$this->updateClassPostMeta($EventID, $metaData)) {
                wp_delete_post($EventID, true);
                return ['error' => 'Failed to update webinar metadata'];
            }

            // Set category terms
            if (!empty($data['category_ids'])) {
                wp_set_post_terms($EventID, $data['category_ids'], 'course_category');
                wp_set_object_terms($EventID, $data['category_ids'], 'tribe_events_cat');
            }

            if (!empty($data['group_ids'])) {
                $this->updateClassPostMeta($EventID, [
                    'Group_ID' => $data['group_ids'],
                    'All_Groups_ID' => $data['group_ids']
                ]);
            }

            // Get organization platform
            $orgId = !empty($data['academy_id']) ? $this->getPostData($data['academy_id'], "org_id") : 0;
            $orgQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.record_id" => $orgId]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => ["data.details.org_vc_app"]
            ];
            
            $platform = $this->es->customQuery($orgQuery, 'org');
            $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';

            // Validate virtual classroom
            $virtualClasses = $this->validateVirtualClassroom(
                $data['instructor_id'],
                $orgId,
                $orgPlatform
            );

            if (!$virtualClasses) {
                return ['error' => 'You cannot schedule a class'];
            }

            $vcPlatform = isset($virtualClasses['platform']) ? strtolower($virtualClasses['platform']) : '';

            // Get instructor details
            $instructorQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.user_id" => $data['instructor_id']]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user.name",
                    "data.details.user.email",
                    "data.details.user.image"
                ]
            ];

            $instructor = $this->es->customQuery($instructorQuery, 'signedup');
            $instructorData = $instructor['body']['hits']['hits'][0]['_source']['data']['details']['user'] ?? [];

            // Create virtual classroom based on platform
            $meetingURL = '';
            $meetUrl = '';
            $zoomUrl = '';
            $meeting_id = '';

            if ($vcPlatform === 'google meet' || $vcPlatform === 'gmeet') {
                $userAccessToken = $this->getGoogleMeetAccessToken($data['instructor_id'], $timeZone);
                if ($userAccessToken) {
                    $meetingURL = $this->createGoogleMeet(
                        $EventID,
                        $data['class_duration'],
                        $userAccessToken,
                        $timeZone,
                        $data['class_title'],
                        $data['description'],
                        $eventStartDate,
                        $eventEndDate,
                        $data['learner_emails'] ?? [],
                        $data['batch_id'] ?? 0,
                        $data['instructor_id']
                    );

                    if ($meetingURL) {
                        $meetCode = '';
                        $guest_join_url = $meetingURL['Recording_Share_Url'] ?? '';
                        if (!empty($guest_join_url)) {
                            $meetCode = preg_replace('/https:\/\/meet\.google\.com\//', '', $guest_join_url);
                            $meetCode = strtoupper(str_replace('-', '', $meetCode));
                            $meeting_id = $meetCode;
                        }
                        $meetData = [
                            'YunoClassGoogleMeetLink'      => $meetingURL['Recording_Share_Url'] ?? '',
                            'YunoClassGoogleCalendarId'     => $meetingURL['WebinarID'] ?? '',
                            'InstructorScheduleClassMeetID' => $meetCode
                        ];
                        $this->updateClassPostMeta($EventID, $meetData);
                        $meetUrl = $guest_join_url;
                    }
                }
            } elseif ($vcPlatform === 'zoom') {
                $meetingURL = $this->createZoomMeeting(
                    $data['class_title'],
                    $ClassStartDateTimeIn24HourFormat,
                    $data['class_duration'],
                    $timeZone,
                    $instructorData['email'] ?? '',
                    $data['instructor_id']
                );
                if ($meetingURL) {
                    $meeting_id = $meetingURL['id'];
                    $meeting_join_url = $meetingURL['join_url'];
                    $meeting_start_url = $meetingURL['start_url'];
                    $zoomUrl = $meeting_start_url;
                    $zoomData = [
                        'InstructorScheduleClassZoomURL' => $meeting_join_url,
                        'InstructorScheduleClassStartZoomURL' => $meeting_start_url,
                        'InstructorScheduleClassZoomID' => $meeting_id
                    ];
                    $this->updateClassPostMeta($EventID, $zoomData);
                }
            }

            if (!$meetingURL) {
                wp_delete_post($EventID, true);
                return ['error' => 'Failed to create virtual classroom'];
            }

            // Create guest URL
            $encodeValue = base64_encode($meeting_id . '@@' . 'undefined' . '@@' . $EventID . '@@' . '');
            list($protocol, $siteURL) = explode("://", site_url());
            $guest_url = "https://" . $siteURL . "/open/" . $encodeValue;
            update_post_meta($EventID, 'InstructorScheduleClassGuestZoomURL', $guest_url);

            // Process learner data
            $classLearnerIds = [];
            $classLearnerEmails = [];
            $ClassScheduleLearnerEmail = [];
            $groupIds = [];
            
            foreach ($data['learners'] as $group) {
                if (isset($group['user_count']) && $group['user_count'] > 0) {
                    foreach ($group['users'] as $user) {
                        $classLearnerIds[] = intval($user['user_id']);
                        $groupIds[] = intval($user['group_id']);
                    }
                } elseif (isset($group['id']) && !empty($group['id'])) {
                    $classLearnerIds[] = intval($group['id']);
                    if (isset($group['email']) && is_email($group['email'])) {
                        $classLearnerEmails[] = sanitize_email($group['email']);
                        $ClassScheduleLearnerEmail[]['email'] = $group['email'];
                    }
                } elseif (empty($group['id']) && isset($group['email']) && is_email($group['email'])) {
                    $classLearnerEmails[] = sanitize_email($group['email']);
                }
            }
            $classLearnerIds = array_unique($classLearnerIds);
            $groupIds = array_unique($groupIds);
            $classLearnerEmails = array_unique($classLearnerEmails);

            // Prepare Elasticsearch payload
            $elasticsearchPayload = [
                'data' => [
                    'details' => [
                        'record_id' => $EventID,
                        'user_id' => $data['instructor_id'],
                        'event_type' => 'webinar',
                        "launch_status" => false,
                        'event_label' => 'Webinar Created',
                        'resource_type' => 'tribe_events',
                        'title' => $data['class_title'],
                        'date' => [
                            'month' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "M"),
                            'year' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y"),
                            'day' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "d"),
                        ],
                        'grid_date' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, 'M d, Y'),
                        'url' => get_permalink($EventID),
                        'class_type' => 'webinar',
                        'time' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, 'h:i A'),
                        'duration' => "{$data['class_duration']} Minutes",
                        'category' => $data['category_ids'] ?? [],
                        'start' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, 'Y-m-d\TH:i:s'),
                        'edit_url' => esc_url_raw(site_url("/ielts/schedule-class/?edit&classID={$EventID}")),
                        'batch_id' => 0,
                        'delete_url' => esc_url_raw(site_url("/wp-json/yuno/v1/delete/class/{$EventID}/{$data['instructor_id']}")),
                        'meet_url' => $meetUrl,
                        'zoom_url' => $zoomUrl,
                        'class_instructors' => $data['instructor_id'],
                        'instructor_name' => $instructorData['name'] ?? '',
                        'instructor_image' => $instructorData['image'] ?? '',
                        'guest_url' => $guest_url,
                        'enrollment_count' => count($classLearnerIds),
                        'related_courses' => $data['course_id'] ?? 0,
                        'event_date' => $this->dt->convertToActiveDT($ClassStartDate . ' ' . $ClassStartTimeWithSecond, "Y-m-d\TH:i:s"),
                        'course_id' => $data['course_id'] ?? 0,
                        'academy_id' => $data['academy_id'],
                        'class_detail' => get_permalink($EventID),
                        'meeting_id' => $meeting_id,
                        'class_date_time' => $this->getPostData($EventID, "_EventStartDate"),
                        'learner_avg_class_rating' => 0,
                        'staff_avg_class_rating' => 0,
                        'class_end_date_time' => $this->getPostData($EventID, "_EventEndDate"),
                        'listing_price' => $priceList
                    ],
                    '@timestamp' => $this->dt->currentSystemDT()
                ]
            ];

            // Index in Elasticsearch
            $esResponse = $this->addClassDataES('privateclass', $elasticsearchPayload, $EventID, 'webinar', $virtualClasses);

            // Schedule notifications and tasks
            $this->classPendingTasks(
                $classLearnerIds,
                $EventID,
                $data['class_title'],
                $data['excerpt'],
                $ClassStartDateTimeIn24HourFormat,
                $ClassEndDateTimeIn24HourFormat,
                $classLearnerEmails,
                $data['category_ids'] ?? [],
                $data['instructor_id'],
                $meetUrl ?: $zoomUrl,
                $this->dt->convertToActiveDT(strtotime($ClassStartDate), "l"),
                $ClassStartDate,
                $this->dt->convertToActiveDT($ClassStartTime, "h:i A"),
                $instructorData['name'] ?? '',
                $timeZone
            );

            return [
                'data' => $esResponse['data']
            ];

        } catch (Exception $e) {
            error_log("Error in createWebinar: " . $e->getMessage());
            if (isset($EventID)) {
                wp_delete_post($EventID, true);
            }
            return ['error' => 'An error occurred while creating the webinar'];
        }
    }

    /**
     * Updates an existing webinar with new details.
     *
     * @param array $data The webinar data to update
     * @return array Response containing success/error status and data
     */
    public function updateWebinar($data) {
        try {
            $this->loadModel('instructor');
            $this->loadModel('VirtualClassroom');
            $classId = $data['class_id'];
            $instructorId = $data['instructor_id'];

            // Check if class exists and instructor is authorized
            $checkClassExistance = $this->getPostData($classId, "_webinar_class");
            if (!$checkClassExistance) {
                return ['error' => 'Class not found'];
            }

            $classInstructor = $this->getPostData($classId, "_ecp_custom_13");
            if ($instructorId != $classInstructor) {
                return ['error' => 'You are not the instructor of this class'];
            }

            // Check if class can be edited
            $classActualScheduledTime = $this->getPostData($classId, "_EventStartDate");
            $currentTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            if (strtotime($currentTime) >= strtotime($classActualScheduledTime)) {
                return ['error' => "This class can't be edited as it has happened or is live"];
            }

            // Process date/time
            $ClassDate = substr($data['class_date_time'], 0, strpos($data['class_date_time'], "GMT"));
            $ClassTime = substr($data['class_date_time'], 0, strpos($data['class_date_time'], "GMT"));
            
            $ClassStartDate = $this->dt->convertToActiveDT($ClassDate, "Y-m-d");
            $ClassStartTime = $this->dt->convertToActiveDT($ClassTime, "H:i");
            $ClassStartTimeWithSecond = $this->dt->convertToActiveDT($ClassTime, "H:i:s");

            $ClassStartDateTimeIn24HourFormat = $ClassStartDate . ' ' . $ClassStartTime;
            $endDateTimeExpression = trim($ClassStartDateTimeIn24HourFormat) . " +" . $data['class_duration'] . " minutes";
            $ClassEndDateTimeIn24HourFormat = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i");
            $gcalEndDate = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i:s");
            $ClassEndDate = $this->dt->convertToActiveDT($ClassEndDateTimeIn24HourFormat, "Y-m-d");

            $endTime = $this->dt->convertToActiveDT(trim($ClassTime) . " +" . $data['class_duration'] . " minutes", "H:i");
            $ClassEndHour = $this->dt->convertToActiveDT($endTime, "h");
            $ClassEndHour24 = $this->dt->convertToActiveDT($endTime, "H");
            $ClassEndMinute = $this->dt->convertToActiveDT($endTime, "i");
            $ClassStartSecond = $this->dt->convertToActiveDT($ClassTime, "s");

            $gcalStartDate = $ClassStartDate . ' ' . $ClassStartTimeWithSecond;
            $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s");
            $eventEndDate = $this->dt->convertToActiveDT($gcalEndDate, "Y-m-d\TH:i:s");

            // Update post
            $post = [
                'ID' => $classId,
                'post_title' => $data['class_title'],
                'post_content' => $data['class_description'],
                'post_excerpt' => $data['class_excerpt']
            ];
            wp_update_post($post);

            $priceList = [];
            foreach ($data['price'] as $price) {
                $currencyCode = $price['currency'] ?? $this->locale->activeCurrency('code');
                $amount = $price['amount'] ?? 0;

                $priceDetail = $this->invoiceModel->getListPrice([
                    'ccCode' => $currencyCode,
                    'basePrice' => $amount
                ]);

                if ($priceDetail) {
                    $priceList[] = [
                        'currency_code' => $priceDetail['currency']['code'] ?? $currencyCode,
                        'exclusive_tax' => $priceDetail['exclusive_tax'] ?? 0,
                        'inclusive_tax' => $priceDetail['inclusive_tax'] ?? 0,
                        'tax' => [
                            'type' => $priceDetail['tax']['type'] ?? '',
                            'label' => $priceDetail['tax']['label'] ?? '',
                            'percentage' => $priceDetail['tax']['percentage'] ?? 0,
                            'amount' => $priceDetail['tax']['amount'] ?? 0
                        ]
                    ];
                }
            }

            // Update metadata
            $metaData = [
                '_ecp_custom_17' => $data['pre_class_assignment'],
                '_ecp_custom_30' => $data['post_class_assignment'],
                'PromoVideo' => $data['promo_video'],
                '_ecp_custom_13' => $instructorId,
                '_EventStartDate' => $ClassStartDate . ' ' . $ClassStartTimeWithSecond,
                '_EventEndDate' => $ClassEndDate . ' ' . $ClassEndHour24 . ':' . $ClassEndMinute . ':' . $ClassStartSecond,
                '_EventDuration' => $data['class_duration'] * 60,
                'description' => $data['class_description'],
                'targetAgeGroup' => $data['TargetAgeGroup'],
                'targetprofession' => $data['Targetprofession'],
                'What_Best_Describe_You' => $data['best_describe_you'],
                'Accessible_On' => $data['accessible_on'],
                'Related_Courses' => $data['course_id'],
                'list_price' => $priceList
            ];
            $this->updateClassPostMeta($classId, $metaData);

            // Handle virtual classroom
            $academy = $data['academy_id'];
            if (!empty($academy)) {
                $orgId = $this->getPostData($academy, "org_id") ?? 0;
            } else {
                $orgId = 0;
            }

            $orgQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.record_id" => $orgId]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => ["data.details.org_vc_app"]
            ];

            $platform = $this->es->customQuery($orgQuery, 'org');
            $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';

            $vcQuery['custom'] = [
                'id' => $instructorId,
                'orgId' => $orgId,
                'platform' => $orgPlatform
            ];

            $virtualClasses = $this->VirtualClassroomModel->getVirtualClassroom($vcQuery);
            if (!$virtualClasses || empty($virtualClasses['connection']) || $virtualClasses['connection'] != 1) {
                if (!empty($academy)) {
                    $this->sendClassFailureAlert($academy, $instructorId);
                }
                return ['error' => "You cannot schedule a class"];
            }

            $vcPlatform = strtolower($virtualClasses['platform'] ?? '');
            $meetingURL = null;

            if ($vcPlatform === 'google meet' || $vcPlatform === 'gmeet') {
                $usrTokenGoogle = $this->getGoogleMeetAccessToken($instructorId, $this->locale->activeTimezone(), $orgId);
                $meetingURL = $this->updateGoogleMeet($classId, $usrTokenGoogle, $this->locale->activeTimezone(), 
                    $data['class_title'], $data['class_description'], $eventStartDate, $eventEndDate, [], $data['batch_id'], $instructorId);
            } elseif ($vcPlatform === 'zoom') {
                $zoomMeetingID = $this->getPostData($classId, "InstructorScheduleClassZoomID") ?? 0;
                $meetingURL = $this->updateZoomMeeting($data['class_title'], $eventStartDate, $data['class_duration'], 
                    $this->locale->activeTimezone(), $zoomMeetingID);
            }

            if (!$meetingURL) {
                return ['error' => 'Failed to update virtual meeting'];
            }

            // Update Elasticsearch
            $classStartDateEs = $this->getPostData($classId, "_EventStartDate") ?? '';
            $classEndDateEs = $this->getPostData($classId, "_EventEndDate") ?? '';

            $dayDiff = abs(round((strtotime($ClassStartDate) - strtotime($currentTime)) / 86400));
            $dayLabel = $dayDiff === 0 ? "Today" : ($dayDiff === 1 ? "$dayDiff day" : "$dayDiff days");

            $elasticsearchPayload = [
                'data' => [
                    'details' => [
                        'record_id' => $classId,
                        'event_date' => $eventStartDate,
                        'date' => [
                            'month' => $this->dt->convertToActiveDT($eventStartDate, "M"),
                            'year'  => $this->dt->convertToActiveDT($eventStartDate, "Y"),
                            'day'   => $this->dt->convertToActiveDT($eventStartDate, "d"),
                        ],
                        'grid_date' => $this->dt->convertToActiveDT($eventStartDate, 'M d, Y'),
                        'time' => $this->dt->convertToActiveDT($eventStartDate, 'h:i A'),
                        'class_date_time' => $classStartDateEs,
                        'class_end_date_time' => $classEndDateEs,
                        'start' => $dayLabel,
                        'duration' => "{$data['class_duration']} Minutes",
                        'listing_price' => $priceList
                    ],
                    '@timestamp' => $this->dt->currentSystemDT()
                ]
            ];

            $esResponse = $this->updateClassDataES('privateclass', $elasticsearchPayload, $classId);

            // Handle notifications
            $classExcerpt = $this->getPostData($classId, "post_excerpt");
            $ClassLink = get_the_permalink($classId);
            $dayOfWeek = $this->dt->convertToActiveDT(strtotime($ClassStartDate), "l");
            $ClassStartTimeForWhatsApp = $this->dt->convertToActiveDT($ClassTime, "h:i A");
            $instructorName = get_user_meta($instructorId, 'yuno_display_name', true);

            $this->classPendingUpdateTasks([], [], $classId, $data['class_title'], $classExcerpt, 
                $eventStartDate, $eventEndDate, [], '', $instructorId, $ClassLink, $dayOfWeek, 
                $ClassStartDate, $ClassStartTimeForWhatsApp, $instructorName, $this->locale->activeTimezone());

            return ['data' => $esResponse['data']];

        } catch (Exception $e) {
            error_log("Error in updateWebinar: " . $e->getMessage());
            return ['error' => 'An error occurred while updating the webinar'];
        }
    }

    /**
     * Updates an existing demo class with new information.
     *
     * This function handles the update of a demo class, including:
     * - Updating WordPress post and meta data
     * - Managing virtual classroom integration
     * - Updating Elasticsearch records
     * - Handling notifications
     *
     * @since 1.0.0
     * @access public
     * @param array $data The data array containing:
     *  class_id, title, description, excerpt, instructor_id, academy_id, category_id, category_name,
     *  batch_id, related_courses, start_date, start_time, start_time_whatsapp, start_time_with_second,
     *  duration, learner_ids, learner_emails, learner_schedule_emails
     * @return array Returns success/error response with data
     */
    public function updateDemoClass($data) {
        try {
            $this->loadModel('instructor');
            $this->loadModel('VirtualClassroom');
            // Update WordPress post
            $post = array(
                'ID'           => $data['class_id'],
                'post_title'   => $data['title'],
                'post_content' => $data['description'],
                'post_excerpt' => $data['excerpt'],
            );
            wp_update_post($post);

            // Calculate end time
            $endDateTimeExpression = trim($data['start_date'] . ' ' . $data['start_time']) . " +" . $data['duration'] . " minutes";
            $ClassEndDateTimeIn24HourFormat = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i");
            $gcalEndDate = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i:s");
            $ClassEndDate = $this->dt->convertToActiveDT($ClassEndDateTimeIn24HourFormat, "Y-m-d");
            
            $endTimeExpression = trim($data['start_time']) . " +" . $data['duration'] . " minutes";
            $endTime = $this->dt->convertToActiveDT($endTimeExpression, "H:i");
            $ClassEndMinute = $this->dt->convertToActiveDT($endTime, "i");
            $ClassEndHour24 = $this->dt->convertToActiveDT($endTime, "H");
            $ClassStartSecond = $this->dt->convertToActiveDT($data['start_time'], "s");

            // Update metadata
            $ClassStartDateUpdated = $data['start_date'] . ' ' . $data['start_time_with_second'];
            $ClassEndDateUpdated = $ClassEndDate . ' ' . $ClassEndHour24 . ':' . $ClassEndMinute . ':' . $ClassStartSecond;
            $ClassDurationInSecond = (int) $data['duration'] * 60;

            $metaData = [
                // '_ecp_custom_13' => $data['instructor_id'],
                '_EventStartDate' => $ClassStartDateUpdated,
                '_EventEndDate' => $ClassEndDateUpdated,
                '_EventDuration' => $ClassDurationInSecond
            ];
            $this->updateClassPostMeta($data['class_id'], $metaData);

            // Get organization platform
            $orgId = !empty($data['academy_id']) ? $this->getPostData($data['academy_id'], "org_id") : 0;
            $orgQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.record_id" => $orgId]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => ["data.details.org_vc_app"]
            ];

            $platform = $this->es->customQuery($orgQuery, 'org');
            $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';

            // Setup virtual classroom
            $vcQuery['custom'] = [
                'id'       => $data['instructor_id'],
                'orgId'    => $orgId,
                'platform' => $orgPlatform
            ];

            $virtualClasses = $this->VirtualClassroomModel->getVirtualClassroom($vcQuery);
            if (!$virtualClasses || empty($virtualClasses['connection']) || $virtualClasses['connection'] != 1) {
                if (!empty($data['academy_id'])) {
                    $this->sendClassFailureAlert($data['academy_id'], $data['instructor_id']);
                }
                return ['success' => false, 'message' => "You cannot edit this class"];
            }

            $vcPlatform = strtolower($virtualClasses['platform'] ?? '');
            $meetingURL = null;

            // Update virtual classroom based on platform
            if ($vcPlatform === 'google meet' || $vcPlatform === 'gmeet') {
                $usrTokenGoogle = $this->getGoogleMeetAccessToken($data['instructor_id'], $this->locale->activeTimezone(), $orgId);
                $gcalStartDate = $data['start_date'] . ' ' . $data['start_time_with_second'];
                $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s");
                $eventEndDate = $this->dt->convertToActiveDT($gcalEndDate, "Y-m-d\TH:i:s");
                
                $meetingURL = $this->updateGoogleMeet(
                    $data['class_id'],
                    $usrTokenGoogle,
                    $this->locale->activeTimezone(),
                    $data['title'],
                    $data['description'],
                    $eventStartDate,
                    $eventEndDate,
                    $data['learner_schedule_emails'],
                    $data['batch_id'],
                    $data['instructor_id']
                );
            } elseif ($vcPlatform === 'zoom') {
                $zoomMeetingID = $this->getPostData($data['class_id'], "InstructorScheduleClassZoomID") ?? 0;
                $gcalStartDate = $data['start_date'] . ' ' . $data['start_time_with_second'];
                $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s");
                
                $meetingURL = $this->updateZoomMeeting(
                    $data['title'],
                    $eventStartDate,
                    $data['duration'],
                    $this->locale->activeTimezone(),
                    $zoomMeetingID
                );
            }

            if (!$meetingURL) {
                return ['success' => false, 'message' => 'Failed to update virtual meeting'];
            }

            // Update Elasticsearch
            $CurrentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            $dayDiff = abs(round((strtotime($data['start_date']) - strtotime($CurrentDateTime)) / 86400));
            $dayLabel = $dayDiff === 0 ? "Today" : ($dayDiff === 1 ? "$dayDiff day" : "$dayDiff days");

            $elasticsearchPayload = [
                'data' => [
                    'details' => [
                        'record_id'             => $data['class_id'],
                        'user_id'               => $data['instructor_id'],
                        'event_label'           => 'Demo Class Updated',
                        'resource_type'         => 'tribe_events',
                        'title'                 => $data['title'],
                        'date' => [
                            'month' => $this->dt->convertToActiveDT($eventStartDate, "M"),
                            'year'  => $this->dt->convertToActiveDT($eventStartDate, "Y"),
                            'day'   => $this->dt->convertToActiveDT($eventStartDate, "d"),
                        ],
                        'grid_date'             => $this->dt->convertToActiveDT($eventStartDate, 'M d, Y'),
                        'url'                   => get_permalink($data['class_id']),
                        'class_type'            => 'democlass',
                        'time'                  => $this->dt->convertToActiveDT($eventStartDate, 'h:i A'),
                        'duration'              => "{$data['duration']} Minutes",
                        'category'              => [$data['category_id']],
                        'start'                 => $dayLabel,
                        'edit_url'              => esc_url_raw(site_url("/ielts/schedule-class/?edit&classID={$data['class_id']}")),
                        'batch_id'              => $data['batch_id'],
                        'delete_url'            => esc_url_raw(site_url("/wp-json/yuno/v1/delete/class/{$data['class_id']}/{$data['instructor_id']}")),
                        'class_instructors'     => $data['instructor_id'],
                        'enrollment_count'      => count($data['learner_ids']),
                        'related_courses'       => $data['related_courses'],
                        'event_date'            => $eventStartDate,
                        'course_id'             => $data['related_courses'],
                        'class_detail'          => get_the_permalink($data['class_id']),
                        'class_date_time'       => $ClassStartDateUpdated,
                        'class_end_date_time'   => $ClassEndDateUpdated
                    ],
                    '@timestamp' => $this->dt->currentSystemDT()
                ]
            ];

            $esResponse = $this->updateClassDataES('privateclass', $elasticsearchPayload, $data['class_id']);

            // Handle notifications
            $ClassLink = get_the_permalink($data['class_id']);
            $unixTimestamp = strtotime($ClassStartDateUpdated);
            $dayOfWeek = $this->dt->convertToActiveDT($unixTimestamp, "l");
            $instructorName = get_user_meta($data['instructor_id'], 'yuno_display_name', true);

            $this->classPendingUpdateTasks(
                [],
                $data['learner_ids'],
                $data['class_id'],
                $data['title'],
                $data['excerpt'],
                $eventStartDate,
                $eventEndDate,
                $data['learner_emails'],
                '',
                $data['instructor_id'],
                $ClassLink,
                $dayOfWeek,
                $data['start_date'],
                $data['start_time_whatsapp'],
                $instructorName,
                $this->locale->activeTimezone()
            );

            return [
                'success' => true,
                'data' => $esResponse['data'],
                'message' => 'Demo class updated successfully'
            ];

        } catch (Exception $e) {
            error_log("Error in updateDemoClass: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while updating the demo class'
            ];
        }
    }

    /**
     * Updates an existing private class with new information.
     *
     * This function handles the update of a private class, including:
     * - Updating WordPress post and meta data
     * - Managing virtual classroom integration
     * - Updating Elasticsearch records
     * - Handling notifications
     *
     * @since 1.0.0
     * @access public
     * @param array $data The data array containing:
     *  class_id, title, description, excerpt, instructor_id, academy_id, category_id, category_name,
     *  batch_id, related_courses, start_date, start_time, start_time_whatsapp, start_time_with_second,
     *  duration, learner_ids, learner_emails, learner_schedule_emails
     * @return array Returns success/error response with data
     */
    public function updatePrivateClass($data) {
        try {
            $this->loadModel('instructor');
            $this->loadModel('VirtualClassroom');
            // Update WordPress post
            $post = array(
                'ID'           => $data['class_id'],
                'post_title'   => $data['title'],
                'post_content' => $data['description'],
                'post_excerpt' => $data['excerpt'],
            );
            wp_update_post($post);

            // Calculate end time
            $endDateTimeExpression = trim($data['start_date'] . ' ' . $data['start_time']) . " +" . $data['duration'] . " minutes";
            $ClassEndDateTimeIn24HourFormat = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i");
            $gcalEndDate = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i:s");
            $ClassEndDate = $this->dt->convertToActiveDT($ClassEndDateTimeIn24HourFormat, "Y-m-d");
            
            $endTimeExpression = trim($data['start_time']) . " +" . $data['duration'] . " minutes";
            $endTime = $this->dt->convertToActiveDT($endTimeExpression, "H:i");
            $ClassEndMinute = $this->dt->convertToActiveDT($endTime, "i");
            $ClassEndHour24 = $this->dt->convertToActiveDT($endTime, "H");
            $ClassStartSecond = $this->dt->convertToActiveDT($data['start_time'], "s");

            // Update metadata
            $ClassStartDateUpdated = $data['start_date'] . ' ' . $data['start_time_with_second'];
            $ClassEndDateUpdated = $ClassEndDate . ' ' . $ClassEndHour24 . ':' . $ClassEndMinute . ':' . $ClassStartSecond;
            $ClassDurationInSecond = (int) $data['duration'] * 60;

            $metaData = [
                'YunoClassPrivateLearners' => $data['learner_ids'],
                '_EventStartDate' => $ClassStartDateUpdated,
                '_EventEndDate' => $ClassEndDateUpdated,
                '_EventDuration' => $ClassDurationInSecond
            ];
            $this->updateClassPostMeta($data['class_id'], $metaData);

            // Get organization platform
            $orgId = !empty($data['academy_id']) ? $this->getPostData($data['academy_id'], "org_id") : 0;
            $orgQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.record_id" => $orgId]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => ["data.details.org_vc_app"]
            ];

            $platform = $this->es->customQuery($orgQuery, 'org');
            $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';

            // Setup virtual classroom
            $vcQuery['custom'] = [
                'id'       => $data['instructor_id'],
                'orgId'    => $orgId,
                'platform' => $orgPlatform
            ];

            $virtualClasses = $this->VirtualClassroomModel->getVirtualClassroom($vcQuery);
            if (!$virtualClasses || empty($virtualClasses['connection']) || $virtualClasses['connection'] != 1) {
                if (!empty($data['academy_id'])) {
                    $this->sendClassFailureAlert($data['academy_id'], $data['instructor_id']);
                }
                return ['success' => false, 'message' => "You cannot edit this class"];
            }

            $vcPlatform = strtolower($virtualClasses['platform'] ?? '');
            $meetingURL = null;

            // Update virtual classroom based on platform
            if ($vcPlatform === 'google meet' || $vcPlatform === 'gmeet') {
                $usrTokenGoogle = $this->getGoogleMeetAccessToken($data['instructor_id'], $this->locale->activeTimezone(), $orgId);
                $gcalStartDate = $data['start_date'] . ' ' . $data['start_time_with_second'];
                $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s");
                $eventEndDate = $this->dt->convertToActiveDT($gcalEndDate, "Y-m-d\TH:i:s");
                
                $meetingURL = $this->updateGoogleMeet(
                    $data['class_id'],
                    $usrTokenGoogle,
                    $this->locale->activeTimezone(),
                    $data['title'],
                    $data['description'],
                    $eventStartDate,
                    $eventEndDate,
                    $data['learner_schedule_emails'],
                    $data['batch_id'],
                    $data['instructor_id']
                );
            } elseif ($vcPlatform === 'zoom') {
                $zoomMeetingID = $this->getPostData($data['class_id'], "InstructorScheduleClassZoomID") ?? 0;
                $gcalStartDate = $data['start_date'] . ' ' . $data['start_time_with_second'];
                $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s");
                
                $meetingURL = $this->updateZoomMeeting(
                    $data['title'],
                    $eventStartDate,
                    $data['duration'],
                    $this->locale->activeTimezone(),
                    $zoomMeetingID
                );
            }

            if (!$meetingURL) {
                return ['success' => false, 'message' => 'Failed to update virtual meeting'];
            }

            // Update Elasticsearch
            $CurrentDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
            $dayDiff = abs(round((strtotime($data['start_date']) - strtotime($CurrentDateTime)) / 86400));
            $dayLabel = $dayDiff === 0 ? "Today" : ($dayDiff === 1 ? "$dayDiff day" : "$dayDiff days");

            $elasticsearchPayload = [
                'data' => [
                    'details' => [
                        'record_id'             => $data['class_id'],
                        'user_id'               => $data['instructor_id'],
                        'event_label'           => 'Private Class Updated',
                        'resource_type'         => 'tribe_events',
                        'title'                 => $data['title'],
                        'date' => [
                            'month' => $this->dt->convertToActiveDT($eventStartDate, "M"),
                            'year'  => $this->dt->convertToActiveDT($eventStartDate, "Y"),
                            'day'   => $this->dt->convertToActiveDT($eventStartDate, "d"),
                        ],
                        'grid_date'             => $this->dt->convertToActiveDT($eventStartDate, 'M d, Y'),
                        'url'                   => get_permalink($data['class_id']),
                        'class_type'            => 'privateclass',
                        'time'                  => $this->dt->convertToActiveDT($eventStartDate, 'h:i A'),
                        'duration'              => "{$data['duration']} Minutes",
                        'category'              => [$data['category_id']],
                        'start'                 => $dayLabel,
                        'edit_url'              => esc_url_raw(site_url("/ielts/schedule-class/?edit&classID={$data['class_id']}")),
                        'batch_id'              => $data['batch_id'],
                        'delete_url'            => esc_url_raw(site_url("/wp-json/yuno/v1/delete/class/{$data['class_id']}/{$data['instructor_id']}")),
                        'class_instructors'     => $data['instructor_id'],
                        'enrollment_count'      => count($data['learner_ids']),
                        'related_courses'       => $data['related_courses'],
                        'event_date'            => $eventStartDate,
                        'course_id'             => $data['related_courses'],
                        'class_detail'          => get_the_permalink($data['class_id']),
                        'class_date_time'       => $ClassStartDateUpdated,
                        'class_end_date_time'   => $ClassEndDateUpdated
                    ],
                    '@timestamp' => $this->dt->currentSystemDT()
                ]
            ];

            $esResponse = $this->updateClassDataES('privateclass', $elasticsearchPayload, $data['class_id']);

            // Handle notifications
            $ClassLink = get_the_permalink($data['class_id']);
            $unixTimestamp = strtotime($ClassStartDateUpdated);
            $dayOfWeek = $this->dt->convertToActiveDT($unixTimestamp, "l");
            $instructorName = get_user_meta($data['instructor_id'], 'yuno_display_name', true);

            $this->classPendingUpdateTasks(
                [],
                $data['learner_ids'],
                $data['class_id'],
                $data['title'],
                $data['excerpt'],
                $eventStartDate,
                $eventEndDate,
                $data['learner_emails'],
                '',
                $data['instructor_id'],
                $ClassLink,
                $dayOfWeek,
                $data['start_date'],
                $data['start_time_whatsapp'],
                $instructorName,
                $this->locale->activeTimezone()
            );

            return [
                'success' => true,
                'data' => $esResponse['data'],
                'message' => 'Private class updated successfully'
            ];

        } catch (Exception $e) {
            error_log("Error in updatePrivateClass: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while updating the private class'
            ];
        }
    }

    /**
     * Reschedules an existing class to a new date and time.
     *
     * This function handles the rescheduling of any class type (private, demo, or webinar) while maintaining
     * all existing class properties. It updates the class timing in WordPress, updates virtual meeting details
     * (Zoom/Google Meet), synchronizes changes with Elasticsearch, and triggers notifications to enrolled learners.
     *
     * @since 1.0.0
     * @access public
     * @param array $data Array containing:
     *  - class_id: The ID of the class to reschedule
     *  - instructor_id: The ID of the instructor
     *  - start_date: The new start date (Y-m-d format)
     *  - start_time: The new start time (H:i format)
     *  - start_time_whatsapp: The new start time (h:i A format)
     *  - start_time_with_second: The new start time (H:i:s format)
     *  - current_time: The current time for validation
     * @return array Returns success/error response with data
     */
    public function rescheduleClass($data) {
        try {
            $this->loadModel('instructor');
            $this->loadModel('VirtualClassroom');
            // Get class details
            $classId = $data['class_id'];
            $classTitle = get_the_title($classId);
            $classDuration = intval($this->getPostData($classId, "_EventDuration") / 60);
            $classExcerpt = $this->getPostData($classId, "post_excerpt");
            $classContent = $this->getPostData($classId, "post_content");
            $classType = $this->getPostData($classId, "_private_class") ? 'privateclass' : 
                        ($this->getPostData($classId, "_demo_class") ? 'democlass' : 'webinar');
            
            // Calculate end time
            $startDateTime = $data['start_date'] . ' ' . $data['start_time'];
            $endDateTimeExpression = trim($startDateTime) . " +" . $classDuration . " minutes";
            $endDateTime = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i");
            $gcalEndDate = $this->dt->convertToActiveDT($endDateTimeExpression, "Y-m-d H:i:s");
            
            // Update WordPress post metadata
            $metaData = [
                '_EventStartDate' => $this->dt->convertToActiveDT($startDateTime, "Y-m-d H:i:s"),
                '_EventEndDate' => $this->dt->convertToActiveDT($endDateTime, "Y-m-d H:i:s")
            ];
            
            $this->updateClassPostMeta($classId, $metaData);
            
            // Prepare virtual meeting update
            $gcalStartDate = $data['start_date'] . ' ' . $data['start_time_with_second'];
            $eventStartDate = $this->dt->convertToActiveDT($gcalStartDate, "Y-m-d\TH:i:s");
            $eventEndDate = $this->dt->convertToActiveDT($gcalEndDate, "Y-m-d\TH:i:s");
            
            // Get organization details
            $academy = intval($this->getPostData($classId, "Academy_ID") ?? 0);
            $orgId = $academy ? intval($this->getPostData($academy, "org_id") ?? 0) : 0;
            
            // Get platform type
            $orgQuery = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.record_id" => $orgId]]
                                ]
                            ]
                        ]
                    ]
                ],
                "_source" => ["data.details.org_vc_app"]
            ];
            
            $platform = $this->es->customQuery($orgQuery, 'org');
            $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';
            
            // Update virtual meeting based on platform
            $meetingURL = null;
            if ($orgPlatform === 'google meet' || $orgPlatform === 'gmeet') {
                $usrTokenGoogle = $this->getGoogleMeetAccessToken($data['instructor_id'], $this->locale->activeTimezone(), $orgId);
                
                // Check instructor availability
                $availabilityQuery = [
                    'resource_id' => $data['instructor_id'],
                    'start_date' => $data['start_date'],
                    'end_date' => $this->dt->convertToActiveDT($endDateTime, "Y-m-d"),
                    'start_time' => $data['start_time'],
                    'end_time' => $this->dt->convertToActiveDT($endDateTimeExpression, "H:i"),
                    'org_id' => $orgId,
                    'class_id' => $classId
                ];
                
                $instructorAvailability = $this->instructorModel->getInstructorAvailability($availabilityQuery, ['schema' => 'Availability']);
                $isAvailable = !empty($instructorAvailability) 
                    && isset($instructorAvailability['time_slots']['is_available']) 
                    && $instructorAvailability['time_slots']['is_available'] == 1;
                
                if (!$isAvailable) {
                    return [
                        'success' => false,
                        'message' => "Instructor is not available at the requested time"
                    ];
                }
                
                $meetingURL = $this->updateGoogleMeet(
                    $classId, 
                    $usrTokenGoogle, 
                    $this->locale->activeTimezone(),
                    $classTitle, 
                    $classContent, 
                    $eventStartDate, 
                    $eventEndDate, 
                    [], 
                    0, 
                    $data['instructor_id']
                );
            } elseif ($orgPlatform === 'zoom') {
                $zoomMeetingID = $this->getPostData($classId, "InstructorScheduleClassZoomID") ?? 0;
                $meetingURL = $this->updateZoomMeeting(
                    $classTitle, 
                    $eventStartDate, 
                    $classDuration, 
                    $this->locale->activeTimezone(), 
                    $zoomMeetingID
                );
            }
            
            if (!$meetingURL) {
                return [
                    'success' => false,
                    'message' => 'Failed to update virtual meeting'
                ];
            }
            
            // Update Elasticsearch data
            $classStartDateEs = $this->getPostData($classId, "_EventStartDate") ?? '';
            $classEndDateEs = $this->getPostData($classId, "_EventEndDate") ?? '';
            $guestUrl = $this->getPostData($classId, "InstructorScheduleClassGuestZoomURL") ?? '';
            
            $dayDiff = abs(round((strtotime($data['start_date']) - strtotime($data['current_time'])) / 86400));
            $dayLabel = $dayDiff === 0 ? "Today" : ($dayDiff === 1 ? "$dayDiff day" : "$dayDiff days");
            
            $elasticsearchPayload = [
                'data' => [
                    'details' => [
                        'record_id' => $classId,
                        'event_date' => $eventStartDate,
                        'date' => [
                            'month' => $this->dt->convertToActiveDT($eventStartDate, "M"),
                            'year'  => $this->dt->convertToActiveDT($eventStartDate, "Y"),
                            'day'   => $this->dt->convertToActiveDT($eventStartDate, "d"),
                        ],
                        'grid_date' => $this->dt->convertToActiveDT($eventStartDate, 'M d, Y'),
                        'time' => $this->dt->convertToActiveDT($eventStartDate, 'h:i A'),
                        'class_date_time' => $classStartDateEs,
                        'class_end_date_time' => $classEndDateEs,
                        'start' => $dayLabel,
                        'duration' => "{$classDuration} Minutes",
                        'user_id' => $data['instructor_id'],
                        'guest_url' => $guestUrl,
                        'class_instructors' => $data['instructor_id'],
                    ],
                    '@timestamp' => $this->dt->currentSystemDT()
                ]
            ];
            
            $esResponse = $this->updateClassDataES($classType, $elasticsearchPayload, $classId);
            
            // Prepare notification data
            $classLink = get_the_permalink($classId);
            $dayOfWeek = $this->dt->convertToActiveDT(strtotime($data['start_date']), "l");
            $instructorName = get_user_meta($data['instructor_id'], 'yuno_display_name', true);
            
            // Send notifications
            $this->classPendingUpdateTasks(
                [], 
                [], 
                $classId, 
                $classTitle, 
                $classExcerpt, 
                $eventStartDate, 
                $eventEndDate, 
                [], 
                '', 
                $data['instructor_id'], 
                $classLink, 
                $dayOfWeek, 
                $data['start_date'], 
                $data['start_time_whatsapp'], 
                $instructorName, 
                $this->locale->activeTimezone()
            );
            
            return [
                'success' => true,
                'message' => 'Class rescheduled successfully',
                'data' => $esResponse['data']
            ];
            
        } catch (Exception $e) {
            error_log("Error in rescheduleClass: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while rescheduling the class'
            ];
        }
    }

    public function getWebinarClass($query = [], $filter = [])
    {
        $this->loadModel('course');
        $this->loadModel('learner');
        $this->loadModel('invoice');

        if (!is_array($query)) {
            $query = ['id' => $query];
        }

        if (isset($query['custom'])) {
            $classDataResponse = $this->es->customQuery($query['custom'], 'privateclass');
            if ($classDataResponse['status_code'] !== 200 || !isset($classDataResponse['body']['hits']['hits'][0])) {
                return false;
            }
            $details = $classDataResponse['body']['hits']['hits'][0]['_source']['data']['details'];
        } elseif (isset($query['id'])) {
            $classDataResponse = $this->es->read('privateclass', $query['id']);
            if ($classDataResponse['status_code'] !== 200) {
                return false;
            }
            $details = $classDataResponse['body']['_source']['data']['details'];
        } else {
            return false;
        }

        $eventType = strtolower($details['event_type'] ?? '');
        if ($eventType !== 'webinar') {
            return false;
        }

        $classId        = $details['record_id'] ?? 0;
        $classStartTime = $details['class_date_time'] ?? '';
        $classEndTime   = $details['class_end_date_time'] ?? '';

        $currDateTime = $this->dt->currentActiveDT("Y-m-d H:i:s");
        $currTS       = strtotime($currDateTime);
        $startTS      = strtotime($classStartTime);
        $endTS        = strtotime($classEndTime);

        if ($currTS < $startTS) {
            $temporalStatus = "upcoming";
        } elseif ($currTS >= $startTS && $currTS <= $endTS) {
            $temporalStatus = "live";
        } else {
            return false;
        }

        $learners = $this->learnerModel->getLearnersOfClass($classId);
        $learnerDetails = [];

        if (!empty($learners)) {
            foreach ($learners as $learner) {
                $learnerData = $this->load->subData("learner", "getLearner", $learner['user_id'], [
                    'key' => 'user',
                    'noResponse' => 'User_Minimal'
                ]);
                if ($learnerData) {
                    $learnerDetails[] = $learnerData;
                }
            }
        }

        $academyId = $details['academy_id'] ?? 0;
        $orgId = !empty($academyId) ? $this->getPostData($academyId, "org_id") : 0;

        $orgQuery = [
            "query" => [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                ["match" => ["data.details.record_id" => $orgId]]
                            ]
                        ]
                    ]
                ]
            ],
            "_source" => ["data.details.org_vc_app"]
        ];

        $platform = $this->es->customQuery($orgQuery, 'org');
        $orgPlatform = $platform['body']['hits']['hits'][0]['_source']['data']['details']['org_vc_app'] ?? '';

        $meetingUrl = '';
        if ($orgPlatform === 'zoom') {
            $meetingUrl = $details['zoom_url'] ?? '';
        } elseif ($orgPlatform === 'gmeet') {
            $meetingUrl = $details['meet_url'] ?? '';
        }

        $activeCurrency = $this->locale->activeCurrency('code');
        $defaultListingPrice = null;

        if (!empty($details['listing_price']) && is_array($details['listing_price'])) {
            foreach ($details['listing_price'] as $price) {
                if ($price['currency_code'] === $activeCurrency) {
                    $defaultListingPrice = $price;
                    break;
                }
            }
            if (!$defaultListingPrice) {
                $defaultListingPrice = $details['listing_price'][0];
            }
        } else {
            $defaultListingPrice = $details['listing_price'] ?? [];
        }
        
        $response = [
            'id' => $classId,
            'type' => 'WEBINAR',
            'temporal_status' => $temporalStatus,
            'class_title' => $this->getClassTitle($classId, ['schema' => 'Class_Title']),
            'scheduled' => [
                'start' => [
                    'time' => $this->dt->convertToSystemDT($details['class_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                    'timezone' => $this->locale->activeTimezone()
                ],
                'end' => [
                    'time' => $this->dt->convertToSystemDT($details['class_end_date_time'], "Y-m-d\TH:i:s\Z") ?? '',
                    'timezone' => $this->locale->activeTimezone()
                ],
                'duration' => intval($details['duration'] ?? 0)
            ],
            'instructor' => $this->userModel->getUser($details['class_instructors'], ['schema' => 'User_Minimal']),
            'course' => $this->load->subData("course", "getCourse", $details['course_id'], [
                'schema' => 'Course_Minimal',
                'noResponse' => ['id' => 0, 'title' => '', 'url' => '']
            ]),
            'academy' => $this->load->subData("academy", "getAcademy", $academyId, [
                'schema' => 'Academy_Basic',
                'noResponse' => ['id' => 0, 'name' => '', 'logo_url' => ['url' => '', 'alt_text' => ''], 'fav_icon_url' => ['url' => '', 'alt_text' => '']]
            ]),
            'enrollments' => $learnerDetails,
            'guest_url' => $details['guest_url'] ?? '',
            'listing_price' => [
                'currency' => [
                    'code' => $defaultListingPrice['currency_code'] ?? '',
                    'name' => '', 
                    'symbol' => '', 
                    'symbol_html' => '' 
                ],
                'exclusive_tax' => floatval($defaultListingPrice['exclusive_tax'] ?? 0),
                'inclusive_tax' => floatval($defaultListingPrice['inclusive_tax'] ?? 0),
                'tax' => [
                    'type' => $defaultListingPrice['tax']['type'] ?? '',
                    'label' => $defaultListingPrice['tax']['label'] ?? '',
                    'percentage' => floatval($defaultListingPrice['tax']['percentage'] ?? 0),
                    'amount' => floatval($defaultListingPrice['tax']['amount'] ?? 0)
                ]
            ],
            'virtual_classroom' => [
                'meeting_app' => $this->load->subData("virtualClassroom", "getVirtualClassroom", $details['class_instructors'], [
                    'noResponse' => 'Virtual_Classroom'
                ]),
                'meeting_id' => isset($details['meeting_id']) ? (string)$details['meeting_id'] : '',
                'meeting_url' => $meetingUrl
            ],
            'in_person' => [
                'place' => $this->load->subData("place", "getPlace", $details['place_id'], [
                    'noResponse' => 'Place_Minimal'
                ]),
                'classroom' => $this->load->subData("classroom", "getClassroom", $details['classroom_id'], [
                    'noResponse' => 'Classroom_Minimal'
                ])
            ]
        ];

        return [
            'class' => $this->schema->validate($response, 'Live_Upcoming_Webinar', $filter),
            'price' => $this->schema->validate($response['listing_price'], 'Price_List')
        ];
    }

}
