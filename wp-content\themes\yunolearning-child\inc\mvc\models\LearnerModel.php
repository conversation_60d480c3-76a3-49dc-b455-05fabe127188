<?php

namespace V4;

/**
 * Learner model
 */

class LearnerModel extends Model
{
    protected $es;
    protected $schema;
    protected $userModel;
    protected $orgModel;
    protected $dt;
    protected $categoryModel;
    protected $academyModel;
    protected $courseModel;

    private const ROLE_YUNO_ADMIN = 'yuno-admin';
    private const ROLE_ORG_ADMIN = 'org-admin';
    private const DEFAULT_FETCH_SIZE = 10000;
    private const STATUS_ACTIVE = 'active';

    /** @var LearnerFilter */
    public $learnerFilter;

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadModel('user');
        $this->loadModel('org');
        $this->loadLibary('dateTime', 'dt');
        $this->loadModel('category');
        $this->loadModel('academy');
        $this->loadModel('course');
    }

    private function executeMsearch(string $payload): ?array
    {
        if (empty(trim($payload))) {
            return null;
        }

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/_msearch",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/x-ndjson"
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            error_log('cURL msearch Error in getLearners: ' . $err);
            return null;
        }
        return json_decode($response, true);
                    }

    private function getBatchIdsFromFilters(array $filters): array
    {
        $msearchPayload = '';
        $queryMap = [];
        $queryIndex = 0;

        if (isset($filters['academies']) && !empty($filters['academies'])) {
            $academyId = (int)$filters['academies'][0];
            $query = [
                '_source' => ['data.details.batch_detail_ids'], 'size' => 1000, 'query' => [ 'nested' => [ 'path' => 'data.details', 'query' => [ 'bool' => [ 'must' => [ ['term' => ['data.details.academies' => $academyId]] ] ] ] ] ]
            ];
            $msearchPayload .= json_encode(['index' => 'course']) . "\n";
            $msearchPayload .= json_encode($query) . "\n";
            $queryMap[$queryIndex++] = 'academy';
        }

        if (!empty($filters['courses'])) {
            $esIds = array_map(fn($id) => 'course-' . $id, $filters['courses']);
            $query = [
                'query' => [ 'bool' => [ 'should' => [ ['terms' => ['data.details.record_id' => $filters['courses']]], ['terms' => ['_id' => $esIds]] ], 'minimum_should_match' => 1 ] ],
                '_source' => ['data.details.batch_detail_ids', 'data.details.batch_details'], 'size' => count($filters['courses'])
            ];
            $msearchPayload .= json_encode(['index' => 'course']) . "\n";
            $msearchPayload .= json_encode($query) . "\n";
            $queryMap[$queryIndex++] = 'course';
        }

        $batchIdsFromAcademy = [];
        $batchIdsFromCourse = [];
        
        $msearchResults = $this->executeMsearch($msearchPayload);

        if ($msearchResults && isset($msearchResults['responses'])) {
            foreach ($msearchResults['responses'] as $i => $response) {
                $filterType = $queryMap[$i] ?? null;
                if (!$filterType || !empty($response['error'])) continue;

                $batchIds = [];
                if (!empty($response['hits']['hits'])) {
                    foreach ($response['hits']['hits'] as $hit) {
                        $details = $hit['_source']['data']['details'] ?? [];
                        if ($filterType === 'academy' && isset($details['batch_detail_ids'])) {
                            $batchIds = array_merge($batchIds, $details['batch_detail_ids']);
                        } elseif ($filterType === 'course') {
                            if (!empty($details['batch_detail_ids'])) $batchIds = array_merge($batchIds, array_map('intval', $details['batch_detail_ids']));
                            if (!empty($details['batch_details'])) $batchIds = array_merge($batchIds, array_column(array_filter($details['batch_details'], fn($b) => !empty($b['batch_id'])), 'batch_id'));
                        }
                    }
                }
                
                if ($filterType === 'academy') $batchIdsFromAcademy = array_values(array_unique(array_filter($batchIds)));
                if ($filterType === 'course') $batchIdsFromCourse = array_values(array_unique(array_filter($batchIds)));
            }
        }
        
        return [
            'batch_ids_from_academy' => $batchIdsFromAcademy,
            'batch_ids_from_course' => $batchIdsFromCourse
        ];
    }

    private function getUserIdsFromEnrollments(array $filters, array $batchIdGroups): array
    {
        $msearchPayload = '';
        $queryMap = [];
        $queryIndex = 0;

        $buildQuery = fn($field, $ids) => [ 'query' => ['terms' => [$field => $ids]], '_source' => ['data.details.user_id'], 'size' => 10000 ];
        $buildClassQuery = fn($ids) => [ 'query' => ['bool' => ['should' => [ ['terms' => ['data.details.enrolled_classes_collection' => $ids]], ['terms' => ['data.details.attended_classes_collection' => $ids]] ], 'minimum_should_match' => 1]], '_source' => ['data.details.user_id'], 'size' => 10000 ];

        if (!empty($batchIdGroups['batch_ids_from_academy'])) {
            $msearchPayload .= json_encode(['index' => 'batchenrollmentevent']) . "\n";
            $msearchPayload .= json_encode($buildQuery('data.details.batch_id', $batchIdGroups['batch_ids_from_academy'])) . "\n";
            $queryMap[$queryIndex++] = 'academy';
        }

        if (!empty($batchIdGroups['batch_ids_from_course'])) {
            $msearchPayload .= json_encode(['index' => 'batchenrollmentevent']) . "\n";
            $msearchPayload .= json_encode($buildQuery('data.details.batch_id', $batchIdGroups['batch_ids_from_course'])) . "\n";
            $queryMap[$queryIndex++] = 'course';
        }

        if (!empty($filters['batches'])) {
            $msearchPayload .= json_encode(['index' => 'batchenrollmentevent']) . "\n";
            $msearchPayload .= json_encode($buildQuery('data.details.batch_id', $filters['batches'])) . "\n";
            $queryMap[$queryIndex++] = 'batch';
        }

        if (!empty($filters['classes'])) {
            $msearchPayload .= json_encode(['index' => 'batchenrollmentevent']) . "\n";
            $msearchPayload .= json_encode($buildClassQuery($filters['classes'])) . "\n";
            $queryMap[$queryIndex++] = 'class';
        }

        $userIdGroups = [];
        $msearchResults = $this->executeMsearch($msearchPayload);

        if ($msearchResults && isset($msearchResults['responses'])) {
            foreach ($msearchResults['responses'] as $i => $response) {
                $filterType = $queryMap[$i] ?? null;
                if (!$filterType || !empty($response['error'])) continue;
                $userIds = array_column(array_map(fn($h) => $h['_source']['data']['details'] ?? [], $response['hits']['hits'] ?? []), 'user_id');
                $userIdGroups[$filterType] = array_values(array_unique(array_filter($userIds)));
            }
        }
        return $userIdGroups;
    }

    private function intersectUserIds(array $userIdGroups): ?array
    {
        $activeGroups = array_values(array_filter($userIdGroups));
        if (empty($activeGroups)) {
            return null; 
        }

        $finalUserIds = array_shift($activeGroups);
        foreach ($activeGroups as $userIds) {
            $finalUserIds = array_intersect($finalUserIds, $userIds);
        }
        return $finalUserIds;
        }

    private function buildFinalLearnerQuery(array $filters, ?array $finalUserIds): array
    {
        $nestedMustConditions = [ [ "terms" => [ "data.details.role" => ["learner", ""] ] ] ];

        if ($finalUserIds !== null) {
            $userShouldClauses = [];
            foreach (array_values(array_unique($finalUserIds)) as $userId) {
                $userShouldClauses[] = ['term' => ['data.details.user_id' => (int) $userId]];
                $userShouldClauses[] = ['term' => ['data.details.user_id' => (string) $userId]];
            }
            $nestedMustConditions[] = [ 'bool' => [ 'should' => $userShouldClauses, 'minimum_should_match' => 1 ] ];
        }

        if (!empty($filters['org'])) {
            $nestedMustConditions[] = [ "nested" => [ "path" => "data.details.details_from_org", "query" => [ "bool" => [ "must" => [ [ "terms" => [ "data.details.details_from_org.org_id" => $filters['org'] ] ] ] ] ] ] ];
        }

        if (!empty($filters['instructors'])) {
            foreach ($filters['instructors'] as $id) {
                $shouldClauses = [ ["term" => ["data.details.my_instructors" => $id]] ];
                    if (is_numeric($id)) {
                    $shouldClauses[] = ["term" => ["data.details.my_instructors" => (int)$id]];
                    }
                $nestedMustConditions[] = [ "bool" => [ "should" => $shouldClauses, "minimum_should_match" => 1 ] ];
                }
            }

        return [
            "from" => (int)($filters['offset'] ?? 0),
            "size" => (int)($filters['limit'] ?? self::DEFAULT_FETCH_SIZE),
            "query" => [ "bool" => [ "must" => [ [ "nested" => [ "path" => "data.details", "query" => [ "bool" => [ "must" => $nestedMustConditions ] ] ] ] ] ] ]
        ];
    }

    public function getLearners($filters = [])
    {
        $batchIdGroups = $this->getBatchIdsFromFilters($filters);

        $userIdGroups = $this->getUserIdsFromEnrollments($filters, $batchIdGroups);

        $finalUserIds = $this->intersectUserIds($userIdGroups);

        if ($finalUserIds !== null && empty($finalUserIds)) {
            return $this->createEmptyLearnerResponse();
        }

        $esQuery = $this->buildFinalLearnerQuery($filters, $finalUserIds);
        $esResults = $this->executeLearnerElasticsearchQuery($esQuery);

        if (!$esResults || !empty($esResults['error']) || empty($esResults['hits']['hits'])) {
            if (!empty($esResults['error'])) {
                error_log('Elasticsearch Error in getLearners: ' . json_encode($esResults['error']));
            }
            return $this->createEmptyLearnerResponse();
        }

        $hits = $esResults['hits']['hits'];
        $totalCount = $esResults['hits']['total']['value'] ?? 0;

        $learners = [];
        $processedUserIds = [];

        foreach ($hits as $hit) {
            $details = $hit['_source']['data']['details'] ?? [];
            $userId = (int)($details['user_id'] ?? 0);

            if ($userId > 0 && !isset($processedUserIds[$userId])) {
                $learners[] = $this->shapeLearnerDataFromHit($details, $userId);
                $processedUserIds[$userId] = true;
            }
        }

        if (empty($learners)) {
            return $this->createEmptyLearnerResponse();
        }

        usort($learners, function ($a, $b) {
            return strcasecmp($a['user']['full_name'], $b['user']['full_name']);
        });

        return $this->formatLearnerResponse($learners, $totalCount);
    }

    private function shapeLearnerDataFromHit(array $details, int $userId): array
    {
        $user = $details['user'] ?? [];
        $fullName = $user['name'] ?? '';
        $nameParts = explode(' ', $fullName, 2);
        $basicDetails = $details['basic_details'] ?? [];

        $activeCategorySlug = get_user_meta($userId, 'active_category', true);
        
        $activeCategoryObj = [
            'id' => 0,
            'name' => '',
            'slug' => '',
        ];
        if (!empty($activeCategorySlug)) {
            $term = get_term_by('slug', $activeCategorySlug, 'course_category');
            if ($term !== false && !is_wp_error($term)) {
                $activeCategoryObj = [
                    'id' => (int) $term->term_id,
                    'name' => $term->name,
                    'slug' => $term->slug,
                ];
            }
        }
        
        $organizations = array_map(function ($org) {
            return [
                'organization' => [
                    'id' => (int) ($org['org_id'] ?? 0),
                    'name' => $org['name'] ?? '',
                ],
            ];
        }, $details['details_from_org'] ?? []);

        $studyAbroad = $details['study_abroad'] ?? [];
        $dreamCountry = $studyAbroad['Choose_your_dream_country'] ?? '';

        $demoRequests = [];
        foreach ($details as $key => $value) {
            if (strpos($key, 'category_details_') === 0 && is_array($value)) {
                $slug = str_replace('category_details_', '', $key);
                $info = [];
                foreach($value as $q => $a) {
                    if (is_string($a) && !in_array($q, ['updated_at', 'category'])) {
                        $info[] = [
                            'question' => ucwords(str_replace('_', ' ', $q)),
                            'response' => $a
                        ];
                    }
                }

                $demoRequests[] = [
                    'category' => [
                        'id' => null,
                        'name' => ucfirst($slug),
                        'slug' => $slug,
                    ],
                    'updated_at' => [
                        'time' => $value['updated_at'] ?? '',
                        'timezone' => 'UTC',
                    ],
                    'info' => $info,
                    'demo_requests' => array_map(function ($req) {
                        return [
                            'from_org' => ['id' => null, 'name' => ''],
                            'for_academy' => ['id' => (int)($req['academy_id'] ?? 0), 'name' => '', 'courses' => 0],
                            'course' => ['id' => (int)($req['course'] ?? 0), 'title' => '', 'url' => ''],
                            'created_at' => ['time' => $req['created_at'] ?? '', 'timezone' => 'UTC'],
                        ];
                    }, $value['demo_requests'] ?? [])
                ];
            }
        }

        return [
            'user' => [
                'id' => (int) ($details['user_id'] ?? 0),
                'role' => [$details['role'] ?? 'learner'],
                'first_name' => $nameParts[0] ?? '',
                'last_name' => $nameParts[1] ?? '',
                'full_name' => $fullName,
                'image_url' => $user['image'] ?? '',
                'is_enabled' => true,
                'current_state' => [
                    'has_phone_number' => !empty($user['phone']),
                    'active_category' => $activeCategoryObj,
                    'org_id' => (int) (($details['details_from_org'][0]['org_id']) ?? 0),
                    'redirect_url' => '',
                ],
            ],
            'time_of_study' => $details['time_of_study'] ?? '',
            'referred_by' => [
                'id' => (int)($basicDetails['referred_by'] ?? 0) ?: null,
                'role' => [],
                'first_name' => '',
                'last_name' => '',
                'full_name' => '',
                'image_url' => '',
                'is_enabled' => false,
                'current_state' => new \stdClass(),
            ],
            'organizations' => $organizations,
            'study_abroad' => [
                'what_do_you_wish_to_pursue' => $studyAbroad['What_do_you_wish_to_pursue'] ?? '',
                'choose_your_dream_country' => [
                    [
                        'id' => null,
                        'name' => $dreamCountry,
                        'code' => $dreamCountry,
                    ],
                ],
            ],
            'demo_requests' => $demoRequests,
        ];
    }
    
    private function formatLearnerResponse(array $learners, int $totalCount): array
    {
        $data = [
            [
                'rows' => $learners,
                'columns' => $this->getLearnerColumnDefinitions()
            ]
        ];

        return [
            'count' => $totalCount,
            'data' => $data,
        ];
    }

    private function createEmptyLearnerResponse(): array
    {
        return [
            'count' => 0,
            'data' => [],
        ];
    }

    private function getLearnerColumnDefinitions(): array
    {
        return [
            ['field' => 'user.full_name', 'label' => 'Name', 'sortable' => true],
            ['field' => 'user.email', 'label' => 'Email', 'sortable' => true],
            ['field' => 'user.phone', 'label' => 'Phone', 'sortable' => true],
            ['field' => 'organizations', 'label' => 'Organizations', 'sortable' => false],
            ['field' => 'user.current_state.active_category.name', 'label' => 'Active Category', 'sortable' => true],
            ['field' => 'time_of_study', 'label' => 'Time of Study', 'sortable' => true],
        ];
    }

    private function _parseIdList($value): array
    {
        if (is_array($value)) {
            return array_filter(array_map('intval', $value));
        }
        if (is_string($value) && !empty($value)) {
            return array_filter(array_map('intval', explode(',', trim($value, '[] '))));
        }
        return [];
    }

    private function executeLearnerElasticsearchQuery(array $queryBody): ?array
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/signedup/_search",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($queryBody, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "content-type: application/json"
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        if ($err) {
            error_log('cURL Error in getLearners: ' . $err);
            return null;
        }
        return json_decode($response, true);
    }

    private function _createErrorResponse(int $code, string $message): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'status' => 'error',
            'data' => null
        ];
    }

    public function getLearner($query, $current_filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch learner data from Elasticsearch (or any other data source)
            $learnerDataResponse = $this->es->read('signedup', 'signedup-' . $query['id']);
        } else {
            return false;
        }

        if ($learnerDataResponse['status_code'] == 200) {
            $learner = $learnerDataResponse['body']['_source']['data']['details'];

            $userResponse = $this->userModel->getUser($learner['user_id'], $filter = ['schema' => 'User_Minimal']);


            $detailsFromOrgResponse = $this->getDetailsFromOrg($query);

            $learneOrgs = $learner['details_from_org'];
            // Initialize an array to store the results
            $learnerOrgData = [];

            // foreach ($learneOrgs as $learneOrg) {
            //     // Set the query parameter to the current unique org ID
            //     $query = ['id' => $learneOrg['org_id']]; // Ensure org_id is used and cast as integer

            // Optional: Customize the filter array if needed
            // $filter = ['schema' => 'Organization_Minimal']; // Add filter conditions if any

            //     // Call the getOrg function and store the result
            //     $learnerOrgData[] = $this->orgModel->getOrganization($query, $filter);
            // }

            $demo_request = [];
            $categories = ["ielts", "pte", "english-speaking", "toefl", "duolingo", "french"];
            $request_count = 0;
            $study_abroad = [];
            $target_score = ["what_is_your_target_pte_score", "what_is_your_target_toefl_score", "what_is_your_target_det_score"];
            $planned = ["when_do_you_plan_to_take_the_DET_exam", "when_do_you_plan_to_take_the_IELTS_exam", "when_do_you_plan_to_take_the_PTE_exam"];
            foreach ($categories as $category) {
                $cat_name = 'category_details_' . $category;
                $request = [];

                // Check if $learner->$cat_name exists and is not an array
                if (isset($learner[$cat_name]) && is_array($learner[$cat_name])) {

                    // If $org_id exists, fetch academies
                    if (!empty($org_id)) {
                        $academies = get_post_meta($org_id, "academies", true); // Fetch academies array

                        // Ensure academies is an array
                        if (!is_array($academies)) {
                            $academies = [];
                        }
                    }

                    // Check if demo_requests exist in $cat_name and is an array
                    $proceed = true;
                    if (isset($learner[$cat_name]['demo_requests']) && is_array($learner[$cat_name]['demo_requests'])) {

                        // If $org_id exists, only proceed if any academy_id matches
                        if (!empty($org_id)) {
                            $proceed = false; // Initially set to false, will set true only if a match is found

                            foreach ($learner[$cat_name]['demo_requests'] as $demo_req) {
                                if (in_array($demo_req['academy_id'], $academies)) {
                                    $proceed = true; // Found matching academy_id, proceed with this category
                                    break; // Exit the loop once a match is found
                                }
                                // $demo_req_academies = array_map(function ($academy) {
                                // $academyData = $this->academyModel->getAcademy($demo_req['academy_id']);
                                // return [
                                //     'academy' => [
                                //         'id' => $academyData['id'],
                                //         'name' => $academyData['name'],
                                //         'fav_icon_url' => $academyData['fav_icon_url'],
                                //         'category' => $academyData['category']
                                //     ],
                                // ];
                                // }, $demo_req ?? []);
                            }
                        }
                    }
                    // Only proceed with the category if $org_id is empty or academy_id matches
                    if ($proceed) {


                        foreach ($learner[$cat_name] as $key => $val) {

                            if (isset($learner[$cat_name]['updated_at'])) {
                                $updated = $learner[$cat_name]['updated_at'];
                                // $updaated_time = $updated->format('M d, Y');
                            } else {
                                $updaated_time = "";
                            }

                            if (isset($learner[$cat_name]['course'])) {
                                $course = $learner[$cat_name]['course'];
                            } else {
                                $course = "";
                            }

                            $req = [];
                            switch ($key) {
                                case "which_" . $category . "_exam_do_you_want_to_take":
                                    $req['label'] = "Type";
                                    $req['value'] = $val;
                                    break;
                                case in_array($key, $planned):
                                    $req['label'] = "Planned";
                                    $req['value'] = $val;
                                    break;
                                case in_array($key, $target_score):
                                    $req['label'] = "Target score";
                                    $req['value'] = $val;
                                    break;
                                case "which_toefl_exam_are_you_planning_to_take":
                                    $req['label'] = "Planning For:";
                                    $req['value'] = ($val == "Don't_know" ? "Don't Know" : strtoupper($val));
                                    break;
                                case "what_is_your_target_band_score":
                                    $req['label'] = "Target Band Score";
                                    $req['value'] = $val;
                                    break;
                                case "updated_at":
                                    $request[lcfirst($key)] = array(
                                        'time' => $updaated_time,
                                        'timezone' => 'Error:MissinginES'
                                    );
                                    break;
                                case "what_is_your_current_level_of_english":
                                    $req['label'] = "Current level of English";
                                    $req['value'] = $val;
                                    break;
                                case "mainly_why_do_you_want_to_improve_your_english_speaking_skills":
                                    $req['label'] = "Why do you want to improve your English speaking skills";
                                    $req['value'] = $val;
                                    break;
                                case "what_best_describes_you":
                                    $req['label'] = "What best describes you";
                                    $req['value'] = $val;
                                    break;
                                case "why_do_you_want_to_learn_french":
                                    $req['label'] = "Why do you want to learn French";
                                    $req['value'] = $val;
                                    break;
                                case "what_is_your_current_level_of_proficiency_in_the_french_language":
                                    $req['label'] = "Current level of proficiency in the French language";
                                    $req['value'] = $val;
                                    break;
                                case "whats_your_experience_level":
                                    $req['label'] = "Experience Level";
                                    $req['value'] = $val;
                                    break;
                                case "whats_your_main_goal":
                                    $req['label'] = "Main Goal";
                                    $req['value'] = $val;
                                    break;
                                case "whats_your_area_of_interest":
                                    $req['label'] = "Area Of Interest";
                                    $req['value'] = $val;
                                    break;
                            }

                            if (!empty($req)) {
                                $request['stats'][] = $req;
                            }
                        }

                        // Get category name based on taxonomy and term slug
                        $taxonomy = 'course_category'; // Custom taxonomy
                        $term_slug = $category; // Term slug
                        $term = get_term_by('slug', $term_slug, $taxonomy);

                        if ($term) {
                            $term_name = $term->name;
                            $cat_id = $term->term_id;
                        } else {
                            $term_name = ucfirst($val);
                        }

                        $request['academy'] = array_map(function ($academy) {
                            $academyData = $this->academyModel->getAcademy($academy['academy_id']);
                            return [
                                // [
                                'id' => $academyData['id'],
                                'name' => $academyData['name'],
                                'fav_icon_url' => (!empty($academyData['fav_icon_url']) ? $academyData['fav_icon_url'] : ['url' => "", 'alt_text' => ""]),
                                'category' => $this->load->subData(
                                    'category',
                                    'getCategory',
                                    $academyData['category'],
                                    ['schema' => 'Category_Minimal']
                                )
                                // ],
                            ];
                        }, $learner[$cat_name]['demo_requests'] ?? []);
                        $request['course'] = array_map(function ($course) {
                            $courseData = $this->courseModel->getCourse($course['course'], ['schema' => 'Course_Minimal']);
                            return [
                                'id' => $courseData['id'],
                                'title' => $courseData['title'],
                                'url' => $courseData['url']
                            ];
                        }, $learner[$cat_name]['demo_requests'] ?? []);

                        $request['category'] = $this->load->subData(
                            'category',
                            'getCategory',
                            $cat_id,
                            ['schema' => 'Category_Minimal']
                        );


                        // Add the request to demo_request array

                        $demo_request[] = $request;
                    } // End of proceed check
                }
            }
            foreach ($learner as $k => $v) {
                if (strpos($k, 'category_details_') === 0) {
                    $request_count++;
                }
            }
            $org_data =  array_map(function ($org) {
                return [
                    'organization' => $org,
                    'created_at' => array(
                        'time' => '',
                        'timezone' => ''
                    ),
                ];
            }, $learnerOrgData ?? []);
            // print_r($org_data);

            // Build the structured response for learner
            $responseData = array(
                'user' => $userResponse,
                'time_of_study' => $learner['time_of_study'],  // Time of study
                // 'referred_by' => $learner['referred_by'],  // Referred by
                'cta' => array(
                    'cta' => $learner['cta']['cta'],  // Call-to-action
                    'name' => $learner['cta']['cta'],
                    'updated_at' =>  $learner['cta']['updated_at']  // CTA updated time
                ),
                'organizations' => $org_data,  // Organizations
                // 'organizations' => array(
                //     'organization' => $learnerOrgData,
                //     'created_at' => array(
                //         'time' => '',
                //         'time_zone' => ''
                //     ),
                //     'details_from_org' => $detailsFromOrgResponse

                // ),  // Organizations
                'study_abroad' => array(
                    'what_do_you_wish_to_pursue' => $learner['study_abroad']['What_do_you_wish_to_pursue'],  // Program to pursue
                    'choose_your_dream_country' => array(
                        'id' => 0,  //Unique Country ID stored in database
                        'name' => $learner['study_abroad']['Choose_your_dream_country'],  // Dream country, //Name of Country against its ID
                        'code' => $learner['study_abroad']['Choose_your_dream_country']  // Dream country
                    )
                ),
                'demo_request_count' => $request_count,  // Demo request count
                'demo_requests' => $demo_request
            );

            // Validate the formatted response against the 'Learner' schema
            return $this->schema->validate($responseData, 'Learner', $current_filter);
        }

        return false;
    }

    public function getDetailsFromOrg($query)
    {

        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch learner data from Elasticsearch (or any other data source)
            $learnerDataResponse = $this->es->read('signedup', 'signedup-' . $query['id']);
        } else {
            return false;
        }

        if ($learnerDataResponse['status_code'] == 200) {
            $learner = $learnerDataResponse['body']['_source']['data']['details'];
            $learneOrgs = $learner['details_from_org'];

            foreach ($learneOrgs as $learneOrg) {
                // Call the getOrg function and store the result
                // Collect all details you want to retrieve from each organization entry
                $learnerOrgData[] = [
                    'id' => $learneOrg['org_id'],
                    'user_id' => $learneOrg['user_id'],
                    'phone' => $learneOrg['phone'],
                    'crm_id' => $learneOrg['crm_id'],
                    'name' => $learneOrg['name'],
                    'created_at' => $learneOrg['created_at'],
                    'cohort' => $learneOrg['cohort'],
                    'programs' => $learneOrg['programs'],
                    'business_unit' => $learneOrg['business_unit'],
                    'email' => $learneOrg['email'],
                    'parents' => $learneOrg['parents']
                ];
            }
        }

        return false;
    }

    /**
     * Posts instructor data for a learner's signup event.
     *
     * @param array $request The request data containing batch and user details.
     * @return void
     */
    private function linkInstructor(array $request): void
    {
        $instructorId = get_post_meta($request['batch_id'], 'instructor_id', true);

        if (!empty($instructorId)) {
            $instructorPayload = [
                'data' => [
                    'details' => [
                        'user_id'       => $request['user_id'],
                        'event_type'    => 'signedup',
                        'role'          => 'learner',
                        'my_instructors' => [$instructorId]
                    ],
                    '@timestamp' => date('Y-m-d H:i:s')
                ]
            ];

            $this->postInstructorPayload($instructorPayload);
        }
    }

    /**
     * Sends instructor payload to the external service.
     *
     * @param array $payload The instructor payload to send.
     * @return void
     */
    private function postInstructorPayload(array $payload): void
    {
        post_instructors_data($payload);
    }

    /**
     * Updates the organization users object based on the request data.
     *
     * @param array $request The request data containing organization and user details.
     * @return void
     */
    public function linkOrgDetails($reqArgs)
    {
        $organizationId = $reqArgs['org_id'];

        if ($organizationId != 0) {
            // Prepare organization user details
            $organizationDetails = [
                'user_id'       => $reqArgs['user_id'],
                'org_id'        => $reqArgs['org_ig'],
                'email'         => $reqArgs['org_user_email'] ?? '',
                'name'          => $reqArgs['org_user_name'] ?? '',
                'phone'         => $reqArgs['org_user_phone'] ?? '',
                'crm_id'        => $reqArgs['crm_id'] ?? '',
                'created_at'    => $reqArgs['created_at'] ?? '',
                'cohort'        => $reqArgs['cohort'] ?? '',
                'programs'      => $reqArgs['programs'] ?? '',
                'parents'       => $reqArgs['parents'] ?? [],
                'business_unit' => $reqArgs['business_unit'] ?? '',
            ];

            // Update or add the organization users object
            $this->updateOrganizationDetails($organizationDetails);
        }
    }

    /**
     * Updates the organization users object for signin or signup.
     *
     * @param array $organizationDetails The details of the organization user to update.
     * @return void
     */
    public function updateOrganizationDetails($orgArgs)
    {
        signin_signedup_update_org_users_object($orgArgs);
    }

    /**
     * Triggers a Segment call by adding enrollment tracking data to the pending segment table.
     *
     * @param int $enrollmentId The ID of the enrollment to track.
     * @return bool Returns true if the data was inserted successfully, false otherwise.
     */
    public function triggerSegmentCall($enrollmentId)
    {
        global $wpdb;

        // Prepare the data to insert
        $segmentData = [
            'data'      => json_encode(['enrollment_id' => $enrollmentId]),
            'event_type' => 'add_enrollment_track',
            'status'     => 'pending',
        ];

        // Insert the data into the pending segment table
        $insertResult = $wpdb->insert("{$wpdb->prefix}segment_pending_data", $segmentData);

        // Return true if insertion was successful, false otherwise
        return $insertResult !== false;
    }

    /**
     * Adds a learner-instructor relationship to the 'wp_my_learners' table if it does not already exist.
     *
     * @param array $learnerData Associative array containing 'instructor_id' and 'learner_id'.
     * @return bool Returns true if a new record was inserted, false if the record already exists.
     */
    public function addLearnerToInstructor($reqArgs, $batchDetails)
    {
        global $wpdb;

        // Validate input
        $instructorId = isset($batchDetails['instructor_id']) ? (int) $batchDetails['instructor_id'] : 0;
        $learnerId = isset($reqArgs['learner_id']) ? (int) $reqArgs['learner_id'] : 0;

        if ($instructorId === 0 || $learnerId === 0) {
            error_log("Invalid instructor_id or learner_id provided.");
            return false;
        }

        // Check if the relationship already exists
        $table = "{$wpdb->prefix}my_learners";
        $existingRecord = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table WHERE instructor_id = %d AND learner_id = %d",
                $instructorId,
                $learnerId
            ),
            ARRAY_A
        );

        if (!empty($existingRecord)) {
            // Log the existence of the record
            error_log("Learner ID: {$learnerId} is already linked to Instructor ID: {$instructorId}.");
            return true; // Record already exists
        }

        // Insert the new relationship
        $data = [
            'instructor_id' => $instructorId,
            'learner_id'    => $learnerId,
        ];

        $inserted = $wpdb->insert($table, $data);

        // Log the result and return true if the insertion succeeded
        if ($inserted !== false) {
            error_log("Successfully added Learner ID: {$learnerId} to Instructor ID: {$instructorId}.");
            return true;
        }

        // Log failure and return false
        error_log("Failed to add Learner ID: {$learnerId} to Instructor ID: {$instructorId}.");
        return false;
    }

    public function getLearnersOfClass($query)
    {
        $query = is_array($query) ? $query : ['id' => $query];

        if (isset($query['id'])) {

            $learnerQuery = [
                'custom' => [
                    "query" => [
                        "match" => [
                            'data.details.id' => $query['id']
                        ]
                    ]
                ]
            ];
            $learnerDataResponse = $this->es->customQuery($learnerQuery['custom'], 'enrolledclassevent');
        } else {
            return false;
        }

        if (isset($learnerDataResponse['status_code']) && $learnerDataResponse['status_code'] == 200) {
            $hits = $learnerDataResponse['body']['hits']['hits'];

            $learnerData = [];
            foreach ($hits as $hit) {
                $details = $hit['_source']['data']['details'];

                if (!empty($details)) {
                    $learnerData[] = [
                        'user_id' => $details['user_id'],
                        'class_id' => $details['id'],
                    ];
                }
            }
            return $learnerData;
        }

        return false;
    }

    /**
     * Retrieves a list of unique learner IDs associated with a given class
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query A scalar value or an associative array with an 'id' key representing the user's identifier.
     * @return array|false Returns an array of unique learner IDs if successful, or false on failure.
     * <AUTHOR>
     */
    public function getClassLearners($query)
    {
        $query = is_array($query) ? $query : ['id' => $query];
        if (isset($query['id'])) {

            $learnerQuery = [
                'custom' => [
                    "size" => 100,
                    "query" => [
                        "match" => [
                            'data.details.user_id' => $query['id']
                        ]
                    ]
                ]
            ];

            $learnerDataResponse = $this->es->customQuery($learnerQuery['custom'], 'enrolledclassevent');
        } else {
            return false;
        }
        if (isset($learnerDataResponse['status_code']) && $learnerDataResponse['status_code'] == 200) {


            $hits = $learnerDataResponse['body']['hits']['hits'];

            $learnerData = [];

            foreach ($hits as $hit) {
                if (isset($hit['_source']['data']['details']['id'])) {
                    $learnerData[] = $hit['_source']['data']['details']['id'];
                }
            }
            return array_values(array_unique($learnerData));
        }
        return false;
    }

    /**
     * Generates learner filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The ID of the instructor (for filtering instructor-specific learners).
     * @param int $learnerId The selected learner ID (if any).
     * @param int $counselorId The ID of the counselor (for filtering counselor-specific learners).
     * @return array Returns an array containing learner filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        return [
            'filter' => 'learner_id',
            'title' => 'Learner',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Learner',
            'ui_control_type' => 'query_suggestion',
            'selected' => $learnerId,
            'current' => '', 
            'loading' => false,
            'success' => false,
            'items' => []
        ];
    }
    public function generateEnrollmentLearnerFiltersOld($userId, $orgId, $instructorId, $learnerId, $counselorId)
    {
        $filterData = [
            'filter' => 'learner',
            'title' => 'Learner',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Learner',
            'ui_control_type' => 'dropdown',
            'selected' => $learnerId,
            'items' => []
        ];

        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);

        //  Initialize query conditions based on role
        $queryConditions = [
            [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.role" => "learner"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        if ($role === 'yuno-admin' || $role === 'counselor') {
            //  Yuno Admin: Fetch all learners (no additional filters)
        } elseif ($role === 'org-admin' && $orgId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details_from_org",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details_from_org.org_id" => $orgId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } elseif ($role === 'instructor' && $instructorId > 0) {
            $queryConditions[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.instructor_id" => $instructorId
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } 

        //  Build Elasticsearch Query
        $customQuery = [
            "_source" => ["data.details.user_id", "data.details.role", "data.details.user"],
            "query" => [
                "bool" => [
                    "must" => $queryConditions
                ]
            ]
        ];

        error_log("Elasticsearch Learner Query: " . json_encode($customQuery));

        //  Fetch Learners from Elasticsearch
        $learnerRecords = $this->es->customQuery($customQuery, 'signedup', []);

        if (!empty($learnerRecords['status_code']) && $learnerRecords['status_code'] === 200) {
            $learners = $learnerRecords['body']['hits']['hits'];

            foreach ($learners as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $userDetails = $record['_source']['data']['details']['user'] ?? [];

                $learnerId = $details['user_id'] ?? 0;
                $learnerName = $userDetails['name'] ?? '';
                $learnerEmail = $userDetails['email'] ?? '';

                if ($learnerId) {
                    $filterData['items'][] = [
                        'id' => $learnerId,
                        'label' => $learnerName . " (" . $learnerEmail . ")",
                        'filter' => 'learner'
                    ];
                }
            }
        }

        return $filterData;
    }
}
